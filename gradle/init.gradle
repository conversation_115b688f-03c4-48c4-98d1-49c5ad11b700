// ========================================
// Gradle 全局性能优化初始化脚本 🚀
// ========================================

allprojects {
    // 构建生命周期优化
    gradle.projectsEvaluated {
        // 禁用不必要的任务
        tasks.matching { task ->
            task.name in ['javadoc', 'javadocJar', 'sourcesJar'] && 
            !project.hasProperty('release')
        }.configureEach {
            enabled = false
        }
    }

    // 依赖下载优化
    repositories.configureEach { repo ->
        if (repo instanceof MavenArtifactRepository) {
            repo.mavenContent {
                // 只从特定仓库下载特定组的依赖
                if (repo.name.contains('aliyun')) {
                    includeGroupByRegex 'com\\.alibaba.*'
                    includeGroupByRegex 'org\\.springframework.*'
                    includeGroupByRegex 'com\\.baomidou.*'
                }
            }
        }
    }

    // 任务执行优化
    tasks.configureEach { task ->
        // 为所有任务启用构建缓存
        if (task instanceof JavaCompile) {
            task.options.incremental = true
            task.options.fork = true
        }
        
        // 测试任务优化
        if (task instanceof Test) {
            task.maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
            task.forkEvery = 100
        }
    }
}


// ========================================
// Gradle 全局初始化脚本 - 仓库镜像加速 🚀
// ========================================

allprojects {
    repositories {
        // 移除所有默认仓库
        configureEach { ArtifactRepository repo ->
            if (repo instanceof MavenArtifactRepository) {
                def url = repo.url.toString()
                // 替换官方仓库为国内镜像
                if (url.startsWith('https://repo1.maven.org/maven2') || 
                    url.startsWith('https://repo.maven.apache.org/maven2') ||
                    url.startsWith('https://jcenter.bintray.com')) {
                    project.logger.lifecycle "Repository ${repo.name} replaced by Aliyun mirror"
                    remove repo
                }
            }
        }
        
        // 添加国内镜像仓库（按速度排序）
        maven {
            name = 'aliyun-central'
            url = 'https://maven.aliyun.com/repository/central'
        }
        maven {
            name = 'aliyun-public'
            url = 'https://maven.aliyun.com/repository/public'
        }
        maven {
            name = 'aliyun-spring'
            url = 'https://maven.aliyun.com/repository/spring'
        }
        maven {
            name = 'aliyun-spring-plugin'
            url = 'https://maven.aliyun.com/repository/spring-plugin'
        }
        
        // 腾讯云镜像作为备用
        maven {
            name = 'tencent-central'
            url = 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/'
        }
        
        // 华为云镜像作为备用
        maven {
            name = 'huawei-central'
            url = 'https://mirrors.huaweicloud.com/repository/maven/'
        }
        
        // 最后添加官方仓库作为兜底
        mavenCentral()
    }
}

// 插件仓库镜像
settingsEvaluated { settings ->
    settings.pluginManagement {
        repositories {
            maven {
                name = 'aliyun-gradle-plugin'
                url = 'https://maven.aliyun.com/repository/gradle-plugin'
            }
            maven {
                name = 'aliyun-spring-plugin'
                url = 'https://maven.aliyun.com/repository/spring-plugin'
            }
            gradlePluginPortal()
        }
    }
} 