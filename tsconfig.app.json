{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable",
      "WebWorker"
    ],
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ]
    },
    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noUncheckedSideEffectImports": true
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*",
    "tests/**/*.ts",
    "mock/asyncRoutes.ts",
    "src/service/interface/CrudInterface.ts"
  ],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
