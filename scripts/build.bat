@echo off
echo 🔨 TXY-ERP 构建脚本
echo ==================

echo 选择构建类型:
echo 1. 快速构建（跳过测试）
echo 2. 完整构建（包含测试）
echo 3. 清理构建

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 🚀 快速构建...
    call %~dp0gradlew.bat build -x test --no-build-cache
) else if "%choice%"=="2" (
    echo 🧪 完整构建...
    call %~dp0gradlew.bat clean build --no-build-cache
) else if "%choice%"=="3" (
    echo 🧹 清理构建...
    call %~dp0gradlew.bat clean
    echo 清理完成！
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

if %errorlevel% equ 0 (
    echo ✅ 构建成功！
) else (
    echo ❌ 构建失败！
)

pause 