#!/bin/bash

echo "🔨 TXY-ERP 构建脚本"
echo "=================="

echo "选择构建类型:"
echo "1. 快速构建（跳过测试）"
echo "2. 完整构建（包含测试）"
echo "3. 清理构建"

read -p "请选择 (1-3): " choice

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GRADLEW="$SCRIPT_DIR/gradlew"

case $choice in
    1)
        echo "🚀 快速构建..."
        "$GRADLEW" build -x test --no-build-cache
        ;;
    2)
        echo "🧪 完整构建..."
        "$GRADLEW" clean build --no-build-cache
        ;;
    3)
        echo "🧹 清理构建..."
        "$GRADLEW" clean
        echo "清理完成！"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
else
    echo "❌ 构建失败！"
    exit 1
fi 