@echo off
echo ========================================
echo 测试 Gradle 构建配置 🚀
echo ========================================

echo.
echo 1. 检查项目结构...
call %~dp0..\gradlew.bat projects

echo.
echo 2. 检查依赖关系...
call %~dp0..\gradlew.bat :ax-admin:dependencies --configuration runtimeClasspath | findstr "ax-base"

echo.
echo 3. 快速编译测试...
call %~dp0..\gradlew.bat compileJava -q

echo.
echo 4. 检查构建缓存...
call %~dp0..\gradlew.bat build --build-cache --info | findstr "cache"

echo.
echo 5. 性能分析...
call %~dp0..\gradlew.bat build --profile

echo.
echo ========================================
echo 测试完成！检查 build/reports/profile 查看性能报告
echo ========================================
pause 