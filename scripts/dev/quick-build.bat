@echo off
chcp 65001 >nul

REM ========================================
REM 极速构建脚本 ⚡
REM ========================================

echo ⚡ 启动极速构建模式...

REM 设置环境变量
set GRADLE_OPTS=-Xmx8g -Xms2g -XX:+UseG1GC -XX:+UseStringDeduplication
set JAVA_OPTS=-XX:+TieredCompilation -XX:TieredStopAtLevel=1

REM 执行极速构建（使用scripts目录中的gradlew.bat）
call %~dp0..\gradlew.bat build ^
    --build-cache ^
    --parallel ^
    --configure-on-demand ^
    --daemon ^
    --no-scan ^
    --quiet

if %ERRORLEVEL% EQU 0 (
    echo ✅ 构建成功完成! 🎉
) else (
    echo ❌ 构建失败，错误代码: %ERRORLEVEL%
)

echo 构建完成时间: %TIME% 