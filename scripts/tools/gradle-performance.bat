@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ========================================
REM Gradle 构建性能分析和优化脚本 🚀
REM ========================================

echo 🚀 开始Gradle构建性能分析...
echo.

REM 检查系统资源
echo ========================================
echo 系统资源检查 📊
echo ========================================

REM CPU信息
echo CPU信息: %PROCESSOR_IDENTIFIER%

REM 内存信息
powershell -Command "Write-Host '总内存:' ([math]::Round((Get-CimInstance Win32_PhysicalMemory | Measure-Object -Property capacity -Sum).sum /1gb)) 'GB'"

REM 磁盘信息
echo 磁盘空间:
powershell -Command "Get-PSDrive -PSProvider FileSystem | Select-Object Name, @{Name='Size(GB)';Expression={[math]::Round($_.Used/1GB,2)}}, @{Name='Free(GB)';Expression={[math]::Round($_.Free/1GB,2)}} | Format-Table -AutoSize"

echo.

REM 检查Java版本
echo ========================================
echo Java环境检查 ☕
echo ========================================
java -version
echo.

REM 检查Gradle版本
echo ========================================
echo Gradle环境检查 🐘
echo ========================================
call %~dp0..\gradlew.bat --version
echo.

REM 清理构建缓存
echo 🧹 清理构建缓存...
call %~dp0..\gradlew.bat clean --quiet

REM 预热构建
echo 🔥 预热Gradle守护进程...
call %~dp0..\gradlew.bat help --quiet

REM 执行性能分析构建
echo ========================================
echo 开始性能分析构建 🏃‍♂️
echo ========================================

REM 记录开始时间
set START_TIME=%time%

REM 执行构建并生成性能报告
call %~dp0..\gradlew.bat build --profile --build-cache --parallel --configure-on-demand --daemon --info

REM 记录结束时间
set END_TIME=%time%

REM 简单显示时间（复杂计算在批处理中容易出错）
echo 开始时间: !START_TIME!
echo 结束时间: !END_TIME!

echo.
echo ========================================
echo 构建完成! 🎉
echo ========================================
echo 构建时间: 从 !START_TIME! 到 !END_TIME!

REM 查找性能报告
if exist "build\reports\profile" (
    echo 性能报告位置: build\reports\profile\
    echo 请在浏览器中打开最新的HTML文件查看详细性能分析
)

REM 显示构建缓存统计
echo.
echo ========================================
echo 构建缓存统计 📈
echo ========================================

if exist ".gradle\caches" (
    for /f "tokens=3" %%i in ('dir ".gradle\caches" /-c ^| find "个文件"') do echo 缓存文件数: %%i
)

if exist ".gradle\build-cache" (
    for /f "tokens=3" %%i in ('dir ".gradle\build-cache" /-c ^| find "个文件"') do echo 构建缓存文件数: %%i
)

REM 性能优化建议
echo.
echo ========================================
echo 性能优化建议 💡
echo ========================================

echo 💡 性能优化建议:
echo    1. 检查网络连接，使用国内镜像源
echo    2. 增加JVM内存分配
echo    3. 启用更多并行工作线程
echo    4. 使用SSD硬盘
echo    5. 启用构建缓存
echo    6. 优化依赖管理

echo.
echo 🎯 性能分析完成!

pause 