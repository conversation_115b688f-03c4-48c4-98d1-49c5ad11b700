# 停止Gradle守护进程脚本
Write-Host "正在停止Gradle守护进程..." -ForegroundColor Yellow
Write-Host ""

# 获取脚本所在目录的上两级目录（项目根目录）
$projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
Set-Location $projectRoot

Write-Host "项目根目录: $projectRoot" -ForegroundColor Green

try {
    # 停止Gradle守护进程
    Write-Host "执行: ./gradlew --stop" -ForegroundColor Cyan
    & ./gradlew --stop
    
    Write-Host ""
    Write-Host "Gradle守护进程已停止！" -ForegroundColor Green
    
    # 显示当前Java进程
    Write-Host ""
    Write-Host "当前Java进程：" -ForegroundColor Cyan
    Get-Process | Where-Object {$_.ProcessName -eq "java"} | Format-Table ProcessName, Id, WorkingSet -AutoSize
    
} catch {
    Write-Host "执行过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "脚本执行完成！" -ForegroundColor Green
Read-Host "按回车键退出" 