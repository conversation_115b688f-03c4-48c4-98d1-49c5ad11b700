# 简化版强制删除build目录脚本
Write-Host "开始强制删除build目录..." -ForegroundColor Yellow

# 切换到项目根目录
$scriptDir = Split-Path -Parent $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
Set-Location $projectRoot

Write-Host "项目根目录: $projectRoot" -ForegroundColor Green

# 停止Gradle守护进程
Write-Host "停止Gradle守护进程..." -ForegroundColor Cyan
& ./gradlew --stop

# 等待2秒
Start-Sleep -Seconds 2

# 强制删除build目录
$buildPaths = @(
    "ax-framework\build",
    "ax-module-system\build", 
    "ax-module-erp\build",
    "ax-module-ai\build",
    "ax-dependencies\build",
    "ax-server\build",
    "build"
)

foreach ($path in $buildPaths) {
    if (Test-Path $path) {
        Write-Host "删除: $path" -ForegroundColor Yellow
        try {
            # 使用robocopy清空目录
            $emptyDir = Join-Path $env:TEMP "empty_temp"
            if (!(Test-Path $emptyDir)) {
                New-Item -ItemType Directory -Path $emptyDir -Force | Out-Null
            }
            
            robocopy $emptyDir $path /MIR /R:0 /W:0 | Out-Null
            Remove-Item -Path $path -Recurse -Force
            Write-Host "成功删除: $path" -ForegroundColor Green
        } catch {
            Write-Host "删除失败: $path - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 清理临时目录
$emptyDir = Join-Path $env:TEMP "empty_temp"
if (Test-Path $emptyDir) {
    Remove-Item -Path $emptyDir -Force
}

Write-Host "删除操作完成！" -ForegroundColor Green
Read-Host "按回车键退出" 