# 强制删除被锁定的build目录
param(
    [string]$TargetPath = "ax-framework\build"
)

Write-Host "强制删除被锁定的build目录..." -ForegroundColor Yellow
Write-Host "目标路径: $TargetPath" -ForegroundColor Cyan
Write-Host ""

# 获取项目根目录
$projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
Set-Location $projectRoot

# 停止Gradle守护进程
Write-Host "1. 停止Gradle守护进程..." -ForegroundColor Green
try {
    & ./gradlew --stop
    Start-Sleep -Seconds 2
} catch {
    Write-Host "停止Gradle进程时出错: $($_.Exception.Message)" -ForegroundColor Red
}

# 尝试正常删除
Write-Host "2. 尝试正常删除..." -ForegroundColor Green
if (Test-Path $TargetPath) {
    try {
        Remove-Item -Path $TargetPath -Recurse -Force -ErrorAction Stop
        Write-Host "正常删除成功！" -ForegroundColor Green
        exit 0
    } catch {
        Write-Host "正常删除失败: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# 使用robocopy清空目录
Write-Host "3. 使用robocopy清空目录..." -ForegroundColor Green
if (Test-Path $TargetPath) {
    try {
        # 创建一个空的临时目录
        $emptyDir = Join-Path $env:TEMP "empty_$(Get-Random)"
        New-Item -ItemType Directory -Path $emptyDir -Force | Out-Null
        
        # 使用robocopy同步空目录到目标目录（实际上是清空目标目录）
        robocopy $emptyDir $TargetPath /MIR /R:0 /W:0 | Out-Null
        
        # 删除空目录
        Remove-Item -Path $emptyDir -Force
        
        # 尝试删除目标目录
        Remove-Item -Path $TargetPath -Recurse -Force -ErrorAction Stop
        Write-Host "robocopy清空成功！" -ForegroundColor Green
    } catch {
        Write-Host "robocopy清空失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 使用takeown和icacls强制获取权限
Write-Host "4. 强制获取权限并删除..." -ForegroundColor Green
if (Test-Path $TargetPath) {
    try {
        # 获取所有权
        takeown /f $TargetPath /r /d y | Out-Null
        
        # 设置完全控制权限
        icacls $TargetPath /grant administrators:F /t | Out-Null
        
        # 再次尝试删除
        Remove-Item -Path $TargetPath -Recurse -Force -ErrorAction Stop
        Write-Host "强制权限删除成功！" -ForegroundColor Green
    } catch {
        Write-Host "强制权限删除失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 检查删除结果
Write-Host ""
Write-Host "5. 检查删除结果..." -ForegroundColor Green
if (Test-Path $TargetPath) {
    Write-Host "删除失败！目录仍然存在: $TargetPath" -ForegroundColor Red
    Write-Host ""
    Write-Host "建议的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 关闭所有IDE和编辑器" -ForegroundColor White
    Write-Host "2. 重启电脑" -ForegroundColor White
    Write-Host "3. 检查是否有杀毒软件在扫描该目录" -ForegroundColor White
    
    # 显示占用文件的进程（如果有handle.exe工具）
    if (Get-Command handle -ErrorAction SilentlyContinue) {
        Write-Host ""
        Write-Host "占用文件的进程:" -ForegroundColor Cyan
        handle $TargetPath
    }
} else {
    Write-Host "删除成功！目录已被清理: $TargetPath" -ForegroundColor Green
}

Write-Host ""
Read-Host "按回车键退出" 