# 工具脚本使用说明

## 概述
这个目录包含了一些常用的开发工具脚本，用于解决开发过程中的常见问题。

## 脚本列表

### 1. stop-gradle.bat / stop-gradle.ps1
**功能**: 停止Gradle守护进程

**使用场景**:
- 当遇到"Unable to delete directory"错误时
- Gradle进程占用文件导致无法删除build目录
- 需要重新启动Gradle守护进程时

**使用方法**:
```bash
# Windows批处理版本
.\scripts\tools\stop-gradle.bat

# PowerShell版本
.\scripts\tools\stop-gradle.ps1
```

### 2. force-clean.bat
**功能**: 强制清理所有构建文件和缓存

**使用场景**:
- 构建出现奇怪问题需要完全重新构建
- build目录被锁定无法删除
- 需要清理所有Gradle缓存

**使用方法**:
```bash
.\scripts\tools\force-clean.bat
```

**执行步骤**:
1. 停止Gradle守护进程
2. 等待进程完全停止
3. 删除所有模块的build目录
4. 清理Gradle缓存(.gradle目录)
5. 显示剩余的Java进程

## 常见问题解决

### Q: 遇到"Unable to delete directory"错误怎么办？
**A**: 按以下顺序尝试：
1. 运行 `stop-gradle.bat` 停止Gradle进程
2. 如果还是无法删除，运行 `force-clean.bat`
3. 如果仍然失败，关闭IDE后重新尝试
4. 最后手段：重启电脑

### Q: 为什么有些Java进程无法停止？
**A**: 可能的原因：
- IDE (如IntelliJ IDEA) 正在运行项目
- 有其他应用程序在使用Java
- 系统服务在使用Java

**解决方法**:
- 关闭IDE
- 检查任务管理器中的Java进程
- 必要时重启电脑

### Q: 脚本执行失败怎么办？
**A**: 检查以下几点：
- 确保在项目根目录执行脚本
- 检查是否有足够的权限
- 确保gradlew文件存在且可执行

## 注意事项

1. **数据安全**: `force-clean.bat` 会删除所有构建文件，确保重要数据已保存
2. **权限要求**: 某些操作可能需要管理员权限
3. **IDE集成**: 建议在IDE外部运行这些脚本，避免冲突
4. **备份建议**: 重要修改前建议先提交代码到Git

## 自定义脚本

如果需要添加新的工具脚本，请：
1. 在此目录创建脚本文件
2. 更新此README文档
3. 确保脚本包含适当的错误处理和用户提示 