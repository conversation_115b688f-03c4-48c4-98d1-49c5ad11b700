@echo off
echo ========================================
echo 强制清理Gradle构建文件
echo ========================================
echo.

REM 切换到项目根目录
cd /d "%~dp0..\.."

echo 1. 停止Gradle守护进程...
gradlew --stop

echo.
echo 2. 等待进程完全停止...
timeout /t 3 /nobreak >nul

echo.
echo 3. 强制删除build目录...

REM 删除各个模块的build目录
if exist "ax-framework\build" (
    echo 删除 ax-framework\build...
    rmdir /s /q "ax-framework\build" 2>nul
)

if exist "ax-module-system\build" (
    echo 删除 ax-module-system\build...
    rmdir /s /q "ax-module-system\build" 2>nul
)

if exist "ax-module-erp\build" (
    echo 删除 ax-module-erp\build...
    rmdir /s /q "ax-module-erp\build" 2>nul
)

if exist "ax-module-ai\build" (
    echo 删除 ax-module-ai\build...
    rmdir /s /q "ax-module-ai\build" 2>nul
)

if exist "ax-dependencies\build" (
    echo 删除 ax-dependencies\build...
    rmdir /s /q "ax-dependencies\build" 2>nul
)

if exist "ax-server\build" (
    echo 删除 ax-server\build...
    rmdir /s /q "ax-server\build" 2>nul
)

if exist "build" (
    echo 删除根目录build...
    rmdir /s /q "build" 2>nul
)

echo.
echo 4. 清理Gradle缓存...
if exist ".gradle" (
    echo 删除 .gradle 目录...
    rmdir /s /q ".gradle" 2>nul
)

echo.
echo ========================================
echo 清理完成！
echo ========================================
echo.

REM 显示剩余的Java进程
echo 当前Java进程：
tasklist | findstr java

echo.
echo 如果仍有文件无法删除，请手动重启IDE或重启电脑
pause 