#!/bin/bash

echo "🔨 构建并部署TXY-ERP生产环境"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GRADLEW="$SCRIPT_DIR/../gradlew"

echo "🧹 清理旧构建..."
"$GRADLEW" clean

echo "🚀 构建生产JAR包..."
"$GRADLEW" :ax-admin:bootJar -x test

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "📦 JAR包位置: ax-admin/build/libs/ax-admin-3.0.0.jar"
    echo ""
    echo "🚀 部署命令:"
    echo "java -jar -Dspring.profiles.active=prod ax-admin/build/libs/ax-admin-3.0.0.jar"
else
    echo "❌ 构建失败！"
    exit 1
fi 