#!/bin/bash

echo "TXY-ERP Host网络模式部署脚本"
echo "================================"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GRADLEW="$SCRIPT_DIR/../gradlew"

# 检查PostgreSQL连接
echo "检查PostgreSQL连接..."
if pg_isready -h localhost -p 5432 -U txy >/dev/null 2>&1; then
    echo "✅ PostgreSQL连接正常"
else
    echo "❌ PostgreSQL连接失败，请确保PostgreSQL服务已启动"
    exit 1
fi

# 检查Redis连接
echo "检查Redis连接..."
if redis-cli -h localhost -p 6379 ping | grep -q PONG; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败，请确保Redis服务已启动"
    exit 1
fi

# 构建应用
echo "构建TXY-ERP应用..."
"$GRADLEW" clean build -x test

if [ $? -eq 0 ]; then
    echo "✅ 应用构建成功"
else
    echo "❌ 应用构建失败"
    exit 1
fi

# 切换到.docker目录进行Docker操作
cd "$SCRIPT_DIR/../../.docker"

# 构建Docker镜像
echo "构建Docker镜像..."
docker-compose build

# 启动应用（host网络模式）
echo "启动TXY-ERP应用（host网络模式）..."
docker-compose up -d

# 检查应用状态
echo "等待应用启动..."
sleep 10

if curl -s http://localhost:10245/actuator/health >/dev/null 2>&1; then
    echo "✅ TXY-ERP应用启动成功！"
    echo "🌐 访问地址: http://localhost:10245"
    echo "📚 API文档: http://localhost:10245/doc.html"
else
    echo "❌ 应用启动失败，请检查日志"
    docker-compose logs erp-api
fi 