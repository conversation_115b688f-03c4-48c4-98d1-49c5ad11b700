# TXY-ERP 脚本使用指南

## 📁 目录结构

```
scripts/
├── dev/                    # 开发环境脚本
│   ├── quick-build.bat     # 快速构建（跳过测试）
│   └── test-build.bat      # 完整构建（包含测试）
├── prod/                   # 生产环境脚本
│   ├── start-prod.bat      # Windows生产环境启动
│   ├── start-prod.sh       # Linux生产环境启动
│   ├── build-and-deploy.bat # 构建JAR包部署
│   ├── docker-host-deploy.bat # Windows Docker部署
│   └── docker-host-deploy.sh  # Linux Docker部署
├── tools/                  # 工具脚本
│   └── gradle-performance.bat # Gradle性能分析
├── dev.bat                 # 开发环境快速启动
├── dev.sh                  # Linux开发环境快速启动
├── build.bat               # Windows构建脚本
├── build.sh                # Linux构建脚本
└── README.md               # 本文档
```

## 🚀 快速使用

### 开发环境
```bash
# Windows
scripts\dev.bat

# Linux
chmod +x scripts/dev.sh && ./scripts/dev.sh
```

### 构建项目
```bash
# Windows
scripts\build.bat

# Linux/Mac
chmod +x scripts/build.sh && scripts/build.sh
```

## 📋 详细说明

### 开发环境脚本
- `dev/quick-build.bat` - 快速构建，跳过测试，适合开发调试
- `dev/test-build.bat` - 完整构建，包含测试，适合提交前验证

### 生产环境脚本
- `prod/start-prod.*` - 直接启动生产环境（需要预先构建）
- `prod/build-and-deploy.bat` - 构建JAR包并提供部署命令
- `prod/docker-host-deploy.*` - Docker容器部署（host网络模式）

### 工具脚本
- `tools/gradle-performance.bat` - Gradle构建性能分析和优化建议

## 📋 详细说明

### Gradle Wrapper
- `gradlew` / `gradlew.bat` - Gradle Wrapper脚本，确保团队使用统一的Gradle版本
- 所有脚本都使用scripts目录中的Gradle Wrapper，保持项目根目录干净

### 开发环境脚本
- `dev/quick-build.bat` - 快速构建，跳过测试，适合开发调试
- `dev/test-build.bat` - 完整构建，包含测试，适合提交前验证

### 生产环境脚本
- `prod/start-prod.*` - 直接启动生产环境（需要预先构建）
- `prod/build-and-deploy.*` - 构建JAR包并提供部署命令
- `prod/docker-host-deploy.*` - Docker容器部署（host网络模式）

### 工具脚本
- `tools/gradle-performance.bat` - Gradle构建性能分析和优化建议

## 🔧 环境要求

- Java 17+
- Gradle 8.14.2+（通过Wrapper自动下载）
- PostgreSQL（生产环境）
- Redis（生产环境）
- Docker（可选，用于容器化部署）

## 💡 设计理念

- **统一性**: 所有脚本使用统一的Gradle Wrapper
- **干净性**: Gradle Wrapper放在scripts目录，保持项目根目录干净
- **跨平台**: 提供Windows和Linux/Mac版本的脚本
- **模块化**: 按功能分类组织脚本，便于维护