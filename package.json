{"name": "carton-box-admin-web", "type": "module", "version": "1.0.0", "private": true, "packageManager": "pnpm@9.15.2", "description": "Modify by https://www.1024lab.net", "author": {"name": "sevth", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://xxx.git"}, "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}, "scripts": {"dev": "vite", "localhost": "vite --mode localhost", "build:test": "vite build  --base=/admin/ --mode test", "build:pre": "vite build  --mode pre", "build:prod": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --mode production", "lint": "eslint ."}, "dependencies": {"@ant-design/icons-vue": "^6.1.0", "@formkit/auto-animate": "^0.8.2", "@iconify/vue": "^4.3.0", "@primeuix/themes": "^1.1.1", "@types/nprogress": "^0.2.3", "@unocss/reset": "^0.65.3", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.3", "@vueuse/core": "^12.2.0", "@wangeditor/editor": "5.1.14", "@wangeditor/editor-for-vue": "5.1.12", "ali-oss": "^6.20.0", "ant-design-vue": "4.2.6", "ant-design-x-vue": "^1.1.0", "axios": "1.6.8", "clipboard": "2.0.11", "countup.js": "^2.8.0", "crypto-js": "4.1.1", "dayjs": "^1.11.13", "decimal.js": "10.3.1", "diff": "5.2.0", "diff2html": "3.4.47", "echarts": "^5.6.0", "file-saver": "^2.0.5", "highlight.js": "11.8.0", "lodash": "4.17.21", "lodash-es": "^4.17.21", "lunar-javascript": "1.6.12", "mitt": "3.0.1", "nanoid": "^5.1.4", "nprogress": "0.2.0", "pinia": "^2.3.0", "primevue": "4.3.3", "sm-crypto": "0.3.13", "sortablejs": "1.15.0", "tiny-swiper": "^2.2.0", "ua-parser-js": "1.0.35", "v-viewer": "~1.6.4", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "9.13.1", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.5.0", "vue3-json-viewer": "2.2.2", "vue3-tabs-chrome": "^0.3.3", "xlsx": "https://cdn.sheetjs.com/xlsx-latest/xlsx-latest.tgz", "zod": "^3.24.1"}, "devDependencies": {"@antfu/eslint-config": "^3.12.1", "@faker-js/faker": "^9.3.0", "@iconify-json/ant-design": "^1.2.5", "@types/ali-oss": "^6.16.11", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.11", "@unocss/eslint-plugin": "^0.65.2", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/tsconfig": "0.5.1", "code-inspector-plugin": "^0.20.4", "cross-env": "^7.0.3", "eslint": "^9.17.0", "less": "~4.2.0", "less-loader": "~11.1.3", "lint-staged": "^15.3.0", "lodash-imports": "^0.0.3", "rimraf": "~5.0.1", "sass-embedded": "^1.83.1", "simple-git-hooks": "^2.11.1", "terser": "~5.29.2", "typescript": "~5.6.2", "unocss": "^0.65.3", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "unplugin-vue-router": "^0.10.9", "vite": "^6.0.5", "vite-plugin-vue-inspector": "^5.3.1"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*": "eslint --fix"}}