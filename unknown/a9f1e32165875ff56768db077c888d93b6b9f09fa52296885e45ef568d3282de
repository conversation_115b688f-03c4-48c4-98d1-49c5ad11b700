:deep(.ant-checkbox-group) {
  width: 100%;
}
.tree-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
}
.col-desc {
  margin: 20px 0;
  font-size: 15px;
  color: #95a5a6;
  padding: 0 20px;
}
.button-style {
  margin: 20px 0 20px 0;
  padding-left: 20px;
  text-align: right;
}
.check-right {
  margin-right: 20px;
}
.row-border {
  border: 1px solid #f0f0f0;
}
.col-border {
  line-height: 50px;
  padding-left: 20px;
  border-right: 1px solid #f0f0f0;
}
.col-left {
  line-height: 50px;
  padding-left: 40px;
  border-right: 1px solid #f0f0f0;
}
.col-right {
  padding-left: 20px;
  border-right: 1px solid #f0f0f0;
}
.checked-box {
  padding: 0 15px;
  :deep(ul li::marker) {
    content: '';
  }
  :deep(ul) {
    padding: 0;
    margin: 0;

    li {
      list-style: none;
      padding: 0;
      margin: 10px 0;

      .menu {
        border-bottom: 1px solid rgb(240, 240, 240);
        display: flex;
        align-items: center;
        line-height: 25px;
      }

      .point {
        display: flex;
        align-items: center;

        .point-label {
          flex: 1;
          padding-left: 40px;
          border-left: 1px rgb(240, 240, 240) solid;
        }
      }

      .checked-box-label {
        min-width: 150px;
      }
    }
  }
}
