@echo off
echo 🚀 启动TXY-ERP服务器...
echo 📁 当前目录: %CD%

REM 设置环境变量
set SPRING_PROFILES_ACTIVE=dev
set JAVA_OPTS=-Xms256m -Xmx512m -XX:+TieredCompilation -XX:TieredStopAtLevel=1

REM 构建classpath - 包含所有模块的编译输出和依赖
set CP=ax-server\build\classes\java\main
set CP=%CP%;ax-server\build\resources\main
set CP=%CP%;ax-framework\build\classes\java\main
set CP=%CP%;ax-framework\build\resources\main
set CP=%CP%;ax-module-system\build\classes\java\main
set CP=%CP%;ax-module-system\build\resources\main
set CP=%CP%;ax-module-erp\build\classes\java\main
set CP=%CP%;ax-module-erp\build\resources\main
set CP=%CP%;ax-module-ai\build\classes\java\main
set CP=%CP%;ax-module-ai\build\resources\main

REM 获取依赖jar包
echo 📦 获取依赖jar包...
for /f "tokens=*" %%i in ('gradlew.bat :ax-server:dependencies --configuration runtimeClasspath ^| findstr "\.jar"') do (
    echo 找到依赖: %%i
    REM 这里需要解析gradle输出，暂时跳过
)

echo ⚡ 启动应用...
java %JAVA_OPTS% -Dspring.profiles.active=%SPRING_PROFILES_ACTIVE% -cp "%CP%" com.anxys.server.AxAdminApplication

pause 