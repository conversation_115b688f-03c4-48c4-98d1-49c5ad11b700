$common.vm
$define.vm
#save("$!{packagePath}/service", "Service.java")
        package $!{packageName}.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import $!{packageName}.dao.${beanClass}Mapper;
import  $!{packageName}.domain.${beanClass};
import  $!{packageName}.domain.vo.${beanClass}VO;
import  $!{packageName}.domain.form.${beanClass}QueryParam;
import  $!{packageName}.domain.form.${beanClass}PageParam;
import  $!{packageName}.domain.form.${beanClass}AddParam;
import  $!{packageName}.domain.form.${beanClass}UpdateParam;
import  $!{packageName}.domain.excel.${beanClass}Excel;
import cn.idev.excel.FastExcel;
import lombok.extern.slf4j.Slf4j;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * $!{tableInfo.comment}服务层
 *
 * @since $!time.currTime()
 */
@Slf4j
@Service
public class ${beanClass}Service {

    @Resource
    private ${beanClass}Mapper ${beanName}Mapper;

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页结果
     */
    public PageResult<${beanClass}VO> page(${beanClass}PageParam queryParam) {
        Page<${beanClass}VO> page = PageUtil.convert2PageQuery(queryParam.getPageParam());
        List<${beanClass}VO> list = ${beanName}Mapper.page(page, queryParam);
        return PageUtil.convert2PageResult(page, list);
    }

    /**
      * 列表查询
      *
      * @param queryParam 查询参数
      * @return 列表结果
      */
    public List<${beanClass}VO> list(${beanClass}QueryParam queryParam) {
        return ${beanName}Mapper.list(queryParam);
    }

    /**
     * 添加
     *
     * @param addParam 添加参数
     * @return 新创建的对象VO
     */
    @Transactional(rollbackFor = Exception.class)
    public ${beanClass}VO insert(${beanClass}AddParam addParam) {
        ${beanClass} ${beanName} =BeanUtil.copy(addParam, ${beanClass}. class);
            ${beanName}Mapper.insert(${beanName});
        // 返回新创建的对象
        return BeanUtil.copy(${beanName}, ${beanClass}VO.class);
    }

    /**
     * 更新
     *
     * @param updateParam 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(${beanClass}UpdateParam updateParam) {
        ${beanClass} ${beanName} =BeanUtil.copy(updateParam, ${beanClass}. class);
            ${beanName}Mapper.updateById(${beanName});
    }

    /**
     * 批量删除
     *
     * @param idList ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
                ${beanName}Mapper.deleteByIds(idList, true);
        }
    }

    /**
     * 单个删除
     *
     * @param id ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
            ${beanName}Mapper.deleteById(id);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return 详情
     */
    public ${beanClass}VO queryById(Long id) {
        ${beanClass} ${beanName} =${beanName}Mapper.selectById(id);
        if (${beanName} ==null){
            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
        }
        return BeanUtil.copy(${beanName}, ${beanClass}VO.class);
    }

    /**
     * 导出Excel
     *
     * @param queryParam 查询参数
     * @return Excel数据列表
     */
    public List<${beanClass}Excel> exportExcel(${beanClass}QueryParam queryParam) {
        return ${beanName}Mapper.exportExcel(queryParam);
    }


    /**
     * 导入Excel
     *
     * @param file Excel文件
     * @return 导入的数据条数
     */
    @Transactional(rollbackFor = Exception.class)
    public int importExcel(MultipartFile file) {
        List<${beanClass}Excel> dataList;
        try {
            dataList = FastExcel.read(file.getInputStream())
                    .head(${beanClass}Excel.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            log.error("Excel导入失败", e);
            throw new BusinessException("数据格式存在问题，无法读取");
        }

        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("数据为空");
        }

        // 处理导入数据
        log.info("开始处理导入数据，共 {} 条", dataList.size());
        List<${beanClass}> entityList = dataList.stream()
                .map(excel -> {
                    log.info("处理数据: {}", excel);
                    return BeanUtil.copy(excel, ${beanClass}.class);
                })
                .toList();

            ${beanName}Mapper.batchInsert(entityList);

        return dataList.size();
    }
}
