$common.vm
$define.vm

$!callback.setFileName($tool.append($tsFileName, "-api.ts"))
$!callback.setSavePath($tool.append($projectPath,"/admin-web/src/api/business/$tsFileName"))

import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  ${beanClass}Form,
  ${beanClass}PageParam,
  ${beanClass}QueryParam,
  ${beanClass}Result,
} from './model/$tsFileName-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const ${beanName}Api = {

  // 分页查询$!{tableInfo.comment}
  ${beanName}Page: (param: ${beanClass}PageParam): Promise<ResponseModel<PageResult<${beanClass}Result>>> => {
    return postRequest<ResponseModel<PageResult<${beanClass}Result>>, ${beanClass}QueryParam>('/${beanName}/${beanName}Page', param)
  },
  // 列表查询$!{tableInfo.comment}
  ${beanName}List: (param: ${beanClass}QueryParam): Promise<ResponseModel<${beanClass}Form[]>> => {
    return postRequest<ResponseModel<${beanClass}Form[]>, ${beanClass}QueryParam>('/${beanName}/${beanName}List', param)
  },
  // 获取$!{tableInfo.comment}详情
  ${beanName}Detail: (${beanName}Id: number): Promise<ResponseModel<${beanClass}Result>> => {
    return getRequest<ResponseModel<${beanClass}Result>>(`/${beanName}/${beanName}Detail/${${beanName}Id}`, {})
  },
  // 添加$!{tableInfo.comment}
  add${beanClass}: (param: ${beanClass}Form): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ${beanClass}Form>('/${beanName}/add${beanClass}', param)
  },
  // 更新$!{tableInfo.comment}
  update${beanClass}: (param: ${beanClass}Form): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ${beanClass}Form>('/${beanName}/update${beanClass}', param)
  },
  // 删除$!{tableInfo.comment}
  delete${beanClass}: (${beanName}Id: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/${beanName}/delete${beanClass}/${${beanName}Id}`, {})
  },
  // 批量删除$!{tableInfo.comment}
  batchDelete${beanClass}: (${beanName}IdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/${beanName}/batchDelete${beanClass}', ${beanName}IdList)
  },

  // 导入$!{tableInfo.comment}
  import${beanClass}: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/${beanName}/import${beanClass}Excel', file)
  },
  // 导出$!{tableInfo.comment}
  export${beanClass}: (param?: ${beanClass}QueryParam): void => {
    return getDownload('/${beanName}/export${beanClass}Excel', param || {})
  },
}