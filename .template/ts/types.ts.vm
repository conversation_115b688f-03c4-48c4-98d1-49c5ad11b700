$common.vm
$define.vm

$!callback.setFileName($tool.append($tsFileName, "-types.ts"))
$!callback.setSavePath($tool.append($projectPath,"/admin-web/src/api/business/$tsFileName/model"))
import type {PageParam} from '@/api/base-model/page-model'
import type { BaseModel } from '@/api/base-model/base-model'

/**
 * $!{tableInfo.comment}类型定义
 */
export interface ${beanClass} extends BaseModel {
  id?: number // $!{tableInfo.comment}ID

  #foreach($column in $tableInfo.fullColumn)
    #if($column.name != "id" && $column.name != "deletedFlag" && $column.name != "createTime" && $column.name != "updateTime" && $column.name != "createdBy" && $column.name != "updatedBy")
      #if($column.type.equals("java.lang.String"))
  $!{column.name}?: string // $!{column.comment}
      #elseif($column.type.equals("java.lang.Integer") || $column.type.equals("java.lang.Long") || $column.type.equals("java.lang.Float") || $column.type.equals("java.lang.Double"))
  $!{column.name}?: number // $!{column.comment}
      #elseif($column.type.equals("java.lang.Boolean"))
  $!{column.name}?: boolean // $!{column.comment}
      #elseif($column.type.equals("java.math.BigDecimal"))
  $!{column.name}?: string // $!{column.comment}
      #elseif($column.type.equals("java.time.LocalDate") || $column.type.equals("java.time.LocalDateTime"))
  $!{column.name}?: string // $!{column.comment}
      #else
  $!{column.name}?: string // $!{column.comment}
      #end

    #end
  #end
}
/**
 * $!{tableInfo.comment}查询参数
 */
export interface ${beanClass}QueryParam extends ${beanClass} {
    #foreach($column in $tableInfo.fullColumn)
        #if($column.type.equals("java.time.LocalDateTime") || $column.type.equals("java.time.LocalDate"))

                $!{column.name}From?: string // $!{column.comment}始

                $!{column.name}To?: string // $!{column.comment}至
        #else
        #end
    #end
}

/**
 * $!{tableInfo.comment}分页查询参数
 */
export interface ${beanClass}PageParam extends ${beanClass}QueryParam {
    pageParam: PageParam
}

/**
 * $!{tableInfo.comment}表单类型
 */
export interface ${beanClass}Form extends ${beanClass} {

}

/**
 * $!{tableInfo.comment}查询结果类型
 */
export interface ${beanClass}Result extends ${beanClass} {

}