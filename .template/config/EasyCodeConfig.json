{"author": "<PERSON><PERSON><PERSON>", "version": "1.2.7", "userSecure": "", "currTypeMapperGroupName": "<PERSON><PERSON><PERSON>", "currTemplateGroupName": "txy", "currColumnConfigGroupName": "<PERSON><PERSON><PERSON>", "currGlobalConfigGroupName": "txy", "typeMapper": {"Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"matchType": "REGEX", "columnType": "varchar(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "char(\\(\\d+\\))?", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "(tiny|medium|long)*text", "javaType": "java.lang.String"}, {"matchType": "REGEX", "columnType": "decimal(\\(\\d+,\\d+\\))?", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "integer", "javaType": "java.lang.Integer"}, {"matchType": "REGEX", "columnType": "(tiny|small|medium)*int(\\(\\d+\\))?", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int4", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "int8", "javaType": "java.lang.Long"}, {"matchType": "REGEX", "columnType": "bigint(\\(\\d+\\))?", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "date", "javaType": "java.time.LocalDate"}, {"matchType": "ORDINARY", "columnType": "datetime", "javaType": "java.time.LocalDateTime"}, {"matchType": "ORDINARY", "columnType": "timestamp", "javaType": "java.time.LocalDateTime"}, {"matchType": "ORDINARY", "columnType": "time", "javaType": "java.time.LocalTime"}, {"matchType": "ORDINARY", "columnType": "boolean", "javaType": "java.lang.Bo<PERSON>an"}, {"matchType": "ORDINARY", "columnType": "double", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "NVARCHAR2(50)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "NUMBER(18)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "NVARCHAR2(100)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "NUMBER(18,2)", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "VARCHAR2(3)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "VARCHAR2(50)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "NUMBER(10,2)", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "datetime(3)", "javaType": "java.lang.String"}, {"matchType": "ORDINARY", "columnType": "bigint(20) unsigned", "javaType": "java.lang.Long"}, {"matchType": "ORDINARY", "columnType": "int(10) unsigned", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "decimal(8,2) unsigned", "javaType": "java.lang.Double"}, {"matchType": "ORDINARY", "columnType": "tinyint(3) unsigned", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "bit(1)", "javaType": "java.lang.Integer"}, {"matchType": "ORDINARY", "columnType": "json", "javaType": "java.lang.String"}]}}, "template": {"txy": {"name": "txy", "elementList": [{"name": "AddParam.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/form/\", \"AddParam.java\")\npackage $!{packageName}.domain.form;\nimport jakarta.validation.constraints.NotBlank;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport $!{packageName}.domain.${beanClass};\n/**\n * $!{tableInfo.comment}添加表单\n *\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = true)\n@Data\npublic class ${beanClass}AddParam extends ${beanClass} {\n\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=\"id\" && $column.name!=\"deletedFlag\" && $column.name!=\"createTime\" && $column.name!=\n            \"updateTime\" && $column.name!=\"createdBy\" && $column.name!=\"updatedBy\" && $column.obj.notNull)\n            @NotBlank(message = \"$!{column.comment}不能为空\")\n            private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n        #end\n    #end\n}\n"}, {"name": "Entity.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain\", \".java\")\npackage $!{packageName}.domain;\n$autoImport.vm\nimport com.baomidou.mybatisplus.annotation.IdType;\nimport com.baomidou.mybatisplus.annotation.TableId;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.AllArgsConstructor;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport lombok.NoArgsConstructor;\nimport lombok.experimental.SuperBuilder;\nimport com.anxys.framework.common.domain.BaseEntity;\n\n\n/**\n * $!{tableInfo.comment}实体类\n *\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = true)\n@Data\n@SuperBuilder\n@AllArgsConstructor\n@NoArgsConstructor\n@TableName(\"$!{tableInfo.obj.name}\")\npublic class ${beanClass} extends BaseEntity {\n    @Schema(description = \"主键id\")\n    @TableId(\n            value = \"id\",\n            type = IdType.AUTO\n    )\n    private Long id;\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=\"id\" && $column.name!=\"createdBy\" && $column.name!=\"updatedBy\" && $column.name!=\"deletedFlag\" && $column.name!=\"createTime\" && $column.name!=\"updateTime\")\n            @Schema(description = \"$!{column.comment}\")\n            #if($column.type.equals(\"java.time.LocalDateTime\"))\n            @JsonFormat(pattern = \"yyyy-MM-dd HH:mm:ss\")\n            #elseif($column.type.equals(\"java.time.LocalDate\"))\n            @JsonFormat(pattern = \"yyyy-MM-dd\")\n            #end\n            private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n        #end\n    #end\n}\n"}, {"name": "Excel.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/excel\", \"Excel.java\")\npackage $!{packageName}.domain.excel;\n$autoImport.vm\nimport com.fasterxml.jackson.annotation.JsonFormat;\nimport cn.idev.excel.annotation.ExcelProperty;\nimport lombok.Data;\nimport lombok.Builder;\nimport lombok.NoArgsConstructor;\nimport lombok.AllArgsConstructor;\n\nimport java.io.Serial;\nimport java.io.Serializable;\nimport java.time.LocalDateTime;\n\n/**\n * $!{tableInfo.comment}Excel导出VO\n * @since $!time.currTime()\n */\n@Data\n@Builder\n@NoArgsConstructor\n@AllArgsConstructor\npublic class ${beanClass}Excel implements Serializable {\n    @Serial\n    private static final long serialVersionUID = 1L;\n\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=\"id\" && $column.name!=\"deletedFlag\")\n            #if($!{column.comment})@ExcelProperty(\"$!{column.comment}\")#end\n        private $!{tool.getClsNameByFullName($column.type)} $!{column.name};\n        #end\n    #end\n}\n"}, {"name": "PageParam.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/form\", \"PageParam.java\")\npackage $!{packageName}.domain.form;\n$autoImport.vm\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport jakarta.validation.Valid;\nimport jakarta.validation.constraints.NotNull;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport com.anxys.framework.common.domain.PageParam;\n\n\n/**\n * $!{tableInfo.comment}分页查询表单\n *\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = false)\n@Data\npublic class ${beanClass}PageParam extends ${beanClass}QueryParam {\n\n    @Schema(description = \"分页参数\")\n    @Valid\n    @NotNull(message = \"分页参数不能为空\")\n    PageParam pageParam = new PageParam();\n\n}\n"}, {"name": "QueryParam.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/form\", \"QueryParam.java\")\npackage $!{packageName}.domain.form;\n$autoImport.vm\nimport com.fasterxml.jackson.annotation.JsonFormat;\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport lombok.NoArgsConstructor;\nimport lombok.AllArgsConstructor;\nimport lombok.experimental.SuperBuilder;\nimport $!{packageName}.domain.${beanClass};\n\nimport java.math.BigDecimal;\nimport java.time.LocalDate;\nimport java.time.LocalDateTime;\n\n/**\n * $!{tableInfo.comment}查询表单\n *\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = false)\n@Data\n@SuperBuilder\n@AllArgsConstructor\n@NoArgsConstructor\npublic class ${beanClass}QueryParam extends ${beanClass} {\n\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=\"id\" && $column.name!=\"deletedFlag\")\n            #if($column.type.equals(\"java.time.LocalDateTime\"))\n            @JsonFormat(pattern = \"yyyy-MM-dd HH:mm:ss\")\n            @Schema(description = \"$!{column.comment}始\")\n            private LocalDateTime $!{column.name}From;\n\n            @JsonFormat(pattern = \"yyyy-MM-dd HH:mm:ss\")\n            @Schema(description = \"$!{column.comment}至\")\n            private LocalDateTime $!{column.name}To;\n            #elseif($column.type.equals(\"java.time.LocalDate\"))\n            @JsonFormat(pattern = \"yyyy-MM-dd\")\n            @Schema(description = \"$!{column.comment}始\")\n            private LocalDate $!{column.name}From;\n\n            @JsonFormat(pattern = \"yyyy-MM-dd\")\n            @Schema(description = \"$!{column.comment}至\")\n            private LocalDate $!{column.name}To;\n            #end\n        #end\n    #end\n}\n"}, {"name": "UpdateParam.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/form\", \"UpdateForm.java\")\npackage $!{packageName}.domain.form;\n$autoImport.vm\nimport io.swagger.v3.oas.annotations.media.Schema;\nimport jakarta.validation.constraints.NotNull;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport $!{packageName}.domain.${beanClass};\n\n/**\n * $!{tableInfo.comment}更新表单\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = true)\n@Data\npublic class ${beanClass}UpdateForm extends ${beanClass} {\n\n    @Schema(description = \"ID\")\n    @NotNull(message = \"ID不能为空\")\n    private Long id;\n\n}\n"}, {"name": "VO.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/domain/vo\", \"VO.java\")\npackage $!{packageName}.domain.vo;\nimport com.baomidou.mybatisplus.annotation.TableName;\nimport lombok.Data;\nimport lombok.EqualsAndHashCode;\nimport $!{packageName}.domain.${beanClass};\n\n/**\n * $!{tableInfo.comment}VO\n * @since $!time.currTime()\n */\n@EqualsAndHashCode(callSuper = true)\n@Data\n@TableName(\"$!{tableInfo.obj.name}\")\npublic class ${beanClass}VO extends ${beanClass} {\n\n}\n"}, {"name": "Controller.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/controller\", \"Controller.java\")\npackage $!{packageName}.controller;\n\nimport cn.dev33.satoken.annotation.SaCheckPermission;\nimport io.swagger.v3.oas.annotations.Operation;\nimport io.swagger.v3.oas.annotations.tags.Tag;\nimport jakarta.annotation.Resource;\nimport jakarta.servlet.http.HttpServletResponse;\nimport jakarta.validation.Valid;\nimport lombok.extern.slf4j.Slf4j;\nimport  $!{packageName}.domain.form.${beanClass}AddParam;\nimport  $!{packageName}.domain.form.${beanClass}PageParam;\nimport  $!{packageName}.domain.form.${beanClass}QueryParam;\nimport  $!{packageName}.domain.form.${beanClass}UpdateForm;\nimport  $!{packageName}.domain.vo.${beanClass}VO;\nimport  $!{packageName}.domain.excel.${beanClass}Excel;\nimport $!{packageName}.service.${beanClass}Service;\nimport com.anxys.framework.common.domain.HttpResponse;\nimport com.anxys.framework.common.domain.PageResult;\nimport com.anxys.framework.common.domain.ValidateList;\nimport com.anxys.framework.common.util.SmartExcelUtil;\nimport com.anxys.framework.common.util.SmartLocalDateUtil;\nimport com.anxys.framework.common.util.SmartDateFormatterEnum;\nimport com.anxys.admin.util.AdminRequestUtil;\nimport org.springframework.web.bind.annotation.*;\nimport org.springframework.web.multipart.MultipartFile;\nimport io.swagger.v3.oas.annotations.Parameter;\n\nimport java.io.IOException;\nimport java.time.LocalDateTime;\nimport java.util.List;\n\n/**\n * $!{tableInfo.comment}控制器\n *\n * @since $!time.currTime()\n */\n@Slf4j\n@RestController\n@RequestMapping(\"/${beanName}\")\n@Tag(name = \"$!{tableInfo.comment}接口\")\npublic class ${controllerClass} {\n\n    @Resource\n    private ${beanClass}Service ${beanName}Service;\n\n    @Operation(summary = \"${tableInfo.comment}分页查询\")\n    @PostMapping(\"/${beanName}Page\")\n    @SaCheckPermission(\"${beanName}:query\")\n    public HttpResponse<PageResult<${beanClass}VO>> query(@RequestBody @Valid ${beanClass}PageParam queryForm) {\n        PageResult<${beanClass}VO> pageResult = ${beanName}Service.query(queryForm);\n        return HttpResponse.ok(pageResult);\n    }\n\n    @Operation(summary = \"${tableInfo.comment}列表查询\")\n    @PostMapping(\"/${beanName}List\")\n    @SaCheckPermission(\"${beanName}:query\")\n    public HttpResponse<List<${beanClass}VO>> list(@RequestBody @Valid ${beanClass}QueryParam queryForm) {\n        List<${beanClass}VO> list = ${beanName}Service.list(queryForm);\n        return HttpResponse.ok(list);\n    }\n\n    @Operation(summary = \"获取${tableInfo.comment}详情\")\n    @GetMapping(\"/${beanName}Detail/{id}\")\n    @SaCheckPermission(\"${beanName}:query\")\n    public HttpResponse<${beanClass}VO> getDetail(@PathVariable Long id) {\n        ${beanClass}VO ${beanName}VO = ${beanName}Service.getDetail(id);\n        return HttpResponse.ok(${beanName}VO);\n    }\n\n    @Operation(summary = \"添加${tableInfo.comment}\")\n    @PostMapping(\"/add${beanClass}\")\n    @SaCheckPermission(\"${beanName}:add\")\n    public HttpResponse<String> add(@RequestBody @Valid ${beanClass}AddParam addForm) {\n        ${beanName}Service.add(addForm);\n        return HttpResponse.ok();\n    }\n\n    @Operation(summary = \"更新${tableInfo.comment}\")\n    @PostMapping(\"/update${beanClass}\")\n    @SaCheckPermission(\"${beanName}:update\")\n    public HttpResponse<String> update(@RequestBody @Valid ${beanClass}UpdateForm updateForm) {\n        ${beanName}Service.update(updateForm);\n        return HttpResponse.ok();\n    }\n\n    @Operation(summary = \"删除${tableInfo.comment}\")\n    @GetMapping(\"/delete${beanClass}/{id}\")\n    @SaCheckPermission(\"${beanName}:delete\")\n    public HttpResponse<String> delete(@PathVariable Long id) {\n        ${beanName}Service.delete(id);\n        return HttpResponse.ok();\n    }\n\n    @Operation(summary = \"批量删除${tableInfo.comment}\")\n    @PostMapping(\"/batchDelete${beanClass}\")\n    @SaCheckPermission(\"${beanName}:delete\")\n    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {\n        ${beanName}Service.batchDelete(idList);\n        return HttpResponse.ok();\n    }\n\n    @Operation(summary = \"导出${tableInfo.comment}Excel\")\n    @GetMapping(\"/export${beanClass}Excel\")\n    @SaCheckPermission(\"${beanName}:export\")\n    public void exportExcel(@Parameter(description = \"查询参数\") ${beanClass}QueryParam queryForm, HttpServletResponse response) throws IOException {\n        List<${beanClass}Excel> list = ${beanName}Service.exportExcel(queryForm);\n        // 添加水印\n        String watermark = AdminRequestUtil.getRequestUser().getActualName();\n        watermark += SmartLocalDateUtil.format(LocalDateTime.now(), SmartDateFormatterEnum.YMD_HMS);\n\n        SmartExcelUtil.exportExcelWithWatermark(response, \"${tableInfo.comment}列表.xlsx\", \"${tableInfo.comment}\", ${beanClass}Excel.class, list, watermark);\n    }\n\n    @Operation(summary = \"导入${tableInfo.comment}Excel\")\n    @PostMapping(\"/import${beanClass}Excel\")\n    @SaCheckPermission(\"${beanName}:import\")\n    public HttpResponse<String> importExcel(@RequestParam(\"file\") MultipartFile file) {\n        int count = ${beanName}Service.importExcel(file);\n        return HttpResponse.okMsg(\"成功导入\" + count + \"条数据\");\n    }\n}\n"}, {"name": "Mapper.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/dao\", \"Dao.java\")\npackage $!{packageName}.dao;\n\nimport com.baomidou.mybatisplus.core.mapper.BaseMapper;\nimport org.apache.ibatis.annotations.Mapper;\nimport org.apache.ibatis.annotations.Param;\nimport org.springframework.stereotype.Component;\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport  $!{packageName}.domain.${beanClass};\nimport  $!{packageName}.domain.excel.${beanClass}Excel;\nimport  $!{packageName}.domain.vo.${beanClass}VO;\nimport  $!{packageName}.domain.form.${beanClass}QueryParam;\nimport  $!{packageName}.domain.form.${beanClass}PageParam;\n\nimport java.util.List;\n\n/**\n * $!{tableInfo.comment}数据库访问层\n * @since $!time.currTime()\n */\n@Mapper\n@Component\npublic interface ${beanClass}<PERSON><PERSON> extends BaseMapper<${beanClass}> {\n\n    /**\n     * 分页查询\n     *\n     * @param page 分页参数\n     * @param queryForm 查询表单\n     * @return 对象列表\n     */\n    List<${beanClass}VO> query(Page page, @Param(\"param\") ${beanClass}PageParam queryForm);\n\n  /**\n  * 查询列表\n  * @param queryForm 查询表单\n  * @return 对象列表\n  *\n  */\n    List<$!{beanClass}VO> queryList(@Param(\"param\") $!{beanClass}QueryParam queryForm);\n\n    /**\n     * 批量更新删除状态\n     */\n    void batchUpdateDeleted(@Param(\"idList\") List<Long> idList, @Param(\"deletedFlag\") Boolean deletedFlag);\n\n    /**\n     * 根据ID删除\n     *\n     * @param id ID\n     * @return 影响行数\n     */\n    int deleteById(@Param(\"id\") Long id);\n\n\n    /**\n     * 导出Excel\n     *\n     * @param queryForm 查询条件\n     * @return 对象列表\n     */\n    List<${beanClass}Excel> exportExcel(@Param(\"query\") ${beanClass}QueryParam queryForm);\n}\n"}, {"name": "Mapper.xml.vm", "code": "$mybatisSupport.vm\n$common.vm\n$define.vm\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"Mapper.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper/business\"))\n\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{packageName}.dao.${beanClass}Dao\">\n    <!-- 通用查询条件 -->\n    <sql id=\"${beanClass}Condition\">\n        <!--@sql select * from $!{tableInfo.obj.name} t-->\n        <where>\n            #foreach($column in $tableInfo.fullColumn)\n                #if($column.type.equals(\"java.lang.String\"))\n            <if test=\"param.$!column.name != null and param.$!column.name != ''\">\n                AND t.$!{column.obj.name} LIKE CONCAT('%', #{param.$!column.name}, '%')\n            </if>\n                #elseif($column.type.equals(\"java.time.LocalDate\") || $column.type.equals(\"java.time.LocalDateTime\"))\n            <if test=\"param.${column.name}From != null and param.${column.name}From != ''\">\n                AND t.$!{column.obj.name} &gt;= #{param.${column.name}From}\n            </if>\n            <if test=\"param.${column.name}To != null and param.${column.name}To != ''\">\n                AND t.$!{column.obj.name} &lt;= #{param.${column.name}To}\n            </if>\n                #else\n            <if test=\"param.$!column.name != null and param.$!column.name != ''\">\n                AND t.$!{column.obj.name} = #{param.$!column.name}\n            </if>\n                #end\n            #end\n            and t.deleted_flag = false\n        </where>\n\n    </sql>\n\n    <update id=\"batchUpdateDeleted\">\n        UPDATE $!{tableInfo.obj.name}\n        SET deleted_flag = #{deletedFlag}\n        WHERE id IN\n        <foreach collection=\"idList\" item=\"id\" open=\"(\" separator=\",\" close=\")\">\n            #{id}\n        </foreach>\n    </update>\n\n    <insert id=\"batchInsert\">\n        INSERT INTO $!{tableInfo.obj.name}\n        (#foreach($column in $tableInfo.fullColumn)#if($column.name != \"id\")$!{column.obj.name}#if($foreach.hasNext), #end#end#end)\n        VALUES\n        <foreach collection=\"entities\" item=\"entity\" separator=\",\">\n            (#foreach($column in $tableInfo.fullColumn)#if($column.name != \"id\")#{entity.$!{column.name}}#if($foreach.hasNext), #end#end#end)\n        </foreach>\n    </insert>\n\n    <delete id=\"deleteById\">\n        UPDATE $!{tableInfo.obj.name}\n        SET deleted_flag = 1\n        WHERE id = #{id}\n    </delete>\n\n\n</mapper>\n\n\n"}, {"name": "MapperExt.xml.vm", "code": "$mybatisSupport.vm\n$common.vm\n$define.vm\n$!callback.setFileName($tool.append($!{tableInfo.name}, \"MapperExt.xml\"))\n$!callback.setSavePath($tool.append($modulePath, \"/src/main/resources/mapper/business\"))\n\n<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">\n<mapper namespace=\"$!{packageName}.dao.${beanClass}Dao\">\n\n    <!-- 分页查询 -->\n    <select id=\"query\" resultType=\"$!{packageName}.domain.vo.${beanClass}VO\">\n        SELECT t.*\n        FROM $!{tableInfo.obj.name} t\n            <include refid=\"${beanClass}Condition\"/>\n        ORDER BY t.create_time DESC\n    </select>\n    <!-- 查询列表 -->\n    <select id=\"queryList\" resultType=\"$!{packageName}.domain.vo.${beanClass}VO\">\n        SELECT t.*\n        FROM $!{tableInfo.obj.name} t\n            <include refid=\"${beanClass}Condition\"/>\n        ORDER BY t.create_time DESC\n    </select>\n    <!-- 导出Excel -->\n    <select id=\"exportExcel\" resultType=\"$!{packageName}.domain.excel.${beanClass}Excel\">\n        SELECT t.*\n        FROM $!{tableInfo.obj.name} t\n            <include refid=\"${beanClass}Condition\"/>\n        ORDER BY t.create_time DESC\n    </select>\n\n</mapper>\n"}, {"name": "Service.java.vm", "code": "$common.vm\n$define.vm\n#save(\"$!{packagePath}/service\", \"Service.java\")\npackage $!{packageName}.service;\n\nimport com.baomidou.mybatisplus.extension.plugins.pagination.Page;\nimport jakarta.annotation.Resource;\nimport org.springframework.stereotype.Service;\nimport org.springframework.transaction.annotation.Transactional;\nimport $!{packageName}.dao.${beanClass}Dao;\nimport  $!{packageName}.domain.${beanClass};\nimport  $!{packageName}.domain.vo.${beanClass}VO;\nimport  $!{packageName}.domain.form.${beanClass}QueryParam;\nimport  $!{packageName}.domain.form.${beanClass}PageParam;\nimport  $!{packageName}.domain.form.${beanClass}AddParam;\nimport  $!{packageName}.domain.form.${beanClass}UpdateForm;\nimport  $!{packageName}.domain.excel.${beanClass}Excel;\nimport cn.idev.excel.FastExcel;\nimport lombok.extern.slf4j.Slf4j;\nimport com.anxys.framework.common.domain.PageResult;\nimport com.anxys.framework.common.domain.HttpResponse;\nimport com.anxys.framework.common.code.UserErrorCode;\nimport com.anxys.framework.common.exception.BusinessException;\nimport com.anxys.framework.common.util.SmartBeanUtil;\nimport com.anxys.framework.common.util.SmartPageUtil;\nimport org.apache.commons.collections4.CollectionUtils;\nimport org.springframework.web.multipart.MultipartFile;\n\nimport java.io.IOException;\nimport java.util.List;\n\n/**\n * $!{tableInfo.comment}服务层\n *\n * @since $!time.currTime()\n */\n@Slf4j\n@Service\npublic class ${beanClass}Service {\n\n    @Resource\n    private ${beanClass}Dao ${beanName}Dao;\n\n    /**\n     * 分页查询\n     *\n     * @param queryForm 查询参数\n     * @return 分页结果\n     */\n    public PageResult<${beanClass}VO> query(${beanClass}PageParam queryForm) {\n        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm.getPageParam());\n        List<${beanClass}VO> list = ${beanName}Dao.query(page, queryForm);\n        return SmartPageUtil.convert2PageResult(page, list);\n    }\n\n    /**\n      * 列表查询\n      *\n      * @param queryForm 查询参数\n      * @return 列表结果\n      */\n    public List<${beanClass}VO> list(${beanClass}QueryParam queryForm) {\n        return ${beanName}Dao.queryList(queryForm);\n    }\n\n    /**\n     * 添加\n     *\n     * @param addForm 添加参数\n     */\n    @Transactional(rollbackFor = Exception.class)\n    public void add(${beanClass}AddParam addForm) {\n        ${beanClass} ${beanName} = SmartBeanUtil.copy(addForm, ${beanClass}.class);\n        ${beanName}Dao.insert(${beanName});\n    }\n\n    /**\n     * 更新\n     *\n     * @param updateForm 更新参数\n     */\n    @Transactional(rollbackFor = Exception.class)\n    public void update(${beanClass}UpdateForm updateForm) {\n        ${beanClass} ${beanName} = SmartBeanUtil.copy(updateForm, ${beanClass}.class);\n        ${beanName}Dao.updateById(${beanName});\n    }\n\n    /**\n     * 批量删除\n     *\n     * @param idList ID列表\n     */\n    @Transactional(rollbackFor = Exception.class)\n    public void batchDelete(List<Long> idList) {\n        if (!idList.isEmpty()) {\n            ${beanName}Dao.batchUpdateDeleted(idList, true);\n        }\n    }\n\n    /**\n     * 单个删除\n     *\n     * @param id ID\n     */\n    @Transactional(rollbackFor = Exception.class)\n    public void delete(Long id) {\n        ${beanName}Dao.deleteById(id);\n    }\n\n    /**\n     * 通过ID查询\n     *\n     * @param id ID\n     * @return 详情\n     */\n    public ${beanClass}VO getById(Long id) {\n        ${beanClass} ${beanName} = ${beanName}Dao.selectById(id);\n        if (${beanName} == null) {\n            throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);\n        }\n        return SmartBeanUtil.copy(${beanName}, ${beanClass}VO.class);\n    }\n\n    /**\n     * 导出Excel\n     *\n     * @param queryForm 查询参数\n     * @return Excel数据列表\n     */\n    public List<${beanClass}Excel> exportExcel(${beanClass}QueryParam queryForm) {\n        return ${beanName}Dao.exportExcel(queryForm);\n    }\n\n    /**\n     * 获取详情\n     *\n     * @param id ID\n     * @return 详情\n     */\n    public ${beanClass}VO getDetail(Long id) {\n        return getById(id);\n    }\n\n    /**\n     * 导入Excel\n     *\n     * @param file Excel文件\n     * @return 导入的数据条数\n     */\n    @Transactional(rollbackFor = Exception.class)\n    public int importExcel(MultipartFile file) {\n        List<${beanClass}Excel> dataList;\n        try {\n            dataList = FastExcel.read(file.getInputStream())\n                    .head(${beanClass}Excel.class)\n                    .sheet()\n                    .doReadSync();\n        } catch (IOException e) {\n            log.error(\"Excel导入失败\", e);\n            throw new BusinessException(\"数据格式存在问题，无法读取\");\n        }\n\n        if (CollectionUtils.isEmpty(dataList)) {\n            throw new BusinessException(\"数据为空\");\n        }\n\n        // 处理导入数据\n        log.info(\"开始处理导入数据，共 {} 条\", dataList.size());\n        List<${beanClass}> entityList = dataList.stream()\n                .map(excel -> {\n                    log.info(\"处理数据: {}\", excel);\n                    return SmartBeanUtil.copy(excel, ${beanClass}.class);\n                })\n                .toList();\n\n     \n        // ${beanName}Dao.batchInsert(entityList);\n\n        return dataList.size();\n    }\n}\n"}, {"name": "api.ts.vm", "code": "$common.vm\n$define.vm\n\n$!callback.setFileName($tool.append($tsFileName, \"-api.ts\"))\n$!callback.setSavePath($tool.append($projectPath,\"/admin-web/src/api/business/$tsFileName\"))\n\nimport type { PageResultModel } from '@/api/base-model/page-result-model'\nimport type { ResponseModel } from '@/api/base-model/response-model'\nimport type {\n  ${beanClass}FormParam,\n  ${beanClass}PageParam,\n  ${beanClass}QueryParam,\n  ${beanClass}Result,\n} from './model/$tsFileName-form-model'\nimport { getDownload, getRequest, postRequest } from '@/lib/http-request'\n\nexport const ${beanName}Api = {\n\n  // 分页查询$!{tableInfo.comment}\n  ${beanName}Page: (param: ${beanClass}PageParam): Promise<ResponseModel<PageResultModel<${beanClass}Result>>> => {\n    return postRequest<ResponseModel<PageResultModel<${beanClass}Result>>, ${beanClass}QueryParam>('/${beanName}/${beanName}Page', param)\n  },\n  // 列表查询$!{tableInfo.comment}\n  ${beanName}List: (param: ${beanClass}QueryParam): Promise<ResponseModel<${beanClass}Result[]>> => {\n    return postRequest<ResponseModel<${beanClass}Result[]>, ${beanClass}QueryParam>('/${beanName}/${beanName}List', param)\n  },\n  // 获取$!{tableInfo.comment}详情\n  ${beanName}Detail: (${beanName}Id: number): Promise<ResponseModel<${beanClass}Result>> => {\n    return getRequest<ResponseModel<${beanClass}Result>>(`/${beanName}/${beanName}Detail/${${beanName}Id}`, {})\n  },\n  // 添加$!{tableInfo.comment}\n  add${beanClass}: (param: ${beanClass}FormParam): Promise<ResponseModel<string>> => {\n    return postRequest<ResponseModel<string>, ${beanClass}FormParam>('/${beanName}/add${beanClass}', param)\n  },\n  // 更新$!{tableInfo.comment}\n  update${beanClass}: (param: ${beanClass}FormParam): Promise<ResponseModel<string>> => {\n    return postRequest<ResponseModel<string>, ${beanClass}FormParam>('/${beanName}/update${beanClass}', param)\n  },\n  // 删除$!{tableInfo.comment}\n  delete${beanClass}: (${beanName}Id: number): Promise<ResponseModel<string>> => {\n    return getRequest<ResponseModel<string>>(`/${beanName}/delete${beanClass}/${${beanName}Id}`, {})\n  },\n  // 批量删除$!{tableInfo.comment}\n  batchDelete${beanClass}: (${beanName}IdList: number[]): Promise<ResponseModel<string>> => {\n    return postRequest<ResponseModel<string>, number[]>('/${beanName}/batchDelete${beanClass}', ${beanName}IdList)\n  },\n\n  // 导入$!{tableInfo.comment}\n  import${beanClass}: (file: FormData): Promise<ResponseModel<string>> => {\n    return postRequest<ResponseModel<string>, FormData>('/${beanName}/import${beanClass}Excel', file)\n  },\n  // 导出$!{tableInfo.comment}\n  export${beanClass}: (param?: ${beanClass}QueryParam): void => {\n    return getDownload('/${beanName}/export${beanClass}Excel', param || {})\n  },\n}"}, {"name": "form-model.ts.vm", "code": "$common.vm\n$define.vm\n\n$!callback.setFileName($tool.append($tsFileName, \"-form-model.ts\"))\n$!callback.setSavePath($tool.append($projectPath,\"/admin-web/src/api/business/$tsFileName/model\"))\nimport type { PageParamModel } from '@/api/base-model/page-param-model'\nimport type { ${beanClass}Model } from './$tsFileName-model'\n\n/**\n * $!{tableInfo.comment}查询参数\n */\nexport interface ${beanClass}QueryParam extends ${beanClass}Model {\n #foreach($column in $tableInfo.fullColumn)\n            #if($column.type.equals(\"java.time.LocalDateTime\") || $column.type.equals(\"java.time.LocalDate\"))\n  /**\n   * $!{column.comment}始\n   */\n  $!{column.name}From?: string\n  /**\n   * $!{column.comment}至\n   */\n  $!{column.name}To?: string\n            #else\n            #end\n    #end\n}\n\n/**\n * $!{tableInfo.comment}分页查询参数\n */\nexport interface ${beanClass}PageParam extends ${beanClass}QueryParam {\n  pageParam: PageParamModel\n}\n\n/**\n * $!{tableInfo.comment}添加参数类型\n */\nexport interface ${beanClass}FormParam extends ${beanClass}Model {\n\n}\n\n/**\n * $!{tableInfo.comment}查询结果类型\n */\nexport interface ${beanClass}Result extends ${beanClass}Model {\n\n}"}, {"name": "model.ts.vm", "code": "$common.vm\n$define.vm\n\n$!callback.setFileName($tool.append($tsFileName, \"-model.ts\"))\n$!callback.setSavePath($tool.append($projectPath,\"/admin-web/src/api/business/$tsFileName/model\"))\n\nimport type { EntityBaseModel } from '@/api/base-model/entity-base-model'\n\n/**\n * $!{tableInfo.comment}类型定义\n */\nexport interface ${beanClass}Model extends EntityBaseModel {\n  /**\n   * $!{tableInfo.comment}ID\n   */\n  id?: number\n\n  #foreach($column in $tableInfo.fullColumn)\n    #if($column.name != \"id\" && $column.name != \"deletedFlag\" && $column.name != \"createTime\" && $column.name != \"updateTime\" && $column.name != \"createdBy\" && $column.name != \"updatedBy\")\n  /**\n   * $!{column.comment}\n   */\n      #if($column.type.equals(\"java.lang.String\"))\n  $!{column.name}?: string\n      #elseif($column.type.equals(\"java.lang.Integer\") || $column.type.equals(\"java.lang.Long\") || $column.type.equals(\"java.lang.Float\") || $column.type.equals(\"java.lang.Double\"))\n  $!{column.name}?: number\n      #elseif($column.type.equals(\"java.lang.Boolean\"))\n  $!{column.name}?: boolean\n      #elseif($column.type.equals(\"java.math.BigDecimal\"))\n  $!{column.name}?: string\n      #elseif($column.type.equals(\"java.time.LocalDate\") || $column.type.equals(\"java.time.LocalDateTime\"))\n  $!{column.name}?: string\n      #else\n  $!{column.name}?: string\n      #end\n\n    #end\n  #end\n}"}, {"name": "upload-model.ts.vm", "code": "$common.vm\n$define.vm\n\n$!callback.setFileName($tool.append($tsFileName, \"-upload-model.ts\"))\n$!callback.setSavePath($tool.append($projectPath,\"/admin-web/src/api/business/$tsFileName/model\"))\n\n/**\n * $!{tableInfo.comment}上传参数模型\n */\nexport interface ${beanClass}UploadParam {\n  /**\n   * $!{tableInfo.comment}ID\n   */\n  id?: number\n\n  #foreach($column in $tableInfo.fullColumn)\n    #if($column.name != \"id\" && $column.name != \"deletedFlag\" && $column.name != \"createTime\" && $column.name != \"updateTime\" && $column.name != \"createdBy\" && $column.name != \"updatedBy\")\n  /**\n   * $!{column.comment}\n   */\n      #if($column.type.equals(\"java.lang.String\"))\n  $!{column.name}#if($column.obj.notNull == false)?#end: string\n      #elseif($column.type.equals(\"java.lang.Integer\") || $column.type.equals(\"java.lang.Long\"))\n  $!{column.name}#if($column.obj.notNull == false)?#end: number\n      #elseif($column.type.equals(\"java.lang.Boolean\"))\n  $!{column.name}#if($column.obj.notNull == false)?#end: boolean\n      #elseif($column.type.equals(\"java.math.BigDecimal\"))\n  $!{column.name}#if($column.obj.notNull == false)?#end: string\n      #elseif($column.type.equals(\"java.time.LocalDate\") || $column.type.equals(\"java.time.LocalDateTime\"))\n  $!{column.name}#if($column.obj.notNull == false)?#end: string\n      #else\n  $!{column.name}#if($column.obj.notNull == false)?#end: string\n      #end\n    #end\n  #end\n}"}]}}, "columnConfig": {"Default": {"name": "<PERSON><PERSON><PERSON>", "elementList": [{"title": "disable", "type": "BOOLEAN", "selectValue": ""}, {"title": "support", "type": "SELECT", "selectValue": "add,edit,query,del,ui"}]}}, "globalConfig": {"txy": {"name": "txy", "elementList": [{"name": "autoImport.vm", "value": "##自动导入包（仅导入实体属性需要的包，通常用于实体类）\n#foreach($import in $importList)\nimport $!import;\n#end"}, {"name": "init.vm", "value": "##初始化区域\n\n##参考阿里巴巴开发手册，POJO 类中布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误\n#foreach($column in $tableInfo.fullColumn)\n    #if($column.name.startsWith(\"is\") && $column.type.equals(\"java.lang.Boolean\"))\n        $!column.setName($tool.firstLowerCase($column.name.substring(2)))\n    #end\n#end\n\n##实现动态排除列\n#set($temp = $tool.newHashSet(\"testCreateTime\", \"otherColumn\"))\n#foreach($item in $temp)\n    #set($newList = $tool.newArrayList())\n    #foreach($column in $tableInfo.fullColumn)\n        #if($column.name!=$item)\n            ##带有反回值的方法调用时使用$tool.call来消除返回值\n            $tool.call($newList.add($column))\n        #end\n    #end\n    ##重新保存\n    $tableInfo.setFullColumn($newList)\n#end\n\n##对importList进行篡改\n#set($temp = $tool.newHashSet())\n#foreach($column in $tableInfo.fullColumn)\n    #if(!$column.type.startsWith(\"java.lang.\"))\n        ##带有反回值的方法调用时使用$tool.call来消除返回值\n        $tool.call($temp.add($column.type))\n    #end\n#end\n##覆盖\n#set($importList = $temp)\n"}, {"name": "common.vm", "value": "##空格\n#set($projectName = \"erp\")\n#set($entityFolderName = \"entity\")\n#set($create_time = \"create_time\")\n#set($update_time = \"update_time\")\n#set($del_flag = \"deleted_flag\")\n\n\n$!tableInfo.setName($tool.getClassName($tableInfo.obj.name.replaceFirst(\"${projectName}_\",\"\")))\n#set($space = \" \")\n#set($tsFileName = $tableInfo.obj.name.replaceFirst(\"${projectName}_\",\"\").replace(\"_\",\"-\"))\n##实体类\n#set($beanClass = $tableInfo.name)\n##实体变量名\n#set($beanName = $!tool.firstLowerCase($tableInfo.name))\n##实体类定义\n#set($beanDef = $tool.append(${beanClass} ,$tool.append(${space} , ${beanName}) ))\n\n\n#if(\"$!tableInfo.savePackageName\" == \"\")\n    #set($packageName = $tool.append(\"com.anxys.erp.\",$beanName))\n#else\n    #set($packageName = $tool.append($tableInfo.savePackageName,\".\",$beanName))\n#end\n\n#if(\"$!tableInfo.savePackageName\" == \"\")\n    #set($packagePath = $tool.append(\"./sa-admin/src/main/java/net/lab1024/sa/admin/module/business/\",$beanName))\n#else\n    #set($packagePath = $tool.append($tableInfo.savePath,\"/\",$beanName))\n#end\n\n\n\n##controller类\n#set($controllerClass = $tool.append(${beanClass}, \"Controller\") )\n##controller变量名\n#set($controllerName = $tool.append(${beanName}, \"Controller\"))\n##controller定义\n#set($controllerDef = $tool.append(${controllerClass} , $tool.append(${space} , ${controllerName})))\n\n##service变量\n#set($serviceClass = $tool.append(${beanClass}, \"Service\") )\n#set($serviceName = $tool.append(${beanName}, \"Service\"))\n#set($serviceDef = $tool.append(${serviceClass} , $tool.append(${space} , ${serviceName})))\n\n##excel变量\n#set($excelClass = $tool.append(${beanClass}, \"ExcelVO\") )\n#set($excelName = $tool.append(${beanName}, \"ExcelVO\"))\n#set($excelDef = $tool.append(${excelClass} , $tool.append(${space} , ${excelName})))\n\n##Mapper\n#set($mapperClass = $tool.append(${beanClass}, \"Mapper\") )\n#set($mapperName = $tool.append(${beanName}, \"Mapper\"))\n#set($mapperDef = $tool.append(${mapperClass} , $tool.append(${space} , ${mapperName})))\n\n##拿到主键\n#if(!$tableInfo.pkColumn.isEmpty())\n    #set($pk = $tableInfo.pkColumn.get(0))\n    #set($pkType =  $pk.shortType)\n    #set($pkName =  $pk.name)\n    #set($pkDef = $tool.append($pkType , $tool.append(${space} , $pkName)))\n#end\n\n##作者信息\n#set($author = $!tool.getAuthor())\n"}, {"name": "define.vm", "value": "##（Velocity宏定义）\n##定义设置表名后缀的宏定义，调用方式：#setTableSuffix(\"Test\")\n#macro(setTableSuffix $suffix)\n    #set($tableName = $!tool.append($tableInfo.name, $suffix))\n#end\n\n##定义设置包名后缀的宏定义，调用方式：#setPackageSuffix(\"Test\")\n#macro(setPackageSuffix $suffix)\n#if($suffix!=\"\")package #end#if($tableInfo.savePackageName!=\"\")$!{tableInfo.savePackageName}.#{end}$!suffix;\n#end\n\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n##定义直接保存路径与文件名简化的宏定义，调用方式：#save(\"/entity\", \".java\")\n#macro(save $path $fileName)\n    $!callback.setSavePath($path)\n    $!callback.setFileName($tool.append($tableInfo.name, $fileName))\n#end\n\n##定义表注释的宏定义，调用方式：#tableComment(\"注释信息\")\n#macro(tableComment $desc)\n/**\n* $!{tableInfo.comment}($!{tableInfo.name})$desc\n*\n* <AUTHOR>\n* @since $!time.currTime()\n*/\n#end\n\n\n##定义GET，SET方法的宏定义，调用方式：#getSetMethod($column)\n#macro(getSetMethod $column)\n\n    public $!{tool.getClsNameByFullName($column.type)} get$!{tool.firstUpperCase($column.name)}() {\n        return $!{column.name};\n    }\n\n    public void set$!{tool.firstUpperCase($column.name)}($!{tool.getClsNameByFullName($column.type)} $!{column.name}) {\n        this.$!{column.name} = $!{column.name};\n    }\n#end"}, {"name": "mybatisSupport.vm", "value": "##针对Mybatis 进行支持，主要用于生成xml文件\n#foreach($column in $tableInfo.fullColumn)\n    ##储存列类型\n    $tool.call($column.ext.put(\"sqlType\", $tool.getField($column.obj.dataType, \"typeName\")))\n    #if($tool.newHashSet(\"java.lang.String\").contains($column.type))\n        #set($jdbcType=\"VARCHAR\")\n    #elseif($tool.newHashSet(\"java.lang.Boolean\", \"boolean\").contains($column.type))\n        #set($jdbcType=\"BOOLEAN\")\n    #elseif($tool.newHashSet(\"java.lang.Byte\", \"byte\").contains($column.type))\n        #set($jdbcType=\"BYTE\")\n    #elseif($tool.newHashSet(\"java.lang.Integer\", \"int\", \"java.lang.Short\", \"short\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Long\", \"long\").contains($column.type))\n        #set($jdbcType=\"INTEGER\")\n    #elseif($tool.newHashSet(\"java.lang.Float\", \"float\", \"java.lang.Double\", \"double\").contains($column.type))\n        #set($jdbcType=\"NUMERIC\")\n    #elseif($tool.newHashSet(\"java.util.Date\", \"java.sql.Timestamp\", \"java.time.Instant\", \"java.time.LocalDateTime\", \"java.time.OffsetDateTime\", \"\tjava.time.ZonedDateTime\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #elseif($tool.newHashSet(\"java.sql.Date\", \"java.time.LocalDate\").contains($column.type))\n        #set($jdbcType=\"TIMESTAMP\")\n    #else\n        ##其他类型\n        #set($jdbcType=\"VARCHAR\")\n    #end\n    $tool.call($column.ext.put(\"jdbcType\", $jdbcType))\n#end\n\n##定义宏，查询所有列\n#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($velocityHasNext), #end#end#end\n"}]}}}