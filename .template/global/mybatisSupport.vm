##针对Mybatis 进行支持，主要用于生成xml文件
#foreach($column in $tableInfo.fullColumn)
    ##储存列类型
    $tool.call($column.ext.put("sqlType", $tool.getField($column.obj.dataType, "typeName")))
    #if($tool.newHashSet("java.lang.String").contains($column.type))
        #set($jdbcType="VARCHAR")
    #elseif($tool.newHashSet("java.lang.Boolean", "boolean").contains($column.type))
        #set($jdbcType="BOOLEAN")
    #elseif($tool.newHashSet("java.lang.Byte", "byte").contains($column.type))
        #set($jdbcType="BYTE")
    #elseif($tool.newHashSet("java.lang.Integer", "int", "java.lang.Short", "short").contains($column.type))
        #set($jdbcType="INTEGER")
    #elseif($tool.newHashSet("java.lang.Long", "long").contains($column.type))
        #set($jdbcType="BIGINT")
    #elseif($tool.newHashSet("java.lang.Float", "float", "java.lang.Double", "double", "java.math.BigDecimal").contains($column.type))
        #set($jdbcType="DECIMAL")
    #elseif($tool.newHashSet("java.util.Date", "java.sql.Timestamp", "java.time.Instant", "java.time.LocalDateTime", "java.time.OffsetDateTime", "java.time.ZonedDateTime").contains($column.type))
        #set($jdbcType="TIMESTAMP")
    #elseif($tool.newHashSet("java.sql.Date", "java.time.LocalDate").contains($column.type))
        #set($jdbcType="DATE")
    #else
        ##其他类型
        #set($jdbcType="VARCHAR")
    #end
    $tool.call($column.ext.put("jdbcType", $jdbcType))
#end

##定义宏，查询所有列
#macro(allSqlColumn)#foreach($column in $tableInfo.fullColumn)$column.obj.name#if($velocityHasNext), #end#end#end 