##初始化区域

##去掉表的前缀
$!tableInfo.setName($tool.getClassName($tableInfo.obj.name.replaceFirst("txy_","")))

##参考阿里巴巴开发手册，POJO 类中布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误
#foreach($column in $tableInfo.fullColumn)
    #if($column.name.startsWith("is") && $column.type.equals("java.lang.Boolean"))
        $!column.setName($tool.firstLowerCase($column.name.substring(2)))
    #end
#end

##实现动态排除列
#set($temp = $tool.newHashSet("testCreateTime", "otherColumn"))
#foreach($item in $temp)
    #set($newList = $tool.newArrayList())
    #foreach($column in $tableInfo.fullColumn)
        #if($column.name!=$item)
            ##带有反回值的方法调用时使用$tool.call来消除返回值
            $tool.call($newList.add($column))
        #end
    #end
    ##重新保存
    $tableInfo.setFullColumn($newList)
#end

##对importList进行篡改
#set($temp = $tool.newHashSet())
#foreach($column in $tableInfo.fullColumn)
    #if(!$column.type.startsWith("java.lang."))
        ##带有反回值的方法调用时使用$tool.call来消除返回值
        $tool.call($temp.add($column.type))
    #end
#end
##覆盖
#set($importList = $temp) 