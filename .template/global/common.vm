##空格
#set($projectName = "erp")
#set($entityFolderName = "entity")
#set($create_time = "create_time")
#set($update_time = "update_time")
#set($del_flag = "deleted_flag")


$!tableInfo.setName($tool.getClassName($tableInfo.obj.name.replaceFirst("${projectName}_","")))
#set($space = " ")
#set($tsFileName = $tableInfo.obj.name.replaceFirst("${projectName}_","").replace("_","-"))
##实体类
#set($beanClass = $tableInfo.name)
##实体变量名
#set($beanName = $!tool.firstLowerCase($tableInfo.name))
##实体类定义
#set($beanDef = $tool.append(${beanClass} ,$tool.append(${space} , ${beanName}) ))


#if("$!tableInfo.savePackageName" == "")
    #set($packageName = $tool.append("com.anxys.erp.",$beanName))
#else
    #set($packageName = $tool.append($tableInfo.savePackageName,".",$beanName))
#end

#if("$!tableInfo.savePackageName" == "")
    #set($packagePath = $tool.append("./ax-admin/src/main/java/com/anxys/admin/module/business/",$beanName))
#else
    #set($packagePath = $tool.append($tableInfo.savePath,"/",$beanName))
#end

##controller类
#set($controllerClass = $tool.append(${beanClass}, "Controller") )
##controller变量名
#set($controllerName = $tool.append(${beanName}, "Controller"))
##controller定义
#set($controllerDef = $tool.append(${controllerClass} , $tool.append(${space} , ${controllerName})))

##service变量
#set($serviceClass = $tool.append(${beanClass}, "Service") )
#set($serviceName = $tool.append(${beanName}, "Service"))
#set($serviceDef = $tool.append(${serviceClass} , $tool.append(${space} , ${serviceName})))

##excel变量
#set($excelClass = $tool.append(${beanClass}, "ExcelVO") )
#set($excelName = $tool.append(${beanName}, "ExcelVO"))
#set($excelDef = $tool.append(${excelClass} , $tool.append(${space} , ${excelName})))

##Mapper
#set($mapperClass = $tool.append(${beanClass}, "Mapper") )
#set($mapperName = $tool.append(${beanName}, "Mapper"))
#set($mapperDef = $tool.append(${mapperClass} , $tool.append(${space} , ${mapperName})))

##拿到主键
#if(!$tableInfo.pkColumn.isEmpty())
    #set($pk = $tableInfo.pkColumn.get(0))
    #set($pkType =  $pk.shortType)
    #set($pkName =  $pk.name)
    #set($pkDef = $tool.append($pkType , $tool.append(${space} , $pkName)))
#end

##作者信息
#set($author = $!tool.getAuthor())
