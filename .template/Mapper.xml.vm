$mybatisSupport.vm
$common.vm
$define.vm
$!callback.setFileName($tool.append($!{tableInfo.name}, "Mapper.xml"))
$!callback.setSavePath($tool.append($modulePath, "/src/main/resources/mapper/$projectName"))


            <?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    <mapper namespace="$!{packageName}.dao.${beanClass}Mapper">
        <!-- 通用查询条件 -->
        <sql id="${beanClass}Condition">
            <!--@sql select * from $!{tableInfo.obj.name} t where deleted_flag-->
            #foreach($column in $tableInfo.fullColumn)
                #if($column.type.equals("java.lang.String"))
                    <if test="param.$!column.name != null and param.$!column.name != ''">
                        AND t.$!{column.obj.name} LIKE CONCAT('%', #{param.$!column.name}, '%')
                    </if>
                #elseif($column.type.equals("java.time.LocalDate") || $column.type.equals("java.time.LocalDateTime"))
                    <if test="param.${column.name}From != null ">
                        AND t.$!{column.obj.name} &gt;= #{param.${column.name}From}
                    </if>
                    <if test="param.${column.name}To != null">
                        AND t.$!{column.obj.name} &lt;= #{param.${column.name}To}
                    </if>
                #else
                    <if test="param.$!column.name != null and param.$!column.name != ''">
                        AND t.$!{column.obj.name} = #{param.$!column.name}
                    </if>
                #end
            #end
            and t.deleted_flag = false

        </sql>

        <update id="batchUpdateDeleted">
            UPDATE $!{tableInfo.obj.name}
            SET deleted_flag = #{deletedFlag}
            WHERE id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </update>

        <insert id="batchInsert">
            INSERT INTO $!{tableInfo.obj.name}
            (#foreach($column in $tableInfo.fullColumn)#if($column.name != "id")$!{column.obj.name}#if($foreach.hasNext)
            , #end#end#end)
            VALUES
            <foreach collection="list" item="entity" separator=",">
                (#foreach($column in $tableInfo.fullColumn)#if($column.name != "id")#{entity.$!{column.name}
                }#if($foreach.hasNext), #end#end#end)
            </foreach>
        </insert>



    </mapper>


