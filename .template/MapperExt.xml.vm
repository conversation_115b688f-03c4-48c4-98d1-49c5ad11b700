$mybatisSupport.vm
$common.vm
$define.vm
$!callback.setFileName($tool.append($!{tableInfo.name}, "MapperExt.xml"))
$!callback.setSavePath($tool.append($modulePath, "/src/main/resources/mapper/$projectName"))

            <?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    <mapper namespace="$!{packageName}.dao.${beanClass}Mapper">

        <sql id="${beanClass}ConditionExt">
            <!--@sql select * from $!{tableInfo.obj.name} t -->
            <where>
                <include refid="${beanClass}Condition"/>
                #foreach($column in $tableInfo.fullColumn)
                    #if($column.name != "id" && $column.name != "deletedFlag" && $column.name !=
                        "createTime" && $column.name != "updateTime" && $column.name != "createdBy" && $column.name !=
                        "updatedBy")
                        #if($column.type == "java.lang.String")
                            <if test="param.$column.name != null and param.$column.name != ''">
                                AND t.$mybatisSupport.getColumnName($column.name) LIKE CONCAT('%', #{param.$column.name
                                }, '%')
                            </if>
                        #else
                            <if test="param.$column.name != null">
                                AND t.$mybatisSupport.getColumnName($column.name) = #{param.$column.name}
                            </if>
                        #end
                    #end
                #end
            </where>
        </sql>
        <!-- 分页查询 -->
        <select id="page" resultType="$!{packageName}.domain.vo.${beanClass}VO">
            SELECT t.*
            FROM $!{tableInfo.obj.name} t
            <include refid="${beanClass}ConditionExt"/>
            ORDER BY t.create_time DESC
        </select>
        <!-- 查询列表 -->
        <select id="list" resultType="$!{packageName}.domain.vo.${beanClass}VO">
            SELECT t.*
            FROM $!{tableInfo.obj.name} t
            <include refid="${beanClass}ConditionExt"/>
            ORDER BY t.create_time DESC
        </select>
        <!-- 导出Excel -->
        <select id="exportExcel" resultType="$!{packageName}.domain.excel.${beanClass}Excel">
            SELECT t.*
            FROM $!{tableInfo.obj.name} t
            <include refid="${beanClass}ConditionExt"/>
            ORDER BY t.create_time DESC
        </select>

    </mapper>
