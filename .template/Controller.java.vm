$common.vm
$define.vm
#save("$!{packagePath}/controller", "Controller.java")
package $!{packageName}.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import  $!{packageName}.domain.form.${beanClass}AddParam;
import  $!{packageName}.domain.form.${beanClass}PageParam;
import  $!{packageName}.domain.form.${beanClass}QueryParam;
import  $!{packageName}.domain.form.${beanClass}UpdateParam;
import  $!{packageName}.domain.vo.${beanClass}VO;
import  $!{packageName}.domain.excel.${beanClass}Excel;
import $!{packageName}.service.${beanClass}Service;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.system.utils.AdminRequestUtil;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Parameter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

/**
 * $!{tableInfo.comment}控制器
 *
 * @since $!time.currTime()
 */
@Slf4j
@RestController 
@RequestMapping("/${beanName}")
@Tag(name = "$!{tableInfo.comment}接口")
public class ${controllerClass} {

    @Resource
    private ${beanClass}Service ${beanName}Service;

    @Operation(summary = "${tableInfo.comment}分页查询")
    @PostMapping("/${beanName}Page")
    @SaCheckPermission("${tsFileName}:query")
    public HttpResponse<PageResult<${beanClass}VO>> query(@RequestBody @Valid ${beanClass}PageParam queryParam) {
        PageResult<${beanClass}VO> pageResult = ${beanName}Service.page(queryParam);
        return HttpResponse.ok(pageResult);
    }

    @Operation(summary = "${tableInfo.comment}列表查询")
    @PostMapping("/${beanName}List")
    @SaCheckPermission("${tsFileName}:query")
    public HttpResponse<List<${beanClass}VO>> list(@RequestBody @Valid ${beanClass}QueryParam queryParam) {
        List<${beanClass}VO> list = ${beanName}Service.list(queryParam);
        return HttpResponse.ok(list);
    }

    @Operation(summary = "获取${tableInfo.comment}详情")
    @GetMapping("/${beanName}Detail/{id}")
    @SaCheckPermission("${tsFileName}:query")
    public HttpResponse<${beanClass}VO> detail(@PathVariable Long id) {
        ${beanClass}VO ${beanName}VO = ${beanName}Service.queryById(id);
        return HttpResponse.ok(${beanName}VO);
    }

    @Operation(summary = "添加${tableInfo.comment}")
    @PostMapping("/add${beanClass}")
    @SaCheckPermission("${tsFileName}:add")
    public HttpResponse<${beanClass}VO> insert(@RequestBody @Valid ${beanClass}AddParam addParam) {
        ${beanClass}VO result = ${beanName}Service.insert(addParam);
        return HttpResponse.ok(result);
    }

    @Operation(summary = "更新${tableInfo.comment}")
    @PostMapping("/update${beanClass}")
    @SaCheckPermission("${tsFileName}:update")
    public HttpResponse<String> update(@RequestBody @Valid ${beanClass}UpdateParam updateParam) {
        ${beanName}Service.update(updateParam);
        return HttpResponse.ok();
    }

    @Operation(summary = "删除${tableInfo.comment}")
    @GetMapping("/delete${beanClass}/{id}")
    @SaCheckPermission("${tsFileName}:delete")
    public HttpResponse<String> delete(@PathVariable Long id) {
        ${beanName}Service.delete(id);
        return HttpResponse.ok();
    }

    @Operation(summary = "批量删除${tableInfo.comment}")
    @PostMapping("/batchDelete${beanClass}")
    @SaCheckPermission("${tsFileName}:delete")
    public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
        ${beanName}Service.batchDelete(idList);
        return HttpResponse.ok();
    }

    @Operation(summary = "导出${tableInfo.comment}Excel")
    @GetMapping("/export${beanClass}Excel")
    @SaCheckPermission("${tsFileName}:export")
    public void exportExcel(@Parameter(description = "查询参数") ${beanClass}QueryParam queryParam, HttpServletResponse response) throws IOException {
        List<${beanClass}Excel> list = ${beanName}Service.exportExcel(queryParam);
        // 添加水印
        String watermark = AdminRequestUtil.getRequestUser().getActualName();
        watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

        ExcelUtil.exportExcelWithWatermark(response, "${tableInfo.comment}列表.xlsx", "${tableInfo.comment}", ${beanClass}Excel.class, list, watermark);
    }

    @Operation(summary = "导入${tableInfo.comment}Excel")
    @PostMapping("/import${beanClass}Excel")
    @SaCheckPermission("${tsFileName}:import")
    public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
        int count = ${beanName}Service.importExcel(file);
        return HttpResponse.okMsg("成功导入" + count + "条数据");
    }
}