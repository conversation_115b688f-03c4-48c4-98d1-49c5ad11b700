$common.vm
$define.vm
#save("$!{packagePath}/dao", "Mapper.java")
        package $!{packageName}.dao;

import com.anxys.framework.mybatis.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import  $!{packageName}.domain.${beanClass};
import  $!{packageName}.domain.excel.${beanClass}Excel;
import  $!{packageName}.domain.vo.${beanClass}VO;
import  $!{packageName}.domain.form.${beanClass}QueryParam;
import  $!{packageName}.domain.form.${beanClass}PageParam;

import java.util.List;

/**
 * $!{tableInfo.comment}数据库访问层
 * @since $!time.currTime()
 *
 */
@Mapper
public interface ${beanClass}Mapper extends BaseMapperX<${beanClass}> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<${beanClass}VO> page(Page<${beanClass}VO> page, @Param("param") ${beanClass}PageParam queryParam);


    /**
    * 查询列表
    * @param queryParam 查询参数
    * @return 对象列表
    *
    */
    List<$!{beanClass}VO> list(@Param("param") $!{beanClass}QueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(@Param("list")List<${beanClass}> entityList);




    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<${beanClass}Excel> exportExcel(@Param("query") ${beanClass}QueryParam queryParam);
}
