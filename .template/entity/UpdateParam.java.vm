$common.vm
$define.vm
#save("$!{packagePath}/domain/form", "UpdateParam.java")
package $!{packageName}.domain.form;
$autoImport.vm
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import $!{packageName}.domain.${beanClass};

/**
 * $!{tableInfo.comment}更新表单
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ${beanClass}UpdateParam extends ${beanClass} {

    @Schema(description = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;

}
