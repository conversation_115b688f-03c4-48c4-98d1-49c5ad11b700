$common.vm
$define.vm
#save("$!{packagePath}/domain", ".java")
        package $!{packageName}.domain;
$autoImport.vm
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.anxys.framework.common.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;


/**
 * $!{tableInfo.comment}实体类
 *
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("$!{tableInfo.obj.name}")
public class ${beanClass} extends BaseEntity {
    @Schema(description = "主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;
    #foreach($column in $tableInfo.fullColumn)
        #if($column.name!="id" && $column.name!="createdBy" && $column.name!="updatedBy" && $column.name!="deletedFlag" && $column.name!="createTime" && $column.name!="updateTime")
            @Schema(description = "$!{column.comment}")
                #if($column.type.equals("java.time.LocalDateTime"))
                @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
                #elseif($column.type.equals("java.time.LocalDate"))
                @JsonFormat(pattern = "yyyy-MM-dd")
                #end
            private $!{tool.getClsNameByFullName($column.type)} $!{column.name};
        #end
    #end
}
