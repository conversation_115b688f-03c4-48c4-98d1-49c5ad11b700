$common.vm
$define.vm
#save("$!{packagePath}/domain/vo", "VO.java")
package $!{packageName}.domain.vo;
$autoImport.vm
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import $!{packageName}.domain.${beanClass};

/**
 * $!{tableInfo.comment}VO
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ${beanClass}VO extends ${beanClass} {

}
