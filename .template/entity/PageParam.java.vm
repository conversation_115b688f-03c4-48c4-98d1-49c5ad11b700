$common.vm
$define.vm
#save("$!{packagePath}/domain/form", "PageParam.java")
package $!{packageName}.domain.form;
$autoImport.vm
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.anxys.framework.common.domain.PageParam;


/**
 * $!{tableInfo.comment}分页查询表单
 *
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class ${beanClass}PageParam extends ${beanClass}QueryParam {

    @Schema(description = "分页参数")
    @Valid
    @NotNull(message = "分页参数不能为空")
    PageParam pageParam = new PageParam();

}
