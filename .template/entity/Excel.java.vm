$common.vm
$define.vm
#save("$!{packagePath}/domain/excel", "Excel.java")
package $!{packageName}.domain.excel;
$autoImport.vm
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * $!{tableInfo.comment}Excel导出VO
 * @since $!time.currTime()
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ${beanClass}Excel implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    #foreach($column in $tableInfo.fullColumn)
        #if($column.name!="id" && $column.name!="deletedFlag")
            #if($!{column.comment})@ExcelProperty("$!{column.comment}")#end
        private $!{tool.getClsNameByFullName($column.type)} $!{column.name};
        #end
    #end
}
