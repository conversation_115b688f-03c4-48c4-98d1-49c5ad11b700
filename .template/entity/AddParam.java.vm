$common.vm
$define.vm
#save("$!{packagePath}/domain/form/", "AddParam.java")
package $!{packageName}.domain.form;
$autoImport.vm
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import $!{packageName}.domain.${beanClass};
/**
 * $!{tableInfo.comment}添加表单
 *
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ${beanClass}AddParam extends ${beanClass} {

    #foreach($column in $tableInfo.fullColumn)
        #if($column.name!="id" && $column.name!="deletedFlag" && $column.name!="createTime" && $column.name!=
            "updateTime" && $column.name!="createdBy" && $column.name!="updatedBy" && $column.obj.notNull)
            #if($column.type == "java.lang.String")
            @NotBlank(message = "$!{column.comment}不能为空")
            #else
            @NotNull(message = "$!{column.comment}不能为空")
            #end
        private $!{tool.getClsNameByFullName($column.type)} $!{column.name};
        #end
    #end
}
