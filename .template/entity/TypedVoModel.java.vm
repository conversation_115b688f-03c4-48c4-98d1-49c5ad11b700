$common.vm
$define.vm
#save("$!{packagePath}/domain/vo/", "${beanClass}TypedVo.java")
package $!{packageName}.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import $!{packageName}.domain.${beanClass};
import java.util.List;
import java.util.ArrayList;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.apache.commons.lang3.StringUtils;

/**
 * $!{tableInfo.comment}类型化视图对象
 * 用于前端展示，包含对JSON字段的类型化扩展处理
 * 使用时需要提供具体的类型参数
 *
 * @param <T> JSON数组字段中的元素类型
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ${beanClass}TypedVo<T> extends ${beanClass} {

    /**
     * 使用示例：
     * // 创建一个带有SomeType类型的视图对象
     * ${beanClass}TypedVo<SomeType> vo = new ${beanClass}TypedVo<>();
     * 
     * // 设置JSON字符串
     * vo.setJsonFieldName("[{\"id\":1,\"name\":\"test\"}]");
     * 
     * // 获取类型化的数组
     * List<SomeType> array = vo.getJsonFieldNameArray();
     * 
     * // 修改数组并同步更新JSON字符串
     * vo.setJsonFieldNameArray(newArray);
     */
    
    #foreach($column in $tableInfo.fullColumn)
        #if($column.comment.contains("JSON") || $column.comment.contains("json") || $column.type == "java.lang.String" && ($column.name.endsWith("Json") || $column.name.endsWith("List") || $column.name.contains("Array") || $column.name.endsWith("Details")))
    // ${column.comment}对应的类型化数组
    private List<T> ${column.name}Array;
    
    /**
     * 获取${column.comment}类型化数组
     */
    @SuppressWarnings("unchecked")
    public List<T> get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}Array() {
        if (${column.name}Array == null && StringUtils.isNotEmpty(get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}())) {
            try {
                ${column.name}Array = JSON.parseArray(get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}(), (Class<T>)Class.forName(getClass().getGenericSuperclass().toString()));
            } catch (Exception e) {
                ${column.name}Array = new ArrayList<>();
            }
        }
        return ${column.name}Array == null ? new ArrayList<>() : ${column.name}Array;
    }
    
    /**
     * 设置${column.comment}类型化数组并同步更新字符串字段
     */
    public void set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}Array(List<T> ${column.name}Array) {
        this.${column.name}Array = ${column.name}Array;
        if (${column.name}Array != null) {
            set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}(JSON.toJSONString(${column.name}Array));
        } else {
            set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}(null);
        }
    }
        #end
    #end
} 