$common.vm
$define.vm
#save("$!{packagePath}/domain/form", "QueryParam.java")
package $!{packageName}.domain.form;
$autoImport.vm
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import $!{packageName}.domain.${beanClass};

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * $!{tableInfo.comment}查询表单
 *
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ${beanClass}QueryParam extends ${beanClass} {

    #foreach($column in $tableInfo.fullColumn)
        #if($column.name!="id" && $column.name!="deletedFlag")
            #if($column.type.equals("java.time.LocalDateTime"))
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "$!{column.comment}始")
            private LocalDateTime $!{column.name}From;

            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            @Schema(description = "$!{column.comment}至")
            private LocalDateTime $!{column.name}To;
            #elseif($column.type.equals("java.time.LocalDate"))
            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "$!{column.comment}始")
            private LocalDate $!{column.name}From;

            @JsonFormat(pattern = "yyyy-MM-dd")
            @Schema(description = "$!{column.comment}至")
            private LocalDate $!{column.name}To;
            #end
        #end
    #end
}
