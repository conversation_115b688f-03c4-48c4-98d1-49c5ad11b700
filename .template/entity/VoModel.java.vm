$common.vm
$define.vm
#save("$!{packagePath}/domain/vo/", "${beanClass}Vo.java")
package $!{packageName}.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import $!{packageName}.domain.${beanClass};
import java.util.List;
import java.util.ArrayList;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

/**
 * $!{tableInfo.comment}视图对象
 * 用于前端展示，包含对JSON字段的扩展处理
 *
 * @since $!time.currTime()
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ${beanClass}Vo extends ${beanClass} {

    /**
     * 在这里添加需要扩展的JSON数组字段
     * 示例:
     * // JSON字符串字段对应的数组
     * private List<SomeType> jsonFieldNameArray;
     * 
     * // 获取JSON数组
     * public List<SomeType> getJsonFieldNameArray() {
     *     if (jsonFieldNameArray == null && StringUtils.isNotEmpty(getJsonFieldName())) {
     *         try {
     *             jsonFieldNameArray = JSON.parseArray(getJsonFieldName(), SomeType.class);
     *         } catch (Exception e) {
     *             jsonFieldNameArray = new ArrayList<>();
     *         }
     *     }
     *     return jsonFieldNameArray == null ? new ArrayList<>() : jsonFieldNameArray;
     * }
     * 
     * // 设置JSON数组并同步更新字符串字段
     * public void setJsonFieldNameArray(List<SomeType> jsonFieldNameArray) {
     *     this.jsonFieldNameArray = jsonFieldNameArray;
     *     if (jsonFieldNameArray != null) {
     *         setJsonFieldName(JSON.toJSONString(jsonFieldNameArray));
     *     } else {
     *         setJsonFieldName(null);
     *     }
     * }
     */
    
    #foreach($column in $tableInfo.fullColumn)
        #if($column.comment.contains("JSON") || $column.comment.contains("json") || $column.type == "java.lang.String" && ($column.name.endsWith("Json") || $column.name.endsWith("List") || $column.name.contains("Array") || $column.name.endsWith("Details")))
    // ${column.comment}对应的数组
    private List<Object> ${column.name}Array;
    
    /**
     * 获取${column.comment}数组
     */
    public List<Object> get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}Array() {
        if (${column.name}Array == null && StringUtils.isNotEmpty(get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}())) {
            try {
                ${column.name}Array = JSON.parseArray(get${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}());
            } catch (Exception e) {
                ${column.name}Array = new ArrayList<>();
            }
        }
        return ${column.name}Array == null ? new ArrayList<>() : ${column.name}Array;
    }
    
    /**
     * 设置${column.comment}数组并同步更新字符串字段
     */
    public void set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}Array(List<Object> ${column.name}Array) {
        this.${column.name}Array = ${column.name}Array;
        if (${column.name}Array != null) {
            set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}(JSON.toJSONString(${column.name}Array));
        } else {
            set${column.name.substring(0,1).toUpperCase()}${column.name.substring(1)}(null);
        }
    }
        #end
    #end
}
