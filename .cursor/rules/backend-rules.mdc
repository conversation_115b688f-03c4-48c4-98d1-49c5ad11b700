---
description: 
globs: 
alwaysApply: false
---
# 后端开发规范

## 后端实体结构

参考路径: `sa-base/src/main/java/net/lab1024/sa/base/module/**/{module_name}/domain/`

```text
domain/
├── excel/            # Excel导入导出相关类
├── form/             # 表单参数与请求对象
│   ├── 实体名Param.java     # 添加操作参数类
│   ├── 实体名PageParam.java # 分页查询参数类
│   ├── 实体名QueryParam.java # 普通查询参数类
│   └── 实体名UpdateForm.java # 更新表单参数类
├── vo/              # 视图对象(Value Object)
│   └── 实体名VO.java       # VO类，继承领域实体类，额外字段从连表获取
└── 实体名.java       # 领域实体类，与数据库表字段一一对应
```

## 数据结构规则

1.  **字段类型严格区分**：
    *   标识符/状态标志 (0/1): 使用 `tinyint`，不属于字典。
    *   多值状态码 (>2个值): 使用 `varchar(20)`，属于字典项。
    *   所有字典项: 固定使用 `varchar(20)` 类型存储。
2.  **原值返回**: 所有值直接返回前端，不进行转换。

## 开发规范

1.  **SQL规则**:
    *   写连表时, 表别名用 `t`, `t1`, `t2` 简化。
    *   扩展查询条件和新语句创建在 `mapperExt.xml` 文件中。
    *   移除 `CASE` 语句，由前端处理显示文本。
2.  **关联查询优化**:
    *   使用 ID 列表批量查询替代循环查询。
    *   通过 `Map` 分组提高数据组装效率。

## 修改要求

1.  只修改 `mapperExt.xml`，不动 `mapper.xml`。
2.  `Service` 层确保返回原始状态值。
3.  前端根据原始值处理样式和文本显示。

