---
description:
globs:
alwaysApply: false
---
# 业务树组件生成规则

## 🎯 概述

本规则定义了基于现有 `AxProductCategoryTree` 模式生成新业务树组件的标准化方案。适用于所有需要树形结构展示和选择的业务场景。

---

## 📋 组件结构规范

### 一、文件组织结构

```
src/components/business/AxTree/Ax[BusinessName]Tree/
├── Ax[BusinessName]Tree.vue              # 主组件文件
├── Ax[BusinessName]TreeService.ts        # 服务逻辑层
└── index.ts                              # 导出入口
```

### 二、命名约定

| 组件元素 | 命名规范 | 示例 |
|----------|----------|------|
| **组件名称** | `Ax[BusinessName]Tree` | `AxAreaTree`, `AxDepartmentTree` |
| **服务类名** | `Ax[BusinessName]TreeService` | `AxAreaTreeService` |
| **服务Symbol** | `AX_[BUSINESS_NAME]_TREE_SERVICE` | `AX_AREA_TREE_SERVICE` |
| **回调方法** | `on[BusinessName]TreeChange` | `onAreaTreeChange` |

---

## 🔧 核心实现模板

### 1. 服务层模板 (AxXxxTreeService.ts)

```typescript
import type { Ax[BusinessName]TreeParentService } from './index'
import { [businessName]Api } from '@/api/[apiPath]/[business-name]-api'
import { ListProcessor } from '@/service/internal/ListProcessor'

export const AX_[BUSINESS_NAME]_TREE_SERVICE = Symbol('AX_[BUSINESS_NAME]_TREE_SERVICE')

// 定义数据节点接口
interface [BusinessName]Node {
  id: number
  [labelField]: string  // 如: name, title, categoryName
  children?: [BusinessName]Node[]
}

// 查询参数接口
interface [BusinessName]TreeQueryParam {
  pageParam: {
    pageNum: number
    pageSize: number
  }
  // 可添加其他查询条件
}

export class Ax[BusinessName]TreeService extends ListProcessor<[BusinessName]Node, [BusinessName]TreeQueryParam> {
  parentService: Ax[BusinessName]TreeParentService | undefined = undefined

  constructor() {
    super('[业务名称]', {
      queryPage: [businessName]Api.[queryMethod] as unknown as (param: [BusinessName]TreeQueryParam) => Promise<{ data?: PageResult<[BusinessName]Node> | undefined }>,
    })
  }

  provide = (): void => {
    provide(AX_[BUSINESS_NAME]_TREE_SERVICE, this)
  }

  // ------------------------------ 混合业务逻辑 ------------------------------
  on[BusinessName]TreeChange = (value: string | number | undefined): void => {
    console.log('on[BusinessName]TreeChange', value)
    
    // 业务逻辑处理
    if (this.parentService) {
      // 更新父服务的查询参数
      this.parentService.queryParam.[fieldName] = value
      // 可触发父组件数据刷新
      // this.parentService.queryPage?.()
    }
  }
}
```

### 2. 视图层模板 (AxXxxTree.vue)

```vue
<script setup lang="ts">
import type { DataNode } from '@/components/base/CustomTree'
import type { Ax[BusinessName]TreeService } from './Ax[BusinessName]TreeService'

import { CustomTree as Tree } from '@/components/base/CustomTree'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { AX_[BUSINESS_NAME]_TREE_SERVICE } from './Ax[BusinessName]TreeService'

// 获取服务实例
const service = inject(AX_[BUSINESS_NAME]_TREE_SERVICE) as Ax[BusinessName]TreeService
service.parentService = inject(CRUD_KEY)

function handleChange(value: string | number | undefined) {
  service.on[BusinessName]TreeChange(value)
}
</script>

<template>
  <div class="w-64">
    <Tree
      :tree-data="service.rawPageResult as unknown as DataNode[]"
      label-key="[labelField]"
      value-key="id"
      children-key="children"
      placeholder="请选择[业务名称]"
      @change="handleChange"
    />
  </div>
</template>
```

### 3. 入口文件模板 (index.ts)

```typescript
import Ax[BusinessName]Tree from './Ax[BusinessName]Tree.vue'
import { AX_[BUSINESS_NAME]_TREE_SERVICE, Ax[BusinessName]TreeService } from './Ax[BusinessName]TreeService'

export interface Ax[BusinessName]TreeParentService {
  queryParam: Record<string, unknown>
  formParam: Record<string, unknown>
  formOpen: boolean
}

export { AX_[BUSINESS_NAME]_TREE_SERVICE, Ax[BusinessName]Tree, Ax[BusinessName]TreeService }
```

---

## 🎨 配置参数说明

### 关键配置项对照表

| 配置项 | 说明 | 常见值 | 示例 |
|--------|------|--------|------|
| **labelField** | 显示标签字段 | `name`, `title`, `categoryName` | 地区树用 `name` |
| **valueField** | 值字段 | 通常为 `id` | 固定使用 `id` |
| **childrenField** | 子节点字段 | 通常为 `children` | 固定使用 `children` |
| **placeholder** | 占位符文本 | `请选择[业务名称]` | `请选择地区` |
| **组件宽度** | 树组件宽度 | `w-64`, `w-56`, `w-72` | 默认 `w-64` |

---

## 🚀 使用方式规范

### 在父组件中集成

```vue
<script setup lang="ts">
import { Ax[BusinessName]Tree, Ax[BusinessName]TreeService } from '@/components/business/AxTree/Ax[BusinessName]Tree'

// 创建并提供服务实例
const [businessName]TreeService = new Ax[BusinessName]TreeService()
[businessName]TreeService.provide()
</script>

<template>
  <div class="filter-section">
    <Ax[BusinessName]Tree />
  </div>
</template>
```

---

## 📊 实际案例对照

### AxAreaTree 实现示例

| 模板占位符 | AxAreaTree 实际值 |
|------------|-------------------|
| `[BusinessName]` | `Area` |
| `[BUSINESS_NAME]` | `AREA` |
| `[businessName]` | `area` |
| `[business-name]` | `area` |
| `[labelField]` | `name` |
| `[业务名称]` | `地区` |
| `[apiPath]` | `system` |
| `[queryMethod]` | `areaTree` |

---

## ⚡ 扩展性设计原则

### 1. 服务层扩展
- **异步加载支持**：基于 `ListProcessor` 的异步数据处理能力
- **状态管理**：响应式数据状态和加载状态管理
- **错误处理**：统一的错误处理和用户提示机制

### 2. 视图层扩展
- **样式定制**：通过 props 支持宽度、高度等样式定制
- **过滤搜索**：利用 `CustomTree` 的 `filterProp` 支持搜索
- **事件扩展**：支持选择、展开、收缩等多种树操作事件

### 3. 业务逻辑扩展
- **联动机制**：通过 `parentService` 实现与父组件的数据联动
- **缓存策略**：可在服务层添加数据缓存机制提升性能
- **权限控制**：可集成权限验证机制控制树节点的可见性

---

## 🔍 质量检查清单

### 生成组件前检查
- [ ] 确认API接口已定义且返回数据结构符合树形要求
- [ ] 确认业务名称命名规范和一致性
- [ ] 确认数据字段映射关系（labelField, valueField等）

### 生成组件后验证
- [ ] 组件能正常渲染且数据加载正确
- [ ] 选择事件能正确触发业务逻辑
- [ ] 与父组件的数据联动机制工作正常
- [ ] 搜索过滤功能（如需要）工作正常
- [ ] TypeScript 类型检查无错误

---

## 💡 最佳实践建议

### 1. 性能优化
- 大数据量时考虑虚拟滚动或分页加载
- 使用 `toRaw()` 获取非响应式数据避免不必要的响应式开销
- 合理设置树的默认展开层级

### 2. 用户体验
- 提供清晰的加载状态提示
- 支持键盘导航和快捷键操作
- 错误状态下提供友好的提示信息

### 3. 代码维护
- 保持命名的一致性和语义化
- 添加适当的注释说明业务逻辑
- 遵循现有的代码风格和架构模式

---

## 🎯 适用场景

此规则适用于以下业务树组件的快速生成：

- **组织架构树**：部门、职位层级选择
- **地理区域树**：省市区、国家地区选择  
- **分类目录树**：商品分类、文档分类选择
- **权限树**：菜单权限、功能权限选择
- **其他层次化数据**：任何具有父子关系的业务数据

通过遵循此规则，可以确保所有业务树组件的一致性、可维护性和扩展性 🌳
