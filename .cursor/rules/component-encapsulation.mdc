---
description: 
globs: 
alwaysApply: false
---
# 组件封装规范

## 目录结构
组件封装必须遵循以下目录结构，以 [DateRangePicker.vue](mdc:src/components/framework-new/Picker/DateRangePicker.vue) 为标准示例：

```
ComponentName/
├── ComponentName.vue    # 主组件文件
├── index.ts            # 导出文件
└── type.ts             # 类型定义文件
```

## 文件规范

### 1. 主组件文件 (.vue)
- 使用 `<script setup lang="ts">` 语法
- 必须定义 TypeScript 类型的 props
- 为所有 props 提供合理的默认值
- 使用 `defineExpose` 暴露必要的方法和属性
- 组件内部使用 ref 时必须明确类型定义

示例参考 [DateRangePicker.vue](mdc:src/components/framework-new/Picker/DateRangePicker.vue)：
```vue
<script setup lang="ts">
import type { ComponentProps } from './type'
import { ref, defineExpose } from 'vue'

// 为 props 提供默认值
const props = withDefaults(defineProps<ComponentProps>(), {
  class: 'w-72', // 使用 TailwindCSS 默认样式
  placeholder: () => ['开始日期', '结束日期']
})

// 明确 ref 类型
const componentRef = ref<InstanceType<typeof SomeComponent>>()

// 暴露必要的方法和属性
defineExpose({
  componentRef,
})
</script>

<template>
  <div :class="props.class">
    <!-- 组件内容 -->
  </div>
</template>
```

### 2. 类型定义文件 (type.ts)
- 定义清晰的 TypeScript 接口
- 继承基础接口，避免重复定义
- 使用 `[key: string]: unknown` 支持额外属性传递

示例参考 [type.ts](mdc:src/components/framework-new/Picker/type.ts)：
```typescript
export interface BaseProps {
  class?: string;
  [key: string]: unknown;
}

export interface ComponentProps extends BaseProps {
  // 组件特定属性
  value?: SomeType;
  placeholder?: string[];
}
```

### 3. 导出文件 (index.ts)
- 统一导出所有组件
- 使用命名导出，便于按需引入

示例参考 [index.ts](mdc:src/components/framework-new/Picker/index.ts)：
```typescript
import ComponentName from './ComponentName.vue'

export { ComponentName }
```

## 样式规范

### TailwindCSS 使用
- **必须使用** TailwindCSS 进行样式配置
- **禁止使用** Less、Sass 等预处理器
- 为组件提供合理的默认 TailwindCSS 类名
- 通过 `class` prop 允许外部覆盖样式

```vue
<template>
  <div :class="props.class || 'w-72 p-4 border rounded'">
    <!-- 默认样式，可通过 props.class 覆盖 -->
  </div>
</template>
```

## 错误处理规范

### 语法检查
- **仅检查语法错误**，忽略 lint 警告
- 确保 TypeScript 类型正确
- 确保 Vue 组件语法正确

### 属性暴露原则
- **有限暴露**：只暴露必要的属性和方法
- **默认值**：所有暴露的属性必须有合理默认值
- **无赋值调用**：确保不传任何 props 时组件能正常渲染和使用

## 最佳实践

1. **组件命名**：使用 PascalCase 命名组件文件
2. **类型安全**：所有 props 和 ref 都要有明确的 TypeScript 类型
3. **可扩展性**：通过 `[key: string]: unknown` 支持未来扩展
4. **样式灵活性**：提供默认样式但允许完全自定义
5. **文档注释**：为复杂逻辑添加中文注释

## 示例组件结构

参考现有的 Picker 组件实现：
- [DateRangePicker.vue](mdc:src/components/framework-new/Picker/DateRangePicker.vue) - 主组件实现
- [type.ts](mdc:src/components/framework-new/Picker/type.ts) - 类型定义
- [index.ts](mdc:src/components/framework-new/Picker/index.ts) - 统一导出
