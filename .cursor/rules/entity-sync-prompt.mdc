---
description: 
globs: 
alwaysApply: false
---
# 后端实体修改后同步更新前端实体提示词

你是一位精通前后端开发的AI助手，请帮我在后端Java实体类修改后，同步更新对应的前端TypeScript接口定义。

## 任务描述
当后端Java实体类发生修改后（如字段类型变更、新增/删除字段等），需要同步更新前端TypeScript接口定义，确保前后端数据结构保持一致。

## 同步更新规则

1. **类型映射对照表**
   - `String` → `string`
   - `Integer`/`int` → `number`
   - `Long`/`long` → `number`
   - `Boolean`/`boolean` → `boolean`
   - `Float`/`float`/`Double`/`double` → `number`
   - `BigDecimal` → `number`
   - `Date`/`LocalDate`/`LocalDateTime` → `string` (ISO格式)
   - `List<T>` → `T[]`
   - `Map<K, V>` → `Record<K, V>`
   - 枚举类型 → 联合类型字符串 (如 `'OPTION1' | 'OPTION2'`)

2. **字段处理原则**
   - 保持字段名称一致性（驼峰命名）
   - 修改字段类型以匹配后端变更
   - 更新注释中的数据字典说明
   - 处理嵌套对象和关联关系变更
   - 确保所有属性保持可选性质 (加上?)

3. **常见修改场景**
   - 添加新字段
   - 移除废弃字段
   - 修改字段类型
   - 变更关联对象结构

## 执行步骤

1. **自动分析代码文件**
   - 查找指定的后端Java实体类文件
   - 查找对应的前端TypeScript接口文件
   - 对比文件，查找差异和需要同步的变更

2. **执行同步更新**
   - 按照类型映射规则更新前端接口的字段类型
   - 同步添加或删除字段
   - 更新字段注释，特别是数据字典相关说明
   - 确保所有前端接口属性都带有可选标记

3. **验证一致性**
   - 确认前端接口已完全匹配后端实体类的结构
   - 确认所有类型映射正确
   - 确认字段注释已正确更新

