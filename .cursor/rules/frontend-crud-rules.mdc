---
description: 
globs: 
alwaysApply: false
---
# 前端 CRUD 页面开发规范

在创建前端 CRUD 页面时，请严格按照以下步骤执行：

1.  **理解 API 与数据模型**:
    *   在开始开发前，请先阅读和理解相关的 API 和模型文件。
    *   **模型定义**: `admin-web/src/api/business/{功能文件夹}/model/*.ts`
        *   例如: `[entity-param models](mdc:admin-web/src/api/business/entity-param/model)`
        *   了解实体的数据结构、属性类型和关系。
        *   重点关注查询参数模型、请求模型和返回结果模型的定义。
    *   **API 接口**: `admin-web/src/api/business/{功能文件夹}/*-api.ts`
        *   理解可用的 API 方法、参数和返回值。
        *   确认接口路径、方法类型和所需权限。

2.  **参考模板**:
    *   必须阅读以下文件, 记得从第一行看到最后一行:
        *   `[PaperList.vue](mdc:admin-web/src/views/business/erp/paper/PaperList.vue)`
        *   `[PaperDetail.vue](mdc:admin-web/src/views/business/erp/paper/components/PaperDetail.vue)`
        *   `[PaperForm.vue](mdc:admin-web/src/views/business/erp/paper/components/PaperForm.vue)`
        *   `[paperService.ts](mdc:admin-web/src/views/business/erp/paper/service/paperService.ts)`

3.  **页面结构**:
    *   保持列表页、表单页、详情页的结构与现有代码一致。
    *   弹窗、抽屉等交互方式需与现有代码保持一致。

4.  **菜单配置**:
    *   写完代码后，参考 `[.sql/menu/纸张管理.sql.sql](mdc:.sql/menu/纸张管理.sql.sql)`，在 `.sql/menu` 目录添加对应的菜单 SQL。
    *   SQL 模板:
        ```sql
        INSERT INTO t_menu ( menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time)
        VALUES
        (...);
        ```

5.  **代码修复**:
    *   写完代码后，修复代码中所有异常问题 (类型错误、导入问题等)。

