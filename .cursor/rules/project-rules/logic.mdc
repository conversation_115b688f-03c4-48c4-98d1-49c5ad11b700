---
description: 编写业务逻辑代码的最佳实践和规范
globs: src/**/*.{vue,tsx,ts}
alwaysApply: false
---

# 逻辑代码规范

## Context

- 适用于所有业务逻辑代码编写
- 包含代码组织、命名约定和最佳实践
- 帮助保持代码一致性和可维护性

## Critical Rules

- 禁止编写console.log()语句，发现现有console.log()代码要删除
- 先检查auto-imports.d.ts是否有自动导入定义，有则直接使用无需导入
- Lodash方法均以"_"开头，如_get、_map等
- 单个页面代码不超过1000行，合理拆分逻辑与组件
- 编写Vue逻辑代码优先使用VueUse工具库
- 组件库优先使用ant-design-vue v4版本
- 遇到ESLint错误先尝试执行eslint --fix命令修复
- 创建新组件时参照src/components/business/supplier-paper-select/index.vue

## Examples

<example>
// 自动导入示例（无需import）
const { message } = useMessage();
const userInfo = reactive({});
const isEmpty = _isEmpty(userInfo);

// VueUse使用示例
const { x, y } = useMouse();
</example>

<example type="invalid">
// 错误示例
import { message } from 'ant-design-vue';
console.log('调试信息');
import isEmpty from 'lodash/isEmpty';
const isEmpty = isEmpty(userInfo);
</example>