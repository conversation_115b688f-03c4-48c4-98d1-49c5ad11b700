---
description: 前端架构设计规范 - Service层 + 依赖注入模式
globs: ["admin-web/src/**/*.vue", "admin-web/src/**/*.ts"]
alwaysApply: true
---

# 前端架构规范 🏗️

## 核心原则

### 1. 完全分离
- **组件**：只负责视图渲染和依赖注入
- **Service**：负责所有业务逻辑和状态管理
- **禁止**：组件内写业务逻辑，禁止props/emit传递业务状态

### 2. 依赖注入模式
- **父组件**：实例化Service并provide
- **子组件**：只能inject使用Service
- **单向依赖**：服务由父组件提供，子组件注入，形成单向依赖流

### 3. 面向对象设计
- 所有业务Service使用class
- 继承BaseService统一管理
- 支持扩展和重用

## 目录结构
### 业务页面组件 (/views/xxx/)
/views/xxx/
├── Xxx.vue # 顶层组件：只做provide
├── components/
│ ├── Xxx.vue # 子组件：只做inject
├── service/
│ ├── xxxService.ts # 业务Service类
└── model/


### 项目组件库 (/components/xxx/)
/components/xxx/
├── index.vue # 组件入口：提供Service并导出
├── XxxService.ts # 组件Service类
├── types.ts # 组件类型定义
└── components/ # 内部子组件(可选)
├── XxxItem.vue # 内部子组件：inject父Service
└── XxxPanel.vue # 内部子组件：inject父Service

## 代码模板

### Service类
```typescript
export class XxxService extends BaseService<XxxState> {
  // 业务方法
  openDialog() { 
    this.state.visible = true 
  }
  
  closeDialog() { 
    this.state.visible = false 
  }
}
```

### 顶层组件 (只做provide)
```vue
<script setup lang="ts">
// 只负责实例化和provide
const xxxService = new XxxService()
provide('xxxService', xxxService)
</script>

<template>
  <XxxForm />
  <XxxDialog />
</template>
```

### 子组件 (只做inject)
```vue
<script setup lang="ts">
// 只负责inject
const service = inject<XxxService>('xxxService')!
</script>

<template>
  <!-- 直接调用service方法 -->
  <button @click="service.openDialog()">新建</button>
</template>
```

## 严格禁止

1. ❌ setup中写业务逻辑
2. ❌ props/emit传递业务状态
3. ❌ 组件间直接通信
4. ❌ Service间循环依赖
5. ❌ 父依赖子的设计

## 强制要求

1. ✅ setup只做依赖注入
2. ✅ 业务逻辑全在Service
3. ✅ UI事件直接调用Service方法
4. ✅ 单向依赖注入
5. ✅ 使用class + OOP设计