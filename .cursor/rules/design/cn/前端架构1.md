---
description: 前端架构设计规范 - Service层 + 依赖注入模式
globs: ["admin-web/src/**/*.vue", "admin-web/src/**/*.ts"]
alwaysApply: true
---
# 前端架构规范 🏗️

## 核心原则

### 1. 完全分离

- **组件**：只负责视图渲染和依赖注入
- **Service**：负责所有业务逻辑和状态管理
- **禁止**：组件内写业务逻辑，禁止props/emit传递业务状态

### 2. 依赖注入模式

- **父组件**：实例化Service并provide
- **子组件**：只能inject使用Service
- **单向依赖**：服务由父组件提供，子组件注入，形成单向依赖流

### 3. 面向对象设计

- 所有业务Service使用class
- 支持扩展和重用

## 目录结构

### 组件库分层架构 (/components/)

```
/components/
├── base/                    # 基础UI组件层
│   ├── Button/             # 按钮组件
│   ├── Form/               # 表单组件
│   ├── AxTable/            # 表格组件
│   ├── Modal/              # 弹窗组件
│   ├── Input/              # 输入组件
│   ├── Card/               # 卡片组件
│   ├── CustomIcon/         # 图标组件
│   └── ...                 # 其他基础组件
├── utils/                  # 工具组件层
│   ├── Dict/               # 字典组件
│   ├── TableOperator/      # 表格操作组件
│   └── Color/              # 颜色组件
├── business/               # 业务组件层
│   ├── AxSearch/           # 搜索组件
│   │   ├── BrandSearch/
│   │   ├── CustomerSearch/
│   │   └── WarehouseSearch/
│   └── AxTree/             # 树形组件
│       └── AxProductCategoryTree/
└── system/                 # 系统组件层
    ├── department-tree-select/
    ├── employee-select/
    └── menu-tree-select/
```

### 业务页面组件 (/views/xxx/)

```
/views/business/erp/xxx/
├── XxxList.vue             # 顶层组件：只做provide
├── components/             # 页面子组件
│   ├── XxxForm.vue        # 表单组件：只做inject
│   └── XxxDetail.vue      # 详情组件：只做inject
├── service/               # 业务Service层
│   └── XxxService.ts      # 业务Service类
├── columns.ts             # 表格列定义
└── config/                # 配置文件（可选）
    └── xxx-config.ts
```

## 组件分层说明

### 1. 基础组件层 (/components/base/)

- **职责**：提供最基础的UI组件，对ant-design-vue的二次封装
- **特点**：无业务逻辑，高度可复用
- **导入方式**：`import { Button, Form } from '@/components/base/Button'`

### 2. 工具组件层 (/components/utils/)

- **职责**：提供通用的功能性组件
- **特点**：包含一定的逻辑，但不涉及具体业务
- **导入方式**：`import { DictSelect } from '@/components/utils/Dict'`

### 3. 业务组件层 (/components/business/)

- **职责**：封装特定业务逻辑的组件
- **特点**：以Ax开头，包含业务逻辑，可跨页面复用
- **导入方式**：`import CustomerSearch from '@/components/business/AxSearch/CustomerSearch/CustomerSearch.vue'`

### 4. 系统组件层 (/components/system/)

- **职责**：系统级功能组件（用户、权限、部门等）
- **特点**：与系统核心功能相关
- **导入方式**：`import EmployeeSelect from '@/components/system/employee-select/index.vue'`

## 代码模板

### Service类

```typescript
import type { XxxForm, XxxPageParam, XxxResult } from '@/api/business/xxx/model/xxx-types'
import { xxxApi } from '@/api/business/xxx/xxx-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { xxxColumns } from '../columns'

/**
 * Xxx业务服务
 * 提供Xxx相关的业务逻辑和数据管理
 */
export class XxxService extends BaseCrudService<XxxResult, XxxForm, XxxPageParam> {
  constructor() {
    super(
      'Xxx', // 业务名称
      xxxColumns,
      {
        add: xxxApi.addXxx,
        queryPage: xxxApi.xxxPage,
        getDetail: xxxApi.xxxDetail,
        update: xxxApi.updateXxx,
        import: xxxApi.importXxx,
        export: xxxApi.exportXxx,
        delete: xxxApi.deleteXxx,
        batchDelete: xxxApi.batchDeleteXxx,
        downloadTemplate: xxxApi.exportXxx,
      },
    )
  }
  
  // 自定义业务方法
  customMethod() {
    // 业务逻辑
  }
}

// 单例模式
export const xxxService = new XxxService()
```

### 业务页面 - 顶层组件 (只做provide)

```vue
<script lang="ts" setup>
// 导入基础组件
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'

// 导入工具组件
import { DictSelect } from '@/components/utils/Dict'
import { TableOperator } from '@/components/utils/TableOperator'

// 导入业务组件
import CustomerSearch from '@/components/business/AxSearch/CustomerSearch/CustomerSearch.vue'

// 导入页面组件
import XxxForm from './components/XxxForm.vue'
import XxxDetail from './components/XxxDetail.vue'

// 导入服务
import { xxxService } from './service/XxxService'

// 只负责实例化和provide
xxxService.provide()
</script>

<template>
  <!-- 查询表单 -->
  <Form v-privilege="'xxx:query'">
    <FormRow>
      <FormItem label="名称">
        <InputText v-model:value="xxxService.queryParam.name" placeholder="请输入名称" />
      </FormItem>
      <FormItem label="状态">
        <DictSelect v-model:value="xxxService.queryParam.status" key-code="xxx_status" placeholder="请选择状态" />
      </FormItem>
      <FormItem>
        <ButtonGroup>
          <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="xxxService.onSearch" />
          <IconButton label="重置" icon-type="ReloadOutlined" @click="xxxService.resetQuery" />
        </ButtonGroup>
      </FormItem>
    </FormRow>
  </Form>
  
  <!-- 数据表格 -->
  <Card>
    <FlexRow>
      <AxTableOperateButtons 
        add-config="xxx:add" 
        batch-delete-config="xxx:delete" 
        import-config="xxx:import" 
        export-config="xxx:export" 
      />
      <TableOperator
        v-model="xxxService.columns" 
        :table-id="TABLE_ID_CONST.BUSINESS.ERP.XXX"
        :refresh="xxxService.queryPage"
      />
    </FlexRow>
  
    <Table>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'xxx:view'" label="查看" icon-type="EyeOutlined"
              @click="xxxService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'xxx:edit'" label="编辑" icon-type="EditOutlined"
              @click="xxxService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'xxx:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="xxxService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  
  <!-- 子组件 -->
  <XxxForm />
  <XxxDetail />
</template>
```

### 业务页面 - 子组件 (只做inject)

```vue
<script setup lang="ts">
// 导入基础组件
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'

// 导入工具组件
import { DictSelect } from '@/components/utils/Dict'

// 导入服务类型
import type { XxxService } from '../service/XxxService'

// 只负责inject
const service = inject<XxxService>('xxxService')!
</script>

<template>
  <Modal
    v-model:open="service.formState.visible"
    :title="service.formState.title"
    :width="800"
    @ok="service.onSave"
    @cancel="service.closeForm"
  >
    <Spin :spinning="service.formState.loading">
      <Form :model="service.formState.form" :rules="service.formRules">
        <FormRow>
          <FormColumn :span="12">
            <FormItem label="名称" name="name">
              <InputText v-model:value="service.formState.form.name" placeholder="请输入名称" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="状态" name="status">
              <DictSelect v-model:value="service.formState.form.status" key-code="xxx_status" placeholder="请选择状态" />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
```

### 基础组件 - 组件入口

```vue
<script setup lang="ts">
// 基础组件props接收外部配置
interface Props {
  type?: 'primary' | 'secondary' | 'danger'
  size?: 'small' | 'middle' | 'large'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'middle',
  loading: false
})

// 暴露组件方法给外部使用
defineExpose({
  focus: () => {
    // 聚焦逻辑
  }
})
</script>

<template>
  <a-button
    v-bind="$attrs"
    :type="props.type"
    :size="props.size"
    :loading="props.loading"
    :class="['ax-button', `ax-button--${props.type}`]"
  >
    <slot />
  </a-button>
</template>

<style lang="less">
.ax-button {
  // 自定义样式
  
  &--primary {
    // 主要按钮样式
  }
  
  &--secondary {
    // 次要按钮样式
  }
  
  &--danger {
    // 危险按钮样式
  }
}
</style>
```

### 业务组件 - 组件入口

```vue
<script setup lang="ts">
// 业务组件直接使用已有的业务类型，不自定义
interface Props {
  config?: SearchConfig
}
const props = defineProps<Props>()

// 实例化业务组件Service
const axSearchService = new AxSearchService(props.config)
provide('axSearchService', axSearchService)

// 暴露组件方法给外部使用
defineExpose({
  open: () => axSearchService.open(),
  close: () => axSearchService.close(),
  getSelected: () => axSearchService.getSelected()
})
</script>

<template>
  <div class="ax-search-component">
    <SearchContent />
  </div>
</template>
```

## 组件设计原则

### 基础组件 (/components/base/)

基础组件是对ant-design-vue的二次封装

- **对外接口**：通过props接收配置，通过defineExpose暴露方法
- **无业务逻辑**：只处理UI展示和交互
- **高度可复用**：可在任何场景下使用
- **样式统一**：使用BEM命名规范，统一主题

### 工具组件 (/components/utils/)

工具组件提供通用功能

- **功能导向**：解决特定的功能需求
- **配置驱动**：通过配置控制行为
- **无业务依赖**：不依赖具体业务逻辑
- **可组合使用**：可与其他组件组合

### 业务组件 (/components/business/)

业务组件封装特定业务逻辑

- **命名规范**：以Ax开头，表示基于依赖注入的业务组件
- **依赖注入**：创建专用的AxXxxService
- **类型复用**：直接使用已有业务类型，不创建新类型
- **业务封装**：封装特定业务逻辑，可跨页面复用

### 页面组件 (/views/)

页面组件组合各层组件

- **页面级Service**：管理整个页面的状态和逻辑
- **组件组合**：组合多个基础组件、工具组件和业务组件
- **路由绑定**：与路由系统集成
- **权限控制**：集成权限验证

## 导入规范

### 分层导入

```typescript
// 1. 基础组件导入
import { Button, Form, Table } from '@/components/base/Button'
import { Card } from '@/components/base/Card'

// 2. 工具组件导入
import { DictSelect } from '@/components/utils/Dict'
import { TableOperator } from '@/components/utils/TableOperator'

// 3. 业务组件导入
import CustomerSearch from '@/components/business/AxSearch/CustomerSearch/CustomerSearch.vue'

// 4. 系统组件导入
import EmployeeSelect from '@/components/system/employee-select/index.vue'

// 5. 页面组件导入
import XxxForm from './components/XxxForm.vue'

// 6. 服务导入
import { xxxService } from './service/XxxService'
```

## 严格禁止

1. ❌ setup中写业务逻辑
2. ❌ props/emit传递业务状态
3. ❌ 组件间直接通信
4. ❌ Service间循环依赖
5. ❌ 父依赖子的设计
6. ❌ 业务组件创建自定义类型定义

## 强制要求

1. ✅ setup只做依赖注入
2. ✅ 业务逻辑全在Service
3. ✅ UI事件直接调用Service方法
4. ✅ 单向依赖注入
5. ✅ 使用class + OOP设计
6. ✅ 组件以目录形式组织，包含index.vue入口
7. ✅ 业务组件复用已有业务类型
8. ✅ 按分层架构导入组件
9. ✅ 继承BaseCrudService实现标准CRUD功能
