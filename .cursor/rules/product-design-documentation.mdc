---
description: 
globs: 
alwaysApply: false
---
# 产品设计文档规范 📋

## Context
- 适用于TXY-ERP系统的产品设计文档编写
- 专注于功能需求和技术设计
- 统一产品需求和设计规范

## 文档存储规范 📁

### 1. 文档位置
- **存储路径**: `.doc/` 文件夹
- **文件格式**: `.md` (Markdown格式)
- **命名规范**: `功能模块名-产品设计.md`

### 2. 文档结构
```
.doc/
├── 业务模块/
│   ├── 订单管理-产品设计.md
│   ├── 库存管理-产品设计.md
│   └── 客户管理-产品设计.md
├── 系统模块/
│   ├── 用户权限-产品设计.md
│   └── 系统配置-产品设计.md
└── README.md
```

## 产品文档模板 📝

### 1. 标准模板结构
```markdown
# [功能模块名] 产品设计文档

## 1. 产品概述 🎯
### 1.1 功能描述
- 核心功能说明
- 业务价值

### 1.2 使用场景
- 主要使用场景
- 业务流程说明

## 2. 功能需求 📊
### 2.1 核心功能
- [ ] 核心功能1：详细描述
- [ ] 核心功能2：详细描述
- [ ] 核心功能3：详细描述

### 2.2 扩展功能
- [ ] 扩展功能1：详细描述
- [ ] 扩展功能2：详细描述


## 3. 业务流程 🔄
### 3.1 主流程
```mermaid
graph TD
    A[开始] --> B[步骤1]
    B --> C[步骤2]
    C --> D[结束]
```

### 3.2 异常流程
- 异常情况处理
- 错误提示和处理

## 4. 界面设计 🎨
### 4.1 页面结构
- 列表页设计要求
- 表单页设计要求
- 详情页设计要求

### 4.2 交互设计
- 操作流程说明
- 用户体验要求

## 5. 数据模型 🗃️
### 5.1 核心数据表
详见数据表设计文档：
- @主表名
- @关联表名

### 5.2 数据关系
- 主要实体关系说明
- 外键约束说明
- 业务规则约束

## 6. 验收标准 ✅
### 6.1 功能验收
- [ ] 功能点1验收标准
- [ ] 功能点2验收标准
- [ ] 功能点3验收标准


## Critical Rules 💡

### 1. 文档编写规范
- **必须使用中文**: 所有文档内容使用中文编写
- **标题层级**: 使用标准的Markdown标题层级
- **代码块**: 使用语法高亮的代码块
- **表格格式**: 使用标准的Markdown表格格式

### 2. 数据表引用规范
- **禁止重复定义**: 不要在产品文档中重新定义数据表结构
- **引用现有表**: 使用链接引用`.sql/tables/`中的数据表文件
- **说明业务关系**: 重点说明数据表之间的业务关系和约束
- **字段映射**: 提供业务字段与数据库字段的映射关系

### 3. 内容要求
- **功能导向**: 专注于功能需求和技术实现
- **准确性**: 技术细节必须准确无误
- **可读性**: 逻辑清晰，易于理解
- **可实现性**: 确保功能设计可以实现

### 4. 图表规范
```markdown
<!-- 流程图使用mermaid -->
```mermaid
graph TD
    A[开始] --> B[处理]
    B --> C[结束]
```

<!-- 表格使用标准格式 -->
| 列名1 | 列名2 | 列名3 |
|-------|-------|-------|
| 数据1 | 数据2 | 数据3 |
```

## 最佳实践 ⭐

### 1. 功能描述规范
```markdown
## 2. 功能需求 📊
### 2.1 核心功能
- [ ] 用户登录：支持用户名密码登录，包含验证码验证
- [ ] 数据查询：支持多条件组合查询，分页显示结果
- [ ] 数据导出：支持Excel格式导出，包含数据筛选
```

### 2. 数据表引用示例
```markdown
## 5. 数据模型 🗃️
### 5.1 核心数据表
详见数据表设计文档：
- @客户信息表
- @订单主表
- @订单明细表

### 5.2 数据关系
- 客户与订单: 一对多关系，一个客户可以有多个订单
- 订单与订单明细: 一对多关系，一个订单包含多个商品明细
```

### 3. 引用规范
```markdown
<!-- 引用其他文档 -->
详见 @用户管理产品设计

<!-- 引用数据表文件 -->
数据表结构详见 @用户表

<!-- 引用代码文件 -->
参考实现 @UserController.java
```

### 4. 状态标识
```markdown
## 开发状态
- 🟢 已完成
- 🟡 开发中  
- 🔴 待开始
- ⚪ 已取消
```

## 注意事项 ⚠️
1. 文档必须与实际开发保持同步
2. 重大变更需要更新相关文档
3. 新功能开发前必须先完成产品设计文档
4. **数据表变更时，产品文档只需更新引用链接**
5. **禁止在产品文档中维护数据表结构**
6. 接口变更需要通知前后端开发人员
7. 功能需求必须具体明确，避免模糊描述

## 文档审查清单 ✅
1. [ ] 文档结构是否完整
2. [ ] 功能需求是否清晰具体
3. [ ] 业务流程是否合理
4. [ ] 界面设计要求是否明确
5. [ ] **数据表引用是否正确**
6. [ ] **业务关系说明是否清楚**
7. [ ] 验收标准是否明确可执行
