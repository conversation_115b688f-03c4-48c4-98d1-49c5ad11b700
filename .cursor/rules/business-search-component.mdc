---
description: 
globs: 
alwaysApply: false
---
# 前端业务搜索组件开发规范

在创建前端业务搜索组件时，请严格按照以下步骤执行：

## 1. **理解 API 与数据模型** 📋

在开始开发前，请先阅读和理解相关的 API 和模型文件：

- **模型定义**: `src/api/business/{功能文件夹}/model/*.ts`
  - 了解实体的数据结构、属性类型和关系
  - 重点关注查询参数模型、请求模型和返回结果模型的定义
  - 确认表单字段类型和验证规则

- **API 接口**: `src/api/business/{功能文件夹}/*-api.ts`
  - 理解可用的 API 方法、参数和返回值
  - 确认接口路径、方法类型和所需权限

## 2. **参考模板** 🏗️

必须阅读以下 WarehouseSearch 组件文件，记得从第一行看到最后一行：

- `[WarehouseSearch.vue](mdc:admin-web/src/components/business/AxSearch/WarehouseSearch/WarehouseSearch.vue)` - 主组件结构
- `[WarehouseSearchModal.vue](mdc:admin-web/src/components/business/AxSearch/WarehouseSearch/component/WarehouseSearchModal.vue)` - 弹窗表单组件
- `[WarehouseSearchService.ts](mdc:admin-web/src/components/business/AxSearch/WarehouseSearch/service/WarehouseSearchService.ts)` - 业务逻辑服务
- `[rule.ts](mdc:admin-web/src/components/business/AxSearch/WarehouseSearch/component/rule.ts)` - 表单验证规则
- `[index.ts](mdc:admin-web/src/components/business/AxSearch/WarehouseSearch/index.ts)` - 导出入口

## 3. **组件结构规范** 🏛️

### 目录结构
```
components/business/AxSearch/{EntityName}Search/
├── index.ts                        # 导出入口
├── {EntityName}Search.vue          # 主组件
├── service/
│   └── {EntityName}SearchService.ts # 业务服务
└── component/
    ├── {EntityName}SearchModal.vue  # 弹窗表单
    └── rule.ts                      # 表单验证规则
```

### 文件命名规范
- 主组件：`{EntityName}Search.vue`
- 弹窗组件：`component/{EntityName}SearchModal.vue`
- 服务类：`service/{EntityName}SearchService.ts`
- 验证规则：`component/rule.ts`
- 服务常量：`{ENTITY_NAME}_SEARCH_KEY`

## 4. **核心文件模板** 📝

### 主组件结构 (`{EntityName}Search.vue`)
```vue
<script setup lang="ts">
import type { AxSearchParentService } from '@/components/business/AxSearch/type'
import SearchSelect from '@/components/base/SearchSelect/SearchSelect.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { {ENTITY_NAME}_SEARCH_KEY, type {EntityName}SearchService } from './index'
import {EntityName}SearchModal from './component/{EntityName}SearchModal.vue'

// 实体服务
const service = inject<{EntityName}SearchService>({ENTITY_NAME}_SEARCH_KEY)!
// 父级服务
service.parentService = inject<AxSearchParentService>(CRUD_KEY)
</script>

<template>
  <SearchSelect
    :model-value="service?.selectedKey"
    :placeholder="service?.listPlaceholder"
    :allow-add="service?.config.allowAdd"
    :allow-edit="service?.config.allowEdit"
    :allow-delete="service?.config.allowDelete"
    :loading="service?.listLoading"
    :list-data="service?.listData"
    :open-add-form="() => service?.openAddForm?.()"
    :open-edit-form="(value) => service?.openEditForm?.(value.id!)"
    :on-delete="(value) => service?.deleteEntity?.(value.id!)"
    :on-list-select-change="(value) => service?.on{EntityName}Change?.(value)"
    :get-option-value="(value) => service?.getOptionValue?.(value)"
    :get-option-key="(value) => service?.getOptionKey?.(value)"
  />
  <{EntityName}SearchModal />
</template>
```

### 服务类结构 (`service/{EntityName}SearchService.ts`)
```typescript
import type { {EntityName}FormParam, {EntityName}PageParam, {EntityName}Result } from '@/api/business/{module}/model/{entity}-form-model'
import type { AxSearchConfig, AxSearchParentService } from '@/components/business/AxSearch/type'
import { {entity}Api } from '@/api/business/{module}/{entity}-api'
import { BaseCrudService } from '@/service/BaseCrudService'

export const {ENTITY_NAME}_SEARCH_KEY = Symbol('{ENTITY_NAME}_SEARCH_KEY')

/**
 * {实体名称}服务
 * 提供{实体名称}相关的业务逻辑和数据管理
 */
export class {EntityName}SearchService extends BaseCrudService<{EntityName}Result, {EntityName}FormParam, {EntityName}PageParam> {
  config: AxSearchConfig = {
    // 默认配置
    allowAdd: true,
    allowEdit: true,
    allowDelete: true,
  }

  parentService: AxSearchParentService | undefined = undefined

  constructor() {
    // 初始化服务
    super(
      '{实体名称}', // 业务名称
      [],
      {
        // 使用已有的API
        add: {entity}Api.add{EntityName},
        queryPage: {entity}Api.{entity}Page,
        getDetail: {entity}Api.{entity}Detail,
        update: {entity}Api.update{EntityName},
        import: {entity}Api.import{EntityName},
        export: {entity}Api.export{EntityName},
        delete: {entity}Api.delete{EntityName},
        batchDelete: {entity}Api.batchDelete{EntityName},
        downloadTemplate: {entity}Api.export{EntityName},
      },
    )
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide({ENTITY_NAME}_SEARCH_KEY, this)
  }
  
  // ---------------------------- 自身业务逻辑 ----------------------------

  getOptionValue = (item: {EntityName}Result): string | number => {
    return item.{displayField} || ''
  }

  getOptionKey = (item: {EntityName}Result): number | undefined => {
    return item.id || undefined
  }

  // ---------------------------- 混合业务逻辑 ----------------------------
  on{EntityName}Change = (value: unknown, option?: unknown | unknown[]): void => {
    console.log('on{EntityName}Change', value, option)
  }
}
```

### 弹窗表单结构 (`component/{EntityName}SearchModal.vue`)
```vue
<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue/es/form'
import type { {EntityName}SearchService } from '../service/{EntityName}SearchService'
import { CustomDivider } from '@/components/base/CustomDivider'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { inject, ref } from 'vue'
import { {ENTITY_NAME}_SEARCH_KEY } from '../service/{EntityName}SearchService'
import { rules } from './rule'

// 使用辅助函数注入表单服务
const {entity}FormService = inject({ENTITY_NAME}_SEARCH_KEY) as {EntityName}SearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Modal 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await {entity}FormService.submitFormAndRefresh()
  {entity}FormService.resetListQuery()
}
</script>

<template>
  <Modal
    :title="{entity}FormService.formTitle"
    :open="{entity}FormService.formOpen"
    :confirm-loading="{entity}FormService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="{entity}FormService.closeForm()"
  >
    <Spin :spinning="{entity}FormService.formLoading">
      <Form
        ref="formRef"
        :model="{entity}FormService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="字段名称" name="fieldName">
              <InputText v-model:value="{entity}FormService.formData.fieldName" placeholder="请输入字段名称" />
            </FormItem>
          </FormColumn>
          <!-- 更多表单字段... -->
        </FormRow>

        <!-- 分组信息 -->
        <CustomDivider label="分组信息" />
        
        <!-- 更多表单内容... -->
      </Form>
    </Spin>
  </Modal>
</template>
```

### 导出入口文件 (`index.ts`)
```typescript
import { {ENTITY_NAME}_SEARCH_KEY, {EntityName}SearchService } from './service/{EntityName}SearchService'
import {EntityName}Search from './{EntityName}Search.vue'

export { {ENTITY_NAME}_SEARCH_KEY, {EntityName}Search, {EntityName}SearchService }
```

## 5. **样式规范** 🎨

### 使用 TailwindCSS
- **禁止使用 Less/SCSS**，统一使用 TailwindCSS
- 使用 Tailwind 的响应式类名：`sm:`, `md:`, `lg:`, `xl:`
- 使用 Tailwind 的间距系统：`p-4`, `m-2`, `gap-4`
- 使用 Tailwind 的颜色系统：`text-gray-600`, `bg-blue-500`

### 常用样式类
```html
<!-- 表单布局 -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
  <div class="space-y-2">
    <label class="text-sm font-medium text-gray-700">标签</label>
    <input class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
  </div>
</div>

<!-- 按钮组 -->
<div class="flex justify-end space-x-2">
  <button class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200">取消</button>
  <button class="px-4 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600">确定</button>
</div>
```

## 6. **表单验证规则** ✅

在 `component/rule.ts` 中定义验证规则：
```typescript
export const rules = {
  // 基本字段验证
  {codeField}: [
    { required: true, message: '请输入{字段名称}编码' },
    { max: 50, message: '{字段名称}编码最多50个字符' },
  ],
  {nameField}: [
    { required: true, message: '请输入{字段名称}名称' },
    { max: 100, message: '{字段名称}名称最多100个字符' },
  ],
  {typeField}: [
    { required: true, message: '请选择{字段名称}类型' },
  ],
  
  // 地址信息验证
  province: [
    { max: 50, message: '省份最多50个字符' },
  ],
  city: [
    { max: 50, message: '城市最多50个字符' },
  ],
  district: [
    { max: 50, message: '区县最多50个字符' },
  ],
  address: [
    { max: 200, message: '详细地址最多200个字符' },
  ],
  postalCode: [
    { max: 20, message: '邮政编码最多20个字符' },
    { pattern: /^\d{6}$/, message: '邮政编码格式不正确' },
  ],
  
  // 联系信息验证
  contactPerson: [
    { max: 50, message: '联系人最多50个字符' },
  ],
  contactPhone: [
    { max: 20, message: '联系电话最多20个字符' },
    { pattern: /^[0-9-()（）\s]*$/, message: '联系电话格式不正确' },
  ],
  contactMobile: [
    { max: 20, message: '联系手机最多20个字符' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
  ],
  contactEmail: [
    { max: 100, message: '联系邮箱最多100个字符' },
    { type: 'email', message: '邮箱格式不正确' },
  ],
  
  // 数值类型验证
  area: [
    { type: 'number', min: 0, message: '面积不能小于0' },
  ],
  capacity: [
    { type: 'number', min: 0, message: '容量不能小于0' },
  ],
  
  // 温湿度验证
  minTemperature: [
    { type: 'number', message: '最低温度必须为数字' },
  ],
  maxTemperature: [
    { type: 'number', message: '最高温度必须为数字' },
  ],
  minHumidity: [
    { type: 'number', min: 0, max: 100, message: '最低湿度范围为0-100' },
  ],
  maxHumidity: [
    { type: 'number', min: 0, max: 100, message: '最高湿度范围为0-100' },
  ],
  
  // 层级和排序验证
  {levelField}: [
    { type: 'number', min: 1, max: 10, message: '层级范围为1-10' },
  ],
  sortOrder: [
    { type: 'number', min: 0, message: '排序不能小于0' },
  ],
  
  // 状态验证
  status: [
    { required: true, message: '请选择状态' },
  ],
  
  // 备注验证
  remark: [
    { max: 500, message: '备注最多500个字符' },
  ],
}
```

## 7. **开发注意事项** ⚠️

### 忽略 Lint 问题
- 开发过程中可以忽略 ESLint 警告
- 专注于功能实现和业务逻辑

### 组件特性
- 组件必须支持依赖注入 (`provide/inject`)
- 必须继承 `BaseCrudService` 基础服务
- 支持增删改查和批量操作
- 支持表单验证和数据格式化

### 不需要的功能
- **无需 SQL 注入**：组件不涉及数据库操作
- **无需路由配置**：组件内嵌使用
- **无需菜单配置**：组件级别无需菜单

## 8. **代码质量保证** 🔧

开发完成后，确保：
1. [ ] 组件结构符合模板规范
2. [ ] 服务类正确继承基础服务
3. [ ] 表单验证规则完整
4. [ ] 样式使用 TailwindCSS
5. [ ] 依赖注入正确配置
6. [ ] API 调用正常工作
7. [ ] 组件可以正常导入使用

## 9. **使用示例** 💡

```vue
<script setup lang="ts">
import { {EntityName}Search, {EntityName}SearchService } from '@/components/business/AxSearch/{EntityName}Search'

// 创建服务实例
const {entity}SearchService = new {EntityName}SearchService()
{entity}SearchService.provide()
</script>

<template>
  <{EntityName}Search />
</template>
```

---

这个规范确保了组件的一致性和可维护性，让开发者能够快速创建符合项目标准的业务搜索组件 🚀 