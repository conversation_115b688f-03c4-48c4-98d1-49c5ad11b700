---
description: 
globs: 
alwaysApply: false
---

让你写SQL或者编辑SQL的时候，都要遵循以下PostgreSQL规范：每一条都要遵守

0.设计之前阅读.sql\tables\table-relation.md 了解已设计的表关系,如果有必要阅读可能关联的完整表信息,用搜索工具搜 table_name.sql
1.基本规范：
	 - sql文件第一行描述表的功能作用
   - 表的前缀统一使用erp_,使用下划线命名法，如erp_supplier
   - 表的备注不要写"表"字，例如应写"纸箱基础信息"而非"纸箱基础信息表"
   - 建表前加上DROP TABLE IF EXISTS
   - 建表完加上最简单的索引确保基础性能
   - 在每个字段定义右侧添加`-- 字段中文含义`形式的注释，方便阅读
   
2. 字段规范：
   - 所有表必须包含以下字段：
     `created_by` bigint DEFAULT NULL, -- 创建人
     `updated_by` bigint DEFAULT NULL, -- 更新人
     `deleted_flag` BOOLEAN DEFAULT 0, -- 删除标志：0-未删除，1-已删除
     `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
     `update_time` TIMESTAMP DEFAULT NULL, -- 更新时间
   - 主键一般命名为id，使用BIGSERIAL类型
   - 数据字典类字段固定VARCHAR(20)
   - 数据库正式注释使用COMMENT ON语法单独添加
   - 数据字典类字段正式注释格式为：'字典 | 字段中文名 字段名：值1-含义1，值2-含义2'
   - 只有0和1的字段不要用字典，使用BOOLEAN类型
   - 如果数据库的字段包含其他表的虚拟外键，正式注释格式为：'字段中文名 | 关联表名的id' 
				- 所有id的数据类型都是bigint

3. 建完表要插入字典
   - 字典的语句如下: 
      DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = '');
      DELETE FROM t_dict_key WHERE key_code = '';
      INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('', '', '');
      INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES ((SELECT dict_key_id FROM t_dict_key WHERE key_code = ''), '', '', '', '', 0);
   - 字典定义应写在xxx_dict.sql文件中

4. 文件组织规范：
   - SQL文件应放在.sql\tables目录下
   - 按功能模块分文件夹存放，如.sql\tables\商品\erp-xxx_xxx.sql
   - 每个表一个.sql文件
   - 示例数据应单独存放在 表名_data.sql文件中

5.补充表关联关系文件
   - 写完表要在.sql\tables\table-relation.md按照如下格式添加关联关系:
      # 字典
      erp_paper[纸张基础信息]: id[PK], code, paper_category, paper_type, corrugated_shape; erp_paper_quotation[纸张报价]: id[PK], quotation_no, supplier_id[->txy_supplier.supplier_id], quotation_date, status; erp_paper_quotation_item[报价详情]: id[PK], quotation_id[->erp_paper_quotation.id], paper_id[->erp_paper.id], unit_price

6.PostgreSQL表结构示例：
```sql
-- 表创建
DROP TABLE IF EXISTS erp_equipment_demo;
CREATE TABLE erp_equipment_demo (
  id BIGSERIAL PRIMARY KEY,  -- 设备ID
  equipment_code VARCHAR(50) NOT NULL,  -- 设备编码
  equipment_type VARCHAR(20) DEFAULT NULL,  -- 设备类型
  created_by bigint DEFAULT NULL,  -- 创建人
  updated_by bigint DEFAULT NULL,  -- 更新人
  deleted_flag BOOLEAN DEFAULT 0,  -- 删除标志：0-未删除，1-已删除
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  update_time TIMESTAMP DEFAULT NULL,  -- 更新时间
  CONSTRAINT uk_equipment_code UNIQUE (equipment_code)  -- 设备编码唯一约束
);

-- 索引创建
CREATE INDEX idx_equipment_type ON erp_equipment_demo (equipment_type);  -- 设备类型索引

-- 正式注释添加
COMMENT ON TABLE erp_equipment_demo IS '设备演示信息';
COMMENT ON COLUMN erp_equipment_demo.id IS '设备ID';
COMMENT ON COLUMN erp_equipment_demo.equipment_code IS '设备编码';
COMMENT ON COLUMN erp_equipment_demo.equipment_type IS '字典 | 设备类型 equipment_type：production-生产设备，inspection-检测设备，office-办公设备';
COMMENT ON COLUMN erp_equipment_demo.created_by IS '创建人';
COMMENT ON COLUMN erp_equipment_demo.updated_by IS '更新人';
COMMENT ON COLUMN erp_equipment_demo.deleted_flag IS '删除标志：0-未删除，1-已删除';
COMMENT ON COLUMN erp_equipment_demo.create_time IS '创建时间';
COMMENT ON COLUMN erp_equipment_demo.update_time IS '更新时间';
```

7.加点示例数据,放在同目录的/example下