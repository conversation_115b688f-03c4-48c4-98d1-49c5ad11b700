# TXY-ERP Docker部署文件 - 兼容老配置的优化版本
FROM eclipse-temurin:17-jdk-focal

MAINTAINER Klvin

# 创建应用目录
RUN mkdir -p /erp
RUN mkdir -p /home/<USER>/txy-erp

# 设置时区（保持老配置）
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone

# 设置工作目录
WORKDIR /erp

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=prod
ENV PROJECT_LOG_DIR=/home/<USER>

# 暴露端口（兼容老配置的8080和新配置的10245）
EXPOSE 8080
EXPOSE 10245

# 复制JAR包（兼容Gradle构建路径）
ADD ./ax-admin/build/libs/ax-admin-3.0.0.jar ./app.jar

# 启动命令（优化JVM参数）
ENTRYPOINT ["java", "-server", "-Xms600m", "-Xmx600m", "-XX:+UseG1GC", "-XX:+UseStringDeduplication", "-jar", "app.jar"]
