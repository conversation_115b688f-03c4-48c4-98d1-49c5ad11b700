#!/bin/bash

# TXY-ERP 生产环境部署脚本（包含P6Spy SQL监控）

echo "🚀 开始部署 TXY-ERP 生产环境..."

# 检查必要的目录
echo "📁 创建必要的目录..."
mkdir -p logs/sql
mkdir -p upload

# 构建应用
echo "🔨 构建应用..."
cd ..
./gradlew clean bootJar -x test

# 检查构建结果
if [ ! -f "ax-admin/build/libs/ax-admin-3.0.0.jar" ]; then
    echo "❌ 构建失败，JAR文件不存在"
    exit 1
fi

echo "✅ 构建成功"

# 返回docker目录
cd .docker

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 构建并启动容器
echo "🐳 构建并启动Docker容器..."
ACTIVE=prod docker-compose up -d --build

# 等待容器启动
echo "⏳ 等待应用启动..."
sleep 30

# 检查容器状态
echo "🔍 检查容器状态..."
docker-compose ps

# 检查应用健康状态
echo "🏥 检查应用健康状态..."
for i in {1..10}; do
    if curl -f http://localhost:10245/actuator/health > /dev/null 2>&1; then
        echo "✅ 应用启动成功！"
        echo "🌐 访问地址: http://localhost:10245/"
        echo "📊 SQL监控日志: ./logs/sql/spy.log"
        break
    else
        echo "⏳ 等待应用启动... ($i/10)"
        sleep 10
    fi
    
    if [ $i -eq 10 ]; then
        echo "❌ 应用启动失败，请检查日志"
        docker-compose logs --tail=50
        exit 1
    fi
done

echo "🎉 部署完成！"
echo ""
echo "📋 部署信息:"
echo "   - 应用地址: http://localhost:10245/"
echo "   - 环境: 生产环境 (prod)"
echo "   - SQL监控: 已启用 (P6Spy)"
echo "   - 日志目录: ./logs/"
echo "   - 上传目录: ./upload/"
echo ""
echo "📝 常用命令:"
echo "   - 查看日志: docker-compose logs -f"
echo "   - 查看SQL日志: tail -f logs/sql/spy.log"
echo "   - 停止服务: docker-compose down"
echo "   - 重启服务: docker-compose restart" 