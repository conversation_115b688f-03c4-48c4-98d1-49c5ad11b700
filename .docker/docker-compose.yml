# TXY-ERP Docker Compose配置 - 兼容老配置的优化版本
version: "3.8"

services:
  erp-api:
    container_name: erp-api
    network_mode: "host"  # 保持老配置的host网络模式
    restart: always
    build:
      context: ..  # 构建上下文指向项目根目录
      dockerfile: .docker/Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=0
    image: erp-api:latest
    
    # 环境变量配置
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=${ACTIVE:-prod}  # 兼容老的ACTIVE变量，默认prod
      - PROJECT_LOG_DIR=/home/<USER>
      # Host网络模式下直接连接本地服务（使用P6Spy监控）
      - SPRING_DATASOURCE_URL=******************************************  # 生产环境也使用P6Spy
      - SPRING_DATA_REDIS_HOST=localhost
      - SPRING_DATA_REDIS_PORT=6379
    
    # 卷挂载配置
    volumes:
      - ./logs:/home/<USER>/txy-erp  # 保持老配置的日志路径
      - ./upload:/home/<USER>/upload  # 文件上传目录
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:10245/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

