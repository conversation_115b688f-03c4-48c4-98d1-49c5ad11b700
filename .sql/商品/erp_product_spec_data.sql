-- 清空已有数据
DELETE FROM erp_product_spec_relation WHERE deleted_flag = false;
DELETE FROM erp_sku_spec_relation WHERE deleted_flag = false;

-- 获取颜色规格ID
-- 确保变量存在，避免出现NULL导致后续插入失败
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '颜色' LIMIT 1), 0) INTO @color_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '尺寸' LIMIT 1), 0) INTO @size_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '内存' LIMIT 1), 0) INTO @memory_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '存储容量' LIMIT 1), 0) INTO @storage_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '处理器' LIMIT 1), 0) INTO @cpu_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '尺码' LIMIT 1), 0) INTO @cloth_size_id;
SELECT IFNULL((SELECT id FROM erp_spec WHERE name = '材质' LIMIT 1), 0) INTO @material_id;

-- 检查是否所有规格都存在
SET @missing_spec = 0;
SET @missing_spec = @missing_spec + IF(@color_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@size_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@memory_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@storage_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@cpu_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@cloth_size_id = 0, 1, 0);
SET @missing_spec = @missing_spec + IF(@material_id = 0, 1, 0);

-- 如果有缺失规格，则显示警告并退出
-- 注意：MySQL不支持IF语句，所以使用条件表达式
SELECT CASE 
  WHEN @missing_spec > 0 THEN '警告：某些规格不存在，请先确保erp_spec表中存在所有规格再运行此脚本'
  ELSE '所有规格存在，继续执行'
END AS message;

-- 如果没有缺失规格，则继续执行
-- 在MySQL中，不能用IF条件控制脚本执行，以下代码会无论如何都执行
-- 但在实际环境中可以通过程序控制是否继续执行

-- 颜色规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '深空黑' LIMIT 1), 0) INTO @black_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '银色' LIMIT 1), 0) INTO @silver_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '金色' LIMIT 1), 0) INTO @gold_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '午夜黑' LIMIT 1), 0) INTO @midnight_black_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '奶油白' LIMIT 1), 0) INTO @cream_white_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '原木色' LIMIT 1), 0) INTO @wood_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '白色' LIMIT 1), 0) INTO @white_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '黑红' LIMIT 1), 0) INTO @black_red_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @color_id AND value = '白蓝' LIMIT 1), 0) INTO @white_blue_id;

-- 内存规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '128GB' LIMIT 1), 0) INTO @mem_128gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '256GB' LIMIT 1), 0) INTO @mem_256gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '512GB' LIMIT 1), 0) INTO @mem_512gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '8GB' LIMIT 1), 0) INTO @mem_8gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '16GB' LIMIT 1), 0) INTO @mem_16gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @memory_id AND value = '32GB' LIMIT 1), 0) INTO @mem_32gb_id;

-- 存储容量规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @storage_id AND value = '256GB' LIMIT 1), 0) INTO @storage_256gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @storage_id AND value = '512GB' LIMIT 1), 0) INTO @storage_512gb_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @storage_id AND value = '1TB' LIMIT 1), 0) INTO @storage_1tb_id;

-- 处理器规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cpu_id AND value = 'M2' LIMIT 1), 0) INTO @m2_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cpu_id AND value = 'M2 Pro' LIMIT 1), 0) INTO @m2_pro_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cpu_id AND value = 'M2 Max' LIMIT 1), 0) INTO @m2_max_id;

-- 尺寸规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @size_id AND value = '13英寸' LIMIT 1), 0) INTO @size_13_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @size_id AND value = '14英寸' LIMIT 1), 0) INTO @size_14_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @size_id AND value = '16英寸' LIMIT 1), 0) INTO @size_16_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @size_id AND value = '120*60cm' LIMIT 1), 0) INTO @size_120_60_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @size_id AND value = '140*70cm' LIMIT 1), 0) INTO @size_140_70_id;

-- 服装尺码规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = 'S' LIMIT 1), 0) INTO @size_s_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = 'M' LIMIT 1), 0) INTO @size_m_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = 'L' LIMIT 1), 0) INTO @size_l_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = 'XL' LIMIT 1), 0) INTO @size_xl_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = '40' LIMIT 1), 0) INTO @size_40_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = '41' LIMIT 1), 0) INTO @size_41_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = '42' LIMIT 1), 0) INTO @size_42_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = '43' LIMIT 1), 0) INTO @size_43_id;
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @cloth_size_id AND value = '44' LIMIT 1), 0) INTO @size_44_id;

-- 材质规格值
SELECT IFNULL((SELECT id FROM erp_spec_value WHERE spec_id = @material_id AND value = '实木' LIMIT 1), 0) INTO @wood_material_id;

-- 1. 产品规格关联
-- iPhone 14 Pro规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(1, @color_id, 1, 1, 1, 1),
(1, @memory_id, 1, 2, 1, 1);

-- MacBook Pro规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(2, @size_id, 1, 1, 1, 1),
(2, @cpu_id, 1, 2, 1, 1),
(2, @memory_id, 1, 3, 1, 1),
(2, @storage_id, 1, 4, 1, 1);

-- Galaxy S23规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(3, @color_id, 1, 1, 1, 1),
(3, @memory_id, 1, 2, 1, 1);

-- 办公桌规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(8, @color_id, 1, 1, 1, 1),
(8, @size_id, 1, 2, 1, 1),
(8, @material_id, 0, 3, 1, 1);

-- 人棉T恤规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(9, @color_id, 1, 1, 1, 1),
(9, @cloth_size_id, 1, 2, 1, 1);

-- Air Jordan规格
INSERT INTO erp_product_spec_relation (product_id, spec_id, required_flag, sort, created_by, updated_by) VALUES
(10, @color_id, 1, 1, 1, 1),
(10, @cloth_size_id, 1, 2, 1, 1);

-- 2. SKU规格关联
-- iPhone 14 Pro SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, created_by, updated_by) VALUES
-- SKU001: 深空黑,128GB
(1, 'SKU001', @color_id, '颜色', @black_id, '深空黑', 1, 1),
(1, 'SKU001', @memory_id, '内存', @mem_128gb_id, '128GB', 1, 1),
-- SKU002: 深空黑,256GB
(2, 'SKU002', @color_id, '颜色', @black_id, '深空黑', 1, 1),
(2, 'SKU002', @memory_id, '内存', @mem_256gb_id, '256GB', 1, 1),
-- SKU003: 深空黑,512GB
(3, 'SKU003', @color_id, '颜色', @black_id, '深空黑', 1, 1),
(3, 'SKU003', @memory_id, '内存', @mem_512gb_id, '512GB', 1, 1),
-- SKU004: 银色,128GB
(4, 'SKU004', @color_id, '颜色', @silver_id, '银色', 1, 1),
(4, 'SKU004', @memory_id, '内存', @mem_128gb_id, '128GB', 1, 1),
-- SKU005: 银色,256GB
(5, 'SKU005', @color_id, '颜色', @silver_id, '银色', 1, 1),
(5, 'SKU005', @memory_id, '内存', @mem_256gb_id, '256GB', 1, 1),
-- SKU006: 银色,512GB
(6, 'SKU006', @color_id, '颜色', @silver_id, '银色', 1, 1),
(6, 'SKU006', @memory_id, '内存', @mem_512gb_id, '512GB', 1, 1),
-- SKU007: 金色,128GB
(7, 'SKU007', @color_id, '颜色', @gold_id, '金色', 1, 1),
(7, 'SKU007', @memory_id, '内存', @mem_128gb_id, '128GB', 1, 1),
-- SKU008: 金色,256GB
(8, 'SKU008', @color_id, '颜色', @gold_id, '金色', 1, 1),
(8, 'SKU008', @memory_id, '内存', @mem_256gb_id, '256GB', 1, 1),
-- SKU009: 金色,512GB
(9, 'SKU009', @color_id, '颜色', @gold_id, '金色', 1, 1),
(9, 'SKU009', @memory_id, '内存', @mem_512gb_id, '512GB', 1, 1);

-- MacBook Pro SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, created_by, updated_by) VALUES
-- SKU010: 尺寸:13英寸,处理器:M2,内存:8GB,硬盘:256GB
(10, 'SKU010', @size_id, '尺寸', @size_13_id, '13英寸', 1, 1),
(10, 'SKU010', @cpu_id, '处理器', @m2_id, 'M2', 1, 1),
(10, 'SKU010', @memory_id, '内存', @mem_8gb_id, '8GB', 1, 1),
(10, 'SKU010', @storage_id, '存储容量', @storage_256gb_id, '256GB', 1, 1),
-- SKU011: 尺寸:13英寸,处理器:M2,内存:8GB,硬盘:512GB
(11, 'SKU011', @size_id, '尺寸', @size_13_id, '13英寸', 1, 1),
(11, 'SKU011', @cpu_id, '处理器', @m2_id, 'M2', 1, 1),
(11, 'SKU011', @memory_id, '内存', @mem_8gb_id, '8GB', 1, 1),
(11, 'SKU011', @storage_id, '存储容量', @storage_512gb_id, '512GB', 1, 1),
-- SKU012: 尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:512GB
(12, 'SKU012', @size_id, '尺寸', @size_14_id, '14英寸', 1, 1),
(12, 'SKU012', @cpu_id, '处理器', @m2_pro_id, 'M2 Pro', 1, 1),
(12, 'SKU012', @memory_id, '内存', @mem_16gb_id, '16GB', 1, 1),
(12, 'SKU012', @storage_id, '存储容量', @storage_512gb_id, '512GB', 1, 1),
-- SKU013: 尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:1TB
(13, 'SKU013', @size_id, '尺寸', @size_14_id, '14英寸', 1, 1),
(13, 'SKU013', @cpu_id, '处理器', @m2_pro_id, 'M2 Pro', 1, 1),
(13, 'SKU013', @memory_id, '内存', @mem_16gb_id, '16GB', 1, 1),
(13, 'SKU013', @storage_id, '存储容量', @storage_1tb_id, '1TB', 1, 1),
-- SKU014: 尺寸:16英寸,处理器:M2 Max,内存:32GB,硬盘:1TB
(14, 'SKU014', @size_id, '尺寸', @size_16_id, '16英寸', 1, 1),
(14, 'SKU014', @cpu_id, '处理器', @m2_max_id, 'M2 Max', 1, 1),
(14, 'SKU014', @memory_id, '内存', @mem_32gb_id, '32GB', 1, 1),
(14, 'SKU014', @storage_id, '存储容量', @storage_1tb_id, '1TB', 1, 1);

-- Galaxy S23 SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, created_by, updated_by) VALUES
-- SKU015: 颜色:午夜黑,内存:128GB
(15, 'SKU015', @color_id, '颜色', @midnight_black_id, '午夜黑', 1, 1),
(15, 'SKU015', @memory_id, '内存', @mem_128gb_id, '128GB', 1, 1),
-- SKU016: 颜色:午夜黑,内存:256GB
(16, 'SKU016', @color_id, '颜色', @midnight_black_id, '午夜黑', 1, 1),
(16, 'SKU016', @memory_id, '内存', @mem_256gb_id, '256GB', 1, 1),
-- SKU017: 颜色:奶油白,内存:128GB
(17, 'SKU017', @color_id, '颜色', @cream_white_id, '奶油白', 1, 1),
(17, 'SKU017', @memory_id, '内存', @mem_128gb_id, '128GB', 1, 1),
-- SKU018: 颜色:奶油白,内存:256GB
(18, 'SKU018', @color_id, '颜色', @cream_white_id, '奶油白', 1, 1),
(18, 'SKU018', @memory_id, '内存', @mem_256gb_id, '256GB', 1, 1);

-- 办公桌 SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, custom_value, created_by, updated_by) VALUES
-- SKU019: 颜色:原木色,尺寸:120*60cm
(19, 'SKU019', @color_id, '颜色', @wood_id, '原木色', NULL, 1, 1),
(19, 'SKU019', @size_id, '尺寸', @size_120_60_id, '120*60cm', NULL, 1, 1),
(19, 'SKU019', @material_id, '材质', @wood_material_id, '实木', NULL, 1, 1),
-- SKU020: 颜色:原木色,尺寸:140*70cm
(20, 'SKU020', @color_id, '颜色', @wood_id, '原木色', NULL, 1, 1),
(20, 'SKU020', @size_id, '尺寸', @size_140_70_id, '140*70cm', NULL, 1, 1),
(20, 'SKU020', @material_id, '材质', @wood_material_id, '实木', NULL, 1, 1),
-- SKU021: 颜色:白色,尺寸:120*60cm
(21, 'SKU021', @color_id, '颜色', @white_id, '白色', NULL, 1, 1),
(21, 'SKU021', @size_id, '尺寸', @size_120_60_id, '120*60cm', NULL, 1, 1),
(21, 'SKU021', @material_id, '材质', @wood_material_id, '实木', NULL, 1, 1),
-- SKU022: 颜色:白色,尺寸:140*70cm
(22, 'SKU022', @color_id, '颜色', @white_id, '白色', NULL, 1, 1),
(22, 'SKU022', @size_id, '尺寸', @size_140_70_id, '140*70cm', NULL, 1, 1),
(22, 'SKU022', @material_id, '材质', @wood_material_id, '实木', NULL, 1, 1);

-- 人棉T恤 SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, created_by, updated_by) VALUES
-- SKU023-026: 白色，尺码S-XL
(23, 'SKU023', @color_id, '颜色', @white_id, '白色', 1, 1),
(23, 'SKU023', @cloth_size_id, '尺码', @size_s_id, 'S', 1, 1),
(24, 'SKU024', @color_id, '颜色', @white_id, '白色', 1, 1),
(24, 'SKU024', @cloth_size_id, '尺码', @size_m_id, 'M', 1, 1),
(25, 'SKU025', @color_id, '颜色', @white_id, '白色', 1, 1),
(25, 'SKU025', @cloth_size_id, '尺码', @size_l_id, 'L', 1, 1),
(26, 'SKU026', @color_id, '颜色', @white_id, '白色', 1, 1),
(26, 'SKU026', @cloth_size_id, '尺码', @size_xl_id, 'XL', 1, 1),
-- SKU027-030: 黑色，尺码S-XL
(27, 'SKU027', @color_id, '颜色', @black_id, '黑色', 1, 1),
(27, 'SKU027', @cloth_size_id, '尺码', @size_s_id, 'S', 1, 1),
(28, 'SKU028', @color_id, '颜色', @black_id, '黑色', 1, 1),
(28, 'SKU028', @cloth_size_id, '尺码', @size_m_id, 'M', 1, 1),
(29, 'SKU029', @color_id, '颜色', @black_id, '黑色', 1, 1),
(29, 'SKU029', @cloth_size_id, '尺码', @size_l_id, 'L', 1, 1),
(30, 'SKU030', @color_id, '颜色', @black_id, '黑色', 1, 1),
(30, 'SKU030', @cloth_size_id, '尺码', @size_xl_id, 'XL', 1, 1);

-- Air Jordan SKU规格
INSERT INTO erp_sku_spec_relation (sku_id, sku_code, spec_id, spec_name, spec_value_id, spec_value_text, created_by, updated_by) VALUES
-- 黑红配色，尺码40-44
(31, 'SKU031', @color_id, '颜色', @black_red_id, '黑红', 1, 1),
(31, 'SKU031', @cloth_size_id, '尺码', @size_40_id, '40', 1, 1),
(32, 'SKU032', @color_id, '颜色', @black_red_id, '黑红', 1, 1),
(32, 'SKU032', @cloth_size_id, '尺码', @size_41_id, '41', 1, 1),
(33, 'SKU033', @color_id, '颜色', @black_red_id, '黑红', 1, 1),
(33, 'SKU033', @cloth_size_id, '尺码', @size_42_id, '42', 1, 1),
(34, 'SKU034', @color_id, '颜色', @black_red_id, '黑红', 1, 1),
(34, 'SKU034', @cloth_size_id, '尺码', @size_43_id, '43', 1, 1),
(35, 'SKU035', @color_id, '颜色', @black_red_id, '黑红', 1, 1),
(35, 'SKU035', @cloth_size_id, '尺码', @size_44_id, '44', 1, 1),
-- 白蓝配色，尺码40-44
(36, 'SKU036', @color_id, '颜色', @white_blue_id, '白蓝', 1, 1),
(36, 'SKU036', @cloth_size_id, '尺码', @size_40_id, '40', 1, 1),
(37, 'SKU037', @color_id, '颜色', @white_blue_id, '白蓝', 1, 1),
(37, 'SKU037', @cloth_size_id, '尺码', @size_41_id, '41', 1, 1),
(38, 'SKU038', @color_id, '颜色', @white_blue_id, '白蓝', 1, 1),
(38, 'SKU038', @cloth_size_id, '尺码', @size_42_id, '42', 1, 1),
(39, 'SKU039', @color_id, '颜色', @white_blue_id, '白蓝', 1, 1),
(39, 'SKU039', @cloth_size_id, '尺码', @size_43_id, '43', 1, 1),
(40, 'SKU040', @color_id, '颜色', @white_blue_id, '白蓝', 1, 1),
(40, 'SKU040', @cloth_size_id, '尺码', @size_44_id, '44', 1, 1); 