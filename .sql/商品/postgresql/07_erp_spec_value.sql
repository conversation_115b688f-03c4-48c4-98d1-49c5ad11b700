-- 6. 规格值表
DROP TABLE IF EXISTS erp_spec_value CASCADE;
CREATE TABLE erp_spec_value
(
    id               BIGSERIAL PRIMARY KEY,
    spec_id          BIGINT NOT NULL,
    value            VARCHAR(100) NOT NULL,
    sort             INTEGER DEFAULT 0 NOT NULL,
    status           BO<PERSON><PERSON>N DEFAULT true NOT NULL,
    deleted_flag     BOOLEAN DEFAULT false NOT NULL,
    create_time      TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time      TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by       BIGINT,
    updated_by       BIGINT
);

COMMENT ON TABLE erp_spec_value IS '商品规格值';
COMMENT ON COLUMN erp_spec_value.id IS '规格值ID';
COMMENT ON COLUMN erp_spec_value.spec_id IS '规格ID | erp_spec的id';
COMMENT ON COLUMN erp_spec_value.value IS '规格值';
COMMENT ON COLUMN erp_spec_value.sort IS '排序';
COMMENT ON COLUMN erp_spec_value.status IS '规格值状态：false-禁用，true-启用';
COMMENT ON COLUMN erp_spec_value.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_spec_value.create_time IS '创建时间';
COMMENT ON COLUMN erp_spec_value.update_time IS '更新时间';
COMMENT ON COLUMN erp_spec_value.created_by IS '创建人';
COMMENT ON COLUMN erp_spec_value.updated_by IS '更新人';

CREATE INDEX idx_erp_spec_value_spec ON erp_spec_value(spec_id);
-- 可以考虑增加唯一索引确保同一规格下值不重复
-- CREATE UNIQUE INDEX idx_erp_spec_value_uk_spec_value ON erp_spec_value(spec_id, value);

-- 创建触发器以更新 update_time (确保 trigger_set_timestamp 函数已存在)
CREATE TRIGGER set_timestamp_erp_spec_value
BEFORE UPDATE ON erp_spec_value
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp(); 