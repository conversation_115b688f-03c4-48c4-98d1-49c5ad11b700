-- 清空已有数据
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'product_status');
DELETE FROM t_dict_key WHERE key_code = 'product_status';

DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'source_type');
DELETE FROM t_dict_key WHERE key_code = 'source_type';

DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'sku_status');
DELETE FROM t_dict_key WHERE key_code = 'sku_status';

DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'price_type');
DELETE FROM t_dict_key WHERE key_code = 'price_type';

DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'spec_value_source');
DELETE FROM t_dict_key WHERE key_code = 'spec_value_source';

DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'spec_value_type');
DELETE FROM t_dict_key WHERE key_code = 'spec_value_type';

-- 商品状态字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('product_status', '商品状态', '商品基础信息状态');
SET @product_status_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@product_status_id, '0', '下架', '#999999', '商品已下架', 1),
(@product_status_id, '1', '上架', '#1890ff', '商品已上架', 2),
(@product_status_id, '2', '审核中', '#faad14', '商品审核中', 3),
(@product_status_id, '3', '已禁用', '#ff4d4f', '商品已禁用', 4);

-- 商品来源类型字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('source_type', '商品来源', '商品来源类型');
SET @source_type_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@source_type_id, '1', '自产', '#52c41a', '自产商品', 1),
(@source_type_id, '2', '外购', '#1890ff', '外购商品', 2),
(@source_type_id, '3', '委外加工', '#722ed1', '委外加工商品', 3),
(@source_type_id, '4', '虚拟品', '#faad14', '虚拟商品', 4);

-- SKU状态字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('sku_status', 'SKU状态', '商品SKU状态');
SET @sku_status_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@sku_status_id, '0', '下架', '#999999', 'SKU已下架', 1),
(@sku_status_id, '1', '上架', '#1890ff', 'SKU已上架', 2),
(@sku_status_id, '2', '缺货', '#ff4d4f', 'SKU已缺货', 3);

-- 价格类型字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('price_type', '价格类型', '商品价格类型');
SET @price_type_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@price_type_id, '1', '标准价', '#1890ff', '标准售价', 1),
(@price_type_id, '2', '促销价', '#52c41a', '促销价格', 2),
(@price_type_id, '3', 'VIP价', '#722ed1', '会员价格', 3),
(@price_type_id, '4', '成本价', '#faad14', '采购成本', 4);

-- 规格值来源字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('spec_value_source', '规格值来源', '商品规格值来源');
SET @spec_value_source_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@spec_value_source_id, '1', '枚举', '#1890ff', '从预定义值中选择', 1),
(@spec_value_source_id, '2', '用户输入', '#52c41a', '用户自行输入', 2);

-- 规格值类型字典
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('spec_value_type', '规格值类型', '商品规格值类型');
SET @spec_value_type_id = LAST_INSERT_ID();

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES
(@spec_value_type_id, '1', '文本', '#1890ff', '文本类型', 1),
(@spec_value_type_id, '2', '数字', '#52c41a', '数字类型', 2),
(@spec_value_type_id, '3', '日期', '#722ed1', '日期类型', 3); 