-- 5. 工序表
DROP TABLE IF EXISTS erp_process_step CASCADE;
CREATE TABLE erp_process_step
(
    id                  BIGSERIAL PRIMARY KEY,
    route_id            BIGINT NOT NULL,
    step_code           VARCHAR(50) NOT NULL,
    step_name           VARCHAR(100) NOT NULL,
    sequence            INTEGER NOT NULL,
    workstation_id      BIGINT,
    workstation_name    VARCHAR(100),
    standard_time       DECIMAL(10,2),
    setup_time          DECIMAL(10,2),
    operation_desc      TEXT,
    quality_standards   JSONB,
    remark              TEXT,
    deleted_flag        BOOLEAN DEFAULT false NOT NULL,
    create_time         TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time         TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by          BIGINT,
    updated_by          BIGINT
);

COMMENT ON TABLE erp_process_step IS '工序表';
COMMENT ON COLUMN erp_process_step.id IS '工序ID';
COMMENT ON COLUMN erp_process_step.route_id IS '工艺路线ID | erp_process_route的id';
COMMENT ON COLUMN erp_process_step.step_code IS '工序编码';
COMMENT ON COLUMN erp_process_step.step_name IS '工序名称';
COMMENT ON COLUMN erp_process_step.sequence IS '工序顺序';
COMMENT ON COLUMN erp_process_step.workstation_id IS '工作站ID | erp_workstation的id';
COMMENT ON COLUMN erp_process_step.workstation_name IS '工作站名称';
COMMENT ON COLUMN erp_process_step.standard_time IS '标准工时(分钟)';
COMMENT ON COLUMN erp_process_step.setup_time IS '准备时间(分钟)';
COMMENT ON COLUMN erp_process_step.operation_desc IS '操作说明';
COMMENT ON COLUMN erp_process_step.quality_standards IS '质量标准(JSON格式)';
COMMENT ON COLUMN erp_process_step.remark IS '备注';
COMMENT ON COLUMN erp_process_step.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_process_step.create_time IS '创建时间';
COMMENT ON COLUMN erp_process_step.update_time IS '更新时间';
COMMENT ON COLUMN erp_process_step.created_by IS '创建人';
COMMENT ON COLUMN erp_process_step.updated_by IS '更新人';

CREATE UNIQUE INDEX idx_erp_process_step_uk_step_code ON erp_process_step(step_code);
CREATE INDEX idx_erp_process_step_route ON erp_process_step(route_id);
CREATE INDEX idx_erp_process_step_workstation ON erp_process_step(workstation_id);
CREATE INDEX idx_erp_process_step_sequence ON erp_process_step(route_id, sequence);

-- 创建触发器以更新 update_time
CREATE TRIGGER set_timestamp_erp_process_step
BEFORE UPDATE ON erp_process_step
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp(); 