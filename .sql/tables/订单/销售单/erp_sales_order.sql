-- 销售订单表
DROP TABLE IF EXISTS erp_sales_order;
CREATE TABLE erp_sales_order (
  id BIGSERIAL PRIMARY KEY,  -- 订单ID
  order_no VARCHAR(50) NOT NULL,  -- 订单编号
  order_type VARCHAR(20) NOT NULL,  -- 开单方式
  customer_id BIGINT NOT NULL,  -- 客户ID
  customer_name VARCHAR(100) NOT NULL,  -- 客户名称
  customer_code VARCHAR(50) DEFAULT NULL,  -- 客户编码
  order_date DATE NOT NULL,  -- 订单日期 
  delivery_date DATE DEFAULT NULL,  -- 交货日期
  order_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',  -- 订单状态
  payment_method VARCHAR(20) DEFAULT NULL,  -- 付款方式
  payment_terms VARCHAR(20) DEFAULT NULL,  -- 付款条件
  currency VARCHAR(10) DEFAULT 'CNY',  -- 币种
  exchange_rate DECIMAL(10,4) DEFAULT 1.0000,  -- 汇率
  subtotal_amount DECIMAL(12,2) DEFAULT 0.00,  -- 商品小计 (所有明细行金额汇总)
  discount_amount DECIMAL(12,2) DEFAULT 0.00,  -- 订单级折扣金额
  tax_amount DECIMAL(12,2) DEFAULT 0.00,  -- 税额
  shipping_fee DECIMAL(12,2) DEFAULT 0.00,  -- 运费
  final_amount DECIMAL(12,2) DEFAULT 0.00,  -- 最终金额 (小计+税额+运费-折扣)
  shipping_address VARCHAR(500) DEFAULT NULL,  -- 收货地址
  billing_address VARCHAR(500) DEFAULT NULL,  -- 发票地址
  contact_name VARCHAR(50) DEFAULT NULL,  -- 联系人
  contact_phone VARCHAR(20) DEFAULT NULL,  -- 联系电话
  contact_mobile VARCHAR(20) DEFAULT NULL,  -- 联系手机
  sales_person_id BIGINT DEFAULT NULL,  -- 销售员ID
  sales_person_name VARCHAR(50) DEFAULT NULL,  -- 销售员姓名
  order_source VARCHAR(20) DEFAULT 'OFFLINE',  -- 订单来源
  priority_level VARCHAR(20) DEFAULT 'NORMAL',  -- 优先级
  estimated_completion_date DATE DEFAULT NULL,  -- 预计完成日期
  invoice_status VARCHAR(20) DEFAULT 'PENDING',  -- 发票状态
  payment_status VARCHAR(20) DEFAULT 'UNPAID',  -- 付款状态
  paid_amount DECIMAL(12,2) DEFAULT 0.00,  -- 已付金额
  approval_user_id BIGINT DEFAULT NULL,  -- 审批人ID
  approval_time TIMESTAMP DEFAULT NULL,  -- 审批时间
  approval_remark VARCHAR(500) DEFAULT NULL,  -- 审批备注
  remark VARCHAR(500) DEFAULT NULL,  -- 备注
  payment_list JSONB DEFAULT NULL,  -- 付款列表 (JSON字符串)
  created_by BIGINT DEFAULT NULL,  -- 创建人
  updated_by BIGINT DEFAULT NULL,  -- 更新人
  deleted_flag BOOLEAN DEFAULT FALSE,  -- 删除标志：FALSE-未删除，TRUE-已删除
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  update_time TIMESTAMP DEFAULT NULL,  -- 更新时间
  CONSTRAINT uk_sales_order_no UNIQUE (order_no)  -- 订单编号唯一约束
);

-- 索引创建
CREATE INDEX idx_sales_order_customer_id ON erp_sales_order (customer_id);  -- 客户ID索引
CREATE INDEX idx_sales_order_type ON erp_sales_order (order_type);  -- 开单方式索引
CREATE INDEX idx_sales_order_status ON erp_sales_order (order_status);  -- 订单状态索引
CREATE INDEX idx_sales_order_date ON erp_sales_order (order_date);  -- 订单日期索引
CREATE INDEX idx_sales_order_delivery_date ON erp_sales_order (delivery_date);  -- 交货日期索引
CREATE INDEX idx_sales_person_id ON erp_sales_order (sales_person_id);  -- 销售员索引

-- 正式注释添加
COMMENT ON TABLE erp_sales_order IS '销售订单信息';
COMMENT ON COLUMN erp_sales_order.id IS '订单ID';
COMMENT ON COLUMN erp_sales_order.order_no IS '订单编号';
COMMENT ON COLUMN erp_sales_order.order_type IS '字典 | 开单方式 order_type：SALES-销售单，PURCHASE-订货单';
COMMENT ON COLUMN erp_sales_order.customer_id IS '客户ID | erp_customer表的id';
COMMENT ON COLUMN erp_sales_order.customer_name IS '客户名称';
COMMENT ON COLUMN erp_sales_order.customer_code IS '客户编码';
COMMENT ON COLUMN erp_sales_order.order_date IS '订单日期';
COMMENT ON COLUMN erp_sales_order.delivery_date IS '交货日期';
COMMENT ON COLUMN erp_sales_order.order_status IS '字典 | 订单状态 order_status：DRAFT-草稿，SUBMITTED-已提交，CONFIRMED-已确认，PRODUCTION-生产中，SHIPPED-已发货，DELIVERED-已交付，COMPLETED-已完成，CANCELLED-已取消';
COMMENT ON COLUMN erp_sales_order.payment_method IS '字典 | 付款方式 payment_method：CASH-现金，BANK_TRANSFER-银行转账，CREDIT_CARD-信用卡，CHECK-支票，ONLINE-在线支付';
COMMENT ON COLUMN erp_sales_order.payment_terms IS '字典 | 付款条件 payment_terms：PREPAID-预付款，COD-货到付款，NET30-30天付款，NET60-60天付款，NET90-90天付款';
COMMENT ON COLUMN erp_sales_order.currency IS '币种';
COMMENT ON COLUMN erp_sales_order.exchange_rate IS '汇率';
COMMENT ON COLUMN erp_sales_order.subtotal_amount IS '商品小计 (所有明细行金额汇总)';
COMMENT ON COLUMN erp_sales_order.discount_amount IS '订单级折扣金额';
COMMENT ON COLUMN erp_sales_order.tax_amount IS '税额';
COMMENT ON COLUMN erp_sales_order.shipping_fee IS '运费';
COMMENT ON COLUMN erp_sales_order.final_amount IS '最终金额 (小计+税额+运费-折扣)';
COMMENT ON COLUMN erp_sales_order.shipping_address IS '收货地址';
COMMENT ON COLUMN erp_sales_order.billing_address IS '发票地址';
COMMENT ON COLUMN erp_sales_order.contact_name IS '联系人';
COMMENT ON COLUMN erp_sales_order.contact_phone IS '联系电话';
COMMENT ON COLUMN erp_sales_order.contact_mobile IS '联系手机';
COMMENT ON COLUMN erp_sales_order.sales_person_id IS '销售员ID | t_employee表的employee_id';
COMMENT ON COLUMN erp_sales_order.sales_person_name IS '销售员姓名';
COMMENT ON COLUMN erp_sales_order.order_source IS '字典 | 订单来源 order_source：ONLINE-线上，OFFLINE-线下，PHONE-电话，EMAIL-邮件，WECHAT-微信';
COMMENT ON COLUMN erp_sales_order.priority_level IS '字典 | 优先级 priority_level：NORMAL-普通，URGENT-紧急，CRITICAL-特急';
COMMENT ON COLUMN erp_sales_order.estimated_completion_date IS '预计完成日期';
COMMENT ON COLUMN erp_sales_order.invoice_status IS '字典 | 发票状态 invoice_status：PENDING-待开票，INVOICED-已开票，RECEIVED-已收票';
COMMENT ON COLUMN erp_sales_order.payment_status IS '字典 | 付款状态 payment_status：UNPAID-未付款，PARTIAL-部分付款，PAID-已付款，OVERDUE-逾期';
COMMENT ON COLUMN erp_sales_order.paid_amount IS '已付金额';
COMMENT ON COLUMN erp_sales_order.approval_user_id IS '审批人ID | t_employee表的employee_id';
COMMENT ON COLUMN erp_sales_order.approval_time IS '审批时间';
COMMENT ON COLUMN erp_sales_order.approval_remark IS '审批备注';
COMMENT ON COLUMN erp_sales_order.remark IS '备注';
COMMENT ON COLUMN erp_sales_order.payment_list IS '分次付款信息列表 (JSON数组)';
COMMENT ON COLUMN erp_sales_order.created_by IS '创建人';
COMMENT ON COLUMN erp_sales_order.updated_by IS '更新人';
COMMENT ON COLUMN erp_sales_order.deleted_flag IS '删除标志：FALSE-未删除，TRUE-已删除';
COMMENT ON COLUMN erp_sales_order.create_time IS '创建时间';
COMMENT ON COLUMN erp_sales_order.update_time IS '更新时间'; 