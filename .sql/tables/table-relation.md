DB:
# 字典
t_dict_key[字典key表]: dict_key_id[PK], key_code, key_name; t_dict_value[字典值表]: dict_value_id[PK], dict_key_id[->t_dict_key.dict_key_id], value_code, value_name

# 实体参数
erp_entity_types[实体类型表]: id[PK], type_code, type_name; erp_parameter_types[参数类型表]: id[PK], type_code, type_name, data_type; erp_entity_parameters[实体参数表]: id[PK], entity_type_id[->erp_entity_types.id], entity_id, parameter_type_id[->erp_parameter_types.id], parameter_value

# 纸张
erp_paper[纸张基础信息]: id[PK], code, paper_category, paper_type, corrugated_shape; erp_paper_quotation[纸张报价]: id[PK], quotation_no, supplier_id[->txy_supplier.supplier_id], quotation_date, status; erp_paper_quotation_item[报价详情]: id[PK], quotation_id[->erp_paper_quotation.id], paper_id[->erp_paper.id], unit_price

# 纸箱
erp_box[纸箱箱形公式]: id[PK], box_type_code, box_type_name, total_length_formula, door_width_formula; erp_box_quotation[纸箱报价]: id[PK], quotation_no, customer_id[->erp_customer.id], box_length, box_width, box_height, paperboard_supplier_id[->txy_supplier.supplier_id], raw_paper_supplier_id[->txy_supplier.supplier_id]

# 客户
erp_customer[客户信息]: id[PK], customer_code, customer_name, customer_type, status; txy_supplier[供应商信息]: supplier_id[PK], supplier_code, supplier_name, supplier_type, status

# 印刷工艺
erp_printing_process[印刷工艺]: id[PK], code, name, process_type, max_colors; erp_printing_process_product[工艺产品关联]: id[PK], process_id[->erp_printing_process.id], product_id, product_type; erp_printing_color[印刷色彩]: id[PK], code, name, color_system, color_value

# 系统
t_config[系统配置]: config_id[PK], config_name, config_key, config_value; t_dict_key[字典key表]: dict_key_id[PK], key_code, key_name; t_dict_value[字典值表]: dict_value_id[PK], dict_key_id[->t_dict_key.dict_key_id], value_code, value_name

# 菜单与角色
t_menu[菜单表]: menu_id[PK], menu_name, menu_type, parent_id, path, component; t_role[角色表]: role_id[PK], role_name, role_code; t_role_menu[角色-菜单]: role_menu_id[PK], role_id[->t_role.role_id], menu_id[->t_menu.menu_id]

# 职务与分类
t_position[职务表]: position_id[PK], position_name, level, sort; t_category[分类表]: id[PK], category_name, category_type, parent_id

# 商品
t_category[商品分类]: id[PK], category_name, category_type, parent_id, sort, disabled_flag, deleted_flag;
erp_brand[商品品牌]: id[PK], brand_code, brand_name, logo_url, description, website, sort, status;
erp_product[商品基础信息]: id[PK], product_code, name, description, unit, id[->t_category.id], brand_id[->erp_brand.id], status, source_type, is_raw_material;
erp_product_category_relation[商品分类关联]: id[PK], product_id[->erp_product.id], id[->t_category.id];
erp_product_attr[商品属性]: id[PK], product_id[->erp_product.id], attr_name, attr_value, value_type, is_search, is_visible, sort;
erp_spec[商品规格]: id[PK], product_id[->erp_product.id], name, value_source, value_type, min_value, max_value, step_size, sort, status;
erp_spec_value[商品规格值]: id[PK], spec_id[->erp_spec.id], value, sort, status;
erp_sku[商品SKU]: id[PK], product_id[->erp_product.id], sku_code, stock, status, weight, lead_time;
erp_sku_spec_relation[SKU规格值关联]: id[PK], sku_id[->erp_sku.id], spec_id[->erp_spec.id], spec_value_id[->erp_spec_value.id], custom_value;
erp_price[商品价格]: id[PK], product_id[->erp_product.id], sku_id[->erp_sku.id], supplier_id, customer_id, price, price_type, effective_date, expired_date, status;
erp_price_history[价格历史]: id[PK], price_id[->erp_price.id], old_price, new_price, change_date, change_reason;

# 订单
erp_sales_order[销售订单]: id[PK], order_no, order_type, customer_id[->erp_customer.id], customer_name, order_date, delivery_date, order_status, payment_method, payment_terms, final_amount, paid_amount, payment_status, sales_person_id;
erp_sales_order_item[销售订单明细]: id[PK], order_id[->erp_sales_order.id], order_no, line_no, product_id[->erp_product.id], sku_id[->erp_sku.id], quantity, unit_price, line_amount, item_status;
erp_sales_payment[销售收款记录]: id[PK], payment_no, sales_order_id[->erp_sales_order.id], sales_order_no, collection_type, collection_method, collection_amount, planned_collection_date, actual_collection_date, collection_status, sales_person_id, sales_commission_amount;
erp_purchase_order[采购订单]: id[PK], order_no, order_type, supplier_id[->erp_supplier.id], supplier_name, order_date, expected_delivery_date, order_status, payment_method, payment_terms, final_amount, paid_amount, payment_status, purchaser_id;
erp_purchase_order_item[采购订单明细]: id[PK], order_id[->erp_purchase_order.id], order_no, line_no, product_id[->erp_product.id], order_quantity, received_quantity, unit_price, line_amount, warehouse_id;
erp_purchase_payment[采购付款记录]: id[PK], payment_no, purchase_order_id[->erp_purchase_order.id], purchase_order_no, payment_type, payment_method, payment_amount, planned_payment_date, actual_payment_date, payment_status, purchaser_id, quality_retention_amount;
