# 账户管理前端组件示例 🎨

## 1. 账户管理主组件

```vue
<template>
  <div class="account-manager">
    <div class="account-header">
      <h3>收款账户管理</h3>
      <a-button type="primary" @click="showAddModal">
        <PlusOutlined /> 添加账户
      </a-button>
    </div>
    
    <div class="account-list">
      <a-empty v-if="!accounts.length" description="暂无收款账户" />
      
      <div v-for="account in accounts" :key="account.id" class="account-card">
        <div class="account-header">
          <div class="account-title">
            <span class="account-name">{{ account.name }}</span>
            <a-tag :color="getTypeColor(account.type)">
              {{ getTypeText(account.type) }}
            </a-tag>
            <a-tag v-if="account.is_default" color="gold">默认</a-tag>
            <a-tag :color="getNatureColor(account.nature)">
              {{ getNatureText(account.nature) }}
            </a-tag>
          </div>
          <div class="account-actions">
            <a-button size="small" @click="editAccount(account)">编辑</a-button>
            <a-button size="small" @click="setDefault(account)" :disabled="account.is_default">
              设为默认
            </a-button>
            <a-popconfirm title="确定删除此账户？" @confirm="deleteAccount(account.id)">
              <a-button size="small" danger>删除</a-button>
            </a-popconfirm>
          </div>
        </div>
        
        <div class="account-info">
          <AccountInfoDisplay :type="account.type" :info="account.account_info" />
        </div>
        
        <div v-if="account.remark" class="account-remark">
          <small>备注：{{ account.remark }}</small>
        </div>
      </div>
    </div>
    
    <!-- 添加/编辑账户弹窗 -->
    <AccountFormModal
      v-model:visible="modalVisible"
      :account="currentAccount"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface Account {
  id: string
  name: string
  type: 'BANK' | 'ALIPAY' | 'WECHAT' | 'OTHER'
  nature: 'CORPORATE' | 'PERSONAL'
  account_info: any
  is_default: boolean
  remark?: string
  created_at: string
}

const props = defineProps<{
  modelValue: Account[]
}>()

const emit = defineEmits<{
  'update:modelValue': [accounts: Account[]]
}>()

const accounts = computed({
  get: () => props.modelValue || [],
  set: (value) => emit('update:modelValue', value)
})

const modalVisible = ref(false)
const currentAccount = ref<Account | null>(null)

// 类型和性质的显示文本和颜色
const getTypeText = (type: string) => {
  const map = {
    BANK: '银行账户',
    ALIPAY: '支付宝',
    WECHAT: '微信',
    OTHER: '其他'
  }
  return map[type] || type
}

const getTypeColor = (type: string) => {
  const map = {
    BANK: 'blue',
    ALIPAY: 'cyan',
    WECHAT: 'green',
    OTHER: 'orange'
  }
  return map[type] || 'default'
}

const getNatureText = (nature: string) => {
  return nature === 'CORPORATE' ? '对公' : '对私'
}

const getNatureColor = (nature: string) => {
  return nature === 'CORPORATE' ? 'purple' : 'geekblue'
}

// 操作方法
const showAddModal = () => {
  currentAccount.value = null
  modalVisible.value = true
}

const editAccount = (account: Account) => {
  currentAccount.value = { ...account }
  modalVisible.value = true
}

const setDefault = (account: Account) => {
  const newAccounts = accounts.value.map(acc => ({
    ...acc,
    is_default: acc.id === account.id
  }))
  accounts.value = newAccounts
  message.success('已设为默认账户')
}

const deleteAccount = (accountId: string) => {
  accounts.value = accounts.value.filter(acc => acc.id !== accountId)
  message.success('账户已删除')
}

const handleSuccess = (account: Account) => {
  if (currentAccount.value) {
    // 编辑
    const index = accounts.value.findIndex(acc => acc.id === account.id)
    if (index > -1) {
      accounts.value[index] = account
    }
  } else {
    // 新增
    accounts.value = [...accounts.value, account]
  }
  modalVisible.value = false
  message.success(currentAccount.value ? '账户已更新' : '账户已添加')
}
</script>

<style scoped>
.account-manager {
  padding: 16px;
}

.account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.account-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
}

.account-card .account-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.account-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.account-name {
  font-weight: 500;
  font-size: 16px;
}

.account-actions {
  display: flex;
  gap: 8px;
}

.account-info {
  margin-bottom: 8px;
}

.account-remark {
  color: #666;
  font-style: italic;
}
</style>
```

## 2. 账户信息显示组件

```vue
<template>
  <div class="account-info-display">
    <!-- 银行账户 -->
    <div v-if="type === 'BANK'" class="bank-info">
      <div class="info-row">
        <span class="label">账户名称：</span>
        <span class="value">{{ info.account_name }}</span>
      </div>
      <div class="info-row">
        <span class="label">账户号码：</span>
        <span class="value">{{ formatBankAccount(info.account_number) }}</span>
      </div>
      <div class="info-row">
        <span class="label">开户银行：</span>
        <span class="value">{{ info.bank_name }}</span>
      </div>
      <div v-if="info.bank_branch" class="info-row">
        <span class="label">开户支行：</span>
        <span class="value">{{ info.bank_branch }}</span>
      </div>
    </div>
    
    <!-- 支付宝 -->
    <div v-else-if="type === 'ALIPAY'" class="alipay-info">
      <div class="info-row">
        <span class="label">支付宝账号：</span>
        <span class="value">{{ info.account }}</span>
      </div>
      <div class="info-row">
        <span class="label">真实姓名：</span>
        <span class="value">{{ info.real_name }}</span>
      </div>
    </div>
    
    <!-- 微信 -->
    <div v-else-if="type === 'WECHAT'" class="wechat-info">
      <div class="info-row">
        <span class="label">真实姓名：</span>
        <span class="value">{{ info.real_name }}</span>
      </div>
      <div v-if="info.account" class="info-row">
        <span class="label">微信号：</span>
        <span class="value">{{ info.account }}</span>
      </div>
      <div v-if="info.phone" class="info-row">
        <span class="label">手机号：</span>
        <span class="value">{{ info.phone }}</span>
      </div>
    </div>
    
    <!-- 其他方式 -->
    <div v-else-if="type === 'OTHER'" class="other-info">
      <div class="info-row">
        <span class="label">支付平台：</span>
        <span class="value">{{ info.platform }}</span>
      </div>
      <div class="info-row">
        <span class="label">账户信息：</span>
        <span class="value">{{ info.account }}</span>
      </div>
      <div v-if="info.contact_info" class="info-row">
        <span class="label">联系方式：</span>
        <span class="value">{{ info.contact_info }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  type: 'BANK' | 'ALIPAY' | 'WECHAT' | 'OTHER'
  info: any
}

defineProps<Props>()

// 格式化银行卡号（隐藏中间部分）
const formatBankAccount = (account: string) => {
  if (!account || account.length < 8) return account
  const start = account.slice(0, 4)
  const end = account.slice(-4)
  const middle = '*'.repeat(account.length - 8)
  return `${start}${middle}${end}`
}
</script>

<style scoped>
.account-info-display {
  font-size: 14px;
}

.info-row {
  display: flex;
  margin-bottom: 4px;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  font-weight: 500;
}
</style>
```

## 3. 账户表单弹窗组件

```vue
<template>
  <a-modal
    v-model:visible="visible"
    :title="account ? '编辑账户' : '添加账户'"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="form" :rules="rules" layout="vertical">
      <a-form-item label="账户名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入账户名称" />
      </a-form-item>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="账户类型" name="type">
            <a-select v-model:value="form.type" @change="onTypeChange">
              <a-select-option value="BANK">银行账户</a-select-option>
              <a-select-option value="ALIPAY">支付宝</a-select-option>
              <a-select-option value="WECHAT">微信</a-select-option>
              <a-select-option value="OTHER">其他</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="账户性质" name="nature">
            <a-radio-group v-model:value="form.nature">
              <a-radio value="CORPORATE">对公</a-radio>
              <a-radio value="PERSONAL">对私</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      
      <!-- 动态表单区域 -->
      <div class="dynamic-form">
        <BankAccountForm 
          v-if="form.type === 'BANK'" 
          v-model:value="form.account_info" 
          :nature="form.nature"
        />
        <AlipayAccountForm 
          v-if="form.type === 'ALIPAY'" 
          v-model:value="form.account_info" 
        />
        <WechatAccountForm 
          v-if="form.type === 'WECHAT'" 
          v-model:value="form.account_info" 
        />
        <OtherAccountForm 
          v-if="form.type === 'OTHER'" 
          v-model:value="form.account_info" 
        />
      </div>
      
      <a-form-item label="备注">
        <a-textarea v-model:value="form.remark" :rows="3" placeholder="选填" />
      </a-form-item>
      
      <a-form-item>
        <a-checkbox v-model:checked="form.is_default">设为默认账户</a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { v4 as uuidv4 } from 'uuid'

interface Account {
  id: string
  name: string
  type: 'BANK' | 'ALIPAY' | 'WECHAT' | 'OTHER'
  nature: 'CORPORATE' | 'PERSONAL'
  account_info: any
  is_default: boolean
  remark?: string
  created_at: string
}

interface Props {
  visible: boolean
  account?: Account | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  'success': [account: Account]
}>()

const formRef = ref()
const form = reactive({
  name: '',
  type: 'BANK' as const,
  nature: 'CORPORATE' as const,
  account_info: {},
  is_default: false,
  remark: ''
})

const rules = {
  name: [{ required: true, message: '请输入账户名称' }],
  type: [{ required: true, message: '请选择账户类型' }],
  nature: [{ required: true, message: '请选择账户性质' }]
}

// 监听账户变化，初始化表单
watch(() => props.account, (account) => {
  if (account) {
    Object.assign(form, {
      name: account.name,
      type: account.type,
      nature: account.nature,
      account_info: { ...account.account_info },
      is_default: account.is_default,
      remark: account.remark || ''
    })
  } else {
    resetForm()
  }
}, { immediate: true })

const resetForm = () => {
  Object.assign(form, {
    name: '',
    type: 'BANK',
    nature: 'CORPORATE',
    account_info: {},
    is_default: false,
    remark: ''
  })
}

const onTypeChange = () => {
  form.account_info = {}
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const account: Account = {
      id: props.account?.id || uuidv4(),
      name: form.name,
      type: form.type,
      nature: form.nature,
      account_info: form.account_info,
      is_default: form.is_default,
      remark: form.remark,
      created_at: props.account?.created_at || new Date().toISOString()
    }
    
    emit('success', account)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  emit('update:visible', false)
  resetForm()
}
</script>

<style scoped>
.dynamic-form {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background: #fafafa;
}
</style>
```

## 4. 使用示例

```vue
<template>
  <div class="customer-form">
    <!-- 其他客户信息表单 -->
    
    <!-- 账户管理区域 -->
    <a-form-item label="收款账户">
      <AccountManager v-model="customerForm.bank_accounts" />
    </a-form-item>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import AccountManager from './components/AccountManager.vue'

const customerForm = reactive({
  name: '',
  // ... 其他字段
  bank_accounts: [] // 账户数组
})
</script>
```

## 特点总结 ✨

1. **简单易用**：只需要基础的收款信息，不涉及复杂的支付逻辑
2. **灵活扩展**：支持银行、支付宝、微信、其他等多种类型
3. **类型安全**：完整的TypeScript类型定义
4. **用户友好**：直观的界面和操作流程
5. **数据安全**：银行卡号自动脱敏显示 