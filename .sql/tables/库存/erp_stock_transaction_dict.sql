-- 库存流水字典数据
-- 创建时间: 2024-12-19

-- 1. 库存流水类型字典
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type');
DELETE FROM t_dict_key WHERE key_code = 'transaction_type';
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('transaction_type', '库存流水类型', '库存流水交易类型字典');

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES 
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'IN_PURCHASE', '采购入库', '#52c41a', '采购商品入库操作', 1),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'IN_RETURN', '退货入库', '#faad14', '客户退货入库操作', 2),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'IN_TRANSFER', '调拨入库', '#13c2c2', '仓库间调拨入库操作', 3),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'IN_ADJUST', '调整入库', '#52c41a', '盘点盈余入库操作', 4),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'IN_PRODUCTION', '生产入库', '#389e0d', '生产完工入库操作', 5),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'OUT_SALES', '销售出库', '#1890ff', '销售商品出库操作', 6),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'OUT_RETURN', '退货出库', '#fa8c16', '向供应商退货出库操作', 7),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'OUT_TRANSFER', '调拨出库', '#722ed1', '仓库间调拨出库操作', 8),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'OUT_ADJUST', '调整出库', '#f5222d', '盘点亏损出库操作', 9),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'transaction_type'), 'OUT_PRODUCTION', '生产出库', '#d46b08', '生产领料出库操作', 10);

-- 2. 关联单据类型字典
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type');
DELETE FROM t_dict_key WHERE key_code = 'related_type';
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('related_type', '关联单据类型', '库存流水关联单据类型字典');

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES 
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type'), 'ORDER', '订单', '#1890ff', '销售订单', 1),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type'), 'PURCHASE', '采购单', '#52c41a', '采购订单', 2),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type'), 'TRANSFER', '调拨单', '#722ed1', '仓库调拨单', 3),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type'), 'ADJUST', '调整单', '#faad14', '库存调整单', 4),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'related_type'), 'CHECK', '盘点单', '#13c2c2', '库存盘点单', 5);

-- 3. 审核状态字典
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'audit_status');
DELETE FROM t_dict_key WHERE key_code = 'audit_status';
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('audit_status', '审核状态', '库存流水审核状态字典');

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES 
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'audit_status'), 'PENDING', '待审核', '#faad14', '提交审核，等待审核', 1),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'audit_status'), 'APPROVED', '已审核', '#52c41a', '审核通过，库存已变更', 2),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'audit_status'), 'REJECTED', '已拒绝', '#f5222d', '审核拒绝，需要修改', 3),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'audit_status'), 'CANCELLED', '已作废', '#8c8c8c', '已作废，不再生效', 4);

-- 4. 处理状态字典
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'process_status');
DELETE FROM t_dict_key WHERE key_code = 'process_status';
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('process_status', '处理状态', '库存流水处理状态字典');

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES 
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'process_status'), 'PENDING', '待处理', '#d9d9d9', '等待处理', 1),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'process_status'), 'IN_PROGRESS', '处理中', '#faad14', '正在处理中', 2),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'process_status'), 'COMPLETED', '已完成', '#52c41a', '处理完成', 3),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'process_status'), 'CANCELLED', '已取消', '#8c8c8c', '处理已取消', 4);

-- 5. 库存单位字典
DELETE FROM t_dict_value WHERE dict_key_id = (SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit');
DELETE FROM t_dict_key WHERE key_code = 'stock_unit';
INSERT INTO t_dict_key (key_code, key_name, remark) VALUES ('stock_unit', '库存单位', '商品库存计量单位字典');

INSERT INTO t_dict_value (dict_key_id, value_code, value_name, dict_color_hex, remark, sort) VALUES 
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'pcs', '个', '#1890ff', '个', 1),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'piece', '件', '#722ed1', '件', 2),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'box', '盒', '#13c2c2', '盒', 3),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'pack', '包', '#52c41a', '包', 4),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'carton', '箱', '#faad14', '箱', 5),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'kg', '公斤', '#fa8c16', '公斤', 6),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'g', '克', '#f5222d', '克', 7),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'l', '升', '#389e0d', '升', 8),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'ml', '毫升', '#d46b08', '毫升', 9),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'm', '米', '#595959', '米', 10),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'cm', '厘米', '#8c8c8c', '厘米', 11),
((SELECT dict_key_id FROM t_dict_key WHERE key_code = 'stock_unit'), 'm2', '平方米', '#096dd9', '平方米', 12);