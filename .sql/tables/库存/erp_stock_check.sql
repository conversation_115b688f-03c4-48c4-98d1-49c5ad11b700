-- 库存盘点表
DROP TABLE IF EXISTS erp_stock_check;
CREATE TABLE erp_stock_check (
  id BIGSERIAL PRIMARY KEY,  -- 盘点ID
  check_no VARCHAR(50) NOT NULL,  -- 盘点单号
  check_name VARCHAR(100) NOT NULL,  -- 盘点名称
  warehouse_id BIGINT NOT NULL,  -- 仓库ID
  warehouse_code VARCHAR(50) DEFAULT NULL,  -- 仓库编码
  warehouse_name VARCHAR(100) NOT NULL,  -- 仓库名称
  
  -- 盘点信息
  check_type VARCHAR(20) NOT NULL,  -- 盘点类型
  check_date DATE NOT NULL,  -- 盘点日期
  check_status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',  -- 盘点状态
  
  -- 盘点范围
  check_scope VARCHAR(20) DEFAULT 'ALL',  -- 盘点范围
  product_category_ids TEXT DEFAULT NULL,  -- 商品分类ID列表 (JSON格式)
  product_ids TEXT DEFAULT NULL,  -- 商品ID列表 (JSON格式)
  
  -- 统计信息
  total_items INTEGER DEFAULT 0,  -- 盘点商品总数
  checked_items INTEGER DEFAULT 0,  -- 已盘点商品数
  profit_items INTEGER DEFAULT 0,  -- 盘盈商品数
  loss_items INTEGER DEFAULT 0,  -- 盘亏商品数
  total_profit_amount DECIMAL(12,2) DEFAULT 0.00,  -- 盘盈总金额
  total_loss_amount DECIMAL(12,2) DEFAULT 0.00,  -- 盘亏总金额
  
  -- 操作信息
  checker_id BIGINT DEFAULT NULL,  -- 盘点人ID
  checker_name VARCHAR(50) DEFAULT NULL,  -- 盘点人姓名
  check_start_time TIMESTAMP DEFAULT NULL,  -- 盘点开始时间
  check_end_time TIMESTAMP DEFAULT NULL,  -- 盘点结束时间
  
  -- 审批信息
  approval_user_id BIGINT DEFAULT NULL,  -- 审批人ID
  approval_time TIMESTAMP DEFAULT NULL,  -- 审批时间
  approval_remark VARCHAR(500) DEFAULT NULL,  -- 审批备注
  
  remark VARCHAR(500) DEFAULT NULL,  -- 备注
  
  -- 系统信息
  created_by BIGINT DEFAULT NULL,  -- 创建人

  updated_by BIGINT DEFAULT NULL,  -- 更新人
  deleted_flag BOOLEAN DEFAULT FALSE,  -- 删除标志：FALSE-未删除，TRUE-已删除
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  update_time TIMESTAMP DEFAULT NULL,  -- 更新时间
  CONSTRAINT uk_stock_check_no UNIQUE (check_no)  -- 盘点单号唯一约束
);

-- 索引创建
CREATE INDEX idx_stock_check_warehouse_id ON erp_stock_check (warehouse_id);  -- 仓库ID索引
CREATE INDEX idx_stock_check_type ON erp_stock_check (check_type);  -- 盘点类型索引
CREATE INDEX idx_stock_check_status ON erp_stock_check (check_status);  -- 盘点状态索引
CREATE INDEX idx_stock_check_date ON erp_stock_check (check_date);  -- 盘点日期索引
CREATE INDEX idx_stock_check_checker_id ON erp_stock_check (checker_id);  -- 盘点人索引

-- 正式注释添加
COMMENT ON TABLE erp_stock_check IS '库存盘点';
COMMENT ON COLUMN erp_stock_check.id IS '盘点ID';
COMMENT ON COLUMN erp_stock_check.check_no IS '盘点单号';
COMMENT ON COLUMN erp_stock_check.check_name IS '盘点名称';
COMMENT ON COLUMN erp_stock_check.warehouse_id IS '仓库ID | erp_warehouse表的id';
COMMENT ON COLUMN erp_stock_check.warehouse_code IS '仓库编码';
COMMENT ON COLUMN erp_stock_check.warehouse_name IS '仓库名称';
COMMENT ON COLUMN erp_stock_check.check_type IS '字典 | 盘点类型 check_type：FULL-全盘，PARTIAL-部分盘点，CYCLE-循环盘点，SPOT-抽盘';
COMMENT ON COLUMN erp_stock_check.check_date IS '盘点日期';
COMMENT ON COLUMN erp_stock_check.check_status IS '字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，APPROVED-已审批，CANCELLED-已取消';
COMMENT ON COLUMN erp_stock_check.check_scope IS '字典 | 盘点范围 check_scope：ALL-全部商品，CATEGORY-按分类，PRODUCT-指定商品';
COMMENT ON COLUMN erp_stock_check.product_category_ids IS '商品分类ID列表 (JSON格式)';
COMMENT ON COLUMN erp_stock_check.product_ids IS '商品ID列表 (JSON格式)';
COMMENT ON COLUMN erp_stock_check.total_items IS '盘点商品总数';
COMMENT ON COLUMN erp_stock_check.checked_items IS '已盘点商品数';
COMMENT ON COLUMN erp_stock_check.profit_items IS '盘盈商品数';
COMMENT ON COLUMN erp_stock_check.loss_items IS '盘亏商品数';
COMMENT ON COLUMN erp_stock_check.total_profit_amount IS '盘盈总金额';
COMMENT ON COLUMN erp_stock_check.total_loss_amount IS '盘亏总金额';
COMMENT ON COLUMN erp_stock_check.checker_id IS '盘点人ID | t_employee表的employee_id';
COMMENT ON COLUMN erp_stock_check.checker_name IS '盘点人姓名';
COMMENT ON COLUMN erp_stock_check.check_start_time IS '盘点开始时间';
COMMENT ON COLUMN erp_stock_check.check_end_time IS '盘点结束时间';
COMMENT ON COLUMN erp_stock_check.approval_user_id IS '审批人ID | t_employee表的employee_id';
COMMENT ON COLUMN erp_stock_check.approval_time IS '审批时间';
COMMENT ON COLUMN erp_stock_check.approval_remark IS '审批备注';
COMMENT ON COLUMN erp_stock_check.remark IS '备注';
COMMENT ON COLUMN erp_stock_check.created_by IS '创建人';
COMMENT ON COLUMN erp_stock_check.updated_by IS '更新人';
COMMENT ON COLUMN erp_stock_check.deleted_flag IS '删除标志：FALSE-未删除，TRUE-已删除';
COMMENT ON COLUMN erp_stock_check.create_time IS '创建时间';
COMMENT ON COLUMN erp_stock_check.update_time IS '更新时间'; 