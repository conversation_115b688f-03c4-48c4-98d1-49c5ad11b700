begin ;
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_box.sql
-- ============================================================

    -- 创建触发器函数（如果尚未创建）
 CREATE OR REPLACE FUNCTION trigger_set_timestamp()
 RETURNS TRIGGER AS $$
 BEGIN
   NEW.update_time = NOW();
   RETURN NEW;
 END;
 $$ LANGUAGE plpgsql;

-- 表结构: erp_box
CREATE TABLE erp_box (
    id BIGSERIAL PRIMARY KEY,
    box_type_code VARCHAR(32) NOT NULL,
    box_type_name VARCHAR(100) NOT NULL,
    box_type_desc VARCHAR(255) DEFAULT NULL,
    image_url VARCHAR(255) DEFAULT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'
    params_json JSONB DEFAULT NULL, -- Converted from json
    sort INT DEFAULT 0,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    deleted_flag BOOLEAN NOT NULL DEFAULT false -- Converted from tinyint(1) DEFAULT '0'
);

-- 添加表和列的注释
COMMENT ON TABLE erp_box IS '纸箱箱形';
COMMENT ON COLUMN erp_box.id IS '主键ID';
COMMENT ON COLUMN erp_box.box_type_code IS '箱型编码';
COMMENT ON COLUMN erp_box.box_type_name IS '箱型名称';
COMMENT ON COLUMN erp_box.box_type_desc IS '箱型描述';
COMMENT ON COLUMN erp_box.image_url IS '箱型图片URL';
COMMENT ON COLUMN erp_box.is_active IS '是否启用(true启用/false禁用)';
COMMENT ON COLUMN erp_box.params_json IS '参数JSON';
COMMENT ON COLUMN erp_box.sort IS '排序';
COMMENT ON COLUMN erp_box.created_by IS '创建人';
COMMENT ON COLUMN erp_box.updated_by IS '更新人';
COMMENT ON COLUMN erp_box.create_time IS '创建时间';
COMMENT ON COLUMN erp_box.update_time IS '更新时间';
COMMENT ON COLUMN erp_box.deleted_flag IS '删除标识(false未删除/true已删除)';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_box_uk_box_type_code ON erp_box (box_type_code); -- Renamed from uk_box_type_code
CREATE INDEX idx_erp_box_idx_is_active ON erp_box (is_active); -- Renamed from idx_is_active

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_box
BEFORE UPDATE ON erp_box
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_box
INSERT INTO erp_box (id, box_type_code, box_type_name, box_type_desc, image_url, is_active, params_json, sort, created_by, updated_by, create_time, update_time, deleted_flag) VALUES
(1, 'standard', '标准箱', '标准箱dddd', 'https://www.baidu.com', true, '{"needDieCutting": true, "doorWidthFormula": "(长+宽)*2+舌头长度+50", "needPrintingPlate": true, "totalLengthFormula": "(长+宽)*2+舌头长度", "doorWidthFormulaDesc": "高度+修边位+摇盖加分", "totalLengthFormulaDesc": "(长+宽)*2+舌头长度"}'::jsonb, 0, 1, 1, '2025-04-03 16:02:00', '2025-04-06 01:08:45', false),
(3, '444', '4444', '', '', true, '{"needDieCutting": false, "doorWidthFormula": "", "needPrintingPlate": false, "totalLengthFormula": "", "doorWidthFormulaDesc": "", "totalLengthFormulaDesc": ""}'::jsonb, 5, NULL, NULL, '2025-04-03 19:07:04', '2025-04-03 19:15:22', true), -- deleted_flag was 1
(4, 'ccc', 'ccc', '', '', true, '{"needDieCutting": false, "doorWidthFormula": "", "needPrintingPlate": false, "totalLengthFormula": "", "doorWidthFormulaDesc": "", "totalLengthFormulaDesc": ""}'::jsonb, 0, NULL, NULL, '2025-04-03 19:53:47', '2025-04-03 19:54:21', true), -- deleted_flag was 1
(5, '555', '555', '', '', true, '{"needDieCutting": false, "doorWidthFormula": "", "needPrintingPlate": false, "totalLengthFormula": "", "doorWidthFormulaDesc": "", "totalLengthFormulaDesc": ""}'::jsonb, 0, NULL, NULL, '2025-04-03 20:46:51', '2025-04-06 01:10:20', true), -- deleted_flag was 1
(6, '666', '666', '', '', true, '{"needDieCutting": false, "doorWidthFormula": "", "needPrintingPlate": false, "totalLengthFormula": "", "doorWidthFormulaDesc": "", "totalLengthFormulaDesc": ""}'::jsonb, 0, NULL, NULL, '2025-04-06 01:10:28', '2025-04-06 01:10:28', false);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_box_quotation.sql
-- ============================================================

-- 表结构: erp_box_quotation
CREATE TABLE erp_box_quotation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    quotation_no VARCHAR(32) NOT NULL,
    customer_id BIGINT NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    quotation_date DATE NOT NULL,
    box_length INT DEFAULT NULL,
    box_width INT DEFAULT NULL,
    box_height INT DEFAULT NULL,
    paperboard_layer_count INT DEFAULT 1,
    box_quantity INT DEFAULT 1,
    corrugated_shape_group VARCHAR(20) DEFAULT NULL,
    printing_method VARCHAR(20) DEFAULT 'watermark',
    box_id BIGINT DEFAULT NULL,
    box_name VARCHAR(100) DEFAULT NULL,
    paperboard_supplier_id BIGINT DEFAULT NULL,
    paperboard_supplier_name VARCHAR(100) DEFAULT NULL,
    raw_paper_supplier_id BIGINT DEFAULT NULL,
    raw_paper_supplier_name VARCHAR(100) DEFAULT NULL,
    supplier_discount DECIMAL(5,2) DEFAULT 0.88, -- decimal maps to DECIMAL/NUMERIC
    tax_rate DECIMAL(5,2) DEFAULT 0.94, -- decimal maps to DECIMAL/NUMERIC
    include_tax BOOLEAN DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'
    box_area DECIMAL(12,5) DEFAULT NULL,
    box_volume DECIMAL(12,5) DEFAULT NULL,
    corrugated_area DECIMAL(12,5) DEFAULT NULL,
    total_length INT DEFAULT NULL,
    box_door_width INT DEFAULT NULL,
    die_cutting_door_width INT DEFAULT NULL,
    die_cutting_quantity INT DEFAULT NULL,
    corrugated_door_width INT DEFAULT NULL,
    actual_corrugated_door_width INT DEFAULT NULL,
    corrugated_count INT DEFAULT 1,
    face_paper_door_width INT DEFAULT NULL,
    actual_face_paper_door_width INT DEFAULT NULL,
    face_paper_count INT DEFAULT 1,
    tongue_length INT DEFAULT 40,
    wave_cap_addition INT DEFAULT 0,
    trim_position INT DEFAULT 15,
    watermark_colors INT DEFAULT 0,
    printing_plate_price DECIMAL(12,2) DEFAULT 0.00,
    die_cutting_plate_price DECIMAL(12,2) DEFAULT 0.00,
    watermark_price DECIMAL(12,2) DEFAULT 0.50,
    paperboard_price DECIMAL(12,2) DEFAULT 0.00,
    paperboard_processing_price DECIMAL(12,2) DEFAULT 0.25,
    die_cutting_price DECIMAL(12,2) DEFAULT 0.05,
    box_closing_type VARCHAR(20) DEFAULT 'nailing',
    paperboard_json JSONB DEFAULT NULL, -- Converted from json
    raw_paper_json JSONB DEFAULT NULL, -- Converted from json
    printing_process_json JSONB DEFAULT NULL, -- Converted from json
    paperboard_unit_price DECIMAL(12,2) DEFAULT 0.00,
    paperboard_processing_unit_price DECIMAL(12,2) DEFAULT 0.00,
    printing_plate_unit_price DECIMAL(12,2) DEFAULT 0.00,
    die_cutting_plate_unit_price DECIMAL(12,2) DEFAULT 0.00,
    raw_paper_unit_price DECIMAL(12,2) DEFAULT 0.00,
    watermark_unit_price DECIMAL(12,2) DEFAULT 0.00,
    die_cutting_unit_price DECIMAL(12,2) DEFAULT 0.00,
    box_closing_unit_price DECIMAL(12,2) DEFAULT 0.00,
    printing_process_unit_price DECIMAL(12,2) DEFAULT 0.00,
    box_unit_price DECIMAL(12,2) DEFAULT 0.00,
    paperboard_total_price DECIMAL(12,2) DEFAULT 0.00,
    printing_plate_total_price DECIMAL(12,2) DEFAULT 0.00,
    die_cutting_plate_total_price DECIMAL(12,2) DEFAULT 0.00,
    raw_paper_total_price DECIMAL(12,2) DEFAULT 0.00,
    watermark_total_price DECIMAL(12,2) DEFAULT 0.00,
    die_cutting_total_price DECIMAL(12,2) DEFAULT 0.00,
    box_closing_total_price DECIMAL(12,2) DEFAULT 0.00,
    printing_process_total_price DECIMAL(12,2) DEFAULT 0.00,
    box_total_price DECIMAL(12,2) DEFAULT 0.00,
    total_cost DECIMAL(12,2) DEFAULT 0.00,
    profit_margin DECIMAL(5,2) DEFAULT 0.15,
    profit_amount DECIMAL(12,2) DEFAULT 0.00,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    approval_user_id BIGINT DEFAULT NULL,
    approval_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- Converted from datetime
    approval_remark VARCHAR(500) DEFAULT NULL,
    remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    deleted_flag BOOLEAN NOT NULL DEFAULT false -- Converted from tinyint(1) DEFAULT '0'
);

-- 添加表和列的注释
COMMENT ON TABLE erp_box_quotation IS '纸箱报价';
COMMENT ON COLUMN erp_box_quotation.id IS '主键ID';
COMMENT ON COLUMN erp_box_quotation.quotation_no IS '报价单号';
COMMENT ON COLUMN erp_box_quotation.customer_id IS '客户ID | erp_customer表的id';
COMMENT ON COLUMN erp_box_quotation.customer_name IS '客户名称';
COMMENT ON COLUMN erp_box_quotation.quotation_date IS '报价日期';
COMMENT ON COLUMN erp_box_quotation.box_length IS '纸箱长度(mm)';
COMMENT ON COLUMN erp_box_quotation.box_width IS '纸箱宽度(mm)';
COMMENT ON COLUMN erp_box_quotation.box_height IS '纸箱高度(mm)';
COMMENT ON COLUMN erp_box_quotation.paperboard_layer_count IS '纸板层数';
COMMENT ON COLUMN erp_box_quotation.box_quantity IS '纸箱数量';
COMMENT ON COLUMN erp_box_quotation.corrugated_shape_group IS '楞型组合';
COMMENT ON COLUMN erp_box_quotation.printing_method IS '字典 | 印刷方式 printing_method：watermark-水印，colorPrint-彩印';
COMMENT ON COLUMN erp_box_quotation.box_id IS '纸箱ID | erp_box表的id';
COMMENT ON COLUMN erp_box_quotation.box_name IS '纸箱名称';
COMMENT ON COLUMN erp_box_quotation.paperboard_supplier_id IS '纸板供应商ID';
COMMENT ON COLUMN erp_box_quotation.paperboard_supplier_name IS '纸板供应商名称';
COMMENT ON COLUMN erp_box_quotation.raw_paper_supplier_id IS '原纸供应商ID';
COMMENT ON COLUMN erp_box_quotation.raw_paper_supplier_name IS '原纸供应商名称';
COMMENT ON COLUMN erp_box_quotation.supplier_discount IS '纸版供应商折扣率';
COMMENT ON COLUMN erp_box_quotation.tax_rate IS '纸板供应商税率';
COMMENT ON COLUMN erp_box_quotation.include_tax IS '字典 | 是否含税：true-含税，false-不含税';
COMMENT ON COLUMN erp_box_quotation.box_area IS '纸箱面积(m²)';
COMMENT ON COLUMN erp_box_quotation.box_volume IS '纸箱体积(m³)';
COMMENT ON COLUMN erp_box_quotation.corrugated_area IS '瓦纸面积(m²)';
COMMENT ON COLUMN erp_box_quotation.total_length IS '纸箱总长(mm)';
COMMENT ON COLUMN erp_box_quotation.box_door_width IS '纸箱门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_door_width IS '模切门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_quantity IS '模切数';
COMMENT ON COLUMN erp_box_quotation.corrugated_door_width IS '瓦纸门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.actual_corrugated_door_width IS '实际瓦纸门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.corrugated_count IS '瓦纸开数';
COMMENT ON COLUMN erp_box_quotation.face_paper_door_width IS '面纸门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.actual_face_paper_door_width IS '实际面纸门宽(mm)';
COMMENT ON COLUMN erp_box_quotation.face_paper_count IS '面纸开数';
COMMENT ON COLUMN erp_box_quotation.tongue_length IS '舌头宽度(mm)';
COMMENT ON COLUMN erp_box_quotation.wave_cap_addition IS '摇盖加分(mm)';
COMMENT ON COLUMN erp_box_quotation.trim_position IS '修边位(mm)';
COMMENT ON COLUMN erp_box_quotation.watermark_colors IS '水印色数';
COMMENT ON COLUMN erp_box_quotation.printing_plate_price IS '印版单价(元/m²)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_plate_price IS '刀版单价(元/m²)';
COMMENT ON COLUMN erp_box_quotation.watermark_price IS '水印单价(元/m²)';
COMMENT ON COLUMN erp_box_quotation.paperboard_price IS '纸板单价(元/m²)';
COMMENT ON COLUMN erp_box_quotation.paperboard_processing_price IS '纸板加工费(元/m²)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_price IS '模切单价(元/张)';
COMMENT ON COLUMN erp_box_quotation.box_closing_type IS '字典 | 封箱方式：nailing-钉箱，gluing-粘箱';
COMMENT ON COLUMN erp_box_quotation.paperboard_json IS '纸板明细JSON数组';
COMMENT ON COLUMN erp_box_quotation.raw_paper_json IS '原纸信息JSON';
COMMENT ON COLUMN erp_box_quotation.printing_process_json IS '印刷JSON数组';
COMMENT ON COLUMN erp_box_quotation.paperboard_unit_price IS '纸板单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.paperboard_processing_unit_price IS '纸板加工费单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.printing_plate_unit_price IS '印版单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_plate_unit_price IS '刀版单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.raw_paper_unit_price IS '原纸单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.watermark_unit_price IS '水印单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_unit_price IS '模切单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.box_closing_unit_price IS '封箱单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.printing_process_unit_price IS '印刷工艺单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.box_unit_price IS '纸箱单价(元/个)';
COMMENT ON COLUMN erp_box_quotation.paperboard_total_price IS '纸板总价(元)';
COMMENT ON COLUMN erp_box_quotation.printing_plate_total_price IS '印版总价(元)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_plate_total_price IS '刀版总价(元)';
COMMENT ON COLUMN erp_box_quotation.raw_paper_total_price IS '原纸总价(元)';
COMMENT ON COLUMN erp_box_quotation.watermark_total_price IS '水印总价(元)';
COMMENT ON COLUMN erp_box_quotation.die_cutting_total_price IS '模切总价(元)';
COMMENT ON COLUMN erp_box_quotation.box_closing_total_price IS '封箱总价(元)';
COMMENT ON COLUMN erp_box_quotation.printing_process_total_price IS '印刷工艺总价(元)';
COMMENT ON COLUMN erp_box_quotation.box_total_price IS '纸箱总价(元)';
COMMENT ON COLUMN erp_box_quotation.total_cost IS '总成本(元)';
COMMENT ON COLUMN erp_box_quotation.profit_margin IS '利润率';
COMMENT ON COLUMN erp_box_quotation.profit_amount IS '利润金额(元)';
COMMENT ON COLUMN erp_box_quotation.status IS '字典 | 报价状态：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，REJECTED-已拒绝';
COMMENT ON COLUMN erp_box_quotation.approval_user_id IS '审批人';
COMMENT ON COLUMN erp_box_quotation.approval_time IS '审批时间';
COMMENT ON COLUMN erp_box_quotation.approval_remark IS '审批备注';
COMMENT ON COLUMN erp_box_quotation.remark IS '备注';
COMMENT ON COLUMN erp_box_quotation.created_by IS '创建人';
COMMENT ON COLUMN erp_box_quotation.updated_by IS '更新人';
COMMENT ON COLUMN erp_box_quotation.create_time IS '创建时间';
COMMENT ON COLUMN erp_box_quotation.update_time IS '更新时间';
COMMENT ON COLUMN erp_box_quotation.deleted_flag IS '删除标志：false-未删除，true-已删除';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_box_quotation_uk_quotation_no ON erp_box_quotation (quotation_no); -- Renamed from uk_quotation_no
CREATE INDEX idx_erp_box_quotation_idx_customer_id ON erp_box_quotation (customer_id); -- Renamed from idx_customer_id
CREATE INDEX idx_erp_box_quotation_idx_quotation_date ON erp_box_quotation (quotation_date); -- Renamed from idx_quotation_date
CREATE INDEX idx_erp_box_quotation_idx_status ON erp_box_quotation (status); -- Renamed from idx_status
CREATE INDEX idx_erp_box_quotation_idx_paperboard_supplier_id ON erp_box_quotation (paperboard_supplier_id); -- Renamed from idx_paperboard_supplier_id
CREATE INDEX idx_erp_box_quotation_idx_raw_paper_supplier_id ON erp_box_quotation (raw_paper_supplier_id); -- Renamed from idx_raw_paper_supplier_id

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_box_quotation
BEFORE UPDATE ON erp_box_quotation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_box_quotation (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_brand.sql
-- ============================================================

-- 表结构: erp_brand
CREATE TABLE erp_brand (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    brand_code VARCHAR(30) NOT NULL,
    brand_name VARCHAR(50) NOT NULL,
    logo_url VARCHAR(255) DEFAULT NULL,
    description VARCHAR(500) DEFAULT NULL,
    website VARCHAR(100) DEFAULT NULL,
    sort INT NOT NULL DEFAULT 0,
    status BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_brand IS '商品品牌';
COMMENT ON COLUMN erp_brand.id IS '品牌ID';
COMMENT ON COLUMN erp_brand.brand_code IS '品牌编码';
COMMENT ON COLUMN erp_brand.brand_name IS '品牌名称';
COMMENT ON COLUMN erp_brand.logo_url IS '品牌Logo';
COMMENT ON COLUMN erp_brand.description IS '品牌描述';
COMMENT ON COLUMN erp_brand.website IS '品牌网站';
COMMENT ON COLUMN erp_brand.sort IS '排序';
COMMENT ON COLUMN erp_brand.status IS '品牌状态：false-禁用，true-启用';
COMMENT ON COLUMN erp_brand.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_brand.create_time IS '创建时间';
COMMENT ON COLUMN erp_brand.update_time IS '更新时间';
COMMENT ON COLUMN erp_brand.created_by IS '创建人';
COMMENT ON COLUMN erp_brand.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_brand_idx_brand_code ON erp_brand (brand_code); -- Renamed from idx_brand_code
CREATE INDEX idx_erp_brand_idx_brand_status ON erp_brand (status); -- Renamed from idx_brand_status

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_brand
BEFORE UPDATE ON erp_brand
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_brand
INSERT INTO erp_brand (id, brand_code, brand_name, logo_url, description, website, sort, status, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, 'B001', '苹果', 'https://example.com/logos/apple.png', '美国知名科技公司', 'https://www.apple.com', 1, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(2, 'B002', '三星', 'https://example.com/logos/samsung.png', '韩国知名电子企业', 'https://www.samsung.com', 2, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(3, 'B003', '华为', 'https://example.com/logos/huawei.png', '中国知名通信企业', 'https://www.huawei.com', 3, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(4, 'B004', '小米', 'https://example.com/logos/xiaomi.png', '中国智能硬件制造商', 'https://www.mi.com', 4, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(5, 'B005', '联想', 'https://example.com/logos/lenovo.png', '中国领先电脑制造商', 'https://www.lenovo.com', 5, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(6, 'B006', '戴尔', 'https://example.com/logos/dell.png', '美国电脑制造商', 'https://www.dell.com', 6, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(7, 'B007', '惠普', 'https://example.com/logos/hp.png', '美国知名电脑品牌', 'https://www.hp.com', 7, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(8, 'B008', '宜家', 'https://example.com/logos/ikea.png', '瑞典家居品牌', 'https://www.ikea.com', 8, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(9, 'B009', '优衣库', 'https://example.com/logos/uniqlo.png', '日本服装品牌', 'https://www.uniqlo.com', 9, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1),
(10, 'B010', '耐克', 'https://example.com/logos/nike.png', '美国体育用品品牌', 'https://www.nike.com', 10, true, false, '2025-03-30 22:25:49', '2025-03-30 22:25:49', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_customer.sql
-- ============================================================

-- 表结构: erp_customer
CREATE TABLE erp_customer (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    customer_code VARCHAR(50) DEFAULT NULL,
    customer_name VARCHAR(100) NOT NULL,
    customer_type VARCHAR(50) NOT NULL, -- Dictionary: REGULAR, VIP, STRATEGIC, DISTRIBUTOR, AGENT
    credit_level VARCHAR(50) DEFAULT NULL, -- Dictionary: AAA, AA, A, BBB, BB, B, CCC, CC, C
    contact_name VARCHAR(50) DEFAULT NULL,
    contact_position VARCHAR(50) DEFAULT NULL,
    contact_department VARCHAR(50) DEFAULT NULL,
    contact_phone VARCHAR(20) DEFAULT NULL,
    contact_mobile VARCHAR(20) DEFAULT NULL,
    contact_email VARCHAR(100) DEFAULT NULL,
    province VARCHAR(50) DEFAULT NULL,
    city VARCHAR(50) DEFAULT NULL,
    district VARCHAR(50) DEFAULT NULL,
    address VARCHAR(200) DEFAULT NULL,
    shipping_address VARCHAR(500) DEFAULT NULL,
    billing_address VARCHAR(500) DEFAULT NULL,
    business_license VARCHAR(50) DEFAULT NULL,
    tax_id VARCHAR(50) DEFAULT NULL,
    bank_name VARCHAR(100) DEFAULT NULL,
    bank_account VARCHAR(50) DEFAULT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE', -- Dictionary: ACTIVE, INACTIVE, BLACKLIST, POTENTIAL
    remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_customer IS '客户信息';
COMMENT ON COLUMN erp_customer.id IS '主键ID';
COMMENT ON COLUMN erp_customer.customer_code IS '客户编码';
COMMENT ON COLUMN erp_customer.customer_name IS '客户名称';
COMMENT ON COLUMN erp_customer.customer_type IS '字典|customer_type 客户类型：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商';
COMMENT ON COLUMN erp_customer.credit_level IS '字典|credit_level 信用等级：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级';
COMMENT ON COLUMN erp_customer.contact_name IS '联系人姓名';
COMMENT ON COLUMN erp_customer.contact_position IS '联系人职位';
COMMENT ON COLUMN erp_customer.contact_department IS '联系人部门';
COMMENT ON COLUMN erp_customer.contact_phone IS '联系人电话';
COMMENT ON COLUMN erp_customer.contact_mobile IS '联系人手机';
COMMENT ON COLUMN erp_customer.contact_email IS '联系人邮箱';
COMMENT ON COLUMN erp_customer.province IS '省份';
COMMENT ON COLUMN erp_customer.city IS '城市';
COMMENT ON COLUMN erp_customer.district IS '区县';
COMMENT ON COLUMN erp_customer.address IS '详细地址';
COMMENT ON COLUMN erp_customer.shipping_address IS '收货地址';
COMMENT ON COLUMN erp_customer.billing_address IS '发票地址';
COMMENT ON COLUMN erp_customer.business_license IS '营业执照号';
COMMENT ON COLUMN erp_customer.tax_id IS '税务登记号';
COMMENT ON COLUMN erp_customer.bank_name IS '开户银行';
COMMENT ON COLUMN erp_customer.bank_account IS '银行账号';
COMMENT ON COLUMN erp_customer.status IS '字典|customer_status 客户状态：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户';
COMMENT ON COLUMN erp_customer.remark IS '备注';
COMMENT ON COLUMN erp_customer.created_by IS '创建人ID';
COMMENT ON COLUMN erp_customer.updated_by IS '更新人ID';
COMMENT ON COLUMN erp_customer.deleted_flag IS '删除标识 false未删除 true已删除';
COMMENT ON COLUMN erp_customer.create_time IS '创建时间';
COMMENT ON COLUMN erp_customer.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_customer_uk_customer_code ON erp_customer (customer_code); -- Renamed from uk_customer_code
CREATE INDEX idx_erp_customer_idx_customer_name ON erp_customer (customer_name); -- Renamed from idx_customer_name
CREATE INDEX idx_erp_customer_idx_customer_type ON erp_customer (customer_type); -- Renamed from idx_customer_type
CREATE INDEX idx_erp_customer_idx_status ON erp_customer (status); -- Renamed from idx_status
CREATE INDEX idx_erp_customer_idx_create_time ON erp_customer (create_time); -- Renamed from idx_create_time

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_customer
BEFORE UPDATE ON erp_customer
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_customer
INSERT INTO erp_customer (id, customer_code, customer_name, customer_type, credit_level, contact_name, contact_position, contact_department, contact_phone, contact_mobile, contact_email, province, city, district, address, shipping_address, billing_address, business_license, tax_id, bank_name, bank_account, status, remark, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(1, '*********', '上海星辰科技有限公司', 'STRATEGIC', 'AAA', '张经理', '采购经理', '采购部', '021-********', '***********', '<EMAIL>', '上海市', '浦东新区', '张江高科技园区', '科苑路88号', '上海市松江区新桥镇新飞路123号3号仓库', '上海市浦东新区张江高科技园区科苑路88号', '91310000XXXXXXXX', '310115XXXXXXXX', '中国银行上海分行', '6225880137XXXXXX', 'ACTIVE', '重要战略客户，提供优先服务', 1, NULL, false, '2023-01-01 10:00:00', '2025-03-14 16:36:47'),
(2, '*********', '北京未来电子有限公司', 'VIP', 'AA', '李总', '总经理', '管理层', '010-********', '***********', '<EMAIL>', '北京市', '海淀区', '中关村', '科技路100号', '北京市通州区张家湾镇工业园区B12号', '北京市海淀区中关村科技路100号', '91110000XXXXXXXX', '110108XXXXXXXX', '工商银行北京分行', '6222020446XXXXXX', 'ACTIVE', 'VIP客户，每月固定订单', 1, NULL, false, '2023-01-05 14:30:00', '2025-03-14 16:36:47'),
(3, 'C20230003', '广州南方包装有限公司', 'REGULAR', 'A', '王经理', '采购经理', '采购部', '020-55556666', '13500123456', '<EMAIL>', '广东省', '广州市', '番禺区', '南浦大道123号', '广东省广州市番禺区南浦大道123号后栋', '广东省广州市番禺区南浦大道123号', '91440000XXXXXXXX', '440113XXXXXXXX', '建设银行广州分行', '6227002145XXXXXX', 'ACTIVE', '常规客户，主要采购纸箱产品', 1, NULL, false, '2023-01-10 09:15:00', '2025-03-14 16:36:47'),
(4, 'C20230004', '深圳市智能科技有限公司', 'STRATEGIC', 'AA', '陈总', '总经理', '管理层', '0755-33334444', '13612345678', '<EMAIL>', '广东省', '深圳市', '南山区', '科技园路88号', '广东省深圳市宝安区航城大道航城工业区A栋', '广东省深圳市南山区科技园路88号', '91440300XXXXXXXX', '440305XXXXXXXX', '招商银行深圳分行', '6225887654XXXXXX', 'ACTIVE', '战略合作伙伴，需定期回访', 1, NULL, false, '2023-01-15 11:20:00', '2025-03-14 16:36:47'),
(5, 'C20230005', '杭州湖畔电商有限公司', 'VIP', 'A', '赵经理', '运营总监', '运营部', '0571-77778888', '13712345678', '<EMAIL>', '浙江省', '杭州市', '西湖区', '文三路478号', '浙江省杭州市萧山区萧山经济开发区红垦路', '浙江省杭州市西湖区文三路478号', '91330000XXXXXXXX', '330106XXXXXXXX', '浙商银行杭州分行', '6228480123XXXXXX', 'ACTIVE', '电商客户，订单量大', 1, NULL, false, '2023-01-20 16:45:00', '2025-03-14 16:36:47'),
(6, 'C20230006', '武汉东湖物流有限公司', 'DISTRIBUTOR', 'BBB', '钱总', '总经理', '管理层', '027-66667777', '13612378945', '<EMAIL>', '湖北省', '武汉市', '洪山区', '珞瑜路766号', '湖北省武汉市东西湖区东西湖大道物流园C区', '湖北省武汉市洪山区珞瑜路766号', '91420000XXXXXXXX', '420111XXXXXXXX', '中信银行武汉分行', '6217856789XXXXXX', 'INACTIVE', '经销商客户，近期合作减少', 1, NULL, false, '2023-01-25 10:30:00', '2025-03-14 16:36:47'),
(7, 'C20230007', '成都西部印刷厂', 'AGENT', 'BB', '孙厂长', '厂长', '管理层', '028-99998888', '13987456321', '<EMAIL>', '四川省', '成都市', '武侯区', '科华北路65号', '四川省成都市武侯区科华北路65号', '四川省成都市武侯区科华北路65号', '91510000XXXXXXXX', '510107XXXXXXXX', '成都银行总行', '6228901234XXXXXX', 'ACTIVE', '代理商，负责西南区域销售', 1, NULL, false, '2023-02-01 09:00:00', '2025-03-14 16:36:47'),
(8, 'C20230008', '天津港湾贸易有限公司', 'REGULAR', 'B', '周经理', '业务经理', '业务部', '022-44445555', '13645678901', '<EMAIL>', '天津市', '滨海新区', '开发区', '第三大街28号', '天津市滨海新区港区天津港保税区A7库', '天津市滨海新区开发区第三大街28号', '91120000XXXXXXXX', '120116XXXXXXXX', '交通银行天津分行', '6222600987XXXXXX', 'POTENTIAL', '潜在大客户，需重点跟进', 1, NULL, false, '2023-02-05 14:20:00', '2025-03-14 16:36:47'),
(9, 'C20230009', '南京东方包装材料有限公司', 'REGULAR', 'BBB', '吴总', '董事长', '管理层', '025-22223333', '13898765432', '<EMAIL>', '江苏省', '南京市', '江宁区', '将军大道29号', '江苏省南京市江宁区将军大道29号后院', '江苏省南京市江宁区将军大道29号', '91320000XXXXXXXX', '320115XXXXXXXX', '江苏银行南京分行', '6225682345XXXXXX', 'BLACKLIST', '曾有拖欠货款记录', 1, NULL, true, '2023-02-10 11:10:00', '2025-03-18 15:06:05'), -- deleted_flag was 1
(10, 'C20230010', '青岛海洋食品有限公司', 'VIP', 'A', '郑经理', '采购总监', '采购部', '0532-11112222', '13756789012', '<EMAIL>', '山东省', '青岛市', '崂山区', '海尔路63号', '山东省青岛市城阳区城阳工业园B5号东门', '山东省青岛市崂山区海尔路63号', '91370000XXXXXXXX', '370212XXXXXXXX', '青岛银行总行', '6228123456XXXXXX', 'ACTIVE', '食品行业VIP客户，对包装要求高', 1, NULL, false, '2023-02-15 15:40:00', '2025-03-14 16:36:47'),
(11, 'xx', 'xx', 'REGULAR', NULL, '', '', '', '', '', '', '天津市', '天津市', '河西区', '', '', '', '', '', '', '', 'ACTIVE', '', NULL, NULL, true, '2025-03-14 19:28:24', '2025-03-16 16:31:12'); -- deleted_flag was 1

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_entity_param.sql
-- ============================================================

-- 表结构: erp_entity_param
CREATE TABLE erp_entity_param (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    entity_type_id BIGINT DEFAULT NULL, -- Foreign key to erp_entity_type
    entity_id BIGINT DEFAULT NULL, -- Foreign key to a specific entity table
    param_type_id BIGINT DEFAULT NULL, -- Foreign key to erp_entity_param_type
    param_value VARCHAR(255) DEFAULT NULL,
    remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_entity_param IS '实体参数';
COMMENT ON COLUMN erp_entity_param.id IS '参数ID';
COMMENT ON COLUMN erp_entity_param.entity_type_id IS '实体类型ID | erp_entity_type的id';
COMMENT ON COLUMN erp_entity_param.entity_id IS '实体ID | 关联具体实体表的id';
COMMENT ON COLUMN erp_entity_param.param_type_id IS '参数类型ID | erp_entity_param_type的id';
COMMENT ON COLUMN erp_entity_param.param_value IS '参数值';
COMMENT ON COLUMN erp_entity_param.remark IS '备注';
COMMENT ON COLUMN erp_entity_param.created_by IS '创建人';
COMMENT ON COLUMN erp_entity_param.updated_by IS '更新人';
COMMENT ON COLUMN erp_entity_param.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_entity_param.create_time IS '创建时间';
COMMENT ON COLUMN erp_entity_param.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_entity_param_uk_entity_param ON erp_entity_param (entity_type_id, entity_id, param_type_id); -- Renamed from uk_entity_param. Comment: 确保每个实体的每种参数类型只有一个值
CREATE INDEX idx_erp_entity_param_idx_entity ON erp_entity_param (entity_type_id, entity_id); -- Renamed from idx_entity. Comment: 实体索引
CREATE INDEX idx_erp_entity_param_idx_param_type ON erp_entity_param (param_type_id); -- Renamed from idx_param_type. Comment: 参数类型索引
CREATE INDEX idx_erp_entity_param_idx_param_deleted ON erp_entity_param (deleted_flag); -- Renamed from idx_param_deleted. Comment: 删除标志索引
CREATE INDEX idx_erp_entity_param_idx_entity_param_type ON erp_entity_param (entity_type_id, param_type_id); -- Renamed from idx_entity_param_type. Comment: 实体类型和参数类型复合索引

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_entity_param
BEFORE UPDATE ON erp_entity_param
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_entity_param (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_entity_param_type.sql
-- ============================================================

-- 表结构: erp_entity_param_type
CREATE TABLE erp_entity_param_type (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    type_code VARCHAR(50) DEFAULT NULL,
    type_name VARCHAR(50) DEFAULT NULL,
    entity_type_id BIGINT DEFAULT NULL, -- Foreign key to erp_entity_type
    data_type VARCHAR(20) DEFAULT 'STRING', -- STRING, INTEGER, DECIMAL, BOOLEAN, DATE, DATETIME, JSON
    default_value VARCHAR(500) DEFAULT NULL,
    validation_rule JSONB DEFAULT NULL, -- Converted from json
    is_required BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    sort INT DEFAULT 0,
    searchable BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    options JSONB DEFAULT NULL, -- Converted from json
    remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_entity_param_type IS '参数类型';
COMMENT ON COLUMN erp_entity_param_type.id IS '参数类型ID';
COMMENT ON COLUMN erp_entity_param_type.type_code IS '参数类型编码';
COMMENT ON COLUMN erp_entity_param_type.type_name IS '参数类型名称';
COMMENT ON COLUMN erp_entity_param_type.entity_type_id IS '实体类型ID | erp_entity_type的id';
COMMENT ON COLUMN erp_entity_param_type.data_type IS '数据类型：STRING-字符串，INTEGER-整数，DECIMAL-小数，BOOLEAN-布尔值，DATE-日期，DATETIME-日期时间，JSON-JSON数据';
COMMENT ON COLUMN erp_entity_param_type.default_value IS '默认值';
COMMENT ON COLUMN erp_entity_param_type.validation_rule IS '验证规则(JSONB格式，包含正则表达式、最小/最大值等验证信息)';
COMMENT ON COLUMN erp_entity_param_type.is_required IS '是否必填：false-非必填，true-必填';
COMMENT ON COLUMN erp_entity_param_type.sort IS '排序';
COMMENT ON COLUMN erp_entity_param_type.searchable IS '是否可搜索：false-否，true-是';
COMMENT ON COLUMN erp_entity_param_type.options IS '选项值列表(JSONB)，适用于下拉选择类型';
COMMENT ON COLUMN erp_entity_param_type.remark IS '备注';
COMMENT ON COLUMN erp_entity_param_type.created_by IS '创建人';
COMMENT ON COLUMN erp_entity_param_type.updated_by IS '更新人';
COMMENT ON COLUMN erp_entity_param_type.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_entity_param_type.create_time IS '创建时间';
COMMENT ON COLUMN erp_entity_param_type.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_entity_param_type_erp_param_type_pk ON erp_entity_param_type (type_code); -- Renamed from erp_param_type_pk
CREATE INDEX idx_erp_entity_param_type_idx_param_type_code ON erp_entity_param_type (type_code); -- Renamed from idx_param_type_code
CREATE INDEX idx_erp_entity_param_type_idx_param_type_name ON erp_entity_param_type (type_name); -- Renamed from idx_param_type_name
CREATE INDEX idx_erp_entity_param_type_idx_param_data_type ON erp_entity_param_type (data_type); -- Renamed from idx_param_data_type
CREATE INDEX idx_erp_entity_param_type_idx_param_required ON erp_entity_param_type (is_required); -- Renamed from idx_param_required
CREATE INDEX idx_erp_entity_param_type_idx_param_sort ON erp_entity_param_type (sort); -- Renamed from idx_param_sort
CREATE INDEX idx_erp_entity_param_type_idx_param_deleted ON erp_entity_param_type (deleted_flag); -- Renamed from idx_param_deleted

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_entity_param_type
BEFORE UPDATE ON erp_entity_param_type
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_entity_param_type
INSERT INTO erp_entity_param_type (id, type_code, type_name, entity_type_id, data_type, default_value, validation_rule, is_required, sort, searchable, options, remark, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(1, 'quotation', '报价参数', 14, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:46:05', '2025-03-31 21:47:24'),
(2, NULL, '摇盖加分 (mm)：', 14, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:47:53', '2025-03-31 21:58:59'),
(3, NULL, '舌头宽度 (mm)：', 14, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:47:53', '2025-03-31 22:00:30'),
(4, NULL, '钉箱单价(元/个)：', 14, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:47:53', '2025-03-31 22:01:08'),
(5, NULL, '水印单价(元/m²)：', 14, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:47:54', '2025-03-31 22:00:30'),
(6, NULL, '模切单价(元/张)：', NULL, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 21:59:45', '2025-03-31 22:00:30'),
(7, NULL, NULL, NULL, 'STRING', NULL, NULL, false, 0, false, NULL, NULL, NULL, NULL, false, '2025-03-31 22:00:30', '2025-03-31 22:00:30');

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_entity_type.sql
-- ============================================================

-- 表结构: erp_entity_type
CREATE TABLE erp_entity_type (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    type_code VARCHAR(50) DEFAULT NULL,
    type_name VARCHAR(50) DEFAULT NULL,
    module VARCHAR(50) DEFAULT NULL,
    is_system BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    allow_custom_param BOOLEAN DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'
    remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_entity_type IS '实体类型';
COMMENT ON COLUMN erp_entity_type.id IS '实体类型ID';
COMMENT ON COLUMN erp_entity_type.type_code IS '实体类型编码';
COMMENT ON COLUMN erp_entity_type.type_name IS '实体类型名称';
COMMENT ON COLUMN erp_entity_type.module IS '所属模块';
COMMENT ON COLUMN erp_entity_type.is_system IS '是否系统内置：false-否，true-是';
COMMENT ON COLUMN erp_entity_type.allow_custom_param IS '是否允许自定义参数：false-否，true-是';
COMMENT ON COLUMN erp_entity_type.remark IS '备注';
COMMENT ON COLUMN erp_entity_type.created_by IS '创建人';
COMMENT ON COLUMN erp_entity_type.updated_by IS '更新人';
COMMENT ON COLUMN erp_entity_type.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_entity_type.create_time IS '创建时间';
COMMENT ON COLUMN erp_entity_type.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_entity_type_erp_entity_type_pk ON erp_entity_type (type_code); -- Renamed from erp_entity_type_pk
CREATE INDEX idx_erp_entity_type_idx_entity_type_code ON erp_entity_type (type_code); -- Renamed from idx_entity_type_code
CREATE INDEX idx_erp_entity_type_idx_entity_type_name ON erp_entity_type (type_name); -- Renamed from idx_entity_type_name
CREATE INDEX idx_erp_entity_type_idx_entity_module ON erp_entity_type (module); -- Renamed from idx_entity_module
CREATE INDEX idx_erp_entity_type_idx_entity_deleted ON erp_entity_type (deleted_flag); -- Renamed from idx_entity_deleted

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_entity_type
BEFORE UPDATE ON erp_entity_type
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_entity_type
INSERT INTO erp_entity_type (id, type_code, type_name, module, is_system, allow_custom_param, remark, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(13, 'box_type', '箱型', 'ERP', false, true, NULL, NULL, NULL, false, '2025-03-31 18:30:20', '2025-03-31 18:30:20'),
(14, 'quotation_param', '报价参数', 'ERP', false, true, NULL, NULL, NULL, false, '2025-03-31 18:30:35', '2025-03-31 21:47:10');

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_entity_type_param_relation.sql
-- ============================================================

-- 表结构: erp_entity_type_param_relation
CREATE TABLE erp_entity_type_param_relation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    entity_type_id BIGINT NOT NULL, -- Foreign key to erp_entity_type
    param_type_id BIGINT NOT NULL, -- Foreign key to erp_entity_param_type
    sort INT DEFAULT 0,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_entity_type_param_relation IS '实体类型与参数类型关联表';
COMMENT ON COLUMN erp_entity_type_param_relation.id IS '关联ID';
COMMENT ON COLUMN erp_entity_type_param_relation.entity_type_id IS '实体类型ID | erp_entity_type的id';
COMMENT ON COLUMN erp_entity_type_param_relation.param_type_id IS '参数类型ID | erp_entity_param_type的id';
COMMENT ON COLUMN erp_entity_type_param_relation.sort IS '排序';
COMMENT ON COLUMN erp_entity_type_param_relation.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_entity_type_param_relation.create_time IS '创建时间';
COMMENT ON COLUMN erp_entity_type_param_relation.update_time IS '更新时间';
COMMENT ON COLUMN erp_entity_type_param_relation.created_by IS '创建人';
COMMENT ON COLUMN erp_entity_type_param_relation.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_entity_type_param_relation_uk_entity_param_relation ON erp_entity_type_param_relation (entity_type_id, param_type_id); -- Renamed from uk_entity_param_relation. Comment: 确保每个实体类型和参数类型唯一
CREATE INDEX idx_erp_entity_type_param_relation_idx_entity_type ON erp_entity_type_param_relation (entity_type_id); -- Renamed from idx_entity_type
CREATE INDEX idx_erp_entity_type_param_relation_idx_param_type ON erp_entity_type_param_relation (param_type_id); -- Renamed from idx_param_type

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_entity_type_param_relation
BEFORE UPDATE ON erp_entity_type_param_relation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_entity_type_param_relation (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_paper.sql
-- ============================================================

-- 表结构: erp_paper
CREATE TABLE erp_paper (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    description VARCHAR(255) DEFAULT NULL,
    code VARCHAR(50) DEFAULT NULL,
    paper_category VARCHAR(50) NOT NULL, -- Dictionary: paper_category
    corrugated_shape VARCHAR(20) DEFAULT NULL, -- Dictionary: corrugated_shape
    color VARCHAR(20) DEFAULT '#FFFFFF',
    weight VARCHAR(20) DEFAULT NULL, -- Dictionary: paper_weight (克重g)
    status BOOLEAN DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'. Dictionary: paper_status
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_paper IS '纸张基础信息表';
COMMENT ON COLUMN erp_paper.id IS '纸张ID';
COMMENT ON COLUMN erp_paper.description IS '描述';
COMMENT ON COLUMN erp_paper.code IS '纸张编码';
COMMENT ON COLUMN erp_paper.paper_category IS '字典|paper_category 纸张类别';
COMMENT ON COLUMN erp_paper.corrugated_shape IS '字典|corrugated_shape 楞形 ';
COMMENT ON COLUMN erp_paper.color IS '颜色代码';
COMMENT ON COLUMN erp_paper.weight IS '字典|paper_weight 克重(g)';
COMMENT ON COLUMN erp_paper.status IS '字典|paper_status 状态 (true=1)';
COMMENT ON COLUMN erp_paper.created_by IS '创建人';
COMMENT ON COLUMN erp_paper.updated_by IS '更新人';
COMMENT ON COLUMN erp_paper.deleted_flag IS '删除标志 (false=0, true=1)';
COMMENT ON COLUMN erp_paper.create_time IS '创建时间';
COMMENT ON COLUMN erp_paper.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_paper_uk_code ON erp_paper (code); -- Renamed from uk_code

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_paper
BEFORE UPDATE ON erp_paper
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_paper
INSERT INTO erp_paper (id, description, code, paper_category, corrugated_shape, color, weight, status, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(1, '白卡 135g', 'V1', 'white_card_board', NULL, '#f0f0f0', '135', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:34:16'),
(2, '白卡 170g', 'V2', 'white_card_board', NULL, '#f5f5f5', '170', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:34:16'),
(3, '牛卡 230g', 'K1', 'kraft_liner_board', NULL, '#d4a76a', '230', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(4, '牛卡 160g', 'K2', 'kraft_liner_board', NULL, '#c49b63', '160', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(5, '牛卡 190g', 'H1', 'kraft_liner_board', NULL, '#b38b59', '190', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(6, '牛卡 145g', 'D1', 'kraft_liner_board', NULL, '#b38b59', '145', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(7, '牛卡 115g', 'B1', 'kraft_liner_board', NULL, '#b38b59', '115', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(8, '牛卡 85g', 'A1', 'kraft_liner_board', NULL, '#b38b59', '85', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:33:22'),
(9, '特种纸 75g', 'C1', 'specialty', NULL, '#d2b48c', '75', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-14 22:20:48'),
(10, '涂布 100g', 'C-100', 'coated_white_board', '', '#ffffff', '100', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:34:34'),
(11, '涂布 120g', 'C-120', 'coated_white_board', NULL, '#ffffff', '120', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-23 22:34:34'),
(12, '高瓦 中纸 47g', '0', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '47', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(13, '高瓦 中纸 80g', '2-middle', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '80', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(14, '高瓦 B瓦 80g', '2-B', 'high_strength_corrugating_medium', 'B', '#a9a9a9', '80', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(15, '高瓦 C瓦 80g', '2-C', 'high_strength_corrugating_medium', 'C', '#a9a9a9', '80', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(16, '高瓦 E瓦 80g', '2-E', 'high_strength_corrugating_medium', 'E', '#a9a9a9', '80', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(17, '高瓦 中纸 95g', '3-middle', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '95', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(18, '高瓦 B瓦 95g', '3-B', 'high_strength_corrugating_medium', 'B', '#a9a9a9', '95', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(19, '高瓦 C瓦 95g', '3-C', 'high_strength_corrugating_medium', 'C', '#a9a9a9', '95', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(20, '高瓦 E瓦 95g', '3-E', 'high_strength_corrugating_medium', 'E', '#a9a9a9', '95', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(21, '高瓦 中纸 110g', '4-middle', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '110', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(22, '高瓦 B瓦 110g', '4-B', 'high_strength_corrugating_medium', 'B', '#a9a9a9', '110', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(23, '高瓦 C瓦 110g', '4-C', 'high_strength_corrugating_medium', 'C', '#a9a9a9', '110', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(24, '高瓦 E瓦 110g', '4-E', 'high_strength_corrugating_medium', 'E', '#a9a9a9', '110', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(25, '高瓦 中纸 130g', '6-middle', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '130', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(26, '高瓦 B瓦 130g', '6-B', 'high_strength_corrugating_medium', 'B', '#a9a9a9', '130', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(27, '高瓦 C瓦 130g', '6-C', 'high_strength_corrugating_medium', 'C', '#a9a9a9', '130', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(28, '高瓦 E瓦 130g', '6-E', 'high_strength_corrugating_medium', 'E', '#a9a9a9', '130', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(29, '高瓦 中纸 160g', '8-middle', 'high_strength_corrugating_medium', 'middle', '#a9a9a9', '160', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(30, '高瓦 C瓦 160g', '8-C', 'high_strength_corrugating_medium', 'C', '#a9a9a9', '160', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(31, '高瓦 E瓦 160g', '8-E', 'high_strength_corrugating_medium', 'E', '#a9a9a9', '160', true, 1, 1, false, '2025-03-14 22:20:48', '2025-03-21 15:48:17'),
(1042, '博汇铜版 95g', 'AP_001', 'art_paper', NULL, '#FFFFFF', '95', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1043, '博汇铜版 105g', 'AP_002', 'art_paper', NULL, '#FFFFFF', '105', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1044, '博汇铜版 110g', 'AP_003', 'art_paper', NULL, '#FFFFFF', '110', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1045, '博汇铜版 115g', 'AP_004', 'art_paper', NULL, '#FFFFFF', '115', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1046, '博汇铜版 128g', 'AP_005', 'art_paper', NULL, '#FFFFFF', '128', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1047, '博汇铜版 150g', 'AP_006', 'art_paper', NULL, '#FFFFFF', '150', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1048, '博汇铜版 230g', 'AP_007', 'art_paper', NULL, '#FFFFFF', '230', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1049, '博汇铜版 250g', 'AP_008', 'art_paper', NULL, '#FFFFFF', '250', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1050, '博汇铜版 300g', 'AP_009', 'art_paper', NULL, '#FFFFFF', '300', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1051, '博汇铜版 350g', 'AP_010', 'art_paper', NULL, '#FFFFFF', '350', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1052, '高松铜版 230g', 'AP_011', 'art_paper', NULL, '#FFFFFF', '230', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1053, '高松铜版 270g', 'AP_012', 'art_paper', NULL, '#FFFFFF', '270', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1054, '高松铜版 320g', 'AP_013', 'art_paper', NULL, '#FFFFFF', '320', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1055, '高松铜版 250g', 'AP_014', 'art_paper', NULL, '#FFFFFF', '250', true, NULL, NULL, false, '2025-03-23 15:43:31', '2025-03-23 15:43:31'),
(1056, 'rnm', 'xxx', 'white_card_board', '', '#FFFFFF', '', true, NULL, NULL, true, '2025-03-27 21:01:23', '2025-03-27 22:31:08'), -- deleted_flag was 1
(1057, 'mm', 'mm', 'coated_white_board', NULL, '#FFFFFF', NULL, true, NULL, NULL, true, '2025-03-27 22:33:18', '2025-03-27 22:33:21'), -- deleted_flag was 1
(1058, '高瓦 B 105g', 'hh', 'high_strength_corrugating_medium', 'B', '#FFFFFF', '105', true, NULL, NULL, false, '2025-03-29 15:53:12', '2025-03-29 15:53:12'),
(1060, '玖龙T级 75g', 'C', 'kraft_liner_board', NULL, '#FFFFFF', '75', true, NULL, NULL, false, '2025-03-29 16:03:02', '2025-03-29 16:03:02'),
(1061, '高瓦 B瓦 160g', '8', 'high_strength_corrugating_medium', 'B', '#FFFFFF', '160', true, NULL, NULL, false, '2025-03-30 10:55:01', '2025-03-30 10:55:01'),
(1062, '230g蓝叶', '230g蓝叶', 'coated_white_board', NULL, '#FFFFFF', '230', true, NULL, NULL, false, '2025-03-30 10:59:33', '2025-03-30 10:59:33');

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_paper_quotation.sql
-- ============================================================

-- 表结构: erp_paper_quotation
CREATE TABLE erp_paper_quotation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    quotation_no VARCHAR(32) NOT NULL,
    supplier_id BIGINT NOT NULL,
    supplier_name VARCHAR(100) NOT NULL,
    quotation_date DATE NOT NULL,
    quotation_type VARCHAR(20) NOT NULL DEFAULT 'CORRUGATED', -- Dictionary: CORRUGATED, RAW
    supplier_discount DECIMAL(5,2) DEFAULT 0.88,
    tax_rate DECIMAL(5,2) DEFAULT 0.94,
    processing_fee_two_layer DECIMAL(10,2) DEFAULT 0.00,
    processing_fee_three_layer DECIMAL(10,2) DEFAULT 0.00,
    processing_fee_four_layer DECIMAL(10,2) DEFAULT 0.00,
    processing_fee_five_layer DECIMAL(10,2) DEFAULT 0.00,
    processing_fee_six_layer DECIMAL(10,2) DEFAULT 0.00,
    processing_fee_seven_layer DECIMAL(10,2) DEFAULT 0.00,
    paper_category_corrugated_shape JSONB DEFAULT NULL, -- Converted from json. Dictionary: paper_category_corrugated_shape
    remark VARCHAR(500) DEFAULT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT', -- Dictionary: DRAFT, SUBMITTED, APPROVED, REJECTED, EXPIRED
    approval_user_id BIGINT DEFAULT NULL,
    approval_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- Converted from datetime
    approval_remark VARCHAR(500) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    deleted_flag BOOLEAN NOT NULL DEFAULT false -- Converted from tinyint(1) DEFAULT '0'
);

-- 添加表和列的注释
COMMENT ON TABLE erp_paper_quotation IS '纸张报价';
COMMENT ON COLUMN erp_paper_quotation.id IS '主键ID';
COMMENT ON COLUMN erp_paper_quotation.quotation_no IS '报价单号';
COMMENT ON COLUMN erp_paper_quotation.supplier_id IS '供应商ID';
COMMENT ON COLUMN erp_paper_quotation.supplier_name IS '供应商名称';
COMMENT ON COLUMN erp_paper_quotation.quotation_date IS '报价日期';
COMMENT ON COLUMN erp_paper_quotation.quotation_type IS '字典 | quotation_type 报价类型：CORRUGATED-瓦纸报价，RAW-原纸报价';
COMMENT ON COLUMN erp_paper_quotation.supplier_discount IS '供应商折扣率';
COMMENT ON COLUMN erp_paper_quotation.tax_rate IS '税率';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_two_layer IS '每平米2层加工费';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_three_layer IS '每平米3层加工费';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_four_layer IS '每平米4层加工费';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_five_layer IS '每平米5层加工费';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_six_layer IS '每平米6层加工费';
COMMENT ON COLUMN erp_paper_quotation.processing_fee_seven_layer IS '每平米7层加工费';
COMMENT ON COLUMN erp_paper_quotation.paper_category_corrugated_shape IS '字典|paper_category_corrugated_shape 瓦纸类别楞型 (JSONB)';
COMMENT ON COLUMN erp_paper_quotation.remark IS '备注';
COMMENT ON COLUMN erp_paper_quotation.status IS '字典 | quotation_status 状态：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，REJECTED-已拒绝，EXPIRED-已过期';
COMMENT ON COLUMN erp_paper_quotation.approval_user_id IS '审批人ID';
COMMENT ON COLUMN erp_paper_quotation.approval_time IS '审批时间';
COMMENT ON COLUMN erp_paper_quotation.approval_remark IS '审批备注';
COMMENT ON COLUMN erp_paper_quotation.created_by IS '创建人';
COMMENT ON COLUMN erp_paper_quotation.updated_by IS '更新人';
COMMENT ON COLUMN erp_paper_quotation.create_time IS '创建时间';
COMMENT ON COLUMN erp_paper_quotation.update_time IS '更新时间';
COMMENT ON COLUMN erp_paper_quotation.deleted_flag IS '删除标识(false未删除/true已删除)';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_paper_quotation_uk_quotation_no ON erp_paper_quotation (quotation_no); -- Renamed from uk_quotation_no
CREATE INDEX idx_erp_paper_quotation_idx_quotation_date ON erp_paper_quotation (quotation_date); -- Renamed from idx_quotation_date
CREATE INDEX idx_erp_paper_quotation_idx_status ON erp_paper_quotation (status); -- Renamed from idx_status
CREATE INDEX idx_erp_paper_quotation_idx_supplier_id ON erp_paper_quotation (supplier_id); -- Renamed from idx_supplier_id
CREATE INDEX idx_erp_paper_quotation_idx_quotation_type ON erp_paper_quotation (quotation_type); -- Renamed from idx_quotation_type

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_paper_quotation
BEFORE UPDATE ON erp_paper_quotation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_paper_quotation
INSERT INTO erp_paper_quotation (id, quotation_no, supplier_id, supplier_name, quotation_date, quotation_type, supplier_discount, tax_rate, processing_fee_two_layer, processing_fee_three_layer, processing_fee_four_layer, processing_fee_five_layer, processing_fee_six_layer, processing_fee_seven_layer, paper_category_corrugated_shape, remark, status, approval_user_id, approval_time, approval_remark, created_by, updated_by, create_time, update_time, deleted_flag) VALUES
(1, 'QT1742399347681429930', 17, '福建榕升纸业有限公司', '2025-02-27', 'CORRUGATED', 0.88, 0.94, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, '["coated_middle", "coated_B", "coated_C", "coated_E"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-19 23:49:07', '2025-03-20 00:38:29', true), -- deleted_flag was 1
(2, 'QT1742401947151590418', 17, '福建榕升纸业有限公司', '2025-03-14', 'CORRUGATED', 0.88, 0.94, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, '["coated_C", "coated_E", "coated_middle", "coated_B"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-20 00:32:27', '2025-03-20 00:38:26', true), -- deleted_flag was 1
(3, 'QT1742402285877436711', 17, '福建榕升纸业有限公司', '2025-03-05', 'CORRUGATED', 0.88, 0.94, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, '["coated_middle"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-20 00:38:05', '2025-03-20 00:38:25', true), -- deleted_flag was 1
(4, 'QT1742402293010794701', 17, '福建榕升纸业有限公司', '2025-03-05', 'CORRUGATED', 0.88, 0.94, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, '["coated_B"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-20 00:38:12', '2025-03-20 00:38:23', true), -- deleted_flag was 1
(5, 'QT1742402378229515647', 17, '福建榕升纸业有限公司', '2025-03-12', 'CORRUGATED', 1.00, 0.94, 0.15, 0.25, 0.40, 0.40, 0.70, 0.70, '["coated_middle", "coated_E", "coated_C", "coated_B", "kraft_liner_board", "white_card_board"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-20 00:39:38', '2025-03-20 00:39:38', false),
(7, 'QT1742715907622184935', 18, '福州宏良纸业有限公司', '2025-03-05', 'CORRUGATED', 0.88, 0.94, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, '["art_paper"]'::jsonb, '', 'DRAFT', NULL, NULL, NULL, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 15:45:07', false);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_paper_quotation_item.sql
-- ============================================================

-- 表结构: erp_paper_quotation_item
CREATE TABLE erp_paper_quotation_item (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    quotation_id BIGINT NOT NULL, -- Foreign key to erp_paper_quotation
    paper_id BIGINT NOT NULL, -- Foreign key to erp_paper
    paper_description VARCHAR(255) DEFAULT NULL,
    paper_category VARCHAR(50) NOT NULL, -- Dictionary: paper_category
    corrugated_shape VARCHAR(20) DEFAULT NULL, -- Dictionary: corrugated_shape
    unit VARCHAR(20) NOT NULL DEFAULT 'SQUARE_METER', -- Dictionary: SQUARE_METER, TON, SHEET
    unit_price DECIMAL(12,2) NOT NULL,
    sort INT DEFAULT 0,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    deleted_flag BOOLEAN NOT NULL DEFAULT false -- Converted from tinyint(1) DEFAULT '0'
);

-- 添加表和列的注释
COMMENT ON TABLE erp_paper_quotation_item IS '纸张报价详情表';
COMMENT ON COLUMN erp_paper_quotation_item.id IS '主键ID';
COMMENT ON COLUMN erp_paper_quotation_item.quotation_id IS '报价单ID';
COMMENT ON COLUMN erp_paper_quotation_item.paper_id IS '纸张ID';
COMMENT ON COLUMN erp_paper_quotation_item.paper_description IS '纸张描述';
COMMENT ON COLUMN erp_paper_quotation_item.paper_category IS '字典|paper_category 纸张类别';
COMMENT ON COLUMN erp_paper_quotation_item.corrugated_shape IS '字典|corrugated_shape 楞形 ';
COMMENT ON COLUMN erp_paper_quotation_item.unit IS '字典 | paper_unit 单位：SQUARE_METER-平米，TON-吨，SHEET-张';
COMMENT ON COLUMN erp_paper_quotation_item.unit_price IS '单价';
COMMENT ON COLUMN erp_paper_quotation_item.sort IS '排序';
COMMENT ON COLUMN erp_paper_quotation_item.created_by IS '创建人';
COMMENT ON COLUMN erp_paper_quotation_item.updated_by IS '更新人';
COMMENT ON COLUMN erp_paper_quotation_item.create_time IS '创建时间';
COMMENT ON COLUMN erp_paper_quotation_item.update_time IS '更新时间';
COMMENT ON COLUMN erp_paper_quotation_item.deleted_flag IS '删除标识(false未删除/true已删除)';

-- 创建索引
CREATE INDEX idx_erp_paper_quotation_item_idx_quotation_id ON erp_paper_quotation_item (quotation_id); -- Renamed from idx_quotation_id
CREATE INDEX idx_erp_paper_quotation_item_idx_paper_id ON erp_paper_quotation_item (paper_id); -- Renamed from idx_paper_id

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_paper_quotation_item
BEFORE UPDATE ON erp_paper_quotation_item
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_paper_quotation_item
-- 注意: MySQL 中的 'm²' 在 PostgreSQL 中作为普通字符串处理
INSERT INTO erp_paper_quotation_item (id, quotation_id, paper_id, paper_description, paper_category, corrugated_shape, unit, unit_price, sort, created_by, updated_by, create_time, update_time, deleted_flag) VALUES
(228, 7, 1055, '高松铜版 250g', 'art_paper', '', 'm²', 2.00, 1, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(229, 7, 1042, '博汇铜版 95g', 'art_paper', '', 'm²', 2.00, 2, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(230, 7, 1043, '博汇铜版 105g', 'art_paper', '', 'm²', 2.00, 3, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(231, 7, 1044, '博汇铜版 110g', 'art_paper', '', 'm²', 2.00, 4, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(232, 7, 1045, '博汇铜版 115g', 'art_paper', '', 'm²', 2.00, 5, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(233, 7, 1046, '博汇铜版 128g', 'art_paper', '', 'm²', 2.00, 6, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(234, 7, 1047, '博汇铜版 150g', 'art_paper', '', 'm²', 2.00, 7, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(235, 7, 1048, '博汇铜版 230g', 'art_paper', '', 'm²', 2.00, 8, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(236, 7, 1049, '博汇铜版 250g', 'art_paper', '', 'm²', 2.00, 9, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(237, 7, 1050, '博汇铜版 300g', 'art_paper', '', 'm²', 2.00, 10, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(238, 7, 1051, '博汇铜版 350g', 'art_paper', '', 'm²', 2.00, 11, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(239, 7, 1052, '高松铜版 230g', 'art_paper', '', 'm²', 2.00, 12, NULL, NULL, '2025-03-23 15:45:07', '2025-03-23 18:00:22', false),
(240, 7, 1053, '高松铜版 270g', 'art_paper', '', 'm²', 2.00, 13, NULL, NULL, '2025-03-23 15:45:08', '2025-03-23 18:00:22', false),
(241, 7, 1054, '高松铜版 320g', 'art_paper', '', 'm²', 2.00, 14, NULL, NULL, '2025-03-23 15:45:08', '2025-03-23 18:00:22', false),
(464, 5, 21, '高瓦 中纸 110g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.35, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(465, 5, 17, '高瓦 中纸 95g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.31, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(466, 5, 13, '高瓦 中纸 80g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.28, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(467, 5, 12, '高瓦 中纸 47g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.19, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(468, 5, 25, '高瓦 中纸 130g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.40, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(469, 5, 29, '高瓦 中纸 160g', 'high_strength_corrugating_medium', 'middle', 'm²', 0.49, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(470, 5, 28, '高瓦 E瓦 130g', 'high_strength_corrugating_medium', 'E', 'm²', 0.53, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(471, 5, 16, '高瓦 E瓦 80g', 'high_strength_corrugating_medium', 'E', 'm²', 0.35, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(472, 5, 24, '高瓦 E瓦 110g', 'high_strength_corrugating_medium', 'E', 'm²', 0.45, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(473, 5, 20, '高瓦 E瓦 95g', 'high_strength_corrugating_medium', 'E', 'm²', 0.41, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(474, 5, 31, '高瓦 E瓦 160g', 'high_strength_corrugating_medium', 'E', 'm²', 0.65, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(475, 5, 19, '高瓦 C瓦 95g', 'high_strength_corrugating_medium', 'C', 'm²', 0.43, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(476, 5, 23, '高瓦 C瓦 110g', 'high_strength_corrugating_medium', 'C', 'm²', 0.47, 0, NULL, NULL, '2025-03-25 15:10:46', '2025-03-25 15:10:46', false),
(477, 5, 30, '高瓦 C瓦 160g', 'high_strength_corrugating_medium', 'C', 'm²', 0.67, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(478, 5, 15, '高瓦 C瓦 80g', 'high_strength_corrugating_medium', 'C', 'm²', 0.37, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(479, 5, 27, '高瓦 C瓦 130g', 'high_strength_corrugating_medium', 'C', 'm²', 0.55, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(480, 5, 26, '高瓦 B瓦 130g', 'high_strength_corrugating_medium', 'B', 'm²', 0.55, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(481, 5, 18, '高瓦 B瓦 95g', 'high_strength_corrugating_medium', 'B', 'm²', 0.43, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(482, 5, 14, '高瓦 B瓦 80g', 'high_strength_corrugating_medium', 'B', 'm²', 0.37, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(483, 5, 22, '高瓦 B瓦 110g', 'high_strength_corrugating_medium', 'B', 'm²', 0.47, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(484, 5, 3, '牛卡 230g', 'kraft_liner_board', NULL, 'm²', 0.95, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(485, 5, 4, '牛卡 160g', 'kraft_liner_board', NULL, 'm²', 0.55, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(486, 5, 5, '牛卡 190g', 'kraft_liner_board', NULL, 'm²', 0.65, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(487, 5, 6, '牛卡 145g', 'kraft_liner_board', NULL, 'm²', 0.45, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(488, 5, 7, '牛卡 115g', 'kraft_liner_board', NULL, 'm²', 0.36, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(489, 5, 8, '牛卡 85g', 'kraft_liner_board', NULL, 'm²', 0.31, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(490, 5, 2, '白卡 170g', 'white_card_board', NULL, 'm²', 0.68, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false),
(491, 5, 1, '白卡 135g', 'white_card_board', NULL, 'm²', 0.56, 0, NULL, NULL, '2025-03-25 15:10:47', '2025-03-25 15:10:47', false);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_param_json.sql
-- ============================================================

-- 表结构: erp_param_json
CREATE TABLE erp_param_json (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    param_json JSONB DEFAULT NULL, -- Converted from json
    relation_type VARCHAR(20) DEFAULT NULL,
    relation_id BIGINT DEFAULT NULL, -- ID of the related object
    source_id BIGINT DEFAULT NULL, -- ID of the record defining the JSON structure
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. Note: MySQL ON UPDATE CURRENT_TIMESTAMP removed, behavior assumed not needed or handled elsewhere.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_param_json IS 'json参数';
COMMENT ON COLUMN erp_param_json.id IS 'id';
COMMENT ON COLUMN erp_param_json.param_json IS '参数json (JSONB)';
COMMENT ON COLUMN erp_param_json.relation_type IS '关系类型';
COMMENT ON COLUMN erp_param_json.relation_id IS '关系id json对应的关系对象的id';
COMMENT ON COLUMN erp_param_json.source_id IS '记录数据结构的json的id';
COMMENT ON COLUMN erp_param_json.created_by IS '创建人';
COMMENT ON COLUMN erp_param_json.updated_by IS '更新人';
COMMENT ON COLUMN erp_param_json.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_param_json.create_time IS '创建时间';
COMMENT ON COLUMN erp_param_json.update_time IS '更新时间';

-- 插入数据: erp_param_json
INSERT INTO erp_param_json (id, param_json, relation_type, relation_id, source_id, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(2, '{"id": 2, "max": 101, "min": 4, "code": "tongueLength", "name": "舌头宽度(mm)", "step": 2, "value": 19, "orderNum": 1, "groupCode": "size", "description": "舌头宽度", "relationType": "box_quotation", "decimalPlaces": 0}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(3, '{"id": 3, "max": 100, "min": 3, "code": "trimPosition", "name": "修边位(mm)", "step": 1, "value": 19, "orderNum": 2, "groupCode": "size", "description": "修边位", "relationType": "box_quotation", "decimalPlaces": 0}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(4, '{"id": 4, "max": 100, "min": 5, "code": "waveCapAddition", "name": "摇盖加分(mm)", "step": 1, "value": 5, "orderNum": 3, "groupCode": "size", "description": "摇盖加分", "relationType": "box_quotation", "decimalPlaces": 0}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(5, '{"id": 5, "max": 10, "min": 0.1, "code": "boxGluingUnitPrice", "name": "粘箱单价(元/个)", "step": 0.06, "value": 0.22, "orderNum": 4, "groupCode": "price", "description": "粘箱单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(6, '{"id": 6, "max": 10, "min": 0.08, "code": "boxNailingUnitPrice", "name": "钉箱单价(元/个)", "step": 0.05, "value": 0.13, "orderNum": 5, "groupCode": "price", "description": "钉箱单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(7, '{"id": 7, "max": 10, "min": 0, "code": "dieCuttingUnitPrice", "name": "模切单价(元/张)", "step": 0.01, "value": 0.03, "orderNum": 6, "groupCode": "price", "description": "模切单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(8, '{"id": 8, "max": 10, "min": 0, "code": "platePrice", "name": "印版单价(元/m²)", "step": 0.01, "value": 0.05, "orderNum": 7, "groupCode": "price", "description": "印版单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(9, '{"id": 9, "max": 10, "min": 0, "code": "dieCuttingPlatePrice", "name": "刀版单价(元/m²)", "step": 0.01, "value": 0.05, "orderNum": 8, "groupCode": "price", "description": "刀版单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(10, '{"id": 10, "max": 10, "min": 0, "code": "printingPlatePrice", "name": "水印单价(元/m²)", "step": 0.01, "value": 0.21, "orderNum": 9, "groupCode": "price", "description": "水印单价", "relationType": "box_quotation", "decimalPlaces": 2}'::jsonb, 'box_quotation', NULL, NULL, NULL, NULL, false, '2025-04-02 15:23:42', '2025-04-02 15:23:42'),
(22, '{"id": 22, "relationId": 17, "discountRate": 100}'::jsonb, 'supplier', 17, NULL, NULL, NULL, false, '2025-04-04 12:59:22', '2025-04-04 12:59:22');

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_price.sql
-- ============================================================

-- 表结构: erp_price
CREATE TABLE erp_price (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_id BIGINT NOT NULL, -- Foreign key to erp_product
    product_name VARCHAR(100) DEFAULT NULL,
    sku_id BIGINT NOT NULL, -- Foreign key to erp_sku
    sku_code VARCHAR(50) DEFAULT NULL,
    spec_summary VARCHAR(255) DEFAULT NULL,
    supplier_id BIGINT DEFAULT NULL, -- Foreign key to erp_supplier
    supplier_name VARCHAR(100) DEFAULT NULL,
    customer_id BIGINT DEFAULT NULL, -- Foreign key to erp_customer
    customer_name VARCHAR(100) DEFAULT NULL,
    supplier_item_code VARCHAR(50) DEFAULT NULL,
    is_preferred_supplier BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    price DECIMAL(10,2) NOT NULL,
    price_type VARCHAR(20) NOT NULL, -- Dictionary: 1-Standard, 2-Promo, 3-VIP, 4-Cost
    min_purchase_qty INT DEFAULT NULL,
    lead_time INT DEFAULT NULL, -- Days
    effective_date DATE NOT NULL,
    expired_date DATE DEFAULT NULL,
    status BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'. 0-Inactive, 1-Active
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_price IS '商品价格';
COMMENT ON COLUMN erp_price.id IS '价格ID';
COMMENT ON COLUMN erp_price.product_id IS '商品ID | erp_product的id';
COMMENT ON COLUMN erp_price.product_name IS '商品名称';
COMMENT ON COLUMN erp_price.sku_id IS 'SKU ID | erp_sku的id';
COMMENT ON COLUMN erp_price.sku_code IS 'SKU编码';
COMMENT ON COLUMN erp_price.spec_summary IS '规格摘要';
COMMENT ON COLUMN erp_price.supplier_id IS '供应商ID | erp_supplier的id';
COMMENT ON COLUMN erp_price.supplier_name IS '供应商名称';
COMMENT ON COLUMN erp_price.customer_id IS '客户ID | erp_customer的id';
COMMENT ON COLUMN erp_price.customer_name IS '客户名称';
COMMENT ON COLUMN erp_price.supplier_item_code IS '供应商商品编码';
COMMENT ON COLUMN erp_price.is_preferred_supplier IS '是否首选供应商：false-否，true-是';
COMMENT ON COLUMN erp_price.price IS '价格';
COMMENT ON COLUMN erp_price.price_type IS '字典 | 价格类型 price_type：1-标准价，2-促销价，3-VIP价，4-成本价';
COMMENT ON COLUMN erp_price.min_purchase_qty IS '最小采购量';
COMMENT ON COLUMN erp_price.lead_time IS '采购提前期(天)';
COMMENT ON COLUMN erp_price.effective_date IS '价格生效日期';
COMMENT ON COLUMN erp_price.expired_date IS '价格失效日期';
COMMENT ON COLUMN erp_price.status IS '价格状态：false-失效，true-生效';
COMMENT ON COLUMN erp_price.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_price.create_time IS '创建时间';
COMMENT ON COLUMN erp_price.update_time IS '更新时间';
COMMENT ON COLUMN erp_price.created_by IS '创建人';
COMMENT ON COLUMN erp_price.updated_by IS '更新人';

-- 创建索引
-- Note: Original UNIQUE KEY idx_price_unique_standard might need review if multiple price types can be active for the same SKU on the same date.
--       PostgreSQL requires UNIQUE constraints to be named explicitly.
--       Creating separate unique constraints might be safer if the logic allows it.
--       For now, mimicking the MySQL behavior with a single unique index.
CREATE UNIQUE INDEX idx_erp_price_idx_price_unique_standard ON erp_price (sku_id, price_type, effective_date); -- Renamed from idx_price_unique_standard
CREATE INDEX idx_erp_price_idx_price_product ON erp_price (product_id); -- Renamed from idx_price_product
CREATE INDEX idx_erp_price_idx_price_sku ON erp_price (sku_id); -- Renamed from idx_price_sku
CREATE INDEX idx_erp_price_idx_price_supplier ON erp_price (supplier_id); -- Renamed from idx_price_supplier
CREATE INDEX idx_erp_price_idx_price_customer ON erp_price (customer_id); -- Renamed from idx_price_customer
CREATE INDEX idx_erp_price_idx_price_effective_date ON erp_price (effective_date); -- Renamed from idx_price_effective_date
CREATE INDEX idx_erp_price_idx_price_status ON erp_price (status); -- Renamed from idx_price_status
CREATE INDEX idx_erp_price_idx_price_supplier_combo ON erp_price (sku_id, supplier_id, price_type, effective_date); -- Renamed from idx_price_supplier_combo
CREATE INDEX idx_erp_price_idx_price_customer_combo ON erp_price (sku_id, customer_id, price_type, effective_date); -- Renamed from idx_price_customer_combo


-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_price
BEFORE UPDATE ON erp_price
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_price
INSERT INTO erp_price (id, product_id, product_name, sku_id, sku_code, spec_summary, supplier_id, supplier_name, customer_id, customer_name, supplier_item_code, is_preferred_supplier, price, price_type, min_purchase_qty, lead_time, effective_date, expired_date, status, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, 1, 'iPhone 14 Pro', 1, 'SKU001', '颜色:深空黑,内存:128GB', NULL, NULL, NULL, NULL, NULL, false, 7999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(2, 1, 'iPhone 14 Pro', 2, 'SKU002', '颜色:深空黑,内存:256GB', NULL, NULL, NULL, NULL, NULL, false, 8999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(3, 1, 'iPhone 14 Pro', 3, 'SKU003', '颜色:深空黑,内存:512GB', NULL, NULL, NULL, NULL, NULL, false, 10999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(4, 1, 'iPhone 14 Pro', 1, 'SKU001', '颜色:深空黑,内存:128GB', NULL, NULL, NULL, NULL, NULL, false, 7499.00, '2', NULL, NULL, '2023-06-01', '2023-06-30', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(5, 1, 'iPhone 14 Pro', 2, 'SKU002', '颜色:深空黑,内存:256GB', NULL, NULL, NULL, NULL, NULL, false, 8499.00, '2', NULL, NULL, '2023-06-01', '2023-06-30', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(6, 1, 'iPhone 14 Pro', 3, 'SKU003', '颜色:深空黑,内存:512GB', NULL, NULL, NULL, NULL, NULL, false, 10499.00, '2', NULL, NULL, '2023-06-01', '2023-06-30', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(7, 1, 'iPhone 14 Pro', 1, 'SKU001', '颜色:深空黑,内存:128GB', 1, '苹果官方旗舰店', NULL, NULL, 'AP-IP14P-128-BLK', true, 6500.00, '4', 10, 3, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(8, 1, 'iPhone 14 Pro', 2, 'SKU002', '颜色:深空黑,内存:256GB', 1, '苹果官方旗舰店', NULL, NULL, 'AP-IP14P-256-BLK', true, 7500.00, '4', 10, 3, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(9, 1, 'iPhone 14 Pro', 3, 'SKU003', '颜色:深空黑,内存:512GB', 1, '苹果官方旗舰店', NULL, NULL, 'AP-IP14P-512-BLK', true, 9000.00, '4', 10, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(10, 2, 'MacBook Pro', 10, 'SKU010', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:256GB', NULL, NULL, NULL, NULL, NULL, false, 9999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(11, 2, 'MacBook Pro', 11, 'SKU011', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:512GB', NULL, NULL, NULL, NULL, NULL, false, 11999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(12, 2, 'MacBook Pro', 12, 'SKU012', '尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:512GB', NULL, NULL, NULL, NULL, NULL, false, 14999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(13, 2, 'MacBook Pro', 13, 'SKU013', '尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:1TB', NULL, NULL, NULL, NULL, NULL, false, 16999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(14, 2, 'MacBook Pro', 14, 'SKU014', '尺寸:16英寸,处理器:M2 Max,内存:32GB,硬盘:1TB', NULL, NULL, NULL, NULL, NULL, false, 21999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(15, 2, 'MacBook Pro', 10, 'SKU010', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:256GB', 1, '苹果官方旗舰店', NULL, NULL, 'AP-MBP13-M2-8-256', true, 8500.00, '4', 5, 7, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(16, 2, 'MacBook Pro', 11, 'SKU011', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:512GB', 1, '苹果官方旗舰店', NULL, NULL, 'AP-MBP13-M2-8-512', true, 10000.00, '4', 5, 7, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(17, 3, 'Galaxy S23', 15, 'SKU015', '颜色:午夜黑,内存:128GB', NULL, NULL, NULL, NULL, NULL, false, 6999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(18, 3, 'Galaxy S23', 16, 'SKU016', '颜色:午夜黑,内存:256GB', NULL, NULL, NULL, NULL, NULL, false, 7999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(19, 3, 'Galaxy S23', 17, 'SKU017', '颜色:奶油白,内存:128GB', NULL, NULL, NULL, NULL, NULL, false, 6999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(20, 3, 'Galaxy S23', 18, 'SKU018', '颜色:奶油白,内存:256GB', NULL, NULL, NULL, NULL, NULL, false, 7999.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(21, 3, 'Galaxy S23', 15, 'SKU015', '颜色:午夜黑,内存:128GB', 2, '三星电子旗舰店', NULL, NULL, 'SS-S23-128-BLK', true, 5800.00, '4', 10, 3, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(22, 3, 'Galaxy S23', 16, 'SKU016', '颜色:午夜黑,内存:256GB', 2, '三星电子旗舰店', NULL, NULL, 'SS-S23-256-BLK', true, 6800.00, '4', 10, 3, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(23, 8, '办公桌', 19, 'SKU019', '颜色:原木色,尺寸:120*60cm', NULL, NULL, NULL, NULL, NULL, false, 699.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(24, 8, '办公桌', 20, 'SKU020', '颜色:原木色,尺寸:140*70cm', NULL, NULL, NULL, NULL, NULL, false, 899.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(25, 8, '办公桌', 21, 'SKU021', '颜色:白色,尺寸:120*60cm', NULL, NULL, NULL, NULL, NULL, false, 699.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(26, 8, '办公桌', 22, 'SKU022', '颜色:白色,尺寸:140*70cm', NULL, NULL, NULL, NULL, NULL, false, 899.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(27, 8, '办公桌', 19, 'SKU019', '颜色:原木色,尺寸:120*60cm', NULL, NULL, 1, 'VIP客户A', NULL, false, 599.00, '3', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(28, 8, '办公桌', 20, 'SKU020', '颜色:原木色,尺寸:140*70cm', NULL, NULL, 1, 'VIP客户A', NULL, false, 799.00, '3', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(29, 9, '人棉T恤', 23, 'SKU023', '颜色:白色,尺码:S', NULL, NULL, NULL, NULL, NULL, false, 99.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(30, 9, '人棉T恤', 24, 'SKU024', '颜色:白色,尺码:M', NULL, NULL, NULL, NULL, NULL, false, 99.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(31, 9, '人棉T恤', 25, 'SKU025', '颜色:白色,尺码:L', NULL, NULL, NULL, NULL, NULL, false, 99.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(32, 9, '人棉T恤', 26, 'SKU026', '颜色:白色,尺码:XL', NULL, NULL, NULL, NULL, NULL, false, 109.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(33, 9, '人棉T恤', 23, 'SKU023', '颜色:白色,尺码:S', NULL, NULL, NULL, NULL, NULL, false, 79.00, '2', NULL, NULL, '2023-07-01', '2023-07-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(34, 9, '人棉T恤', 24, 'SKU024', '颜色:白色,尺码:M', NULL, NULL, NULL, NULL, NULL, false, 79.00, '2', NULL, NULL, '2023-07-01', '2023-07-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(35, 9, '人棉T恤', 25, 'SKU025', '颜色:白色,尺码:L', NULL, NULL, NULL, NULL, NULL, false, 79.00, '2', NULL, NULL, '2023-07-01', '2023-07-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(36, 9, '人棉T恤', 26, 'SKU026', '颜色:白色,尺码:XL', NULL, NULL, NULL, NULL, NULL, false, 89.00, '2', NULL, NULL, '2023-07-01', '2023-07-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(37, 10, 'Air Jordan', 31, 'SKU031', '颜色:黑红,尺码:40', NULL, NULL, NULL, NULL, NULL, false, 1299.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(38, 10, 'Air Jordan', 32, 'SKU032', '颜色:黑红,尺码:41', NULL, NULL, NULL, NULL, NULL, false, 1299.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(39, 10, 'Air Jordan', 33, 'SKU033', '颜色:黑红,尺码:42', NULL, NULL, NULL, NULL, NULL, false, 1299.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(40, 10, 'Air Jordan', 34, 'SKU034', '颜色:黑红,尺码:43', NULL, NULL, NULL, NULL, NULL, false, 1299.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(41, 10, 'Air Jordan', 35, 'SKU035', '颜色:黑红,尺码:44', NULL, NULL, NULL, NULL, NULL, false, 1299.00, '1', NULL, NULL, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(42, 10, 'Air Jordan', 31, 'SKU031', '颜色:黑红,尺码:40', 3, '耐克官方代理', NULL, NULL, 'NK-AJ-40-BR', true, 900.00, '4', 20, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(43, 10, 'Air Jordan', 32, 'SKU032', '颜色:黑红,尺码:41', 3, '耐克官方代理', NULL, NULL, 'NK-AJ-41-BR', true, 900.00, '4', 20, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(44, 10, 'Air Jordan', 33, 'SKU033', '颜色:黑红,尺码:42', 3, '耐克官方代理', NULL, NULL, 'NK-AJ-42-BR', true, 900.00, '4', 20, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(45, 10, 'Air Jordan', 34, 'SKU034', '颜色:黑红,尺码:43', 3, '耐克官方代理', NULL, NULL, 'NK-AJ-43-BR', true, 900.00, '4', 20, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1),
(46, 10, 'Air Jordan', 35, 'SKU035', '颜色:黑红,尺码:44', 3, '耐克官方代理', NULL, NULL, 'NK-AJ-44-BR', true, 900.00, '4', 20, 5, '2023-01-01', '2023-12-31', true, false, '2025-03-30 22:28:10', '2025-03-30 22:28:10', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_price_history.sql
-- ============================================================

-- 表结构: erp_price_history
CREATE TABLE erp_price_history (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    price_id BIGINT NOT NULL, -- Foreign key to erp_price
    old_price DECIMAL(10,2) NOT NULL,
    new_price DECIMAL(10,2) NOT NULL,
    change_date DATE NOT NULL,
    change_reason VARCHAR(200) DEFAULT NULL,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_price_history IS '价格历史';
COMMENT ON COLUMN erp_price_history.id IS '历史ID';
COMMENT ON COLUMN erp_price_history.price_id IS '价格ID | erp_price的id';
COMMENT ON COLUMN erp_price_history.old_price IS '旧价格';
COMMENT ON COLUMN erp_price_history.new_price IS '新价格';
COMMENT ON COLUMN erp_price_history.change_date IS '变动日期';
COMMENT ON COLUMN erp_price_history.change_reason IS '变更原因';
COMMENT ON COLUMN erp_price_history.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_price_history.create_time IS '创建时间';
COMMENT ON COLUMN erp_price_history.update_time IS '更新时间';
COMMENT ON COLUMN erp_price_history.created_by IS '创建人';
COMMENT ON COLUMN erp_price_history.updated_by IS '更新人';

-- 创建索引
CREATE INDEX idx_erp_price_history_idx_price_history_price ON erp_price_history (price_id); -- Renamed from idx_price_history_price
CREATE INDEX idx_erp_price_history_idx_price_history_change_date ON erp_price_history (change_date); -- Renamed from idx_price_history_change_date

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_price_history
BEFORE UPDATE ON erp_price_history
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_price_history (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_printing_process.sql
-- ============================================================

-- 表结构: erp_printing_process
CREATE TABLE erp_printing_process (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    code VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) DEFAULT 0.00,
    description VARCHAR(500) DEFAULT NULL,
    status BOOLEAN DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'. 1-Enabled, 0-Disabled
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    deleted_flag BOOLEAN NOT NULL DEFAULT false -- Converted from tinyint(1) DEFAULT '0'
);

-- 添加表和列的注释
COMMENT ON TABLE erp_printing_process IS '印刷工艺表';
COMMENT ON COLUMN erp_printing_process.id IS '主键ID';
COMMENT ON COLUMN erp_printing_process.code IS '工艺编码';
COMMENT ON COLUMN erp_printing_process.name IS '工艺名称';
COMMENT ON COLUMN erp_printing_process.price IS '工艺单价(元)';
COMMENT ON COLUMN erp_printing_process.description IS '工艺描述';
COMMENT ON COLUMN erp_printing_process.status IS '状态：true-启用，false-禁用';
COMMENT ON COLUMN erp_printing_process.created_by IS '创建人';
COMMENT ON COLUMN erp_printing_process.updated_by IS '更新人';
COMMENT ON COLUMN erp_printing_process.create_time IS '创建时间';
COMMENT ON COLUMN erp_printing_process.update_time IS '更新时间';
COMMENT ON COLUMN erp_printing_process.deleted_flag IS '删除标志：false-未删除，true-已删除';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_printing_process_uk_code ON erp_printing_process (code); -- Renamed from uk_code
CREATE INDEX idx_erp_printing_process_idx_status ON erp_printing_process (status); -- Renamed from idx_status

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_printing_process
BEFORE UPDATE ON erp_printing_process
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_printing_process
INSERT INTO erp_printing_process (id, code, name, price, description, status, created_by, updated_by, create_time, update_time, deleted_flag) VALUES
(1000, 'PP001', '覆亮膜', 0.15, '在印刷品表面覆上一层透明亮光膜，增加光泽度和保护效果', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false),
(1001, 'PP002', '覆哑膜', 0.18, '在印刷品表面覆上一层哑光膜，提供防刮擦保护并增加质感', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false),
(1002, 'PP003', '局部UV', 0.25, '在印刷品特定区域涂覆UV油，使局部区域产生高光效果和立体感', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false),
(1003, 'PP004', '全UV', 0.35, '在整个印刷品表面涂覆UV油，提供均匀的光亮度和保护层', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false),
(1004, 'PP005', '烫金', 0.40, '通过热压将金属箔压印到印刷品表面，形成金属光泽效果', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false),
(1005, 'PP006', '击凸', 0.30, '使印刷品表面形成突起的效果，增强触感和视觉立体感', true, NULL, NULL, '2025-03-23 16:07:49', '2025-03-23 16:07:49', false);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_product.sql
-- ============================================================

-- 表结构: erp_product
CREATE TABLE erp_product (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_code VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT DEFAULT NULL,
    unit VARCHAR(20) NOT NULL DEFAULT '',
    category_id BIGINT NOT NULL, -- Foreign key to t_category
    category_name VARCHAR(100) DEFAULT NULL,
    brand_id BIGINT DEFAULT NULL, -- Foreign key to erp_brand
    brand_name VARCHAR(50) DEFAULT NULL,
    status VARCHAR(20) NOT NULL, -- Dictionary: product_status (0-Delisted, 1-Listed, 2-Reviewing, 3-Disabled)
    source_type VARCHAR(20) DEFAULT NULL, -- Dictionary: source_type (1-Self-produced, 2-External, 3-Outsourced, 4-Virtual)
    is_raw_material BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    min_order_qty INT DEFAULT 1,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_product IS '商品基础信息';
COMMENT ON COLUMN erp_product.id IS '商品ID';
COMMENT ON COLUMN erp_product.product_code IS '商品编码';
COMMENT ON COLUMN erp_product.name IS '商品名称';
COMMENT ON COLUMN erp_product.description IS '商品描述';
COMMENT ON COLUMN erp_product.unit IS '单位';
COMMENT ON COLUMN erp_product.category_id IS '分类ID | t_category的id';
COMMENT ON COLUMN erp_product.category_name IS '分类名称';
COMMENT ON COLUMN erp_product.brand_id IS '品牌ID | erp_brand的id';
COMMENT ON COLUMN erp_product.brand_name IS '品牌名称';
COMMENT ON COLUMN erp_product.status IS '字典 | 商品状态 product_status：0-下架，1-上架，2-审核中，3-已禁用';
COMMENT ON COLUMN erp_product.source_type IS '字典 | 商品来源 source_type：1-自产，2-外购，3-委外加工，4-虚拟品';
COMMENT ON COLUMN erp_product.is_raw_material IS '是否原材料：false-否，true-是';
COMMENT ON COLUMN erp_product.min_order_qty IS '最小起订量';
COMMENT ON COLUMN erp_product.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_product.create_time IS '创建时间';
COMMENT ON COLUMN erp_product.update_time IS '更新时间';
COMMENT ON COLUMN erp_product.created_by IS '创建人';
COMMENT ON COLUMN erp_product.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_product_idx_product_code ON erp_product (product_code); -- Renamed from idx_product_code
CREATE INDEX idx_erp_product_idx_product_category ON erp_product (category_id); -- Renamed from idx_product_category
CREATE INDEX idx_erp_product_idx_product_brand ON erp_product (brand_id); -- Renamed from idx_product_brand
CREATE INDEX idx_erp_product_idx_product_status ON erp_product (status); -- Renamed from idx_product_status

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_product
BEFORE UPDATE ON erp_product
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_product
-- Note: status column seems to have 'true' in MySQL data, mapping it directly. Review if '1' or other codes are intended.
INSERT INTO erp_product (id, product_code, name, description, unit, category_id, category_name, brand_id, brand_name, status, source_type, is_raw_material, min_order_qty, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, 'P001', 'iPhone 14 Pro', '苹果iPhone 14 Pro 智能手机', '台', 1, '手机/通讯', 1, '苹果', 'true', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(2, 'P002', 'MacBook Pro', '苹果MacBook Pro 笔记本电脑', '台', 2, '电脑/办公', 1, '苹果', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(3, 'P003', 'Galaxy S23', '三星Galaxy S23 智能手机', '台', 1, '手机/通讯', 2, '三星', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(4, 'P004', 'Mate 50 Pro', '华为Mate 50 Pro 智能手机', '台', 1, '手机/通讯', 3, '华为', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(5, 'P005', 'Redmi Note 12', '小米Redmi Note 12 智能手机', '台', 1, '手机/通讯', 4, '小米', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(6, 'P006', 'ThinkPad X1', '联想ThinkPad X1 笔记本电脑', '台', 2, '电脑/办公', 5, '联想', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(7, 'P007', 'XPS 15', '戴尔XPS 15 笔记本电脑', '台', 2, '电脑/办公', 6, '戴尔', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(8, 'P008', '办公桌', '现代简约实木办公桌', '张', 3, '家具/家居', 8, '宜家', '1', '2', false, 5, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(9, 'P009', '人棉T恤', '优衣库基础款人棉T恤', '件', 4, '服装/配饰', 9, '优衣库', '1', '2', false, 10, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(10, 'P010', 'Air Jordan', '耐克Air Jordan篮球鞋', '双', 5, '运动/户外', 10, '耐克', '1', '2', false, 1, true, '2025-03-30 22:29:57', '2025-03-30 23:37:10',1,1), -- deleted_flag was 1
(11, 'P011', 'iPad Pro', '苹果iPad Pro 平板电脑', '台', 2, '电脑/办公', 1, '苹果', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(12, 'P012', 'Galaxy Tab', '三星Galaxy Tab 平板电脑', '台', 2, '电脑/办公', 2, '三星', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(13, 'P013', '小爱音箱', '小米小爱智能音箱', '个', 6, '智能/数码', 4, '小米', '1', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(14, 'P014', '显示器', 'Dell 27英寸4K显示器', '台', 2, '电脑/办公', 6, '戴尔', '0', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1),
(15, 'P015', '智能台灯', '可调光智能台灯', '个', 6, '智能/数码', 4, '小米', '2', '2', false, 1, false, '2025-03-30 22:29:57', '2025-03-30 22:29:57', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_product_attr.sql
-- ============================================================

-- 表结构: erp_product_attr
CREATE TABLE erp_product_attr (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_id BIGINT NOT NULL, -- Foreign key to erp_product
    attr_name VARCHAR(100) NOT NULL,
    attr_value VARCHAR(255) NOT NULL,
    is_search BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    is_visible BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'
    sort INT NOT NULL DEFAULT 0,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_product_attr IS '商品属性';
COMMENT ON COLUMN erp_product_attr.id IS '属性ID';
COMMENT ON COLUMN erp_product_attr.product_id IS '商品ID | erp_product的id';
COMMENT ON COLUMN erp_product_attr.attr_name IS '属性名称';
COMMENT ON COLUMN erp_product_attr.attr_value IS '属性值';
COMMENT ON COLUMN erp_product_attr.is_search IS '是否参与搜索：false-否，true-是';
COMMENT ON COLUMN erp_product_attr.is_visible IS '是否可见：false-否，true-是';
COMMENT ON COLUMN erp_product_attr.sort IS '排序';
COMMENT ON COLUMN erp_product_attr.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_product_attr.create_time IS '创建时间';
COMMENT ON COLUMN erp_product_attr.update_time IS '更新时间';
COMMENT ON COLUMN erp_product_attr.created_by IS '创建人';
COMMENT ON COLUMN erp_product_attr.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_product_attr_idx_product_attr_name ON erp_product_attr (product_id, attr_name); -- Renamed from idx_product_attr_name
CREATE INDEX idx_erp_product_attr_idx_product_attr_product ON erp_product_attr (product_id); -- Renamed from idx_product_attr_product

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_product_attr
BEFORE UPDATE ON erp_product_attr
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_product_attr (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_product_category_relation.sql
-- ============================================================

-- 表结构: erp_product_category_relation
CREATE TABLE erp_product_category_relation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_id BIGINT NOT NULL, -- Foreign key to erp_product
    product_name VARCHAR(100) DEFAULT NULL,
    category_id BIGINT NOT NULL, -- Foreign key to t_category
    category_name VARCHAR(100) DEFAULT NULL,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_product_category_relation IS '商品分类关联';
COMMENT ON COLUMN erp_product_category_relation.id IS '关联ID';
COMMENT ON COLUMN erp_product_category_relation.product_id IS '商品ID | erp_product的id';
COMMENT ON COLUMN erp_product_category_relation.product_name IS '商品名称';
COMMENT ON COLUMN erp_product_category_relation.category_id IS '分类ID | t_category的id';
COMMENT ON COLUMN erp_product_category_relation.category_name IS '分类名称';
COMMENT ON COLUMN erp_product_category_relation.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_product_category_relation.create_time IS '创建时间';
COMMENT ON COLUMN erp_product_category_relation.update_time IS '更新时间';
COMMENT ON COLUMN erp_product_category_relation.created_by IS '创建人';
COMMENT ON COLUMN erp_product_category_relation.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_product_category_relation_idx_product_category_unique ON erp_product_category_relation (product_id, category_id); -- Renamed from idx_product_category_unique
CREATE INDEX idx_erp_product_category_relation_idx_product_category_relat ON erp_product_category_relation (product_id); -- Renamed from idx_product_category_relation_product
CREATE INDEX idx_erp_product_category_relation_idx_product_category_relat2 ON erp_product_category_relation (category_id); -- Renamed from idx_product_category_relation_category (avoiding potential name conflict)

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_product_category_relation
BEFORE UPDATE ON erp_product_category_relation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_product_category_relation (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_product_spec_relation.sql
-- ============================================================

-- 表结构: erp_product_spec_relation
CREATE TABLE erp_product_spec_relation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_id BIGINT NOT NULL, -- Foreign key to erp_product
    spec_id BIGINT NOT NULL, -- Foreign key to erp_spec
    required_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    sort INT NOT NULL DEFAULT 0,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_product_spec_relation IS '商品规格关联';
COMMENT ON COLUMN erp_product_spec_relation.id IS '关联ID';
COMMENT ON COLUMN erp_product_spec_relation.product_id IS '商品ID | erp_product的id';
COMMENT ON COLUMN erp_product_spec_relation.spec_id IS '规格ID | erp_spec的id';
COMMENT ON COLUMN erp_product_spec_relation.required_flag IS '是否必填：false-否，true-是';
COMMENT ON COLUMN erp_product_spec_relation.sort IS '排序';
COMMENT ON COLUMN erp_product_spec_relation.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_product_spec_relation.create_time IS '创建时间';
COMMENT ON COLUMN erp_product_spec_relation.update_time IS '更新时间';
COMMENT ON COLUMN erp_product_spec_relation.created_by IS '创建人';
COMMENT ON COLUMN erp_product_spec_relation.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_product_spec_relation_idx_product_spec_unique ON erp_product_spec_relation (product_id, spec_id); -- Renamed from idx_product_spec_unique
CREATE INDEX idx_erp_product_spec_relation_idx_product_spec_relation_prod ON erp_product_spec_relation (product_id); -- Renamed from idx_product_spec_relation_product
CREATE INDEX idx_erp_product_spec_relation_idx_product_spec_relation_spec ON erp_product_spec_relation (spec_id); -- Renamed from idx_product_spec_relation_spec

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_product_spec_relation
BEFORE UPDATE ON erp_product_spec_relation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_product_spec_relation (No data in original file)


-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_sku.sql
-- ============================================================

-- 表结构: erp_sku
CREATE TABLE erp_sku (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    product_id BIGINT NOT NULL, -- Foreign key to erp_product
    product_name VARCHAR(100) DEFAULT NULL,
    product_code VARCHAR(50) DEFAULT NULL,
    sku_code VARCHAR(50) NOT NULL,
    spec_summary VARCHAR(255) DEFAULT NULL, -- Example: '颜色:红色,尺寸:XL'
    stock INT NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL, -- Dictionary: sku_status (0-Delisted, 1-Listed, 2-OutOfStock)
    weight DECIMAL(10,2) DEFAULT NULL, -- Weight in kg
    lead_time INT DEFAULT NULL, -- Delivery time in days
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_sku IS '商品SKU';
COMMENT ON COLUMN erp_sku.id IS 'SKU ID';
COMMENT ON COLUMN erp_sku.product_id IS '商品ID | erp_product的id';
COMMENT ON COLUMN erp_sku.product_name IS '商品名称';
COMMENT ON COLUMN erp_sku.product_code IS '商品编码';
COMMENT ON COLUMN erp_sku.sku_code IS 'SKU编码';
COMMENT ON COLUMN erp_sku.spec_summary IS '规格摘要（如 颜色:红色,尺寸:XL）';
COMMENT ON COLUMN erp_sku.stock IS '库存';
COMMENT ON COLUMN erp_sku.status IS '字典 | SKU状态 sku_status：0-下架，1-上架，2-缺货';
COMMENT ON COLUMN erp_sku.weight IS '重量(kg)';
COMMENT ON COLUMN erp_sku.lead_time IS '交付周期(天)';
COMMENT ON COLUMN erp_sku.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_sku.create_time IS '创建时间';
COMMENT ON COLUMN erp_sku.update_time IS '更新时间';
COMMENT ON COLUMN erp_sku.created_by IS '创建人';
COMMENT ON COLUMN erp_sku.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_sku_idx_sku_code ON erp_sku (sku_code); -- Renamed from idx_sku_code
CREATE INDEX idx_erp_sku_idx_sku_product ON erp_sku (product_id); -- Renamed from idx_sku_product
CREATE INDEX idx_erp_sku_idx_sku_status ON erp_sku (status); -- Renamed from idx_sku_status

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_sku
BEFORE UPDATE ON erp_sku
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_sku
INSERT INTO erp_sku (id, product_id, product_name, product_code, sku_code, spec_summary, stock, status, weight, lead_time, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, 1, 'iPhone 14 Pro', 'P001', 'SKU001', '颜色:深空黑,内存:128GB', 100, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(2, 1, 'iPhone 14 Pro', 'P001', 'SKU002', '颜色:深空黑,内存:256GB', 80, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(3, 1, 'iPhone 14 Pro', 'P001', 'SKU003', '颜色:深空黑,内存:512GB', 50, '1', 0.21, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(4, 1, 'iPhone 14 Pro', 'P001', 'SKU004', '颜色:银色,内存:128GB', 90, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(5, 1, 'iPhone 14 Pro', 'P001', 'SKU005', '颜色:银色,内存:256GB', 70, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(6, 1, 'iPhone 14 Pro', 'P001', 'SKU006', '颜色:银色,内存:512GB', 40, '1', 0.21, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(7, 1, 'iPhone 14 Pro', 'P001', 'SKU007', '颜色:金色,内存:128GB', 85, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(8, 1, 'iPhone 14 Pro', 'P001', 'SKU008', '颜色:金色,内存:256GB', 55, '1', 0.21, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(9, 1, 'iPhone 14 Pro', 'P001', 'SKU009', '颜色:金色,内存:512GB', 22, '1', 0.21, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(10, 2, 'MacBook Pro', 'P002', 'SKU010', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:256GB', 50, '1', 1.38, 7, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(11, 2, 'MacBook Pro', 'P002', 'SKU011', '尺寸:13英寸,处理器:M2,内存:8GB,硬盘:512GB', 40, '1', 1.38, 7, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(12, 2, 'MacBook Pro', 'P002', 'SKU012', '尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:512GB', 30, '1', 1.60, 10, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(13, 2, 'MacBook Pro', 'P002', 'SKU013', '尺寸:14英寸,处理器:M2 Pro,内存:16GB,硬盘:1TB', 20, '1', 1.60, 10, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(14, 2, 'MacBook Pro', 'P002', 'SKU014', '尺寸:16英寸,处理器:M2 Max,内存:32GB,硬盘:1TB', 15, '1', 2.15, 14, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(15, 3, 'Galaxy S23', 'P003', 'SKU015', '颜色:午夜黑,内存:128GB', 80, '1', 0.19, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(16, 3, 'Galaxy S23', 'P003', 'SKU016', '颜色:午夜黑,内存:256GB', 60, '1', 0.19, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(17, 3, 'Galaxy S23', 'P003', 'SKU017', '颜色:奶油白,内存:128GB', 75, '1', 0.19, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(18, 3, 'Galaxy S23', 'P003', 'SKU018', '颜色:奶油白,内存:256GB', 55, '1', 0.19, 3, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(19, 8, '办公桌', 'P008', 'SKU019', '颜色:原木色,尺寸:120*60cm', 20, '1', 25.00, 15, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(20, 8, '办公桌', 'P008', 'SKU020', '颜色:原木色,尺寸:140*70cm', 15, '1', 30.00, 15, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(21, 8, '办公桌', 'P008', 'SKU021', '颜色:白色,尺寸:120*60cm', 18, '1', 25.00, 15, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(22, 8, '办公桌', 'P008', 'SKU022', '颜色:白色,尺寸:140*70cm', 12, '1', 30.00, 15, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(23, 9, '人棉T恤', 'P009', 'SKU023', '颜色:白色,尺码:S', 100, '1', 0.15, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(24, 9, '人棉T恤', 'P009', 'SKU024', '颜色:白色,尺码:M', 120, '1', 0.16, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(25, 9, '人棉T恤', 'P009', 'SKU025', '颜色:白色,尺码:L', 100, '1', 0.17, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(26, 9, '人棉T恤', 'P009', 'SKU026', '颜色:白色,尺码:XL', 80, '1', 0.18, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(27, 9, '人棉T恤', 'P009', 'SKU027', '颜色:黑色,尺码:S', 90, '1', 0.15, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(28, 9, '人棉T恤', 'P009', 'SKU028', '颜色:黑色,尺码:M', 110, '1', 0.16, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(29, 9, '人棉T恤', 'P009', 'SKU029', '颜色:黑色,尺码:L', 90, '1', 0.17, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(30, 9, '人棉T恤', 'P009', 'SKU030', '颜色:黑色,尺码:XL', 70, '1', 0.18, 2, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(31, 10, 'Air Jordan', 'P010', 'SKU031', '颜色:黑红,尺码:40', 30, '1', 0.80, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(32, 10, 'Air Jordan', 'P010', 'SKU032', '颜色:黑红,尺码:41', 35, '1', 0.82, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(33, 10, 'Air Jordan', 'P010', 'SKU033', '颜色:黑红,尺码:42', 40, '1', 0.84, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(34, 10, 'Air Jordan', 'P010', 'SKU034', '颜色:黑红,尺码:43', 38, '1', 0.86, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(35, 10, 'Air Jordan', 'P010', 'SKU035', '颜色:黑红,尺码:44', 25, '1', 0.88, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(36, 10, 'Air Jordan', 'P010', 'SKU036', '颜色:白蓝,尺码:40', 28, '1', 0.80, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(37, 10, 'Air Jordan', 'P010', 'SKU037', '颜色:白蓝,尺码:41', 32, '1', 0.82, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(38, 10, 'Air Jordan', 'P010', 'SKU038', '颜色:白蓝,尺码:42', 37, '1', 0.84, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(39, 10, 'Air Jordan', 'P010', 'SKU039', '颜色:白蓝,尺码:43', 35, '1', 0.86, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1),
(40, 10, 'Air Jordan', 'P010', 'SKU040', '颜色:白蓝,尺码:44', 22, '1', 0.88, 5, false, '2025-03-30 22:30:05', '2025-03-30 22:30:05', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL for file: postgresql/erp_sku_spec_relation.sql
-- ============================================================

-- 表结构: erp_sku_spec_relation
CREATE TABLE erp_sku_spec_relation (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    sku_id BIGINT NOT NULL, -- Foreign key to erp_sku
    sku_code VARCHAR(50) DEFAULT NULL,
    spec_id BIGINT NOT NULL, -- Foreign key to erp_spec
    spec_name VARCHAR(50) DEFAULT NULL,
    spec_value_id BIGINT DEFAULT NULL, -- Foreign key to erp_spec_value
    spec_value_text VARCHAR(100) DEFAULT NULL,
    custom_value VARCHAR(100) DEFAULT NULL,
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_sku_spec_relation IS 'SKU规格值关联';
COMMENT ON COLUMN erp_sku_spec_relation.id IS '关联ID';
COMMENT ON COLUMN erp_sku_spec_relation.sku_id IS 'SKU ID | erp_sku的id';
COMMENT ON COLUMN erp_sku_spec_relation.sku_code IS 'SKU编码';
COMMENT ON COLUMN erp_sku_spec_relation.spec_id IS '规格ID | erp_spec的id';
COMMENT ON COLUMN erp_sku_spec_relation.spec_name IS '规格名称';
COMMENT ON COLUMN erp_sku_spec_relation.spec_value_id IS '规格值ID | erp_spec_value的id';
COMMENT ON COLUMN erp_sku_spec_relation.spec_value_text IS '规格值';
COMMENT ON COLUMN erp_sku_spec_relation.custom_value IS '自定义规格值';
COMMENT ON COLUMN erp_sku_spec_relation.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_sku_spec_relation.create_time IS '创建时间';
COMMENT ON COLUMN erp_sku_spec_relation.update_time IS '更新时间';
COMMENT ON COLUMN erp_sku_spec_relation.created_by IS '创建人';
COMMENT ON COLUMN erp_sku_spec_relation.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_sku_spec_relation_idx_sku_spec_unique ON erp_sku_spec_relation (sku_id, spec_id); -- Renamed from idx_sku_spec_unique
CREATE INDEX idx_erp_sku_spec_relation_idx_sku_spec_relation_sku ON erp_sku_spec_relation (sku_id); -- Renamed from idx_sku_spec_relation_sku
CREATE INDEX idx_erp_sku_spec_relation_idx_sku_spec_relation_spec ON erp_sku_spec_relation (spec_id); -- Renamed from idx_sku_spec_relation_spec
CREATE INDEX idx_erp_sku_spec_relation_idx_sku_spec_relation_value ON erp_sku_spec_relation (spec_value_id); -- Renamed from idx_sku_spec_relation_value

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_sku_spec_relation
BEFORE UPDATE ON erp_sku_spec_relation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Dumping data for table erp_sku_spec_relation (No data in original file)


-- ============================================================

-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_spec.sql
-- ============================================================

-- 表结构: erp_spec
CREATE TABLE erp_spec (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    name VARCHAR(100) NOT NULL,
    value_source VARCHAR(20) NOT NULL, -- Dictionary: spec_value_source (1-Enum, 2-UserInput)
    value_type VARCHAR(20) NOT NULL DEFAULT '1', -- Dictionary: spec_value_type (1-Text, 2-Number, 3-Date)
    min_value DECIMAL(10,2) DEFAULT NULL, -- For number types
    max_value DECIMAL(10,2) DEFAULT NULL, -- For number types
    step_size DECIMAL(10,2) DEFAULT NULL, -- For number types
    unit VARCHAR(20) DEFAULT NULL, -- For number types
    sort INT NOT NULL DEFAULT 0,
    status BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'. 0-Disabled, 1-Enabled
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_spec IS '商品规格';
COMMENT ON COLUMN erp_spec.id IS '规格ID';
COMMENT ON COLUMN erp_spec.name IS '规格名称';
COMMENT ON COLUMN erp_spec.value_source IS '字典 | 规格值来源 spec_value_source：1-枚举，2-用户输入';
COMMENT ON COLUMN erp_spec.value_type IS '字典 | 规格值类型 spec_value_type：1-文本，2-数字，3-日期';
COMMENT ON COLUMN erp_spec.min_value IS '最小值（数字类型时有效）';
COMMENT ON COLUMN erp_spec.max_value IS '最大值（数字类型时有效）';
COMMENT ON COLUMN erp_spec.step_size IS '步长（数字类型时有效）';
COMMENT ON COLUMN erp_spec.unit IS '单位（数字类型时有效）';
COMMENT ON COLUMN erp_spec.sort IS '排序';
COMMENT ON COLUMN erp_spec.status IS '规格状态：false-禁用，true-启用';
COMMENT ON COLUMN erp_spec.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_spec.create_time IS '创建时间';
COMMENT ON COLUMN erp_spec.update_time IS '更新时间';
COMMENT ON COLUMN erp_spec.created_by IS '创建人';
COMMENT ON COLUMN erp_spec.updated_by IS '更新人';

-- 创建索引
CREATE UNIQUE INDEX idx_erp_spec_idx_spec_name ON erp_spec (name); -- Renamed from idx_spec_name

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_spec
BEFORE UPDATE ON erp_spec
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_spec
INSERT INTO erp_spec (id, name, value_source, value_type, min_value, max_value, step_size, unit, sort, status, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, '颜色', '1', '1', NULL, NULL, NULL, NULL, 1, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(2, '尺寸', '1', '1', NULL, NULL, NULL, NULL, 2, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(3, '内存', '1', '1', NULL, NULL, NULL, NULL, 3, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(4, '存储容量', '1', '1', NULL, NULL, NULL, NULL, 4, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(5, '处理器', '1', '1', NULL, NULL, NULL, NULL, 5, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(6, '尺码', '1', '1', NULL, NULL, NULL, NULL, 6, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(7, '材质', '1', '1', NULL, NULL, NULL, NULL, 7, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(8, '款式', '1', '1', NULL, NULL, NULL, NULL, 8, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(9, '版型', '1', '1', NULL, NULL, NULL, NULL, 9, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(10, '厚度', '1', '2', 0.10, 10.00, 0.10, 'mm', 10, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(11, '重量', '1', '2', 0.01, 100.00, 0.01, 'kg', 11, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1),
(12, '功率', '1', '2', 1.00, 5000.00, 1.00, 'W', 12, true, false, '2025-03-30 22:29:52', '2025-03-30 22:29:52', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_spec_value.sql
-- ============================================================

-- 表结构: erp_spec_value
CREATE TABLE erp_spec_value (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    spec_id BIGINT NOT NULL, -- Foreign key to erp_spec
    value VARCHAR(100) NOT NULL,
    sort INT NOT NULL DEFAULT 0,
    status BOOLEAN NOT NULL DEFAULT true, -- Converted from tinyint(1) DEFAULT '1'. 0-Disabled, 1-Enabled
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL
);

-- 添加表和列的注释
COMMENT ON TABLE erp_spec_value IS '商品规格值';
COMMENT ON COLUMN erp_spec_value.id IS '规格值ID';
COMMENT ON COLUMN erp_spec_value.spec_id IS '规格ID | erp_spec的id';
COMMENT ON COLUMN erp_spec_value.value IS '规格值';
COMMENT ON COLUMN erp_spec_value.sort IS '排序';
COMMENT ON COLUMN erp_spec_value.status IS '规格值状态：false-禁用，true-启用';
COMMENT ON COLUMN erp_spec_value.deleted_flag IS '删除标志：false-未删除，true-已删除';
COMMENT ON COLUMN erp_spec_value.create_time IS '创建时间';
COMMENT ON COLUMN erp_spec_value.update_time IS '更新时间';
COMMENT ON COLUMN erp_spec_value.created_by IS '创建人';
COMMENT ON COLUMN erp_spec_value.updated_by IS '更新人';

-- 创建索引
CREATE INDEX idx_erp_spec_value_idx_spec_value_spec ON erp_spec_value (spec_id); -- Renamed from idx_spec_value_spec

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_spec_value
BEFORE UPDATE ON erp_spec_value
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_spec_value
INSERT INTO erp_spec_value (id, spec_id, value, sort, status, deleted_flag, create_time, update_time, created_by, updated_by) VALUES
(1, 1, '黑色', 1, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(2, 1, '白色', 2, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(3, 1, '红色', 3, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(4, 1, '蓝色', 4, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(5, 1, '绿色', 5, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(6, 1, '黄色', 6, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(7, 1, '紫色', 7, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(8, 1, '灰色', 8, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(9, 1, '粉色', 9, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(10, 1, '深空黑', 10, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(11, 1, '午夜黑', 11, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(12, 1, '银色', 12, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(13, 1, '金色', 13, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(14, 1, '奶油白', 14, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(15, 1, '原木色', 15, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(16, 1, '黑红', 16, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(17, 1, '白蓝', 17, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(18, 3, '4GB', 1, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(19, 3, '8GB', 2, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(20, 3, '16GB', 3, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(21, 3, '32GB', 4, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(22, 3, '64GB', 5, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(23, 3, '128GB', 6, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(24, 3, '256GB', 7, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(25, 3, '512GB', 8, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(26, 3, '1TB', 9, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(27, 4, '64GB', 1, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(28, 4, '128GB', 2, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(29, 4, '256GB', 3, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(30, 4, '512GB', 4, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(31, 4, '1TB', 5, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(32, 4, '2TB', 6, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(33, 5, 'M2', 1, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(34, 5, 'M2 Pro', 2, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(35, 5, 'M2 Max', 3, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(36, 5, 'Intel i5', 4, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(37, 5, 'Intel i7', 5, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(38, 5, 'Intel i9', 6, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(39, 5, 'AMD Ryzen 5', 7, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(40, 5, 'AMD Ryzen 7', 8, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(41, 5, 'AMD Ryzen 9', 9, true, false, '2025-03-30 22:29:53', '2025-03-30 22:29:53', 1, 1),
(42, 2, '13英寸', 1, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(43, 2, '14英寸', 2, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(44, 2, '15英寸', 3, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(45, 2, '16英寸', 4, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(46, 2, '17英寸', 5, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(47, 2, '27英寸', 6, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(48, 2, '32英寸', 7, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(49, 2, '120*60cm', 8, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(50, 2, '140*70cm', 9, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(51, 2, '160*80cm', 10, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(52, 6, 'XS', 1, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(53, 6, 'S', 2, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(54, 6, 'M', 3, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(55, 6, 'L', 4, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(56, 6, 'XL', 5, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(57, 6, 'XXL', 6, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(58, 6, '38', 7, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(59, 6, '39', 8, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(60, 6, '40', 9, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(61, 6, '41', 10, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(62, 6, '42', 11, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(63, 6, '43', 12, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(64, 6, '44', 13, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(65, 6, '45', 14, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(66, 7, '纯棉', 1, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(67, 7, '人棉', 2, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(68, 7, '聚酯纤维', 3, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(69, 7, '尼龙', 4, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(70, 7, '真皮', 5, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(71, 7, 'PU革', 6, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(72, 7, '实木', 7, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(73, 7, '复合板', 8, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(74, 7, '金属', 9, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(75, 7, '塑料', 10, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1),
(76, 7, '玻璃', 11, true, false, '2025-03-30 22:29:54', '2025-03-30 22:29:54', 1, 1);

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/erp_supplier.sql
-- ============================================================

-- 表结构: erp_supplier
CREATE TABLE erp_supplier (
    id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    supplier_code VARCHAR(32) DEFAULT NULL,
    supplier_name VARCHAR(100) NOT NULL,
    supplier_type VARCHAR(20) NOT NULL, -- material, equipment, service
    business_license VARCHAR(50) DEFAULT NULL,
    tax_id VARCHAR(50) DEFAULT NULL,
    contact_person VARCHAR(50) DEFAULT NULL,
    contact_phone VARCHAR(20) DEFAULT NULL,
    contact_email VARCHAR(100) DEFAULT NULL,
    contact_position VARCHAR(50) DEFAULT NULL,
    alternative_contact VARCHAR(50) DEFAULT NULL,
    alternative_phone VARCHAR(20) DEFAULT NULL,
    province VARCHAR(50) DEFAULT NULL,
    city VARCHAR(50) DEFAULT NULL,
    district VARCHAR(50) DEFAULT NULL,
    address VARCHAR(200) DEFAULT NULL,
    postal_code VARCHAR(20) DEFAULT NULL,
    bank_name VARCHAR(100) DEFAULT NULL,
    bank_account VARCHAR(50) DEFAULT NULL,
    bank_account_name VARCHAR(100) DEFAULT NULL,
    payment_terms VARCHAR(100) DEFAULT NULL,
    credit_limit DOUBLE PRECISION DEFAULT NULL, -- Changed from double
    credit_period INT DEFAULT NULL, -- Days
    cooperation_start_date DATE DEFAULT NULL,
    contract_expiry_date DATE DEFAULT NULL,
    last_order_date DATE DEFAULT NULL,
    total_order_amount DOUBLE PRECISION DEFAULT NULL, -- Changed from double
    is_strategic BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    discount_rate DOUBLE PRECISION DEFAULT NULL, -- Changed from double
    processing_fee_single DOUBLE PRECISION DEFAULT NULL, -- Changed from double (元/m²)
    processing_fee_double DOUBLE PRECISION DEFAULT NULL, -- Changed from double (元/m²)
    tax_rate DOUBLE PRECISION DEFAULT NULL, -- Changed from double
    min_order_quantity INT DEFAULT NULL,
    lead_time INT DEFAULT NULL, -- Days
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive, blacklisted
    approval_status VARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
    approval_user_id BIGINT DEFAULT NULL,
    approval_date TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- Converted from datetime
    approval_notes TEXT DEFAULT NULL,
    remark TEXT DEFAULT NULL,
    attachment_path VARCHAR(255) DEFAULT NULL,
    created_by BIGINT DEFAULT NULL,
    updated_by BIGINT DEFAULT NULL,
    deleted_flag BOOLEAN DEFAULT false, -- Converted from tinyint(1) DEFAULT '0'
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE erp_supplier IS '供应商';
COMMENT ON COLUMN erp_supplier.id IS '供应商ID';
COMMENT ON COLUMN erp_supplier.supplier_code IS '供应商编码';
COMMENT ON COLUMN erp_supplier.supplier_name IS '供应商名称';
COMMENT ON COLUMN erp_supplier.supplier_type IS '供应商类型：material-原材料供应商，equipment-设备供应商，service-服务供应商';
COMMENT ON COLUMN erp_supplier.business_license IS '营业执照号';
COMMENT ON COLUMN erp_supplier.tax_id IS '税务登记号';
COMMENT ON COLUMN erp_supplier.contact_person IS '主要联系人';
COMMENT ON COLUMN erp_supplier.contact_phone IS '联系电话';
COMMENT ON COLUMN erp_supplier.contact_email IS '联系邮箱';
COMMENT ON COLUMN erp_supplier.contact_position IS '联系人职位';
COMMENT ON COLUMN erp_supplier.alternative_contact IS '备用联系人';
COMMENT ON COLUMN erp_supplier.alternative_phone IS '备用联系电话';
COMMENT ON COLUMN erp_supplier.province IS '省份';
COMMENT ON COLUMN erp_supplier.city IS '城市';
COMMENT ON COLUMN erp_supplier.district IS '区县';
COMMENT ON COLUMN erp_supplier.address IS '详细地址';
COMMENT ON COLUMN erp_supplier.postal_code IS '邮政编码';
COMMENT ON COLUMN erp_supplier.bank_name IS '开户银行';
COMMENT ON COLUMN erp_supplier.bank_account IS '银行账号';
COMMENT ON COLUMN erp_supplier.bank_account_name IS '开户名称';
COMMENT ON COLUMN erp_supplier.payment_terms IS '付款条件';
COMMENT ON COLUMN erp_supplier.credit_limit IS '信用额度';
COMMENT ON COLUMN erp_supplier.credit_period IS '信用期(天)';
COMMENT ON COLUMN erp_supplier.cooperation_start_date IS '合作开始日期';
COMMENT ON COLUMN erp_supplier.contract_expiry_date IS '合同到期日期';
COMMENT ON COLUMN erp_supplier.last_order_date IS '最近订单日期';
COMMENT ON COLUMN erp_supplier.total_order_amount IS '累计订单金额';
COMMENT ON COLUMN erp_supplier.is_strategic IS '是否战略供应商：false-否，true-是';
COMMENT ON COLUMN erp_supplier.discount_rate IS '折扣率';
COMMENT ON COLUMN erp_supplier.processing_fee_single IS '单瓦加工费(元/m²)';
COMMENT ON COLUMN erp_supplier.processing_fee_double IS '双瓦加工费(元/m²)';
COMMENT ON COLUMN erp_supplier.tax_rate IS '税率';
COMMENT ON COLUMN erp_supplier.min_order_quantity IS '最小订单量';
COMMENT ON COLUMN erp_supplier.lead_time IS '交货提前期(天)';
COMMENT ON COLUMN erp_supplier.status IS '状态：active-活跃，inactive-非活跃，blacklisted-黑名单';
COMMENT ON COLUMN erp_supplier.approval_status IS '审批状态：pending-待审批，approved-已批准，rejected-已拒绝';
COMMENT ON COLUMN erp_supplier.approval_user_id IS '审批人ID';
COMMENT ON COLUMN erp_supplier.approval_date IS '审批日期';
COMMENT ON COLUMN erp_supplier.approval_notes IS '审批备注';
COMMENT ON COLUMN erp_supplier.remark IS '备注';
COMMENT ON COLUMN erp_supplier.attachment_path IS '附件路径';
COMMENT ON COLUMN erp_supplier.created_by IS '创建人ID';
COMMENT ON COLUMN erp_supplier.updated_by IS '更新人ID';
COMMENT ON COLUMN erp_supplier.deleted_flag IS '是否删除：false-否，true-是';
COMMENT ON COLUMN erp_supplier.create_time IS '创建时间';
COMMENT ON COLUMN erp_supplier.update_time IS '更新时间';

-- 创建索引
CREATE INDEX idx_erp_supplier_idx_supplier_name ON erp_supplier (supplier_name); -- Renamed from idx_supplier_name
CREATE INDEX idx_erp_supplier_idx_supplier_type ON erp_supplier (supplier_type); -- Renamed from idx_supplier_type
CREATE INDEX idx_erp_supplier_idx_status ON erp_supplier (status); -- Renamed from idx_status
CREATE INDEX idx_erp_supplier_idx_approval_status ON erp_supplier (approval_status); -- Renamed from idx_approval_status
CREATE INDEX idx_erp_supplier_idx_deleted_flag ON erp_supplier (deleted_flag); -- Renamed from idx_deleted_flag
CREATE INDEX idx_erp_supplier_idx_create_time ON erp_supplier (create_time); -- Renamed from idx_create_time

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_erp_supplier
BEFORE UPDATE ON erp_supplier
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: erp_supplier
INSERT INTO erp_supplier (id, supplier_code, supplier_name, supplier_type, business_license, tax_id, contact_person, contact_phone, contact_email, contact_position, alternative_contact, alternative_phone, province, city, district, address, postal_code, bank_name, bank_account, bank_account_name, payment_terms, credit_limit, credit_period, cooperation_start_date, contract_expiry_date, last_order_date, total_order_amount, is_strategic, discount_rate, processing_fee_single, processing_fee_double, tax_rate, min_order_quantity, lead_time, status, approval_status, approval_user_id, approval_date, approval_notes, remark, attachment_path, created_by, updated_by, deleted_flag, create_time, update_time) VALUES
(17, '001', '福建榕升纸业有限公司', 'paper_board_supplier', '', '', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '', '', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'pending', NULL, NULL, NULL, '', NULL, NULL, NULL, false, '2025-03-19 20:19:48', '2025-03-19 20:19:48'),
(18, '002', '福州宏良纸业有限公司', 'raw_paper_supplier', '', '', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '', '', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'pending', NULL, NULL, NULL, '', NULL, NULL, NULL, false, '2025-03-21 22:16:03', '2025-03-21 22:16:03'),
(19, '003', '福瑞', 'corrugated_paper', '', '', '', '', '', NULL, NULL, NULL, NULL, NULL, NULL, '', '', '', '', NULL, '', NULL, NULL, NULL, NULL, NULL, NULL, false, NULL, NULL, NULL, NULL, NULL, NULL, 'active', 'pending', NULL, NULL, NULL, '', NULL, NULL, NULL, false, '2025-03-29 09:18:14', '2025-03-29 09:18:14');

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/t_category.sql
-- ============================================================

-- 表结构: t_category
CREATE TABLE t_category (
    id SERIAL PRIMARY KEY, -- Converted from int AUTO_INCREMENT
    category_name VARCHAR(100) NOT NULL,
    category_type SMALLINT NOT NULL, -- Dictionary: 1-Product, 2-Service, 3-Tag
    parent_id INT NOT NULL,
    sort INT NOT NULL DEFAULT 0,
    disabled_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint DEFAULT '0'
    deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Converted from tinyint DEFAULT '0'
    remark VARCHAR(255) DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime. ON UPDATE handled by trigger.
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- Converted from datetime
);

-- 添加表和列的注释
COMMENT ON TABLE t_category IS '分类表，主要用于商品分类';
COMMENT ON COLUMN t_category.id IS '分类id';
COMMENT ON COLUMN t_category.category_name IS '分类名称';
COMMENT ON COLUMN t_category.category_type IS '字典 | 分类类型：1-商品分类，2-服务分类，3-标签分类';
COMMENT ON COLUMN t_category.parent_id IS '父级id';
COMMENT ON COLUMN t_category.sort IS '排序';
COMMENT ON COLUMN t_category.disabled_flag IS '是否禁用 (false=0, true=1)';
COMMENT ON COLUMN t_category.deleted_flag IS '是否删除 (false=0, true=1)';
COMMENT ON COLUMN t_category.remark IS '备注';
COMMENT ON COLUMN t_category.update_time IS '更新时间';
COMMENT ON COLUMN t_category.create_time IS '创建时间';

-- 创建索引
CREATE INDEX idx_t_category_idx_parent_id ON t_category (parent_id); -- Renamed from idx_parent_id

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_t_category
BEFORE UPDATE ON t_category
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: t_category
INSERT INTO t_category (id, category_name, category_type, parent_id, sort, disabled_flag, deleted_flag, remark, update_time, create_time) VALUES
(1, '手机', 1, 0, 0, false, false, NULL, '2022-10-10 22:27:24', '2022-07-14 20:55:15'),
(2, '键盘', 1, 0, 0, false, false, NULL, '2022-09-14 21:39:00', '2022-07-14 20:55:48'),
(3, '自定义1', 2, 0, 0, false, false, NULL, '2022-09-14 22:01:06', '2022-07-14 20:56:03'),
(4, '自定义2', 2, 0, 0, false, false, NULL, '2022-09-14 22:01:10', '2022-07-14 20:56:09'),
(351, '鼠标', 1, 0, 0, false, false, NULL, '2022-09-14 21:39:06', '2022-09-14 21:39:06'),
(352, '苹果', 1, 1, 0, false, false, NULL, '2022-09-14 21:39:25', '2022-09-14 21:39:25'),
(353, '华为', 1, 1, 0, false, false, NULL, '2022-09-14 21:39:32', '2022-09-14 21:39:32'),
(354, 'IKBC', 1, 2, 0, false, false, NULL, '2022-09-14 21:39:38', '2022-09-14 21:39:38'),
(355, '双飞燕', 1, 2, 0, false, false, NULL, '2022-09-14 21:39:47', '2022-09-14 21:39:47'),
(356, '罗技', 1, 351, 0, false, false, NULL, '2022-09-14 21:39:57', '2022-09-14 21:39:57'),
(357, '小米', 1, 1, 0, false, false, NULL, '2022-10-10 22:27:39', '2022-10-10 22:27:39'),
(360, 'iphone', 1, 352, 0, false, false, NULL, '2023-12-04 21:26:55', '2023-12-01 19:54:22');

-- Reset sequence if needed (optional, depends on how you handle IDs)
-- SELECT setval('t_category_id_seq', (SELECT MAX(id) FROM t_category));

-- ============================================================
-- ============================================================
-- Converted PostgreSQL DDL/DML for file: postgresql/t_change_log.sql
-- ============================================================

-- 表结构: t_change_log
CREATE TABLE t_change_log (
    change_log_id BIGSERIAL PRIMARY KEY, -- Converted from bigint AUTO_INCREMENT
    version VARCHAR(255) NOT NULL,
    type INT NOT NULL, -- Update type: 1-Major Feature Update, 2-Feature Update, 3-Bug Fix
    publish_author VARCHAR(255) NOT NULL,
    public_date DATE NOT NULL,
    content TEXT NOT NULL,
    link TEXT DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Converted from datetime
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- Converted from datetime. ON UPDATE handled by trigger.
);

-- 添加表和列的注释
COMMENT ON TABLE t_change_log IS '系统更新日志';
COMMENT ON COLUMN t_change_log.change_log_id IS '更新日志id';
COMMENT ON COLUMN t_change_log.version IS '版本';
COMMENT ON COLUMN t_change_log.type IS '更新类型:[1:特大版本功能更新;2:功能更新;3:bug修复]';
COMMENT ON COLUMN t_change_log.publish_author IS '发布人';
COMMENT ON COLUMN t_change_log.public_date IS '发布日期';
COMMENT ON COLUMN t_change_log.content IS '更新内容';
COMMENT ON COLUMN t_change_log.link IS '跳转链接';
COMMENT ON COLUMN t_change_log.create_time IS '创建时间';
COMMENT ON COLUMN t_change_log.update_time IS '更新时间';

-- 创建索引
CREATE UNIQUE INDEX idx_t_change_log_version_unique ON t_change_log (version); -- Renamed from version_unique

-- 创建触发器 (确保 trigger_set_timestamp() 函数已创建)
CREATE TRIGGER set_timestamp_t_change_log
BEFORE UPDATE ON t_change_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: t_change_log
-- Note: Using E'' string syntax for proper handling of backslash escapes (\n) in PostgreSQL.
INSERT INTO t_change_log (change_log_id, version, type, publish_author, public_date, content, link, create_time, update_time) VALUES
(2, 'v1.1.0', 2, '卓大', '2020-05-09', E'SmartAdmin中后台系统 v1.1.0 版本（20200422）正式更新上线，更新内容如下：\n\n1.【新增】增加员工姓名查询\n\n2.【新增】增加文件预览组件\n\n3.【新增】新增四级菜单\n', 'http://smartadmin.1024lab.net/views/1.x/base/About.html', '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(8, 'v1.0.0', 1, '卓大', '2019-11-01', E'SmartAdmin中后台系统 v1.0.0 版本（20191101）正式更新上线，更新内容如下：\n\n1.【新增】人员管理\n\n2.【新增】系统设置\n\n3.【新增】心跳服务\n\n4.【新增】动态加载\n\n5.【新增】缓存策略\n\n6.【新增】定时任务', 'http://smartadmin.1024lab.net/views/1.x/base/About.html', '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(9, 'v1.2.0', 2, '卓大', '2020-05-23', E'SmartAdmin中后台系统 v1.2.0 版本（20200515）正式更新上线，更新内容如下：\n\n1.【新增】增加数据权限\n\n2.【新增】帮助文档', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(10, 'v1.2.1', 3, '卓大', '2020-05-24', E'SmartAdmin中后台系统 v1.2.1 版本（20200524）正式更新上线，更新内容如下：\n\n1.【修复】四级菜单权限bug\n\n2.【修复】缓存keepalive的Bug\n\n', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(11, 'v1.3.0', 2, '卓大', '2020-06-01', E'SmartAdmin中后台系统 v1.3.0 版本（20200601）正式更新上线，更新内容如下：\n\n1.【新增】工作台看板功能\n\n2.【新增】天气预报功能\n\n3.【新增】记录上次登录IP功能', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(12, 'v1.4.0', 2, '卓大', '2020-06-06', E'SmartAdmin中后台系统 v1.4.0 版本（20200606）正式更新上线，更新内容如下：\n\n1.【新增】联系客服功能\n\n2.【新增】意见反馈功能', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(13, 'v1.5.0', 2, '卓大', '2020-06-14', E'SmartAdmin中后台系统 v1.5.0 版本（20200614）正式更新上线，更新内容如下：\n\n1.【新增】OA系统\n\n2.【新增】通知公告', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(14, 'v1.6.0', 2, '卓大', '2020-06-17', E'SmartAdmin中后台系统 v1.6.0 版本（20200617）正式更新上线，更新内容如下：\n\n1.【新增】代码生成\n\n2.【新增】通知公告', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(15, 'v2.0.0', 1, '卓大', '2022-10-22', E'SmartAdmin中后台系统 v2.0.0 版本（20191101）正式更新上线，更新内容如下：\n\n1.【新增】人员管理\n\n2.【新增】系统设置\n\n3.【新增】心跳服务\n\n4.【新增】动态加载\n\n5.【新增】缓存策略\n\n6.【新增】定时任务', 'http://smartadmin.1024lab.net/views/1.x/base/About.html', '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(16, 'v1.7.0', 2, '卓大', '2022-10-22', E'SmartAdmin中后台系统 v1.7.0 版本（20200624）正式更新上线，更新内容如下：\n\n1.【新增】商品管理\n\n2.【新增】商品分类', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(18, 'v3.0.0', 1, '卓大', '2024-01-01', E'SmartAdmin中后台系统 v3.0.0 版本（20240101）正式更新上线，更新内容如下：\n\n\n1、【新增】权限从SpringSecurity 转成 Sa-Token\n\n2、【新增】增加接口 加密、解密功能\n\n3、【新增】增加网络安全相关功能：登录限制、密码复杂度、最大在线时长等\n\n4、【新增】ant desgin vue 为 4.x 最新版本\n\n5、【新增】升级 vite5\n\n6、【新增】swagger增加knife4j接口文档\n\n7、【优化】后端sa-common 改名为 sa-base\n\n8、【优化】优化官网文档说明\n', NULL, '2022-10-04 21:33:50', '2022-10-04 21:33:50');

-- Reset sequence if needed
-- SELECT setval('t_change_log_change_log_id_seq', (SELECT MAX(change_log_id) FROM t_change_log));

-- ============================================================
-- ============================================================
--   Table Name: t_code_generator_config
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_code_generator_config;

-- Create table
CREATE TABLE t_code_generator_config (
  table_name VARCHAR(255) PRIMARY KEY,
  basic TEXT,
  fields TEXT,
  insert_and_update TEXT,
  delete_info TEXT,
  query_fields TEXT,
  table_fields TEXT,
  detail TEXT,
  create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_code_generator_config IS '代码生成器的每个表的配置';
COMMENT ON COLUMN t_code_generator_config.table_name IS '表名';
COMMENT ON COLUMN t_code_generator_config.basic IS '基础命名信息';
COMMENT ON COLUMN t_code_generator_config.fields IS '字段列表';
COMMENT ON COLUMN t_code_generator_config.insert_and_update IS '新建、修改';
COMMENT ON COLUMN t_code_generator_config.delete_info IS '删除';
COMMENT ON COLUMN t_code_generator_config.query_fields IS '查询';
COMMENT ON COLUMN t_code_generator_config.table_fields IS '列表';
COMMENT ON COLUMN t_code_generator_config.detail IS '详情';
COMMENT ON COLUMN t_code_generator_config.create_time IS '创建时间';
COMMENT ON COLUMN t_code_generator_config.update_time IS '更新时间';

-- Unique constraint (already covered by PRIMARY KEY, but added for completeness if needed)
-- ALTER TABLE t_code_generator_config ADD CONSTRAINT uq_t_code_generator_config_table_name UNIQUE (table_name);
-- Note: A primary key implicitly creates a unique index, so the UNIQUE KEY `table_unique` is redundant in PostgreSQL.

-- Create trigger for update_time
CREATE TRIGGER trg_t_code_generator_config_set_update_time
BEFORE UPDATE ON t_code_generator_config
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_code_generator_config (table_name, basic, fields, insert_and_update, delete_info, query_fields, table_fields, detail, create_time, update_time) VALUES
('erp_customer', '{"backendAuthor":"x","backendDate":1741941404000,"copyright":"x","description":"客户信息","frontAuthor":"x","frontDate":1741941404000,"javaPackageName":"xxx","moduleName":"ErpCustomer"}', '[{"autoIncreaseFlag":true,"columnComment":"主键ID","columnName":"id","fieldName":"id","javaType":"Long","jsType":"Number","label":"主键ID","primaryKeyFlag":true},{"autoIncreaseFlag":false,"columnComment":"客户编码","columnName":"customer_code","fieldName":"customerCode","javaType":"String","jsType":"String","label":"客户编码","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"客户名称","columnName":"customer_name","fieldName":"customerName","javaType":"String","jsType":"String","label":"客户名称","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"字典|customer_type 客户类型：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商","columnName":"customer_type","fieldName":"customerType","javaType":"String","jsType":"String","label":"字典|customer_type 客户类型：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"字典|credit_level 信用等级：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级","columnName":"credit_level","fieldName":"creditLevel","javaType":"String","jsType":"String","label":"字典|credit_level 信用等级：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人姓名","columnName":"contact_name","fieldName":"contactName","javaType":"String","jsType":"String","label":"联系人姓名","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人职位","columnName":"contact_position","fieldName":"contactPosition","javaType":"String","jsType":"String","label":"联系人职位","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人部门","columnName":"contact_department","fieldName":"contactDepartment","javaType":"String","jsType":"String","label":"联系人部门","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人电话","columnName":"contact_phone","fieldName":"contactPhone","javaType":"String","jsType":"String","label":"联系人电话","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人手机","columnName":"contact_mobile","fieldName":"contactMobile","javaType":"String","jsType":"String","label":"联系人手机","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人邮箱","columnName":"contact_email","fieldName":"contactEmail","javaType":"String","jsType":"String","label":"联系人邮箱","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"省份","columnName":"province","fieldName":"province","javaType":"String","jsType":"String","label":"省份","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"城市","columnName":"city","fieldName":"city","javaType":"String","jsType":"String","label":"城市","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"区县","columnName":"district","fieldName":"district","javaType":"String","jsType":"String","label":"区县","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"详细地址","columnName":"address","fieldName":"address","javaType":"String","jsType":"String","label":"详细地址","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"收货地址","columnName":"shipping_address","fieldName":"shippingAddress","javaType":"String","jsType":"String","label":"收货地址","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"发票地址","columnName":"billing_address","fieldName":"billingAddress","javaType":"String","jsType":"String","label":"发票地址","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"营业执照号","columnName":"business_license","fieldName":"businessLicense","javaType":"String","jsType":"String","label":"营业执照号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"税务登记号","columnName":"tax_id","fieldName":"taxId","javaType":"String","jsType":"String","label":"税务登记号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"开户银行","columnName":"bank_name","fieldName":"bankName","javaType":"String","jsType":"String","label":"开户银行","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"银行账号","columnName":"bank_account","fieldName":"bankAccount","javaType":"String","jsType":"String","label":"银行账号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"字典|customer_status 客户状态：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户","columnName":"status","fieldName":"status","javaType":"String","jsType":"String","label":"字典|customer_status 客户状态：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"备注","columnName":"remark","fieldName":"remark","javaType":"String","jsType":"String","label":"备注","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"创建人ID","columnName":"created_by","fieldName":"createdBy","javaType":"Long","jsType":"Number","label":"创建人ID","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"更新人ID","columnName":"updated_by","fieldName":"updatedBy","javaType":"Long","jsType":"Number","label":"更新人ID","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"删除标识 0未删除 1已删除","columnName":"deleted_flag","fieldName":"deletedFlag","javaType":"Integer","jsType":"Number","label":"删除标识 0未删除 1已删除","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"创建时间","columnName":"create_time","fieldName":"createTime","javaType":"LocalDateTime","jsType":"Date","label":"创建时间","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"更新时间","columnName":"update_time","fieldName":"updateTime","javaType":"LocalDateTime","jsType":"Date","label":"更新时间","primaryKeyFlag":false}]', '{"countPerLine":1,"fieldList":[{"columnName":"id","frontComponent":"InputNumber","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"customer_code","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"customer_name","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"customer_type","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"credit_level","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_name","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_position","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_department","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_phone","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_mobile","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_email","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"province","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"city","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"district","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"address","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"shipping_address","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"billing_address","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"business_license","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"tax_id","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"bank_name","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"bank_account","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"status","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"remark","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"created_by","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"updated_by","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"deleted_flag","frontComponent":"InputNumber","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"create_time","frontComponent":"DateTime","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"update_time","frontComponent":"DateTime","insertFlag":true,"requiredFlag":true,"updateFlag":false}],"isSupportInsertAndUpdate":true,"pageType":"modal","width":"44"}', '{"deleteEnum":"SingleAndBatch","isPhysicallyDeleted":false,"isSupportDelete":true}', '[]', '[{"columnName":"id","ellipsisFlag":true,"fieldName":"id","label":"主键ID","showFlag":true},{"columnName":"customer_code","ellipsisFlag":true,"fieldName":"customerCode","label":"客户编码","showFlag":true},{"columnName":"customer_name","ellipsisFlag":true,"fieldName":"customerName","label":"客户名称","showFlag":true},{"columnName":"customer_type","ellipsisFlag":true,"fieldName":"customerType","label":"字典|customer_type 客户类型：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商","showFlag":true},{"columnName":"credit_level","ellipsisFlag":true,"fieldName":"creditLevel","label":"字典|credit_level 信用等级：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级","showFlag":true},{"columnName":"contact_name","ellipsisFlag":true,"fieldName":"contactName","label":"联系人姓名","showFlag":true},{"columnName":"contact_position","ellipsisFlag":true,"fieldName":"contactPosition","label":"联系人职位","showFlag":true},{"columnName":"contact_department","ellipsisFlag":true,"fieldName":"contactDepartment","label":"联系人部门","showFlag":true},{"columnName":"contact_phone","ellipsisFlag":true,"fieldName":"contactPhone","label":"联系人电话","showFlag":true},{"columnName":"contact_mobile","ellipsisFlag":true,"fieldName":"contactMobile","label":"联系人手机","showFlag":true},{"columnName":"contact_email","ellipsisFlag":true,"fieldName":"contactEmail","label":"联系人邮箱","showFlag":true},{"columnName":"province","ellipsisFlag":true,"fieldName":"province","label":"省份","showFlag":true},{"columnName":"city","ellipsisFlag":true,"fieldName":"city","label":"城市","showFlag":true},{"columnName":"district","ellipsisFlag":true,"fieldName":"district","label":"区县","showFlag":true},{"columnName":"address","ellipsisFlag":true,"fieldName":"address","label":"详细地址","showFlag":true},{"columnName":"shipping_address","ellipsisFlag":true,"fieldName":"shippingAddress","label":"收货地址","showFlag":true},{"columnName":"billing_address","ellipsisFlag":true,"fieldName":"billingAddress","label":"发票地址","showFlag":true},{"columnName":"business_license","ellipsisFlag":true,"fieldName":"businessLicense","label":"营业执照号","showFlag":true},{"columnName":"tax_id","ellipsisFlag":true,"fieldName":"taxId","label":"税务登记号","showFlag":true},{"columnName":"bank_name","ellipsisFlag":true,"fieldName":"bankName","label":"开户银行","showFlag":true},{"columnName":"bank_account","ellipsisFlag":true,"fieldName":"bankAccount","label":"银行账号","showFlag":true},{"columnName":"status","ellipsisFlag":true,"fieldName":"status","label":"字典|customer_status 客户状态：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户","showFlag":true},{"columnName":"remark","ellipsisFlag":true,"fieldName":"remark","label":"备注","showFlag":true},{"columnName":"created_by","ellipsisFlag":true,"fieldName":"createdBy","label":"创建人ID","showFlag":true},{"columnName":"updated_by","ellipsisFlag":true,"fieldName":"updatedBy","label":"更新人ID","showFlag":true},{"columnName":"deleted_flag","ellipsisFlag":true,"fieldName":"deletedFlag","label":"删除标识 0未删除 1已删除","showFlag":true},{"columnName":"create_time","ellipsisFlag":true,"fieldName":"createTime","label":"创建时间","showFlag":true},{"columnName":"update_time","ellipsisFlag":true,"fieldName":"updateTime","label":"更新时间","showFlag":true}]', NULL, '2025-03-15 14:10:02', '2025-03-15 14:10:02'),
('txy_supplier', '{"backendAuthor":"x","backendDate":1741772640000,"copyright":" ","description":"供应商信息表","frontAuthor":"x","frontDate":1741772640000,"javaPackageName":"net.lab1024.sa.admin.module.business","moduleName":"Supplier"}', '[{"autoIncreaseFlag":true,"columnComment":"供应商ID","columnName":"supplier_id","fieldName":"supplierId","javaType":"Long","jsType":"Number","label":"供应商ID","primaryKeyFlag":true},{"autoIncreaseFlag":false,"columnComment":"供应商编码","columnName":"supplier_code","fieldName":"supplierCode","javaType":"String","jsType":"String","label":"供应商编码","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"供应商名称","columnName":"supplier_name","fieldName":"supplierName","javaType":"String","jsType":"String","label":"供应商名称","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"供应商类型：material-原材料供应商，equipment-设备供应商，service-服务供应商","columnName":"supplier_type","fieldName":"supplierType","javaType":"String","jsType":"String","label":"供应商类型：material-原材料供应商，equipment-设备供应商，service-服务供应商","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"营业执照号","columnName":"business_license","fieldName":"businessLicense","javaType":"String","jsType":"String","label":"营业执照号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"税务登记号","columnName":"tax_id","fieldName":"taxId","javaType":"String","jsType":"String","label":"税务登记号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"主要联系人","columnName":"contact_person","fieldName":"contactPerson","javaType":"String","jsType":"String","label":"主要联系人","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系电话","columnName":"contact_phone","fieldName":"contactPhone","javaType":"String","jsType":"String","label":"联系电话","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系邮箱","columnName":"contact_email","fieldName":"contactEmail","javaType":"String","jsType":"String","label":"联系邮箱","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"联系人职位","columnName":"contact_position","fieldName":"contactPosition","javaType":"String","jsType":"String","label":"联系人职位","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"备用联系人","columnName":"alternative_contact","fieldName":"alternativeContact","javaType":"String","jsType":"String","label":"备用联系人","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"备用联系电话","columnName":"alternative_phone","fieldName":"alternativePhone","javaType":"String","jsType":"String","label":"备用联系电话","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"省份","columnName":"province","fieldName":"province","javaType":"String","jsType":"String","label":"省份","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"城市","columnName":"city","fieldName":"city","javaType":"String","jsType":"String","label":"城市","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"区县","columnName":"district","fieldName":"district","javaType":"String","jsType":"String","label":"区县","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"详细地址","columnName":"address","fieldName":"address","javaType":"String","jsType":"String","label":"详细地址","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"邮政编码","columnName":"postal_code","fieldName":"postalCode","javaType":"String","jsType":"String","label":"邮政编码","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"开户银行","columnName":"bank_name","fieldName":"bankName","javaType":"String","jsType":"String","label":"开户银行","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"银行账号","columnName":"bank_account","fieldName":"bankAccount","javaType":"String","jsType":"String","label":"银行账号","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"开户名称","columnName":"bank_account_name","fieldName":"bankAccountName","javaType":"String","jsType":"String","label":"开户名称","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"付款条件","columnName":"payment_terms","fieldName":"paymentTerms","javaType":"String","jsType":"String","label":"付款条件","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"信用额度","columnName":"credit_limit","fieldName":"creditLimit","javaType":"BigDecimal","jsType":"Number","label":"信用额度","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"信用期(天)","columnName":"credit_period","fieldName":"creditPeriod","javaType":"Integer","jsType":"Number","label":"信用期(天)","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"合作开始日期","columnName":"cooperation_start_date","fieldName":"cooperationStartDate","javaType":"LocalDate","jsType":"Date","label":"合作开始日期","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"合同到期日期","columnName":"contract_expiry_date","fieldName":"contractExpiryDate","javaType":"LocalDate","jsType":"Date","label":"合同到期日期","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"最近订单日期","columnName":"last_order_date","fieldName":"lastOrderDate","javaType":"LocalDate","jsType":"Date","label":"最近订单日期","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"累计订单金额","columnName":"total_order_amount","fieldName":"totalOrderAmount","javaType":"BigDecimal","jsType":"Number","label":"累计订单金额","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"是否战略供应商：0-否，1-是","columnName":"is_strategic","fieldName":"isStrategic","javaType":"Integer","jsType":"Number","label":"是否战略供应商：0-否，1-是","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"折扣率","columnName":"discount_rate","fieldName":"discountRate","javaType":"BigDecimal","jsType":"Number","label":"折扣率","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"单瓦加工费(元/m²)","columnName":"processing_fee_single","fieldName":"processingFeeSingle","javaType":"BigDecimal","jsType":"Number","label":"单瓦加工费(元/m²)","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"双瓦加工费(元/m²)","columnName":"processing_fee_double","fieldName":"processingFeeDouble","javaType":"BigDecimal","jsType":"Number","label":"双瓦加工费(元/m²)","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"税率","columnName":"tax_rate","fieldName":"taxRate","javaType":"BigDecimal","jsType":"Number","label":"税率","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"最小订单量","columnName":"min_order_quantity","fieldName":"minOrderQuantity","javaType":"Integer","jsType":"Number","label":"最小订单量","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"交货提前期(天)","columnName":"lead_time","fieldName":"leadTime","javaType":"Integer","jsType":"Number","label":"交货提前期(天)","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"状态：active-活跃，inactive-非活跃，blacklisted-黑名单","columnName":"status","fieldName":"status","javaType":"String","jsType":"String","label":"状态：active-活跃，inactive-非活跃，blacklisted-黑名单","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"审批状态：pending-待审批，approved-已批准，rejected-已拒绝","columnName":"approval_status","fieldName":"approvalStatus","javaType":"String","jsType":"String","label":"审批状态：pending-待审批，approved-已批准，rejected-已拒绝","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"审批人ID","columnName":"approval_user_id","fieldName":"approvalUserId","javaType":"Long","jsType":"Number","label":"审批人ID","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"审批日期","columnName":"approval_date","fieldName":"approvalDate","javaType":"LocalDateTime","jsType":"Date","label":"审批日期","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"审批备注","columnName":"approval_notes","fieldName":"approvalNotes","javaType":"String","jsType":"String","label":"审批备注","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"备注","columnName":"remark","fieldName":"remark","javaType":"String","jsType":"String","label":"备注","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"附件路径","columnName":"attachment_path","fieldName":"attachmentPath","javaType":"String","jsType":"String","label":"附件路径","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"创建人ID","columnName":"created_by","fieldName":"createdBy","javaType":"Long","jsType":"Number","label":"创建人ID","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"更新人ID","columnName":"updated_by","fieldName":"updatedBy","javaType":"Long","jsType":"Number","label":"更新人ID","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"是否删除：0-否，1-是","columnName":"deleted_flag","fieldName":"deletedFlag","javaType":"Integer","jsType":"Number","label":"是否删除：0-否，1-是","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"创建时间","columnName":"create_time","fieldName":"createTime","javaType":"LocalDateTime","jsType":"Date","label":"创建时间","primaryKeyFlag":false},{"autoIncreaseFlag":false,"columnComment":"更新时间","columnName":"update_time","fieldName":"updateTime","javaType":"LocalDateTime","jsType":"Date","label":"更新时间","primaryKeyFlag":false}]', '{"countPerLine":2,"fieldList":[{"columnName":"supplier_id","frontComponent":"InputNumber","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"supplier_code","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"supplier_name","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"supplier_type","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"business_license","frontComponent":"DictSelect","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"tax_id","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_person","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_phone","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_email","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contact_position","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"alternative_contact","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"alternative_phone","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"province","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"city","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"district","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"address","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"postal_code","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"bank_name","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"bank_account","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"bank_account_name","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"payment_terms","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"credit_limit","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"credit_period","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"cooperation_start_date","frontComponent":"Date","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"contract_expiry_date","frontComponent":"Date","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"last_order_date","frontComponent":"Date","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"total_order_amount","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"is_strategic","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"discount_rate","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"processing_fee_single","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"processing_fee_double","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"tax_rate","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"min_order_quantity","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"lead_time","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"status","frontComponent":"Input","insertFlag":true,"requiredFlag":true,"updateFlag":false},{"columnName":"approval_status","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"approval_user_id","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"approval_date","frontComponent":"DateTime","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"approval_notes","frontComponent":"Textarea","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"remark","frontComponent":"Textarea","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"attachment_path","frontComponent":"Input","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"created_by","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"updated_by","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"deleted_flag","frontComponent":"InputNumber","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"create_time","frontComponent":"DateTime","insertFlag":false,"requiredFlag":false,"updateFlag":false},{"columnName":"update_time","frontComponent":"DateTime","insertFlag":false,"requiredFlag":false,"updateFlag":false}],"isSupportInsertAndUpdate":true,"pageType":"modal","width":"500"}', '{"deleteEnum":"SingleAndBatch","isPhysicallyDeleted":false,"isSupportDelete":true}', '[{"columnNameList":["supplier_name"],"fieldName":"supplierName","label":"供应商名称","queryTypeEnum":"Like","width":"200px"}]', '[{"columnName":"supplier_id","ellipsisFlag":true,"fieldName":"supplierId","label":"供应商ID","showFlag":true},{"columnName":"supplier_code","ellipsisFlag":true,"fieldName":"supplierCode","label":"供应商编码","showFlag":true},{"columnName":"supplier_name","ellipsisFlag":true,"fieldName":"supplierName","label":"供应商名称","showFlag":false},{"columnName":"supplier_type","ellipsisFlag":true,"fieldName":"supplierType","label":"供应商类型","showFlag":true},{"columnName":"business_license","ellipsisFlag":true,"fieldName":"businessLicense","label":"营业执照号","showFlag":false},{"columnName":"tax_id","ellipsisFlag":true,"fieldName":"taxId","label":"税务登记号","showFlag":false},{"columnName":"contact_person","ellipsisFlag":true,"fieldName":"contactPerson","label":"主要联系人","showFlag":true},{"columnName":"contact_phone","ellipsisFlag":true,"fieldName":"contactPhone","label":"联系电话","showFlag":true},{"columnName":"contact_email","ellipsisFlag":true,"fieldName":"contactEmail","label":"联系邮箱","showFlag":true},{"columnName":"contact_position","ellipsisFlag":true,"fieldName":"contactPosition","label":"联系人职位","showFlag":false},{"columnName":"alternative_contact","ellipsisFlag":true,"fieldName":"alternativeContact","label":"备用联系人","showFlag":false},{"columnName":"alternative_phone","ellipsisFlag":true,"fieldName":"alternativePhone","label":"备用联系电话","showFlag":false},{"columnName":"province","ellipsisFlag":true,"fieldName":"province","label":"省份","showFlag":false},{"columnName":"city","ellipsisFlag":true,"fieldName":"city","label":"城市","showFlag":false},{"columnName":"district","ellipsisFlag":true,"fieldName":"district","label":"区县","showFlag":false},{"columnName":"address","ellipsisFlag":true,"fieldName":"address","label":"详细地址","showFlag":false},{"columnName":"postal_code","ellipsisFlag":true,"fieldName":"postalCode","label":"邮政编码","showFlag":false},{"columnName":"bank_name","ellipsisFlag":true,"fieldName":"bankName","label":"开户银行","showFlag":false},{"columnName":"bank_account","ellipsisFlag":true,"fieldName":"bankAccount","label":"银行账号","showFlag":false},{"columnName":"bank_account_name","ellipsisFlag":true,"fieldName":"bankAccountName","label":"开户名称","showFlag":false},{"columnName":"payment_terms","ellipsisFlag":true,"fieldName":"paymentTerms","label":"付款条件","showFlag":false},{"columnName":"credit_limit","ellipsisFlag":true,"fieldName":"creditLimit","label":"信用额度","showFlag":false},{"columnName":"credit_period","ellipsisFlag":true,"fieldName":"creditPeriod","label":"信用期(天)","showFlag":false},{"columnName":"cooperation_start_date","ellipsisFlag":true,"fieldName":"cooperationStartDate","label":"合作开始日期","showFlag":false},{"columnName":"contract_expiry_date","ellipsisFlag":true,"fieldName":"contractExpiryDate","label":"合同到期日期","showFlag":false},{"columnName":"last_order_date","ellipsisFlag":true,"fieldName":"lastOrderDate","label":"最近订单日期","showFlag":false},{"columnName":"total_order_amount","ellipsisFlag":true,"fieldName":"totalOrderAmount","label":"累计订单金额","showFlag":false},{"columnName":"is_strategic","ellipsisFlag":true,"fieldName":"isStrategic","label":"是否战略供应商：0-否，1-是","showFlag":false},{"columnName":"discount_rate","ellipsisFlag":true,"fieldName":"discountRate","label":"折扣率","showFlag":false},{"columnName":"processing_fee_single","ellipsisFlag":true,"fieldName":"processingFeeSingle","label":"单瓦加工费(元/m²)","showFlag":false},{"columnName":"processing_fee_double","ellipsisFlag":true,"fieldName":"processingFeeDouble","label":"双瓦加工费(元/m²)","showFlag":false},{"columnName":"tax_rate","ellipsisFlag":true,"fieldName":"taxRate","label":"税率","showFlag":false},{"columnName":"min_order_quantity","ellipsisFlag":true,"fieldName":"minOrderQuantity","label":"最小订单量","showFlag":false},{"columnName":"lead_time","ellipsisFlag":true,"fieldName":"leadTime","label":"交货提前期(天)","showFlag":false},{"columnName":"status","ellipsisFlag":true,"fieldName":"status","label":"状态：active-活跃，inactive-非活跃，blacklisted-黑名单","showFlag":false},{"columnName":"approval_status","ellipsisFlag":true,"fieldName":"approvalStatus","label":"审批状态：pending-待审批，approved-已批准，rejected-已拒绝","showFlag":false},{"columnName":"approval_user_id","ellipsisFlag":true,"fieldName":"approvalUserId","label":"审批人ID","showFlag":false},{"columnName":"approval_date","ellipsisFlag":true,"fieldName":"approvalDate","label":"审批日期","showFlag":false},{"columnName":"approval_notes","ellipsisFlag":true,"fieldName":"approvalNotes","label":"审批备注","showFlag":false},{"columnName":"remark","ellipsisFlag":true,"fieldName":"remark","label":"备注","showFlag":false},{"columnName":"attachment_path","ellipsisFlag":true,"fieldName":"attachmentPath","label":"附件路径","showFlag":false},{"columnName":"created_by","ellipsisFlag":true,"fieldName":"createdBy","label":"创建人ID","showFlag":false},{"columnName":"updated_by","ellipsisFlag":true,"fieldName":"updatedBy","label":"更新人ID","showFlag":false},{"columnName":"deleted_flag","ellipsisFlag":true,"fieldName":"deletedFlag","label":"是否删除：0-否，1-是","showFlag":false},{"columnName":"create_time","ellipsisFlag":true,"fieldName":"createTime","label":"创建时间","showFlag":false},{"columnName":"update_time","ellipsisFlag":true,"fieldName":"updateTime","label":"更新时间","showFlag":false}]', NULL, '2025-03-12 18:28:41', '2025-03-12 18:28:41');

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_config
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_config;

-- Create table
CREATE TABLE t_config (
  config_id BIGSERIAL PRIMARY KEY,
  config_name VARCHAR(255) NOT NULL,
  config_key VARCHAR(255) NOT NULL,
  config_value TEXT NOT NULL,
  remark VARCHAR(255) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_config IS '系统配置';
COMMENT ON COLUMN t_config.config_id IS '主键';
COMMENT ON COLUMN t_config.config_name IS '参数名字';
COMMENT ON COLUMN t_config.config_key IS '参数key';
COMMENT ON COLUMN t_config.config_value IS '参数值'; -- Adding a generic comment as none was provided for the content itself
COMMENT ON COLUMN t_config.remark IS '备注'; -- Adding a generic comment as none was provided
COMMENT ON COLUMN t_config.update_time IS '上次修改时间';
COMMENT ON COLUMN t_config.create_time IS '创建时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_config_set_update_time
BEFORE UPDATE ON t_config
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Removed backslashes from the JSON string in config_value for the second record.
INSERT INTO t_config (config_id, config_name, config_key, config_value, remark, update_time, create_time) VALUES
(1, '万能密码', 'super_password', '1024ok', '一路春光啊一路荆棘呀惊鸿一般短暂如夏花一样绚烂这是一个不能停留太久的世界，一路春光啊一路荆棘呀惊鸿一般短暂如夏花一样绚烂这是一个不能停留太久的世界啊', '2024-09-03 21:27:03', '2021-12-16 23:32:46'),
(2, '三级等保', 'level3_protect_config', '{   "fileDetectFlag": true,   "loginActiveTimeoutMinutes": 10000,   "loginFailLockMinutes": 5,   "loginFailMaxTimes": 100,   "maxUploadFileSizeMb": 30,   "passwordComplexityEnabled": true,   "regularChangePasswordMonths": 3,   "regularChangePasswordNotAllowRepeatTimes": 3,   "twoFactorLoginEnabled": false }', 'SmartJob Sample2 update', '2025-03-12 16:44:55', '2024-08-13 11:44:49');

-- Set sequence value
-- The original AUTO_INCREMENT was 10, and the max inserted config_id is 2.
-- Set the next value to 10 to match the original schema behavior.
SELECT setval('t_config_config_id_seq', 10, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_data_tracer
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_data_tracer;

-- Create table
CREATE TABLE t_data_tracer (
  data_tracer_id BIGSERIAL PRIMARY KEY,
  data_id BIGINT NOT NULL,
  type INTEGER NOT NULL,
  content TEXT,
  diff_old TEXT,
  diff_new TEXT,
  extra_data TEXT,
  user_id BIGINT NOT NULL,
  user_type INTEGER NOT NULL,
  user_name VARCHAR(50) NOT NULL,
  ip VARCHAR(50) DEFAULT NULL,
  ip_region VARCHAR(1000) DEFAULT NULL,
  user_agent VARCHAR(2000) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_data_tracer IS '各种单据操作记录';
COMMENT ON COLUMN t_data_tracer.data_tracer_id IS '主键ID'; -- Inferred from AUTO_INCREMENT
COMMENT ON COLUMN t_data_tracer.data_id IS '各种单据的id';
COMMENT ON COLUMN t_data_tracer.type IS '单据类型';
COMMENT ON COLUMN t_data_tracer.content IS '操作内容';
COMMENT ON COLUMN t_data_tracer.diff_old IS '差异：旧的数据';
COMMENT ON COLUMN t_data_tracer.diff_new IS '差异：新的数据';
COMMENT ON COLUMN t_data_tracer.extra_data IS '额外信息';
COMMENT ON COLUMN t_data_tracer.user_id IS '用户id';
COMMENT ON COLUMN t_data_tracer.user_type IS '用户类型：1 后管用户 ';
COMMENT ON COLUMN t_data_tracer.user_name IS '用户名称';
COMMENT ON COLUMN t_data_tracer.ip IS 'ip';
COMMENT ON COLUMN t_data_tracer.ip_region IS 'ip地区';
COMMENT ON COLUMN t_data_tracer.user_agent IS '用户ua';
COMMENT ON COLUMN t_data_tracer.update_time IS '更新时间';
COMMENT ON COLUMN t_data_tracer.create_time IS '创建时间';

-- Create index
CREATE INDEX idx_t_data_tracer_data_id_type ON t_data_tracer (data_id, type);

-- Create trigger for update_time
CREATE TRIGGER trg_t_data_tracer_set_update_time
BEFORE UPDATE ON t_data_tracer
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_data_tracer (data_tracer_id, data_id, type, content, diff_old, diff_new, extra_data, user_id, user_type, user_name, ip, ip_region, user_agent, update_time, create_time) VALUES
(35, 10, 1, '新增', NULL, NULL, NULL, 47, 1, '善逸', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.61', '2023-10-07 19:02:24', '2023-10-07 19:02:24'),
(36, 11, 1, '新增', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 19:55:53', '2023-12-01 19:55:53'),
(37, 12, 1, '新增', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 19:57:26', '2023-12-01 19:57:26'),
(38, 11, 1, '', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 19:58:09', '2023-12-01 19:58:09'),
(39, 2, 3, '修改企业信息', '统一社会信用代码:"1024lab"<br/>详细地址:"1024大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"卓大"<br/>省份名称:"河南省"<br/>企业logo:"public/common/fb827d63dda74a60ab8b4f70cc7c7d0a_20221022145641_jpg"<br/>联系人电话:"***********"<br/>企业名称:"1024创新实验室"<br/>邮箱:"<EMAIL>"', '营业执照:"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg"<br/>统一社会信用代码:"1024lab1"<br/>详细地址:"1024大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"卓大1"<br/>省份名称:"河南省"<br/>企业logo:""<br/>联系人电话:"***********"<br/>企业名称:"1024创新实验室1"<br/>邮箱:"<EMAIL>"', NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 20:05:05', '2023-12-01 20:05:05'),
(40, 2, 3, '修改企业信息', '营业执照:"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg"<br/>统一社会信用代码:"1024lab1"<br/>详细地址:"1024大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"卓大1"<br/>省份名称:"河南省"<br/>企业logo:""<br/>联系人电话:"***********"<br/>企业名称:"1024创新实验室1"<br/>邮箱:"<EMAIL>"', '营业执照:"public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg"<br/>统一社会信用代码:"1024lab"<br/>详细地址:"1024大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:外资企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"卓大"<br/>省份名称:"河南省"<br/>企业logo:""<br/>联系人电话:"***********"<br/>企业名称:"1024创新实验室"<br/>邮箱:"<EMAIL>"', NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 20:05:54', '2023-12-01 20:05:54'),
(41, 2, 3, '更新银行:<br/>', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 20:09:17', '2023-12-01 20:09:17'),
(42, 2, 3, '更新发票：<br/>删除状态:由【false】变更为【】', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2023-12-01 20:09:20', '2023-12-01 20:09:20'),
(49, 1, 3, '修改企业信息', '营业执照:"public/common/852b7e19bef94af39c1a6156edf47cfb_20221022170332_jpg"<br/>统一社会信用代码:"1024lab_block"<br/>详细地址:"区块链大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"开云"<br/>省份名称:"河南省"<br/>企业logo:"public/common/f4a76fa720814949a610f05f6f9545bf_20221022170256_jpg"<br/>联系人电话:"***********"<br/>企业名称:"1024创新区块链实验室"', '营业执照:"public/common/1d89055e5680426280446aff1e7e627c_20240306112451.jpeg"<br/>统一社会信用代码:"1024lab_block"<br/>详细地址:"区块链大楼"<br/>区县名称:"洛龙区"<br/>禁用状态:false<br/>类型:有限企业<br/>城市名称:"洛阳市"<br/>删除状态:false<br/>联系人:"开云"<br/>省份名称:"河南省"<br/>企业logo:"public/common/34f5ac0fc097402294aea75352c128f0_20240306112435.png"<br/>联系人电话:"***********"<br/>企业名称:"1024创新区块链实验室"', NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2024-03-06 11:24:55', '2024-03-06 11:24:55'),
(99, 12, 1, '', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2024-09-03 21:06:32', '2024-09-03 21:06:32'),
(100, 1, 4, '新增', NULL, NULL, NULL, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', '2025-03-12 21:24:04', '2025-03-12 21:24:04');

-- Set sequence value
-- The original AUTO_INCREMENT was 101, and the max inserted data_tracer_id is 100.
-- Set the next value to 101 to match the original schema behavior.
SELECT setval('t_data_tracer_data_tracer_id_seq', 101, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_department
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_department;

-- Create table
CREATE TABLE t_department (
  department_id BIGSERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  manager_id BIGINT DEFAULT NULL,
  parent_id BIGINT NOT NULL DEFAULT 0,
  sort INTEGER NOT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_department IS '部门';
COMMENT ON COLUMN t_department.department_id IS '部门主键id';
COMMENT ON COLUMN t_department.name IS '部门名称';
COMMENT ON COLUMN t_department.manager_id IS '部门负责人id';
COMMENT ON COLUMN t_department.parent_id IS '部门的父级id';
COMMENT ON COLUMN t_department.sort IS '部门排序';
COMMENT ON COLUMN t_department.update_time IS '更新时间';
COMMENT ON COLUMN t_department.create_time IS '创建时间';

-- Create index
CREATE INDEX idx_t_department_parent_id ON t_department (parent_id);

-- Insert data
INSERT INTO t_department (department_id, name, manager_id, parent_id, sort, update_time, create_time) VALUES
(1, '同馨缘彩印包装有限公司', 1, 0, 1, '2022-10-19 20:17:09', '2022-10-19 20:17:09'),
(9, '内勤部', 1, 1, 0, '2025-03-24 10:37:04', '2025-03-24 10:37:04'),
(10, '销售部', 1, 1, 0, '2025-03-25 16:46:24', '2025-03-25 16:46:24');

-- Set sequence value
-- The original AUTO_INCREMENT was 11, and the max inserted department_id is 10.
-- Set the next value to 11 to match the original schema behavior.
SELECT setval('t_department_department_id_seq', 11, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_dict_key
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_dict_key;

-- Create table
CREATE TABLE t_dict_key (
  dict_key_id BIGSERIAL PRIMARY KEY,
  key_code VARCHAR(50) NOT NULL,
  key_name VARCHAR(50) NOT NULL,
  remark VARCHAR(500) DEFAULT NULL,
  deleted_flag BOOLEAN NOT NULL DEFAULT false,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_dict_key IS '字典key';
COMMENT ON COLUMN t_dict_key.dict_key_id IS '主键ID'; -- Inferred from AUTO_INCREMENT
COMMENT ON COLUMN t_dict_key.key_code IS '编码';
COMMENT ON COLUMN t_dict_key.key_name IS '名称';
COMMENT ON COLUMN t_dict_key.remark IS '备注';
COMMENT ON COLUMN t_dict_key.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_dict_key.update_time IS '更新时间';
COMMENT ON COLUMN t_dict_key.create_time IS '创建时间';

-- Create unique constraint
ALTER TABLE t_dict_key ADD CONSTRAINT uq_t_dict_key_key_code UNIQUE (key_code);

-- Create trigger for update_time
CREATE TRIGGER trg_t_dict_key_set_update_time
BEFORE UPDATE ON t_dict_key
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_dict_key (dict_key_id, key_code, key_name, remark, deleted_flag, update_time, create_time) VALUES
(1, 'GODOS_PLACE', '商品产地', '商品产地的字典', false, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(8, 'production_status', '生产状态', '订单生产状态', false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(9, 'payment_status', '付款状态', '订单付款状态', false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(18, 'supplier_type', '供应商类型', '供应商的业务类型分类', false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(19, 'supplier_level', '供应商等级', '供应商的评级分类', false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(20, 'supplier_status', '供应商状态', '供应商的当前合作状态', false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(21, 'approval_status', '审批状态', '供应商信息的审批状态', false, '2025-03-12 18:20:36', '2025-03-12 18:20:36'),
(22, 'yes_no', '是否选项', '通用是否选项', false, '2025-03-12 18:20:36', '2025-03-12 18:20:36'),
(23, 'paper_status', '纸张状态', '纸张的状态', false, '2025-03-13 23:46:10', '2025-03-13 23:46:10'),
(24, 'paper_brand', '纸张品牌', '纸张的生产厂商', false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(26, 'corrugated_shape', '纸张楞形', '纸张楞形', false, '2025-03-18 14:17:38', '2025-03-13 23:46:11'),
(27, 'paper_weight', '纸张克重', '纸张的克重值', false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(29, 'paper_unit', '纸张单位', '纸张计量单位字典', false, '2025-03-14 15:49:17', '2025-03-14 15:49:17'),
(30, 'quotation_type', '报价类型', '纸张报价类型字典', false, '2025-03-14 15:49:17', '2025-03-14 15:49:17'),
(34, 'quotation_status', '报价单状态', '报价单状态字典', false, '2025-03-14 15:54:58', '2025-03-14 15:54:58'),
(41, 'customer_type', '客户类型', '客户类型字典', false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(42, 'customer_status', '客户状态', '客户状态字典', false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(43, 'credit_level', '信用等级', '客户信用等级字典', false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(44, 'address_type', '地址类型', '客户地址类型字典', false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(47, 'paper_category_corrugated_shape', '纸张类别楞形', '纸张类别楞形', false, '2025-03-18 14:18:27', '2025-03-14 23:46:31'),
(48, 'two_layer_corrugated_form', '2层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', true, '2025-03-15 16:48:38', '2025-03-15 16:30:11'), -- deleted_flag was 1
(52, 'three_layer_corrugated_shape', '3层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-18 14:18:24', '2025-03-15 16:30:11'),
(54, 'four_layer_corrugated_shape', '4层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-18 14:18:20', '2025-03-15 16:30:11'),
(59, 'box_quotation_status', '纸箱报价单状态', '纸箱报价单的状态分类', false, '2025-03-19 01:28:27', '2025-03-19 01:28:27'),
(60, 'business_type', '业务类型', '纸箱业务类型分类', false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(62, 'paper_board_structure_layer', '纸板结构层', '纸张类型分类', false, '2025-03-21 13:09:27', '2025-03-19 01:28:30'),
(63, 'include_tax', '是否含税', '价格是否含税标识', false, '2025-03-19 01:28:30', '2025-03-19 01:28:30'),
(64, 'box_type', '箱形', '纸箱箱型分类', false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(65, 'water_print_box_layer_number', '水印纸箱层数', '水印纸箱层数', false, '2025-03-18 16:41:38', '2025-03-18 16:30:11'),
(66, 'color_print_box_layer_number', '彩印纸箱层数', '彩印纸箱层数', false, '2025-03-18 16:41:38', '2025-03-18 16:30:11'),
(70, '2_layer_corrugated_shape', '2层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(71, '3_layer_corrugated_shape', '3层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(72, '4_layer_corrugated_shape', '4层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(73, '5_layer_corrugated_shape', '5层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(74, '6_layer_corrugated_shape', '6层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(75, '7_layer_corrugated_shape', '7层楞形', '报价时选择的楞形,用于限制纸箱每层瓦楞的选择', false, '2025-03-15 16:41:38', '2025-03-15 16:30:11'),
(76, 'paper_category', '纸张类型', '纸张的类型', false, '2025-03-23 13:53:53', '2025-03-23 13:53:53'),
(83, 'brand_status', '品牌状态', '品牌启用禁用状态', false, '2025-03-30 22:30:55', '2025-03-30 22:30:55'),
(84, 'product_status', '商品状态', '商品上下架审核状态', false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(85, 'source_type', '商品来源', '商品来源类型', false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(86, 'attr_value_type', '属性值类型', '商品属性值的类型', false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(87, 'spec_value_source', '规格值来源', '商品规格值的来源', false, '2025-03-30 22:30:58', '2025-03-30 22:30:58'),
(88, 'spec_value_type', '规格值类型', '商品规格值的数据类型', false, '2025-03-30 22:30:59', '2025-03-30 22:30:59'),
(89, 'spec_status', '规格状态', '商品规格的启用状态', false, '2025-03-30 22:30:59', '2025-03-30 22:30:59'),
(90, 'spec_value_status', '规格值状态', '商品规格值的启用状态', false, '2025-03-30 22:31:00', '2025-03-30 22:31:00'),
(91, 'sku_status', 'SKU状态', 'SKU的状态', false, '2025-03-30 22:31:01', '2025-03-30 22:31:01'),
(92, 'price_type', '价格类型', '商品价格的类型', false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(93, 'price_status', '价格状态', '价格的生效状态', false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(95, 'box_closing_type', '封箱方式', '封箱方式', false, '2025-03-31 16:41:38', '2025-03-31 16:30:11'),
(98, 'printing_method', '印刷方式', '印刷方式', false, '2025-03-31 16:41:38', '2025-03-31 16:30:11');

-- Set sequence value
-- The original AUTO_INCREMENT was 99, and the max inserted dict_key_id is 98.
-- Set the next value to 99 to match the original schema behavior.
SELECT setval('t_dict_key_dict_key_id_seq', 99, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_dict_value
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_dict_value;

-- Create table
CREATE TABLE t_dict_value (
  dict_value_id BIGSERIAL PRIMARY KEY,
  dict_key_id BIGINT NOT NULL,
  value_code VARCHAR(50) NOT NULL,
  value_name VARCHAR(50) NOT NULL,
  dict_color_hex VARCHAR(8) DEFAULT NULL,
  remark VARCHAR(500) DEFAULT NULL,
  sort INTEGER NOT NULL DEFAULT 0,
  deleted_flag BOOLEAN NOT NULL DEFAULT false,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_dict_value IS '字典的值';
COMMENT ON COLUMN t_dict_value.dict_value_id IS '主键ID'; -- Inferred from AUTO_INCREMENT
COMMENT ON COLUMN t_dict_value.dict_key_id IS '关联的字典key ID'; -- Inferred from context
COMMENT ON COLUMN t_dict_value.value_code IS '编码';
COMMENT ON COLUMN t_dict_value.value_name IS '名称';
COMMENT ON COLUMN t_dict_value.dict_color_hex IS '字典值颜色';
COMMENT ON COLUMN t_dict_value.remark IS '备注';
COMMENT ON COLUMN t_dict_value.sort IS '排序';
COMMENT ON COLUMN t_dict_value.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_dict_value.update_time IS '更新时间';
COMMENT ON COLUMN t_dict_value.create_time IS '创建时间';

-- Create unique constraint
ALTER TABLE t_dict_value ADD CONSTRAINT uq_t_dict_value_dict_key_id_value_code UNIQUE (dict_key_id, value_code);

-- Create trigger for update_time
CREATE TRIGGER trg_t_dict_value_set_update_time
BEFORE UPDATE ON t_dict_value
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_dict_value (dict_value_id, dict_key_id, value_code, value_name, dict_color_hex, remark, sort, deleted_flag, update_time, create_time) VALUES
(1, 1, 'LUO_YANG', '洛阳', NULL, '', 1, false, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(2, 1, 'ZHENG_ZHOU', '郑州', NULL, '', 1, false, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(3, 1, 'BEI_JING', '北京', NULL, '', 3, false, '2022-10-04 21:33:50', '2022-10-04 21:33:50'),
(5, 1, 'SHANG_HAI', '上海', NULL, '', 1, false, '2024-09-03 21:28:43', '2024-09-03 21:28:43'),
(6, 4, 'draft', '草稿', NULL, '初始创建未提交的报价单', 1, false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(7, 4, 'pending', '待审批', NULL, '已提交等待审批的报价单', 2, false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(8, 4, 'approved', '已批准', NULL, '审批通过的报价单', 3, false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(9, 4, 'rejected', '已拒绝', NULL, '审批拒绝的报价单', 4, false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(10, 4, 'expired', '已过期', NULL, '超过有效期的报价单', 5, false, '2025-03-11 18:47:57', '2025-03-11 18:47:57'),
(11, 5, 'watermark', '水印', NULL, '水印印刷业务', 1, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(12, 5, 'colorPrint', '彩印', NULL, '彩色印刷业务', 2, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(13, 6, 'standard', '标准盒', NULL, '标准纸箱盒型', 1, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(14, 6, 'topBottom', '天地盖', NULL, '带有独立盖子的盒型', 2, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(15, 6, 'custom', '自定义', NULL, '自定义尺寸和结构的盒型', 3, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(16, 7, 'nailing', '钉箱', NULL, '使用钉子固定封闭纸箱', 1, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(17, 7, 'gluing', '粘箱', NULL, '使用胶水粘合封闭纸箱', 2, false, '2025-03-11 18:47:58', '2025-03-11 18:47:58'),
(18, 8, 'not_started', '未开始', NULL, '订单尚未开始生产', 1, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(19, 8, 'in_production', '生产中', NULL, '订单正在生产过程中', 2, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(20, 8, 'completed', '已完成', NULL, '订单生产已完成', 3, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(21, 8, 'cancelled', '已取消', NULL, '订单生产已取消', 4, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(22, 9, 'unpaid', '未付款', NULL, '订单尚未支付任何款项', 1, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(23, 9, 'partial', '部分付款', NULL, '订单已支付部分款项', 2, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(24, 9, 'paid', '已付款', NULL, '订单已全额支付', 3, false, '2025-03-11 18:47:59', '2025-03-11 18:47:59'),
(25, 10, '0', '否', NULL, '否/不是/未删除', 1, false, '2025-03-11 18:48:00', '2025-03-11 18:48:00'),
(26, 10, '1', '是', NULL, '是/是的/已删除', 2, false, '2025-03-11 18:48:00', '2025-03-11 18:48:00'),
(27, 18, 'material', '原材料供应商', NULL, '提供原材料的供应商', 1, true, '2025-03-13 01:20:02', '2025-03-12 18:20:35'),
(28, 18, 'equipment', '设备供应商', NULL, '提供设备的供应商', 2, true, '2025-03-13 01:20:02', '2025-03-12 18:20:35'),
(30, 18, 'paper', '纸张供应商', NULL, '提供纸张的供应商', 4, true, '2025-03-13 01:08:21', '2025-03-12 18:20:35'),
(32, 19, 'A', '优秀', NULL, '优质供应商，合作紧密', 1, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(33, 19, 'B', '良好', NULL, '良好供应商，合作稳定', 2, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(34, 19, 'C', '一般', NULL, '一般供应商，需要改进', 3, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(35, 19, 'D', '较差', NULL, '较差供应商，考虑替换', 4, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(36, 20, 'active', '活跃', NULL, '正常合作中的供应商', 1, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(37, 20, 'inactive', '非活跃', NULL, '暂停合作的供应商', 2, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(38, 20, 'blacklisted', '黑名单', NULL, '被列入黑名单的供应商', 3, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(39, 20, 'potential', '潜在', NULL, '潜在的供应商，尚未正式合作', 4, false, '2025-03-12 18:20:35', '2025-03-12 18:20:35'),
(40, 21, 'pending', '待审批', NULL, '等待审批的状态', 1, false, '2025-03-12 18:20:36', '2025-03-12 18:20:36'),
(41, 21, 'approved', '已批准', NULL, '已通过审批的状态', 2, false, '2025-03-12 18:20:36', '2025-03-12 18:20:36'),
(42, 21, 'rejected', '已拒绝', NULL, '审批被拒绝的状态', 3, false, '2025-03-12 18:20:36', '2025-03-12 18:20:36'),
(45, 18, 'service_supplier', '服务供应商', NULL, '提供服务的供应商', 3, true, '2025-03-21 21:36:34', '2025-03-13 01:19:36'),
(46, 18, 'paper_board_supplier', '纸板供应商', NULL, '提供瓦纸的供应商', 4, false, '2025-03-19 20:52:59', '2025-03-13 01:19:36'),
(47, 18, 'raw_paper_supplier', '原纸供应商', NULL, '提供原纸的供应商', 5, false, '2025-03-19 20:53:41', '2025-03-13 01:19:36'),
(48, 18, 'packaging', '包装供应商', NULL, '提供包装材料的供应商', 6, true, '2025-03-13 01:20:10', '2025-03-13 01:19:36'),
(49, 23, '1', '启用', NULL, '启用状态', 1, false, '2025-03-13 23:46:10', '2025-03-13 23:46:10'),
(50, 24, 'nine_dragons', '玖龙', NULL, '玖龙纸业', 1, false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(51, 24, 'sun_paper', '太阳', NULL, '山东太阳纸业', 2, false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(52, 24, 'chenming', '晨鸣', NULL, '晨鸣纸业', 3, false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(53, 24, 'app', 'APP', NULL, '金光纸业', 4, false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(59, 26, 'middle', '高强瓦', NULL, '中纸', 1, false, '2025-03-31 22:55:29', '2025-03-13 23:46:11'),
(60, 26, 'B', 'B瓦', NULL, 'B瓦', 2, false, '2025-03-21 17:09:37', '2025-03-13 23:46:11'),
(61, 26, 'C', 'C瓦', NULL, 'C瓦', 3, false, '2025-03-21 17:09:41', '2025-03-13 23:46:11'),
(62, 26, 'E', 'E瓦', NULL, 'E瓦', 4, false, '2025-03-21 17:09:45', '2025-03-13 23:46:11'),
(63, 27, '47', '47g', NULL, '47克重', 1, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(64, 27, '75', '75g', NULL, '75克重', 2, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(65, 27, '80', '80g', NULL, '80克重', 3, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(66, 27, '85', '85g', NULL, '85克重', 4, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(67, 27, '95', '95g', NULL, '95克重', 5, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(68, 27, '110', '110g', NULL, '110克重', 6, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(69, 27, '115', '115g', NULL, '115克重', 7, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(70, 27, '130', '130g', NULL, '130克重', 8, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(71, 27, '135', '135g', NULL, '135克重', 9, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(72, 27, '145', '145g', NULL, '145克重', 10, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(73, 27, '160', '160g', NULL, '160克重', 11, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(74, 27, '170', '170g', NULL, '170克重', 12, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(75, 27, '190', '190g', NULL, '190克重', 13, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(76, 27, '230', '230g', NULL, '230克重', 14, false, '2025-03-13 23:46:12', '2025-03-13 23:46:12'),
(93, 34, 'DRAFT', '草稿', NULL, '草稿状态', 1, false, '2025-03-14 16:02:22', '2025-03-14 16:02:22'),
(94, 34, 'SUBMITTED', '已提交', NULL, '已提交状态', 2, false, '2025-03-14 16:02:22', '2025-03-14 16:02:22'),
(95, 34, 'APPROVED', '已审批', NULL, '已审批状态', 3, false, '2025-03-14 16:02:22', '2025-03-14 16:02:22'),
(96, 34, 'REJECTED', '已拒绝', NULL, '已拒绝状态', 4, false, '2025-03-14 16:02:22', '2025-03-14 16:02:22'),
(97, 34, 'EXPIRED', '已过期', NULL, '已过期状态', 5, false, '2025-03-14 16:02:22', '2025-03-14 16:02:22'),
(98, 29, 'SQUARE_METER', 'm²', NULL, '平方米单位', 1, false, '2025-03-21 16:43:00', '2025-03-14 16:02:26'),
(99, 29, 'TON', '吨', NULL, '吨单位', 2, false, '2025-03-14 16:02:26', '2025-03-14 16:02:26'),
(100, 29, 'SHEET', '张', NULL, '张单位', 3, false, '2025-03-14 16:02:26', '2025-03-14 16:02:26'),
(105, 30, 'CORRUGATED', '瓦纸报价', NULL, '瓦纸报价类型', 1, false, '2025-03-14 16:05:05', '2025-03-14 16:05:05'),
(106, 30, 'RAW', '原纸报价', NULL, '原纸报价类型', 2, false, '2025-03-14 16:05:05', '2025-03-14 16:05:05'),
(109, 41, 'REGULAR', '普通客户', NULL, '普通客户类型', 1, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(110, 41, 'VIP', 'VIP客户', NULL, 'VIP客户类型', 2, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(111, 41, 'STRATEGIC', '战略客户', NULL, '战略合作客户', 3, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(112, 41, 'DISTRIBUTOR', '经销商', NULL, '经销商客户', 4, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(113, 41, 'AGENT', '代理商', NULL, '代理商客户', 5, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(114, 42, 'ACTIVE', '活跃', NULL, '活跃状态', 1, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(115, 42, 'INACTIVE', '非活跃', NULL, '非活跃状态', 2, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(116, 42, 'BLACKLIST', '黑名单', NULL, '黑名单状态', 3, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(117, 42, 'POTENTIAL', '潜在客户', NULL, '潜在客户状态', 4, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(118, 43, 'AAA', 'AAA级', NULL, '最高信用等级', 1, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(119, 43, 'AA', 'AA级', NULL, '较高信用等级', 2, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(120, 43, 'A', 'A级', NULL, '高信用等级', 3, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(121, 43, 'BBB', 'BBB级', NULL, '中高信用等级', 4, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(122, 43, 'BB', 'BB级', NULL, '中信用等级', 5, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(123, 43, 'B', 'B级', NULL, '中低信用等级', 6, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(124, 43, 'CCC', 'CCC级', NULL, '低信用等级', 7, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(125, 43, 'CC', 'CC级', NULL, '较低信用等级', 8, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(126, 43, 'C', 'C级', NULL, '最低信用等级', 9, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(127, 44, 'SHIPPING', '收货地址', NULL, '收货地址类型', 1, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(128, 44, 'BILLING', '发票地址', NULL, '发票地址类型', 2, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(129, 44, 'OFFICE', '办公地址', NULL, '办公地址类型', 3, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(130, 44, 'FACTORY', '工厂地址', NULL, '工厂地址类型', 4, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(131, 44, 'WAREHOUSE', '仓库地址', NULL, '仓库地址类型', 5, false, '2025-03-14 16:36:51', '2025-03-14 16:36:51'),
(170, 47, 'coated_middle', '高瓦 中纸', NULL, '高瓦中纸', 1, false, '2025-03-14 23:47:12', '2025-03-14 23:47:12'),
(171, 47, 'coated_B', '高瓦 B瓦', NULL, '高瓦B瓦', 2, false, '2025-03-31 22:11:27', '2025-03-14 23:47:12'),
(172, 47, 'coated_C', '高瓦 C瓦', NULL, '高瓦C瓦', 3, false, '2025-03-31 22:11:31', '2025-03-14 23:47:12'),
(173, 47, 'coated_D', '高瓦 D瓦', NULL, '高瓦E瓦', 5, true, '2025-03-15 15:01:05', '2025-03-14 23:47:12'),
(174, 47, 'coated_E', '高瓦 E瓦', NULL, '高瓦E瓦', 4, false, '2025-03-31 22:11:36', '2025-03-14 23:47:12'),
(175, 47, 'white_card_board', '白卡', NULL, '白卡', 7, false, '2025-03-23 15:36:17', '2025-03-14 23:47:12'),
(176, 47, 'kraft_liner_board', '牛卡', NULL, '牛卡', 8, false, '2025-03-23 15:36:06', '2025-03-14 23:47:12'),
(177, 47, 'coated_white_board', '涂布白板', NULL, '涂布', 9, false, '2025-03-23 15:35:54', '2025-03-14 23:47:12'),
(181, 48, 'E', 'E瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(182, 48, 'B', 'B瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(183, 52, 'B', 'B瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(184, 52, 'C', 'C瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(185, 52, 'E', 'E瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(186, 52, 'A', 'A瓦', NULL, '', 4, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(187, 52, 'F', 'F瓦', NULL, '', 5, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(188, 54, 'EB', 'EB瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(189, 54, 'BC', 'BC瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(190, 54, 'EE', 'EE瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(191, 54, 'EC', 'EC瓦', NULL, '', 4, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(192, 54, 'BA', 'BA瓦', NULL, '', 5, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(193, 55, 'DRAFT', '草稿', NULL, '草稿状态', 1, false, '2025-03-19 01:24:59', '2025-03-19 01:24:59'),
(194, 55, 'SUBMITTED', '已提交', NULL, '已提交状态', 2, false, '2025-03-19 01:24:59', '2025-03-19 01:24:59'),
(195, 55, 'APPROVED', '已审批', NULL, '已审批状态', 3, false, '2025-03-19 01:24:59', '2025-03-19 01:24:59'),
(196, 55, 'REJECTED', '已拒绝', NULL, '已拒绝状态', 4, false, '2025-03-19 01:24:59', '2025-03-19 01:24:59'),
(197, 55, 'EXPIRED', '已过期', NULL, '已过期状态', 5, false, '2025-03-19 01:24:59', '2025-03-19 01:24:59'),
(202, 59, 'DRAFT', '草稿', NULL, '草稿状态', 1, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(203, 59, 'SUBMITTED', '已提交', NULL, '已提交状态', 2, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(204, 59, 'APPROVED', '已审批', NULL, '已审批状态', 3, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(205, 59, 'REJECTED', '已拒绝', NULL, '已拒绝状态', 4, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(206, 59, 'EXPIRED', '已过期', NULL, '已过期状态', 5, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(207, 60, 'watermark', '水印', NULL, '水印业务', 1, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(208, 60, 'colorPrint', '彩印', NULL, '彩印业务', 2, false, '2025-03-19 01:28:28', '2025-03-19 01:28:28'),
(211, 62, 'face', '面纸', NULL, '用于纸箱外表面的纸张', 1, false, '2025-03-19 01:28:30', '2025-03-19 01:28:30'),
(212, 62, 'inner', '里纸', NULL, '用于纸箱内表面的纸张', 2, false, '2025-03-21 13:10:39', '2025-03-19 01:28:30'),
(213, 62, 'corrugated', '瓦楞纸', NULL, '用于纸箱中间层的瓦楞纸', 3, false, '2025-03-21 11:39:51', '2025-03-19 01:28:30'),
(214, 62, 'printing', '印刷面纸', NULL, '用于彩印外表面的纸张', 4, false, '2025-03-21 11:40:15', '2025-03-19 01:28:30'),
(215, 63, '1', '含税', NULL, '含税价格', 1, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(216, 63, '0', '不含税', NULL, '不含税价格', 2, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(217, 64, 'standard', '标准箱', NULL, '标准规格纸箱', 1, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(218, 64, 'tailored', '定制箱', NULL, '按客户需求定制的纸箱', 2, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(219, 64, 'telescope', '套盖箱', NULL, '带有套盖结构的纸箱', 3, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(220, 64, 'folding', '折叠箱', NULL, '可折叠结构的纸箱', 4, false, '2025-03-19 01:28:31', '2025-03-19 01:28:31'),
(221, 65, '3', '3层', NULL, '', 2, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(222, 65, '5', '5层', NULL, '', 3, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(223, 65, '7', '7层', NULL, '', 4, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(224, 66, '1', '1层', NULL, '', 1, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(225, 66, '2', '2层', NULL, '', 2, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(226, 66, '3', '3层', NULL, '', 3, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(227, 66, '4', '4层', NULL, '', 4, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(228, 66, '5', '5层', NULL, '', 5, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(229, 66, '6', '6层', NULL, '', 6, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(230, 66, '7', '7层', NULL, '', 7, false, '2025-03-18 16:31:09', '2025-03-18 16:31:09'),
(232, 70, 'B', 'B瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(233, 70, 'E', 'E瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(234, 70, 'F', 'F瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(235, 71, 'B', 'B瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(236, 71, 'C', 'C瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(237, 71, 'E', 'E瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(238, 71, 'A', 'A瓦', NULL, '', 4, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(239, 71, 'F', 'F瓦', NULL, '', 5, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(240, 72, 'EB', 'EB瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(241, 72, 'BC', 'BC瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(242, 72, 'EE', 'EE瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(243, 72, 'EC', 'EC瓦', NULL, '', 4, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(244, 72, 'BA', 'BA瓦', NULL, '', 5, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(245, 73, 'EB', 'EB瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(246, 73, 'BC', 'BC瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(247, 73, 'EE', 'EE瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(248, 73, 'EC', 'EC瓦', NULL, '', 4, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(249, 73, 'BA', 'BA瓦', NULL, '', 5, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(250, 74, 'EBE', 'EBE瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(251, 74, 'EBC', 'EBC瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(252, 74, 'EBA', 'EBA瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(253, 75, 'EBE', 'EBE瓦', NULL, '', 1, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(254, 75, 'EBC', 'EBC瓦', NULL, '', 2, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(255, 75, 'EBA', 'EBA瓦', NULL, '', 3, false, '2025-03-15 16:31:09', '2025-03-15 16:31:09'),
(256, 62, 'core', '芯纸', NULL, '位于瓦楞纸和瓦楞之之间夹层的纸张', 1, false, '2025-03-21 13:12:33', '2025-03-21 13:10:13'),
(257, 76, 'coated_white_board', '涂布白板纸', NULL, '涂布白板纸', 1, false, '2025-03-22 12:10:50', '2025-03-13 23:46:11'),
(258, 76, 'high_strength_corrugating_medium', '高瓦纸', NULL, '高瓦纸', 4, false, '2025-03-21 15:48:56', '2025-03-13 23:46:11'),
(259, 76, 'kraft_liner_board', '牛卡纸', NULL, '牛卡纸纸种', 3, false, '2025-03-22 12:11:04', '2025-03-13 23:46:11'),
(260, 76, 'white_card_board', '白卡纸', NULL, '白卡纸纸种', 2, false, '2025-03-22 12:10:57', '2025-03-13 23:46:11'),
(261, 76, 'raw_kraft_liner_board', '牛卡原纸', NULL, '牛卡原纸纸种', 2, false, '2025-03-22 12:10:57', '2025-03-13 23:46:11'),
(262, 76, 'art_paper', '铜版纸', NULL, '铜版纸纸种', 6, false, '2025-03-22 12:10:57', '2025-03-13 23:46:11'),
(263, 76, 'double_white_board', '双面白板纸', NULL, '双面白板纸纸种', 7, false, '2025-03-22 12:10:57', '2025-03-13 23:46:11'),
(264, 76, 'double_sided_coated_paper', '双胶纸', NULL, '双胶纸纸种', 8, false, '2025-03-22 12:10:57', '2025-03-13 23:46:11'),
(265, 76, 'specialty', '特种纸', NULL, '特种纸纸种', 5, false, '2025-03-13 23:46:11', '2025-03-13 23:46:11'),
(266, 47, 'art_paper', '铜版', NULL, '', 10, false, '2025-03-23 15:36:40', '2025-03-23 15:36:33'),
(267, 47, 'double_white_board', '双面白板', NULL, '', 11, false, '2025-03-23 15:36:52', '2025-03-23 15:36:52'),
(268, 27, '105', '105g', NULL, '', 1, false, '2025-03-29 09:22:37', '2025-03-29 09:22:37'),
(289, 83, '0', '禁用', '#F56C6C', '品牌禁用状态', 2, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(290, 83, '1', '启用', '#67C23A', '品牌启用状态', 1, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(291, 84, '0', '下架', '#F56C6C', '商品下架状态', 2, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(292, 84, '1', '上架', '#67C23A', '商品上架状态', 1, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(293, 84, '2', '审核中', '#E6A23C', '商品审核中状态', 3, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(294, 84, '3', '已禁用', '#909399', '商品已禁用状态', 4, false, '2025-03-30 22:30:56', '2025-03-30 22:30:56'),
(295, 85, '1', '自产', '#409EFF', '自产商品', 1, false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(296, 85, '2', '外购', '#67C23A', '外购商品', 2, false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(297, 85, '3', '委外加工', '#E6A23C', '委外加工商品', 3, false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(298, 85, '4', '虚拟品', '#909399', '虚拟商品', 4, false, '2025-03-30 22:30:57', '2025-03-30 22:30:57'),
(299, 86, '1', '自由文本', '#409EFF', '自由输入文本', 1, false, '2025-03-30 22:30:58', '2025-03-30 22:30:58'),
(300, 86, '2', '枚举值', '#67C23A', '从预设值中选择', 2, false, '2025-03-30 22:30:58', '2025-03-30 22:30:58'),
(301, 87, '1', '枚举', '#409EFF', '从预设规格值选择', 1, false, '2025-03-30 22:30:58', '2025-03-30 22:30:58'),
(302, 87, '2', '用户输入', '#67C23A', '用户自由输入', 2, false, '2025-03-30 22:30:58', '2025-03-30 22:30:58'),
(303, 88, '1', '文本', '#409EFF', '文本类型', 1, false, '2025-03-30 22:30:59', '2025-03-30 22:30:59'),
(304, 88, '2', '数字', '#67C23A', '数字类型', 2, false, '2025-03-30 22:30:59', '2025-03-30 22:30:59'),
(305, 88, '3', '日期', '#E6A23C', '日期类型', 3, false, '2025-03-30 22:30:59', '2025-03-30 22:30:59'),
(306, 89, '0', '禁用', '#F56C6C', '规格禁用状态', 2, false, '2025-03-30 22:31:00', '2025-03-30 22:31:00'),
(307, 89, '1', '启用', '#67C23A', '规格启用状态', 1, false, '2025-03-30 22:31:00', '2025-03-30 22:31:00'),
(308, 90, '0', '禁用', '#F56C6C', '规格值禁用状态', 2, false, '2025-03-30 22:31:00', '2025-03-30 22:31:00'),
(309, 90, '1', '启用', '#67C23A', '规格值启用状态', 1, false, '2025-03-30 22:31:00', '2025-03-30 22:31:00'),
(310, 91, '0', '下架', '#F56C6C', 'SKU下架状态', 2, false, '2025-03-30 22:31:01', '2025-03-30 22:31:01'),
(311, 91, '1', '上架', '#67C23A', 'SKU上架状态', 1, false, '2025-03-30 22:31:01', '2025-03-30 22:31:01'),
(312, 91, '2', '缺货', '#E6A23C', 'SKU缺货状态', 3, false, '2025-03-30 22:31:01', '2025-03-30 22:31:01'),
(313, 92, '1', '标准价', '#409EFF', '标准销售价格', 1, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(314, 92, '2', '促销价', '#67C23A', '促销特价', 2, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(315, 92, '3', 'VIP价', '#E6A23C', 'VIP客户价格', 3, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(316, 92, '4', '成本价', '#F56C6C', '采购成本价', 4, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(317, 93, '0', '失效', '#F56C6C', '价格已失效', 2, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(318, 93, '1', '生效', '#67C23A', '价格生效中', 1, false, '2025-03-30 22:31:02', '2025-03-30 22:31:02'),
(319, 47, 'coated_A', '高瓦 A瓦', NULL, '', 5, false, '2025-03-31 22:11:40', '2025-03-31 22:08:48'),
(320, 47, 'coated_F', '高瓦 F瓦', NULL, '', 1, false, '2025-03-31 22:12:04', '2025-03-31 22:12:04'),
(321, 26, 'A', 'A瓦', NULL, '', 5, false, '2025-03-31 22:55:11', '2025-03-31 22:55:02'),
(322, 26, 'F', 'F瓦', NULL, '', 6, false, '2025-03-31 22:55:20', '2025-03-31 22:55:20'),
(323, 95, 'nailing', '钉箱', NULL, '', 1, false, '2025-03-31 16:41:38', '2025-03-31 16:30:11'),
(324, 95, 'gluing', '粘箱', NULL, '', 2, false, '2025-03-31 16:41:38', '2025-03-31 16:30:11'),
(327, 98, 'watermark', '水印', NULL, '', 1, false, '2025-03-31 16:41:38', '2025-03-31 16:30:11'),
(328, 98, 'colorPrint', '彩印', NULL, '', 2, false, '2025-03-31 16:41:38', '2025-03-31 16:30:11');

-- Set sequence value
-- The original AUTO_INCREMENT was 329, and the max inserted dict_value_id is 328.
-- Set the next value to 329 to match the original schema behavior.
SELECT setval('t_dict_value_dict_value_id_seq', 329, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_employee
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_employee;

-- Create table
CREATE TABLE t_employee (
  employee_id BIGSERIAL PRIMARY KEY,
  login_name VARCHAR(30) NOT NULL,
  login_pwd VARCHAR(100) NOT NULL,
  actual_name VARCHAR(30) NOT NULL,
  avatar VARCHAR(200) DEFAULT NULL,
  gender INTEGER NOT NULL DEFAULT 0, -- Changed back to INTEGER to keep original 0, 1, 2 values
  phone VARCHAR(15) DEFAULT NULL,
  department_id INTEGER NOT NULL,
  position_id BIGINT DEFAULT NULL,
  email VARCHAR(100) DEFAULT NULL,
  disabled_flag BOOLEAN NOT NULL DEFAULT false, -- Mapped tinyint unsigned to boolean
  deleted_flag BOOLEAN NOT NULL DEFAULT false, -- Mapped tinyint unsigned to boolean
  administrator_flag BOOLEAN NOT NULL DEFAULT false, -- Mapped tinyint to boolean
  remark VARCHAR(200) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_employee IS '员工表';
COMMENT ON COLUMN t_employee.employee_id IS '主键';
COMMENT ON COLUMN t_employee.login_name IS '登录帐号';
COMMENT ON COLUMN t_employee.login_pwd IS '登录密码';
COMMENT ON COLUMN t_employee.actual_name IS '员工名称';
COMMENT ON COLUMN t_employee.avatar IS '头像';
COMMENT ON COLUMN t_employee.gender IS '性别 (0, 1, 2 from original data)'; -- Updated comment for INTEGER type
COMMENT ON COLUMN t_employee.phone IS '手机号码';
COMMENT ON COLUMN t_employee.department_id IS '部门id';
COMMENT ON COLUMN t_employee.position_id IS '职务ID';
COMMENT ON COLUMN t_employee.email IS '邮箱';
COMMENT ON COLUMN t_employee.disabled_flag IS '是否被禁用 false:否 true:是'; -- Mapping 0/1
COMMENT ON COLUMN t_employee.deleted_flag IS '是否删除 false:否 true:是'; -- Mapping 0/1
COMMENT ON COLUMN t_employee.administrator_flag IS '是否为超级管理员: false:不是, true:是'; -- Mapping 0/1
COMMENT ON COLUMN t_employee.remark IS '备注';
COMMENT ON COLUMN t_employee.update_time IS '更新时间';
COMMENT ON COLUMN t_employee.create_time IS '创建时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_employee_set_update_time
BEFORE UPDATE ON t_employee
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Kept original integer values for gender. Converted flags (disabled, deleted, administrator) to boolean.
INSERT INTO t_employee (employee_id, login_name, login_pwd, actual_name, avatar, gender, phone, department_id, position_id, email, disabled_flag, deleted_flag, administrator_flag, remark, update_time, create_time) VALUES
(1, 'admin', '$argon2id$v=19$m=16384,t=2,p=1$e/hqRAZYCYHydMS3SPo7yA$5hdCxLG7q+Jtf6KLJHVg/yb0I8LZrPuKUF66jLq+Drc', '管理员', 'public/common/254cedd7b2bf449197aa1dfff1f94aa3_20250325171921.png', 0, '13500000000', 1, 3, NULL, false, false, true, NULL, '2025-03-25 17:19:22', '2022-10-04 21:33:50'),
(2, 'huke', '40cc20b8891cd3fd1f008ea7f4ac17c3', '胡克', NULL, 0, '13123123121', 1, 4, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(44, 'zhuoda', 'bf63cb6431d613acdee104f692845b22', '卓大', NULL, 1, '***********', 1, 6, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(47, 'shanyi', '$argon2id$v=19$m=16384,t=2,p=1$6abrKuCL4DqeMgkM7xa9uA$KtvXstI90dYpuERkBlmo4t/7DryG4wjSR3c1fF7FXxU', '善逸', 'public/common/f823b00873684f0a9d31f0d62316cc8e_20240630015141.jpg', 1, '17630506613', 2, 5, NULL, false, true, false, '这个是备注', '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(48, 'qinjiu', 'b1cfb0ed0080306199fa76c872d6a32e', '琴酒', NULL, 2, '14112343212', 2, 6, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(63, 'kaiyun', '0e5ec5746bf955f253fa747ab76cfa67', '开云', NULL, 0, '13112312346', 2, 5, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(64, 'qingye', '40cc20b8891cd3fd1f008ea7f4ac17c3', '清野', NULL, 1, '13123123111', 2, 4, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(65, 'feiye', '40cc20b8891cd3fd1f008ea7f4ac17c3', '飞叶', NULL, 1, '13123123112', 4, 3, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(66, 'luoyi', '40cc20b8891cd3fd1f008ea7f4ac17c3', '罗伊', NULL, 1, '13123123142', 4, 2, NULL, true, true, false, NULL, '2025-03-25 16:45:52', '2022-10-04 21:33:50'),
(67, 'chuxiao', '7287168489ed5598741362cbec2b0741', '初晓', NULL, 1, '13123123123', 1, 2, NULL, true, true, false, NULL, '2025-03-25 16:46:39', '2022-10-04 21:33:50'),
(68, 'xuanpeng', '40cc20b8891cd3fd1f008ea7f4ac17c3', '玄朋', NULL, 1, '13123123124', 1, 3, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(69, 'peixian', '40cc20b8891cd3fd1f008ea7f4ac17c3', '玄朋', NULL, 1, '18377482773', 1, 4, NULL, false, true, false, NULL, '2025-03-24 02:08:24', '2022-10-04 21:33:50'),
(73, 'limbo', '50ea4174e4ad0970bcf6423f99c0cbcd', '陈琳博', NULL, 1, '18906662339', 2, 4, NULL, false, true, false, NULL, '2025-03-25 16:42:33', '2024-07-17 10:36:16'),
(74, 'xzh', 'f5ca8e50d26e6070ed2198e136ee967d', 'admin1', NULL, 1, '13654567897', 5, 6, NULL, false, true, false, NULL, '2025-03-25 16:42:33', '2024-08-09 09:49:56'),
(75, '测试财务', '$argon2id$v=19$m=16384,t=2,p=1$+F5yUuDM5PC97BX5+w4akQ$3koY6LdEX3tNsPWQoQBbR+MmUml/GsuvTwukN/hmCpg', '测试财务', NULL, 1, '13333333333', 1, NULL, '<EMAIL>', false, false, false, NULL, '2025-03-25 16:45:19', '2025-03-18 19:08:30'),
(76, '测试1', '$argon2id$v=19$m=16384,t=2,p=1$LhUAHWi0sQIajWXTq7CYaA$P9m6B59fws74DvlAI7Mtj/BhMJHxvmxOiIoNPg+23X8', '内勤测试账号1', NULL, 2, '13777777777', 9, NULL, '<EMAIL>', false, false, false, NULL, '2025-03-24 10:37:43', '2025-03-24 10:37:43');

-- Set sequence value
-- The original AUTO_INCREMENT was 77, and the max inserted employee_id is 76.
-- Set the next value to 77 to match the original schema behavior.
SELECT setval('t_employee_employee_id_seq', 77, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_feedback
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_feedback;

-- Create table
CREATE TABLE t_feedback (
  feedback_id BIGSERIAL PRIMARY KEY,
  feedback_content TEXT,
  feedback_attachment VARCHAR(500) DEFAULT NULL,
  user_id BIGINT NOT NULL,
  user_type INTEGER NOT NULL,
  user_name VARCHAR(50) NOT NULL,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_feedback IS '意见反馈';
COMMENT ON COLUMN t_feedback.feedback_id IS '主键';
COMMENT ON COLUMN t_feedback.feedback_content IS '反馈内容';
COMMENT ON COLUMN t_feedback.feedback_attachment IS '反馈图片';
COMMENT ON COLUMN t_feedback.user_id IS '创建人id';
COMMENT ON COLUMN t_feedback.user_type IS '创建人用户类型';
COMMENT ON COLUMN t_feedback.user_name IS '创建人姓名';
COMMENT ON COLUMN t_feedback.create_time IS '创建时间';
COMMENT ON COLUMN t_feedback.update_time IS '更新时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_feedback_set_update_time
BEFORE UPDATE ON t_feedback
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data (No data in the dump file)

-- Set sequence value
-- The original AUTO_INCREMENT was 14. Set the next value to 14.
SELECT setval('t_feedback_feedback_id_seq', 14, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_file
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_file;

-- Create table
CREATE TABLE t_file (
  file_id BIGSERIAL PRIMARY KEY,
  folder_type INTEGER NOT NULL, -- Mapped tinyint unsigned to integer
  file_name VARCHAR(100) DEFAULT NULL,
  file_size INTEGER DEFAULT NULL,
  file_key VARCHAR(200) NOT NULL,
  file_type VARCHAR(50) NOT NULL,
  creator_id INTEGER DEFAULT NULL,
  creator_user_type INTEGER DEFAULT NULL,
  creator_name VARCHAR(100) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_file IS '文件';
COMMENT ON COLUMN t_file.file_id IS '主键ID';
COMMENT ON COLUMN t_file.folder_type IS '文件夹类型';
COMMENT ON COLUMN t_file.file_name IS '文件名称';
COMMENT ON COLUMN t_file.file_size IS '文件大小';
COMMENT ON COLUMN t_file.file_key IS '文件key，用于文件下载';
COMMENT ON COLUMN t_file.file_type IS '文件类型';
COMMENT ON COLUMN t_file.creator_id IS '创建人，即上传人';
COMMENT ON COLUMN t_file.creator_user_type IS '创建人用户类型';
COMMENT ON COLUMN t_file.creator_name IS '创建人姓名';
COMMENT ON COLUMN t_file.update_time IS '上次更新时间';
COMMENT ON COLUMN t_file.create_time IS '创建时间';

-- Create unique constraint
ALTER TABLE t_file ADD CONSTRAINT uk_t_file_file_key UNIQUE (file_key);

-- Create index
-- Note: MySQL had two identical indexes (`module_id_module_type` and `module_type`) on `folder_type`. Creating one index in PostgreSQL.
CREATE INDEX idx_t_file_folder_type ON t_file (folder_type);

-- Insert data
INSERT INTO t_file (file_id, folder_type, file_name, file_size, file_key, file_type, creator_id, creator_user_type, creator_name, update_time, create_time) VALUES
(108, 1, '屏幕截图 2025-03-25 171628.png', 26896, 'public/common/592f515aa61746008cce19e08bb4e492_20250325171910.png', 'png', 1, 1, '管理员', '2025-03-25 17:19:12', '2025-03-25 17:19:12'),
(109, 1, '屏幕截图 2025-03-25 171628.png', 26896, 'public/common/254cedd7b2bf449197aa1dfff1f94aa3_20250325171921.png', 'png', 1, 1, '管理员', '2025-03-25 17:19:22', '2025-03-25 17:19:22');

-- Set sequence value
-- The original AUTO_INCREMENT was 110, and the max inserted file_id is 109.
-- Set the next value to 110 to match the original schema behavior.
SELECT setval('t_file_file_id_seq', 110, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_goods
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_goods;

-- Create table
CREATE TABLE t_goods (
  goods_id SERIAL PRIMARY KEY, -- Mapped int AUTO_INCREMENT to SERIAL
  goods_status INTEGER DEFAULT NULL,
  category_id INTEGER NOT NULL,
  goods_name VARCHAR(50) NOT NULL,
  place VARCHAR(255) DEFAULT NULL,
  price DECIMAL(10, 2) NOT NULL, -- Mapped decimal(10,2) unsigned to decimal(10,2)
  shelves_flag BOOLEAN NOT NULL, -- Mapped tinyint unsigned to boolean
  deleted_flag BOOLEAN NOT NULL, -- Mapped tinyint unsigned to boolean
  remark VARCHAR(255) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_goods IS '商品';
COMMENT ON COLUMN t_goods.goods_id IS '主键ID'; -- Inferred from AUTO_INCREMENT
COMMENT ON COLUMN t_goods.goods_status IS '商品状态:[1:预约中,2:售卖中,3:售罄]';
COMMENT ON COLUMN t_goods.category_id IS '商品类目';
COMMENT ON COLUMN t_goods.goods_name IS '商品名称';
COMMENT ON COLUMN t_goods.place IS '产地';
COMMENT ON COLUMN t_goods.price IS '价格';
COMMENT ON COLUMN t_goods.shelves_flag IS '上架状态';
COMMENT ON COLUMN t_goods.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_goods.remark IS '备注';
COMMENT ON COLUMN t_goods.update_time IS '更新时间';
COMMENT ON COLUMN t_goods.create_time IS '创建时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_goods_set_update_time
BEFORE UPDATE ON t_goods
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Converted shelves_flag (0->false, 1->true) and deleted_flag (0->false, 1->true) to boolean
INSERT INTO t_goods (goods_id, goods_status, category_id, goods_name, place, price, shelves_flag, deleted_flag, remark, update_time, create_time) VALUES
(1, 1, 353, 'Mote60', 'BEI_JING', 9999.00, true, false, NULL, '2022-10-21 19:57:49', '2021-09-01 22:25:30'),
(7, 1, 352, 'iphone15 pro', 'LUO_YANG', 50000.00, true, false, '备注', '2024-06-16 09:34:08', '2022-10-21 19:58:07'),
(8, 1, 352, 'iphone14', 'ZHENG_ZHOU', 150.00, false, false, '', '2022-10-21 19:12:49', '2022-10-21 19:00:11'),
(10, 1, 357, '小米15', 'LUO_YANG', 7999.00, true, false, '', '2023-10-07 19:02:24', '2023-10-07 19:02:24'),
(11, 1, 354, '青轴键盘', 'ZHENG_ZHOU', 199.00, true, false, '支持usb', '2023-12-01 19:58:09', '2023-12-01 19:55:53'),
(12, 1, 356, '罗技双模鼠标', 'BEI_JING,ZHENG_ZHOU', 99.00, false, false, '支持蓝牙', '2024-09-03 21:06:32', '2023-12-01 19:57:25');

-- Set sequence value
-- The original AUTO_INCREMENT was 34, and the max inserted goods_id is 12.
-- Set the next value to 34 to match the original schema behavior.
SELECT setval('t_goods_goods_id_seq', 34, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_heart_beat_record
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_heart_beat_record;

-- Create table
CREATE TABLE t_heart_beat_record (
  heart_beat_record_id SERIAL PRIMARY KEY, -- Mapped int AUTO_INCREMENT to SERIAL
  project_path VARCHAR(100) NOT NULL,
  server_ip VARCHAR(200) NOT NULL,
  process_no INTEGER NOT NULL,
  process_start_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
  heart_beat_time TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

-- Add comments to the table and columns
COMMENT ON TABLE t_heart_beat_record IS '公用服务 - 服务心跳';
COMMENT ON COLUMN t_heart_beat_record.heart_beat_record_id IS '自增id';
COMMENT ON COLUMN t_heart_beat_record.project_path IS '项目名称';
COMMENT ON COLUMN t_heart_beat_record.server_ip IS '服务器ip';
COMMENT ON COLUMN t_heart_beat_record.process_no IS '进程号';
COMMENT ON COLUMN t_heart_beat_record.process_start_time IS '进程开启时间';
COMMENT ON COLUMN t_heart_beat_record.heart_beat_time IS '心跳时间';

-- Insert data
-- Note: Windows paths with double backslashes are preserved.
INSERT INTO t_heart_beat_record (heart_beat_record_id, project_path, server_ip, process_no, process_start_time, heart_beat_time) VALUES
(188, 'C:\\Users\\<USER>\\project\\txy-erp\\smart-admin-api-java17-springboot3', '127.0.0.1;***********', 23140, '2025-03-10 22:12:16', '2025-03-10 22:13:28'),
(189, 'C:\\Users\\<USER>\\project\\txy-erp\\smart-admin-api-java17-springboot3', '127.0.0.1;***********', 2520, '2025-03-11 18:43:25', '2025-03-11 20:09:16'),
(190, 'C:\\Users\\<USER>\\project\\txy-erp\\smart-admin-api-java17-springboot3', '127.0.0.1;***********', 34620, '2025-03-11 20:17:08', '2025-03-11 23:44:37'),
(191, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;***********', 23372, '2025-03-12 15:59:47', '2025-03-12 16:16:07'),
(192, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;***********', 26712, '2025-03-12 16:19:19', '2025-03-12 16:41:49'),
(193, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 17300, '2025-03-12 16:47:06', '2025-03-12 16:58:29'),
(194, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 30600, '2025-03-12 17:07:24', '2025-03-12 20:23:43'),
(195, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 25640, '2025-03-12 20:25:16', '2025-03-12 20:51:29'),
(196, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 4264, '2025-03-12 20:52:18', '2025-03-12 20:58:38'),
(197, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 14768, '2025-03-12 21:20:33', '2025-03-12 21:21:55'),
(198, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 37100, '2025-03-12 21:23:41', '2025-03-12 22:35:03'),
(199, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 17132, '2025-03-12 22:41:35', '2025-03-12 23:29:50'),
(200, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 21284, '2025-03-12 23:34:15', '2025-03-12 23:46:07'),
(201, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 23060, '2025-03-12 23:50:03', '2025-03-13 00:16:27'),
(202, 'C:\\Users\\<USER>\\project\\txy\\smart-admin-api-java17-springboot3', '127.0.0.1;*************', 13968, '2025-03-13 00:17:36', '2025-03-13 01:39:27'),
(203, 'D:\\Project\\txy\\txy-erp', '127.0.0.1;192.168.2.177;172.25.144.1;192.168.94.1;192.168.179.1', 31760, '2025-03-13 14:01:40', '2025-03-13 14:22:55'),
(204, 'D:\\Project\\txy\\txy-erp', '127.0.0.1;192.168.2.177;172.25.144.1;192.168.94.1;192.168.179.1', 40524, '2025-03-13 15:02:29', '2025-03-13 17:23:45'),
(205, 'D:\\Project\\txy\\txy-erp', '127.0.0.1;192.168.2.177;172.25.144.1;192.168.94.1;192.168.179.1', 40396, '2025-03-13 17:24:26', '2025-03-13 17:25:40'),
(206, 'D:\\Project\\Test\\smart-admin\\smart-admin-api-java17-springboot3', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 20612, '2025-03-13 17:35:05', '2025-03-13 17:51:13'),
(207, 'D:\\Project\\txy\\txy-erp', '127.0.0.1;192.168.2.177;172.25.144.1;192.168.94.1;192.168.179.1', 38896, '2025-03-13 17:52:19', '2025-03-13 17:53:33'),
(208, 'D:\\Project\\Test\\smart-admin\\smart-admin-api-java17-springboot3', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 42576, '2025-03-13 17:55:06', '2025-03-13 18:06:17'),
(209, 'D:\\Project\\Test\\smart-admin\\smart-admin-api-java17-springboot3', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 12896, '2025-03-13 18:43:10', '2025-03-13 18:44:16'),
(210, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 42884, '2025-03-13 18:46:07', '2025-03-13 18:52:12'),
(211, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 42000, '2025-03-13 18:53:21', '2025-03-13 21:39:29'),
(212, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 21852, '2025-03-13 21:42:44', '2025-03-13 23:58:57'),
(213, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 42928, '2025-03-14 00:42:42', '2025-03-14 00:48:48'),
(214, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.25.144.1;192.168.179.1;192.168.94.1;127.0.0.1', 36944, '2025-03-14 00:53:08', '2025-03-14 01:29:19'),
(215, '/Volumes/WorkSpace/PersonWork/aspeed/smart-admin-api', '192.168.97.0;198.18.0.1;192.168.43.29;127.0.0.1', 6452, '2025-03-14 01:13:46', '2025-03-14 01:14:55'),
(216, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 19452, '2025-03-14 11:16:01', '2025-03-14 16:02:17'),
(217, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 38216, '2025-03-14 16:08:09', '2025-03-14 17:04:20'),
(218, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 29440, '2025-03-14 17:06:01', '2025-03-14 18:52:15'),
(219, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 25588, '2025-03-14 18:56:07', '2025-03-14 19:27:13'),
(220, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 41264, '2025-03-14 19:27:23', '2025-03-14 20:03:38'),
(221, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 38696, '2025-03-14 20:08:40', '2025-03-14 20:19:51'),
(222, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 40772, '2025-03-14 20:23:22', '2025-03-14 20:24:32'),
(223, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 6412, '2025-03-14 20:29:05', '2025-03-14 22:20:17'),
(224, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 39248, '2025-03-14 22:22:01', '2025-03-14 23:03:14'),
(225, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 42700, '2025-03-14 23:07:08', '2025-03-14 23:08:19'),
(226, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 8236, '2025-03-14 23:09:38', '2025-03-14 23:55:49'),
(227, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.22.48.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 36340, '2025-03-14 23:59:07', '2025-03-15 00:40:17'),
(228, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.16.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.191', 30592, '2025-03-15 12:50:51', '2025-03-16 01:07:11'),
(229, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 20568, '2025-03-15 14:50:50', '2025-03-15 16:58:41'),
(230, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.54', 6300, '2025-03-15 19:48:45', '2025-03-16 01:47:00'),
(231, '/Volumes/WorkSpace/PersonWork/aspeed/smart-admin-api', '198.18.0.1;192.168.43.29;127.0.0.1', 25560, '2025-03-16 00:56:40', '2025-03-16 00:57:50'),
(232, '/Volumes/WorkSpace/PersonWork/aspeed/smart-admin-api', '198.18.0.1;192.168.43.29;127.0.0.1', 25719, '2025-03-16 00:58:22', '2025-03-16 10:07:33'),
(233, 'C:\\Users\\<USER>\\Desktop', '127.0.0.1;192.168.2.177;172.19.16.1;192.168.94.1;192.168.179.1', 5992, '2025-03-16 01:14:07', '2025-03-16 01:15:17'),
(234, 'C:\\Users\\<USER>\\Desktop', '127.0.0.1;192.168.2.177;172.19.16.1;192.168.94.1;192.168.179.1', 12544, '2025-03-16 01:20:28', '2025-03-16 13:26:48'),
(235, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 24092, '2025-03-16 01:50:11', '2025-03-16 02:01:26'),
(236, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 22584, '2025-03-16 02:04:31', '2025-03-16 02:11:01'),
(237, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 24124, '2025-03-16 02:31:20', '2025-03-16 04:22:45'),
(238, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 30167, '2025-03-16 10:10:46', '2025-03-16 20:17:13'),
(239, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.19.16.1;192.168.179.1;192.168.94.1;127.0.0.1', 30180, '2025-03-16 13:57:58', '2025-03-16 20:44:15'),
(240, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 1044, '2025-03-16 20:49:28', '2025-03-16 21:27:10'),
(241, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 8868, '2025-03-16 21:30:15', '2025-03-16 23:30:37'),
(242, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 29632, '2025-03-16 23:57:09', '2025-03-17 01:17:23'),
(243, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 8652, '2025-03-17 01:20:51', '2025-03-17 01:59:16'),
(244, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 20832, '2025-03-17 02:03:40', '2025-03-17 17:12:06'),
(245, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 25376, '2025-03-17 19:02:09', '2025-03-17 19:03:40'),
(246, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 26768, '2025-03-17 19:04:37', '2025-03-17 19:11:02'),
(247, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 27600, '2025-03-17 19:13:55', '2025-03-17 19:15:26'),
(248, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 16968, '2025-03-17 19:16:42', '2025-03-19 01:07:54'),
(249, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 4828, '2025-03-17 19:53:28', '2025-03-17 19:54:39'),
(250, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 31184, '2025-03-17 19:57:48', '2025-03-17 19:58:58'),
(251, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 2136, '2025-03-17 20:00:51', '2025-03-17 20:07:01'),
(252, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 28088, '2025-03-17 20:09:02', '2025-03-17 20:10:12'),
(253, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 31836, '2025-03-17 20:13:41', '2025-03-17 20:14:50'),
(254, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 34664, '2025-03-17 20:16:56', '2025-03-17 20:18:05'),
(255, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 24036, '2025-03-17 20:20:46', '2025-03-17 20:36:56'),
(256, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 17248, '2025-03-17 20:42:07', '2025-03-17 20:43:17'),
(257, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 26728, '2025-03-17 20:46:02', '2025-03-17 21:32:12'),
(258, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 28803, '2025-03-17 20:46:12', '2025-03-18 00:32:30'),
(259, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 23248, '2025-03-17 21:36:38', '2025-03-17 21:37:48'),
(260, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 19364, '2025-03-17 21:40:00', '2025-03-17 21:41:11'),
(261, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 23776, '2025-03-17 21:42:00', '2025-03-17 21:43:12'),
(262, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 16456, '2025-03-17 21:43:18', '2025-03-17 21:44:28'),
(263, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 32164, '2025-03-17 21:44:50', '2025-03-17 21:46:00'),
(264, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 11548, '2025-03-17 21:51:10', '2025-03-17 21:52:20'),
(265, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 25392, '2025-03-17 21:52:30', '2025-03-17 21:53:40'),
(266, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 4708, '2025-03-17 21:57:21', '2025-03-17 22:03:31'),
(267, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 26636, '2025-03-17 22:06:50', '2025-03-17 22:07:59'),
(268, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 32744, '2025-03-17 22:08:49', '2025-03-17 22:09:59'),
(269, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 33724, '2025-03-17 22:13:12', '2025-03-17 22:14:22'),
(270, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 16720, '2025-03-17 22:19:12', '2025-03-17 22:20:22'),
(271, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.20.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.199', 2152, '2025-03-17 22:24:55', '2025-03-17 22:46:05'),
(272, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 3708, '2025-03-18 12:32:20', '2025-03-18 13:48:32'),
(273, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 3360, '2025-03-18 13:58:29', '2025-03-18 13:59:39'),
(274, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 35888, '2025-03-18 14:00:59', '2025-03-18 14:02:09'),
(275, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 12284, '2025-03-18 14:03:14', '2025-03-18 14:24:24'),
(276, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 33304, '2025-03-18 14:28:15', '2025-03-18 14:39:25'),
(277, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 25744, '2025-03-18 14:40:14', '2025-03-18 15:36:43'),
(278, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 32536, '2025-03-18 15:39:52', '2025-03-18 17:41:03'),
(279, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 30336, '2025-03-18 17:42:57', '2025-03-18 17:44:07'),
(280, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 18416, '2025-03-18 17:46:48', '2025-03-18 17:47:58'),
(281, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 34596, '2025-03-18 17:51:50', '2025-03-18 18:23:01'),
(282, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 12100, '2025-03-18 18:24:54', '2025-03-18 18:36:04'),
(283, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 17452, '2025-03-18 18:38:11', '2025-03-18 18:39:21'),
(284, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 37604, '2025-03-18 18:39:45', '2025-03-18 23:00:59'),
(285, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 12561, '2025-03-18 19:36:06', '2025-03-19 02:17:09'),
(286, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.29.128.1;192.168.179.1;192.168.94.1;127.0.0.1', 44800, '2025-03-18 23:08:49', '2025-03-19 00:50:37'),
(287, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 3444, '2025-03-19 01:12:14', '2025-03-19 01:48:58'),
(288, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 14380, '2025-03-19 01:57:38', '2025-03-19 02:34:02'),
(289, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 25180, '2025-03-19 02:38:06', '2025-03-19 02:41:12'),
(290, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 6964, '2025-03-19 02:41:36', '2025-03-19 10:39:04'),
(291, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 13816, '2025-03-19 10:40:50', '2025-03-19 11:07:33'),
(292, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 29868, '2025-03-19 11:58:31', '2025-03-19 13:36:29'),
(293, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 9358, '2025-03-19 12:54:36', '2025-03-19 20:47:42'),
(294, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 31604, '2025-03-19 13:39:42', '2025-03-19 14:27:27'),
(295, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 24652, '2025-03-19 14:30:04', '2025-03-20 16:44:14'),
(296, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.24.80.1;192.168.179.1;192.168.94.1;127.0.0.1', 17636, '2025-03-19 20:04:34', '2025-03-19 21:10:46'),
(297, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 58035, '2025-03-19 20:49:04', '2025-03-19 22:25:18'),
(298, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.24.80.1;192.168.179.1;192.168.94.1;127.0.0.1', 18300, '2025-03-19 21:12:11', '2025-03-19 23:28:25'),
(299, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.24.80.1;192.168.179.1;192.168.94.1;127.0.0.1', 40804, '2025-03-19 23:30:42', '2025-03-19 23:46:53'),
(300, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.24.80.1;192.168.179.1;192.168.94.1;127.0.0.1', 33308, '2025-03-20 00:05:31', '2025-03-20 00:21:42'),
(301, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.24.80.1;192.168.179.1;192.168.94.1;127.0.0.1', 40544, '2025-03-20 00:23:44', '2025-03-20 00:49:54'),
(302, 'D:\\Project\\txy\\txy-erp', '192.168.2.177;172.28.192.1;192.168.179.1;192.168.94.1;127.0.0.1;192.168.2.206', 37072, '2025-03-20 15:34:03', '2025-03-20 23:45:25'),
(303, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.192', 33492, '2025-03-20 16:45:29', '2025-03-20 17:41:50'),
(304, '/Volumes/WorkSpace/PersonWork/aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 72031, '2025-03-20 20:12:35', '2025-03-21 01:09:00'),
(305, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 5692, '2025-03-21 10:11:27', '2025-03-21 10:13:06'),
(306, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 27120, '2025-03-21 10:15:20', '2025-03-21 10:21:44'),
(307, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 32540, '2025-03-21 18:04:53', '2025-03-21 18:46:33'),
(308, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 16396, '2025-03-21 18:50:21', '2025-03-23 22:32:35'),
(309, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 14540, '2025-03-21 22:22:48', '2025-03-21 23:18:59'),
(310, 'D:\\Project\\txy\\txy-erp', '192.168.2.213;192.168.192.1;192.168.179.1;192.168.94.1;127.0.0.1;169.254.242.4', 26880, '2025-03-22 12:50:18', '2025-03-23 01:26:38'),
(311, '/erp', '172.18.0.1;172.17.0.1;172.29.88.27;127.0.0.1', 1, '2025-03-23 01:22:50', '2025-04-05 11:33:26'),
(312, 'D:\\Project\\txy\\txy-erp', '192.168.2.213;192.168.179.1;192.168.94.1;172.20.80.1;127.0.0.1', 15892, '2025-03-23 11:51:33', '2025-03-23 16:42:55'),
(313, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 40759, '2025-03-23 13:40:08', '2025-03-23 16:56:25'),
(314, 'D:\\Project\\txy\\txy-erp', '192.168.2.213;192.168.179.1;192.168.94.1;172.20.80.1;127.0.0.1', 31004, '2025-03-23 16:47:51', '2025-03-23 21:09:06'),
(315, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.215', 33264, '2025-03-23 22:37:04', '2025-03-24 00:13:31'),
(316, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.215', 14368, '2025-03-24 00:17:40', '2025-03-24 01:11:06'),
(317, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.215', 12740, '2025-03-24 01:20:55', '2025-03-24 10:27:57'),
(318, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.0.209', 23308, '2025-03-24 10:35:44', '2025-03-24 12:47:34'),
(319, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.0.209', 23372, '2025-03-24 12:52:16', '2025-03-24 17:48:43'),
(320, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.134.102', 29328, '2025-03-24 18:57:44', '2025-03-24 19:38:57'),
(321, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.134.102', 4708, '2025-03-24 19:41:50', '2025-03-25 02:03:14'),
(322, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 8345, '2025-03-24 21:45:45', '2025-03-25 01:07:06'),
(323, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.134.102', 23544, '2025-03-25 02:05:42', '2025-03-25 10:24:37'),
(324, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.134.102', 14864, '2025-03-25 10:36:09', '2025-03-25 10:42:40'),
(325, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.0.152', 14048, '2025-03-25 10:47:16', '2025-03-25 13:00:08'),
(326, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.0.152', 3832, '2025-03-25 13:01:28', '2025-03-25 19:02:55'),
(327, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.221', 12620, '2025-03-25 19:08:02', '2025-03-25 20:46:30'),
(328, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.43.29;127.0.0.1', 41841, '2025-03-25 20:34:11', '2025-03-26 00:16:54'),
(329, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.221', 3932, '2025-03-25 20:48:22', '2025-03-26 17:51:26'),
(330, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.134.100', 12424, '2025-03-26 20:31:42', '2025-03-27 00:43:28'),
(331, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 27628, '2025-03-27 16:49:01', '2025-03-28 00:21:31'),
(332, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.117.0;192.168.107.0;192.168.97.0;192.168.43.29;127.0.0.1', 4430, '2025-03-27 21:12:28', '2025-03-28 01:08:47'),
(333, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 4572, '2025-03-28 00:50:36', '2025-03-28 00:51:50'),
(334, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 26952, '2025-03-28 00:53:04', '2025-03-28 03:18:59'),
(335, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.0.125', 10492, '2025-03-28 12:37:10', '2025-03-28 17:37:43'),
(336, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 28044, '2025-03-28 23:00:29', '2025-03-29 01:18:42'),
(337, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 21420, '2025-03-29 01:31:38', '2025-03-29 10:46:32'),
(338, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 4824, '2025-03-29 14:11:12', '2025-03-29 14:32:53'),
(339, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 22452, '2025-03-29 14:46:52', '2025-03-29 14:48:08'),
(340, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 18252, '2025-03-29 14:57:32', '2025-03-29 17:58:09'),
(341, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '198.18.0.1;192.168.117.0;192.168.107.0;192.168.97.0;192.168.43.29;127.0.0.1', 22368, '2025-03-29 22:10:06', '2025-03-30 21:03:55'),
(342, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 3956, '2025-03-30 10:13:37', '2025-03-30 14:35:22'),
(343, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 25412, '2025-03-30 14:37:02', '2025-03-30 14:38:38'),
(344, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 22520, '2025-03-30 14:46:22', '2025-03-30 14:47:37'),
(345, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 22820, '2025-03-30 17:51:26', '2025-03-30 17:57:41'),
(346, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 28068, '2025-03-30 18:06:56', '2025-03-30 19:38:12'),
(347, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 27472, '2025-03-30 19:42:56', '2025-03-30 21:05:41'),
(348, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 23300, '2025-03-30 21:07:53', '2025-03-30 22:14:29'),
(349, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 31664, '2025-03-30 22:25:57', '2025-03-30 23:12:38'),
(350, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.12', 26544, '2025-03-30 23:31:00', '2025-03-31 08:12:54'),
(351, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 32508, '2025-03-31 11:18:18', '2025-03-31 11:54:52'),
(352, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 35680, '2025-03-31 12:27:42', '2025-03-31 12:43:58'),
(353, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 31844, '2025-03-31 12:50:46', '2025-03-31 17:17:25'),
(354, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 26216, '2025-03-31 17:19:37', '2025-03-31 17:31:12'),
(355, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 9704, '2025-03-31 17:35:25', '2025-03-31 19:11:59'),
(356, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 12584, '2025-03-31 19:23:23', '2025-03-31 19:44:53'),
(357, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 27744, '2025-03-31 19:45:52', '2025-03-31 19:47:19'),
(358, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 27384, '2025-03-31 19:48:04', '2025-03-31 19:49:30'),
(359, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 19952, '2025-03-31 21:03:25', '2025-03-31 21:04:56'),
(360, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 31244, '2025-03-31 21:08:13', '2025-03-31 21:19:56'),
(361, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 28988, '2025-03-31 21:31:32', '2025-03-31 21:32:47'),
(362, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 29476, '2025-03-31 21:34:24', '2025-03-31 21:50:39'),
(363, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 17876, '2025-03-31 21:51:18', '2025-03-31 21:52:32'),
(364, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 15656, '2025-03-31 21:52:56', '2025-03-31 21:54:10'),
(365, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 508, '2025-03-31 21:56:06', '2025-04-01 00:55:06'),
(366, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 2656, '2025-04-01 01:04:38', '2025-04-01 11:41:49'),
(367, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 16228, '2025-04-02 01:26:21', '2025-04-02 11:07:02'),
(368, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 19464, '2025-04-02 11:07:29', '2025-04-02 15:29:24'),
(369, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 10444, '2025-04-02 15:31:35', '2025-04-02 15:43:02'),
(370, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 17596, '2025-04-02 15:43:46', '2025-04-02 17:15:22'),
(371, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 18916, '2025-04-02 17:18:26', '2025-04-02 22:54:35'),
(372, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.18', 1492, '2025-04-02 22:57:04', '2025-04-03 13:38:31'),
(373, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.50.169', 36180, '2025-04-03 17:35:45', '2025-04-03 17:37:16'),
(374, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.50.169', 16356, '2025-04-03 17:54:24', '2025-04-03 18:10:46'),
(375, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.50.169', 15952, '2025-04-03 18:28:25', '2025-04-03 19:10:21'),
(376, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.50.169', 39784, '2025-04-03 19:12:27', '2025-04-04 12:27:06'),
(377, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 44468, '2025-04-04 12:28:36', '2025-04-04 12:29:57'),
(378, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 49552, '2025-04-04 12:34:41', '2025-04-05 14:18:08'),
(379, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '10.37.129.2;10.211.55.2;192.168.117.0;192.168.107.0;192.168.97.0;198.18.0.1;192.168.43.29;127.0.0.1', 7471, '2025-04-05 12:13:05', '2025-04-05 14:39:20'),
(380, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 15508, '2025-04-05 15:57:43', '2025-04-05 23:17:58'),
(381, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 18668, '2025-04-05 23:22:19', '2025-04-05 23:23:43'),
(382, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 27324, '2025-04-05 23:26:00', '2025-04-05 23:37:22'),
(383, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 22512, '2025-04-05 23:43:20', '2025-04-05 23:44:39'),
(384, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;192.168.2.22', 22720, '2025-04-06 00:45:42', '2025-04-06 14:17:00'),
(385, '/Volumes/WorkSpace/PersonWork/Aspeed/txy-erp', '192.168.117.0;192.168.107.0;192.168.97.0;198.18.0.1;198.19.249.3;192.168.43.29;127.0.0.1', 17322, '2025-04-06 21:20:05', '2025-04-07 05:19:44'),
(386, 'C:\\Users\\<USER>\\Desktop\\IdeaProject\\txy-erp', '************;*************;*************;127.0.0.1;***********;172.18.0.216', 30192, '2025-04-07 18:26:32', '2025-04-07 18:27:41'),
(387, 'C:\\Users\\<USER>\\project\\txy\\txy-erp', '127.0.0.1;***********', 32736, '2025-04-07 20:33:09', '2025-04-07 23:07:17'),
(388, 'C:\\Users\\<USER>\\Desktop\\IdeaProject\\txy-erp', '************;*************;*************;127.0.0.1;***********;***********', 48720, '2025-04-09 11:14:27', '2025-04-09 11:15:36'),
(389, 'C:\\Users\\<USER>\\Desktop\\IdeaProject\\txy-erp', '************;*************;*************;127.0.0.1;***********;***********', 39128, '2025-04-09 11:18:52', '2025-04-09 15:25:28');

-- Set sequence value
-- The original AUTO_INCREMENT was 390, and the max inserted heart_beat_record_id is 389.
-- Set the next value to 390 to match the original schema behavior.
SELECT setval('t_heart_beat_record_heart_beat_record_id_seq', 390, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_help_doc
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_help_doc;

-- Create table
CREATE TABLE t_help_doc (
  help_doc_id BIGSERIAL PRIMARY KEY,
  help_doc_catalog_id BIGINT NOT NULL,
  title VARCHAR(200) NOT NULL,
  content_text TEXT NOT NULL,
  content_html TEXT NOT NULL,
  attachment VARCHAR(1000) DEFAULT NULL,
  sort INTEGER NOT NULL DEFAULT 0,
  page_view_count INTEGER NOT NULL DEFAULT 0,
  user_view_count INTEGER NOT NULL DEFAULT 0,
  author VARCHAR(1000) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_help_doc IS '帮助文档';
COMMENT ON COLUMN t_help_doc.help_doc_id IS '主键ID'; -- Inferred from AUTO_INCREMENT
COMMENT ON COLUMN t_help_doc.help_doc_catalog_id IS '关联的帮助文档目录ID'; -- Clarified based on name
COMMENT ON COLUMN t_help_doc.title IS '标题';
COMMENT ON COLUMN t_help_doc.content_text IS '文本内容';
COMMENT ON COLUMN t_help_doc.content_html IS 'html内容';
COMMENT ON COLUMN t_help_doc.attachment IS '附件';
COMMENT ON COLUMN t_help_doc.sort IS '排序';
COMMENT ON COLUMN t_help_doc.page_view_count IS '页面浏览量，传说中的pv';
COMMENT ON COLUMN t_help_doc.user_view_count IS '用户浏览量，传说中的uv';
COMMENT ON COLUMN t_help_doc.author IS '作者';
COMMENT ON COLUMN t_help_doc.update_time IS '更新时间';
COMMENT ON COLUMN t_help_doc.create_time IS '创建时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_help_doc_set_update_time
BEFORE UPDATE ON t_help_doc
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Removed backslashes from HTML content.
INSERT INTO t_help_doc (help_doc_id, help_doc_catalog_id, title, content_text, content_html, attachment, sort, page_view_count, user_view_count, author, update_time, create_time) VALUES
(32, 6, '企业名称该写什么？', '需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；', '<ul><li style="text-align: start;">需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改</li><li style="text-align: start;">需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改</li><li style="text-align: start;">需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改</li><li style="text-align: start;">需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；</li></ul>', '', 0, 56, 2, '卓大', '2025-03-12 17:45:40', '2022-11-22 10:41:48'),
(33, 6, '谁有权限查看企业信息', '需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；', '<ul><li style="text-align: start;">需求1：管理公司基本信息，包含：企业名称、Logo、地区、营业执照、联系人 等等，可以 增删拆改</li><li style="text-align: start;">需求2：管理公司的银行账户，包含：银行信息、账户名称、账号、类型等，可以 增删拆改</li><li style="text-align: start;">需求3：管理公司的发票信息，包含：开票抬头、纳税号、银行账户、开户行、备注等，可以 增删拆改</li><li style="text-align: start;">需求4：对于公司信息、银行信息、发票信息 任何的修改，都有记录 数据变动记录；</li></ul>', '', 0, 14, 2, '卓大', '2025-03-12 17:45:46', '2022-11-22 10:42:19');

-- Set sequence value
-- The original AUTO_INCREMENT was 35, and the max inserted help_doc_id is 33.
-- Set the next value to 35 to match the original schema behavior.
SELECT setval('t_help_doc_help_doc_id_seq', 35, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_help_doc_catalog
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_help_doc_catalog;

-- Create table
CREATE TABLE t_help_doc_catalog (
  help_doc_catalog_id BIGSERIAL PRIMARY KEY,
  name VARCHAR(1000) NOT NULL,
  sort INTEGER NOT NULL DEFAULT 0,
  parent_id BIGINT NOT NULL,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_help_doc_catalog IS '帮助文档-目录';
COMMENT ON COLUMN t_help_doc_catalog.help_doc_catalog_id IS '帮助文档目录ID'; -- Clarified based on context
COMMENT ON COLUMN t_help_doc_catalog.name IS '名称';
COMMENT ON COLUMN t_help_doc_catalog.sort IS '排序字段';
COMMENT ON COLUMN t_help_doc_catalog.parent_id IS '父级id';
COMMENT ON COLUMN t_help_doc_catalog.create_time IS '创建时间';
COMMENT ON COLUMN t_help_doc_catalog.update_time IS '更新时间';

-- Create trigger for update_time
CREATE TRIGGER trg_t_help_doc_catalog_set_update_time
BEFORE UPDATE ON t_help_doc_catalog
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_help_doc_catalog (help_doc_catalog_id, name, sort, parent_id, create_time, update_time) VALUES
(6, '企业信息', 0, 0, '2022-11-05 10:52:40', '2022-11-22 10:37:38'),
(9, '企业信用', 0, 6, '2023-12-01 20:16:54', '2023-12-01 20:16:54'),
(10, '采购文档', 0, 11, '2023-12-01 20:17:08', '2023-12-01 20:17:29'),
(11, '进销存', 0, 0, '2023-12-01 20:17:23', '2023-12-01 20:17:23');

-- Set sequence value
-- The original AUTO_INCREMENT was 12, and the max inserted help_doc_catalog_id is 11.
-- Set the next value to 12 to match the original schema behavior.
SELECT setval('t_help_doc_catalog_help_doc_catalog_id_seq', 12, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_help_doc_relation
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_help_doc_relation;

-- Create table
CREATE TABLE t_help_doc_relation (
  relation_id BIGINT NOT NULL,
  relation_name VARCHAR(255) DEFAULT NULL,
  help_doc_id BIGINT NOT NULL,
  create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (relation_id, help_doc_id)
);

-- Add comments to the table and columns
COMMENT ON TABLE t_help_doc_relation IS '帮助文档-关联表';
COMMENT ON COLUMN t_help_doc_relation.relation_id IS '关联id';
COMMENT ON COLUMN t_help_doc_relation.relation_name IS '关联名称';
COMMENT ON COLUMN t_help_doc_relation.help_doc_id IS '文档id';
COMMENT ON COLUMN t_help_doc_relation.create_time IS '创建时间';
COMMENT ON COLUMN t_help_doc_relation.update_time IS '更新时间';

-- Unique constraint (already covered by PRIMARY KEY)
-- ALTER TABLE t_help_doc_relation ADD CONSTRAINT uni_t_help_doc_relation_menu_help_doc UNIQUE (relation_id, help_doc_id);
-- Note: The PRIMARY KEY constraint already enforces uniqueness on these columns.

-- Create trigger for update_time
CREATE TRIGGER trg_t_help_doc_relation_set_update_time
BEFORE UPDATE ON t_help_doc_relation
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_help_doc_relation (relation_id, relation_name, help_doc_id, create_time, update_time) VALUES
(0, '首页', 32, '2023-12-04 13:34:17', '2023-12-04 13:34:17'),
(0, '首页', 33, '2023-12-04 13:34:21', '2023-12-04 13:34:21');

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_help_doc_view_record
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_help_doc_view_record;

-- Create table
CREATE TABLE t_help_doc_view_record (
  help_doc_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  user_name VARCHAR(255) DEFAULT NULL,
  page_view_count INTEGER DEFAULT 0,
  first_ip VARCHAR(255) DEFAULT NULL,
  first_user_agent VARCHAR(1000) DEFAULT NULL,
  last_ip VARCHAR(255) DEFAULT NULL,
  last_user_agent VARCHAR(1000) DEFAULT NULL,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (help_doc_id, user_id)
);

-- Add comments to the table and columns
COMMENT ON TABLE t_help_doc_view_record IS '帮助文档-查看记录';
COMMENT ON COLUMN t_help_doc_view_record.help_doc_id IS '帮助文档id'; -- Renamed from '通知公告id' to match table context
COMMENT ON COLUMN t_help_doc_view_record.user_id IS '用户id';
COMMENT ON COLUMN t_help_doc_view_record.user_name IS '用户名称';
COMMENT ON COLUMN t_help_doc_view_record.page_view_count IS '查看次数';
COMMENT ON COLUMN t_help_doc_view_record.first_ip IS '首次ip';
COMMENT ON COLUMN t_help_doc_view_record.first_user_agent IS '首次用户设备等标识';
COMMENT ON COLUMN t_help_doc_view_record.last_ip IS '最后一次ip';
COMMENT ON COLUMN t_help_doc_view_record.last_user_agent IS '最后一次用户设备等标识';
COMMENT ON COLUMN t_help_doc_view_record.create_time IS '创建时间';
COMMENT ON COLUMN t_help_doc_view_record.update_time IS '更新时间';

-- Unique constraint (already covered by PRIMARY KEY)
-- ALTER TABLE t_help_doc_view_record ADD CONSTRAINT uk_t_help_doc_view_record_notice_employee UNIQUE (help_doc_id, user_id);
-- Note: The PRIMARY KEY constraint already enforces uniqueness on these columns.

-- Create trigger for update_time
CREATE TRIGGER trg_t_help_doc_view_record_set_update_time
BEFORE UPDATE ON t_help_doc_view_record
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_help_doc_view_record (help_doc_id, user_id, user_name, page_view_count, first_ip, first_user_agent, last_ip, last_user_agent, create_time, update_time) VALUES
(32, 1, '管理员', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, NULL, '2025-03-12 17:45:40', '2025-03-12 17:45:40'),
(33, 1, '管理员', 1, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', NULL, NULL, '2025-03-12 17:45:46', '2025-03-12 17:45:46');

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_login_fail
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_login_fail;

-- Create table
CREATE TABLE t_login_fail (
  login_fail_id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  user_type INTEGER NOT NULL,
  login_name VARCHAR(1000) DEFAULT NULL,
  login_fail_count INTEGER DEFAULT NULL,
  lock_flag BOOLEAN DEFAULT false, -- Mapped tinyint to boolean
  login_lock_begin_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
  create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_login_fail IS '登录失败次数记录表';
COMMENT ON COLUMN t_login_fail.login_fail_id IS '自增id';
COMMENT ON COLUMN t_login_fail.user_id IS '用户id';
COMMENT ON COLUMN t_login_fail.user_type IS '用户类型';
COMMENT ON COLUMN t_login_fail.login_name IS '登录名';
COMMENT ON COLUMN t_login_fail.login_fail_count IS '连续登录失败次数';
COMMENT ON COLUMN t_login_fail.lock_flag IS '锁定状态: false:未锁定, true:锁定'; -- Mapped 0/1
COMMENT ON COLUMN t_login_fail.login_lock_begin_time IS '连续登录失败锁定开始时间';
COMMENT ON COLUMN t_login_fail.create_time IS '创建时间';
COMMENT ON COLUMN t_login_fail.update_time IS '更新时间';

-- Create unique constraint
ALTER TABLE t_login_fail ADD CONSTRAINT uq_t_login_fail_uid_and_utype UNIQUE (user_id, user_type);

-- Create trigger for update_time
CREATE TRIGGER trg_t_login_fail_set_update_time
BEFORE UPDATE ON t_login_fail
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Converted lock_flag (0->false, 1->true) to boolean
INSERT INTO t_login_fail (login_fail_id, user_id, user_type, login_name, login_fail_count, lock_flag, login_lock_begin_time, create_time, update_time) VALUES
(86, 2, 1, 'huke', 1, false, NULL, '2025-03-12 16:58:07', '2025-03-12 16:58:07');

-- Set sequence value
-- The original AUTO_INCREMENT was 91, and the max inserted login_fail_id is 86.
-- Set the next value to 91 to match the original schema behavior.
SELECT setval('t_login_fail_login_fail_id_seq', 91, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_login_log
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_login_log;

-- Create table
CREATE TABLE t_login_log (
  login_log_id BIGSERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL,
  user_type INTEGER NOT NULL,
  user_name VARCHAR(1000) NOT NULL,
  login_ip VARCHAR(1000) DEFAULT NULL,
  login_ip_region VARCHAR(1000) DEFAULT NULL,
  user_agent TEXT DEFAULT NULL,
  login_result INTEGER NOT NULL,
  remark VARCHAR(2000) DEFAULT NULL,
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_login_log IS '用户登录日志';
COMMENT ON COLUMN t_login_log.login_log_id IS '主键';
COMMENT ON COLUMN t_login_log.user_id IS '用户id';
COMMENT ON COLUMN t_login_log.user_type IS '用户类型';
COMMENT ON COLUMN t_login_log.user_name IS '用户名';
COMMENT ON COLUMN t_login_log.login_ip IS '用户ip';
COMMENT ON COLUMN t_login_log.login_ip_region IS '用户ip地区';
COMMENT ON COLUMN t_login_log.user_agent IS 'user-agent信息';
COMMENT ON COLUMN t_login_log.login_result IS '登录结果：0成功 1失败 2 退出';
COMMENT ON COLUMN t_login_log.remark IS '备注';
COMMENT ON COLUMN t_login_log.update_time IS '更新时间';
COMMENT ON COLUMN t_login_log.create_time IS '创建时间';

-- Create index
CREATE INDEX idx_t_login_log_user_id ON t_login_log (user_id);

-- Create trigger for update_time
CREATE TRIGGER trg_t_login_log_set_update_time
BEFORE UPDATE ON t_login_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
INSERT INTO t_login_log (login_log_id, user_id, user_type, user_name, login_ip, login_ip_region, user_agent, login_result, remark, update_time, create_time) VALUES
(1754, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-10 22:49:19', '2025-03-10 22:49:20'),
(1755, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-11 15:18:42', '2025-03-11 15:18:42'),
(1756, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-11 16:51:55', '2025-03-11 16:51:56'),
(1757, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-11 19:00:25', '2025-03-11 19:00:26'),
(1758, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-11 20:15:38', '2025-03-11 20:15:39'),
(1759, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-11 20:15:48', '2025-03-11 20:15:49'),
(1760, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:20:29', '2025-03-12 16:20:30'),
(1761, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:20:38', '2025-03-12 16:20:39'),
(1762, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:21:26', '2025-03-12 16:21:27'),
(1763, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:43:41', '2025-03-12 16:43:42'),
(1764, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:48:12', '2025-03-12 16:48:13'),
(1765, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:48:20', '2025-03-12 16:48:22'),
(1766, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:48:57', '2025-03-12 16:48:58'),
(1767, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:52:38', '2025-03-12 16:52:39'),
(1768, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:52:52', '2025-03-12 16:52:53'),
(1769, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:52:59', '2025-03-12 16:53:00'),
(1770, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 2, NULL, '2025-03-12 16:54:48', '2025-03-12 16:54:49'),
(1771, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:54:58', '2025-03-12 16:54:59'),
(1772, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:55:13', '2025-03-12 16:55:14'),
(1773, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:56:36', '2025-03-12 16:56:37'),
(1774, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:57:56', '2025-03-12 16:57:57'),
(1775, 2, 1, '胡克', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-12 16:58:07', '2025-03-12 16:58:08'),
(1776, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-12 17:08:11', '2025-03-12 17:08:12'),
(1777, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-12 17:50:01', '2025-03-12 17:50:03'),
(1778, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-12 21:59:25', '2025-03-12 21:59:26'),
(1779, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-12 23:17:38', '2025-03-12 23:17:38'),
(1780, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 00:33:39', '2025-03-13 00:33:39'),
(1781, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 15:02:26', '2025-03-13 15:02:55'),
(1782, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:35:00', '2025-03-13 17:35:29'),
(1783, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:41:07', '2025-03-13 17:41:37'),
(1784, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:41:38', '2025-03-13 17:42:07'),
(1785, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:44:47', '2025-03-13 17:45:17'),
(1786, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:45:01', '2025-03-13 17:45:30'),
(1787, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:45:07', '2025-03-13 17:45:36'),
(1788, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:45:14', '2025-03-13 17:45:43'),
(1789, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:45:22', '2025-03-13 17:45:51'),
(1790, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:46:41', '2025-03-13 17:47:10'),
(1791, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-13 17:46:52', '2025-03-13 17:47:21'),
(1792, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:48:23', '2025-03-13 17:48:52'),
(1793, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:48:35', '2025-03-13 17:49:05'),
(1794, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:49:02', '2025-03-13 17:49:31'),
(1795, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:49:58', '2025-03-13 17:50:28'),
(1796, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:53:09', '2025-03-13 17:53:38'),
(1797, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-13 17:53:19', '2025-03-13 17:53:49'),
(1798, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:55:13', '2025-03-13 17:55:42'),
(1799, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-13 17:55:55', '2025-03-13 17:56:24'),
(1800, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 2, NULL, '2025-03-14 12:45:39', '2025-03-14 12:46:09'),
(1801, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-14 12:45:47', '2025-03-14 12:46:17'),
(1802, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-14 12:46:11', '2025-03-14 12:46:41'),
(1803, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-14 12:56:56', '2025-03-14 12:57:26'),
(1804, 47, 1, '善逸', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-15 14:19:36', '2025-03-15 14:19:37'),
(1805, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-16 00:23:33', '2025-03-16 00:23:33'),
(1806, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, '密码错误', '2025-03-16 01:16:57', '2025-03-16 01:16:57'),
(1807, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-16 10:31:32', '2025-03-16 10:31:30'),
(1808, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-16 14:07:39', '2025-03-16 14:07:39'),
(1809, 47, 1, '善逸', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-16 16:17:30', '2025-03-16 16:17:30'),
(1810, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-16 16:17:36', '2025-03-16 16:17:36'),
(1811, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-16 16:22:13', '2025-03-16 16:22:13'),
(1812, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-16 16:23:26', '2025-03-16 16:23:26'),
(1813, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-17 20:01:09', '2025-03-17 20:01:09'),
(1814, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-17 20:09:17', '2025-03-17 20:09:18'),
(1815, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-17 20:22:04', '2025-03-17 20:22:05'),
(1816, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-18 19:10:15', '2025-03-18 19:10:17'),
(1817, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-18 19:38:19', '2025-03-18 19:38:20'),
(1818, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 1, '密码错误', '2025-03-18 19:38:29', '2025-03-18 19:38:30'),
(1819, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-18 19:38:39', '2025-03-18 19:38:41'),
(1820, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-18 19:50:00', '2025-03-18 19:50:01'),
(1821, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-18 19:50:21', '2025-03-18 19:50:23'),
(1822, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-18 20:11:17', '2025-03-18 20:11:18'),
(1823, 75, 1, '测试财务', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-18 20:12:09', '2025-03-18 20:12:10'),
(1824, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-19 21:15:07', '2025-03-19 21:15:07'),
(1825, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-19 21:18:04', '2025-03-19 21:18:05'),
(1826, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-23 15:10:01', '2025-03-23 15:10:01'),
(1827, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-23 15:17:01', '2025-03-23 15:17:01'),
(1828, 1, 1, '管理员', '**************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-23 19:04:22', '2025-03-23 19:04:23'),
(1829, 1, 1, '管理员', '**************', '中国|0|广东省|深圳市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-23 19:05:01', '2025-03-23 19:05:02'),
(1830, 1, 1, '管理员', '**************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-23 19:05:14', '2025-03-23 19:05:15'),
(1831, 1, 1, '管理员', '**************', '中国|0|广东省|深圳市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-23 19:06:04', '2025-03-23 19:06:04'),
(1832, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-23 22:06:44', '2025-03-23 22:06:43'),
(1833, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 00:10:37', '2025-03-24 00:10:37'),
(1834, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 10:35:13', '2025-03-24 10:35:14'),
(1835, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 10:39:20', '2025-03-24 10:39:20'),
(1836, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-24 10:43:41', '2025-03-24 10:43:41'),
(1837, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 10:43:54', '2025-03-24 10:43:54'),
(1838, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 10:59:28', '2025-03-24 10:59:28'),
(1839, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 2, NULL, '2025-03-24 11:08:33', '2025-03-24 11:08:33'),
(1840, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 11:08:59', '2025-03-24 11:08:59'),
(1841, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-24 11:25:21', '2025-03-24 11:25:21'),
(1842, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 11:25:39', '2025-03-24 11:25:39'),
(1843, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-24 11:39:46', '2025-03-24 11:39:46'),
(1844, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 11:40:11', '2025-03-24 11:40:11'),
(1845, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 11:40:25', '2025-03-24 11:40:25'),
(1846, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Linux; Android 14; SM-W9025 Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.103 Mobile Safari/537.36 XWEB/1300467 MMWEBSDK/20250201 MMWEBID/4545 MicroMessenger/8.0.57.2820(0x28003933) WeChat/arm64 Weixin NetType/WIFI Language/zh_CN ABI/arm64', 0, '电脑端', '2025-03-24 12:38:41', '2025-03-24 12:38:42'),
(1847, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c2c) XWEB/8518 Flue', 0, '电脑端', '2025-03-24 12:41:56', '2025-03-24 12:41:56'),
(1848, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 15:27:00', '2025-03-24 15:27:00'),
(1849, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 16:15:27', '2025-03-24 16:15:27'),
(1850, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 17:00:50', '2025-03-24 17:00:50'),
(1851, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-24 19:05:19', '2025-03-24 19:05:18'),
(1852, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 19:10:20', '2025-03-24 19:10:20'),
(1853, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-24 21:51:36', '2025-03-24 21:51:36'),
(1854, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-25 14:48:01', '2025-03-25 14:48:02'),
(1855, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-25 20:45:37', '2025-03-25 20:45:35'),
(1856, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-25 20:45:45', '2025-03-25 20:45:44'),
(1857, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 2, NULL, '2025-03-27 21:12:40', '2025-03-27 21:12:41'),
(1858, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-27 21:12:47', '2025-03-27 21:12:48'),
(1859, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-28 23:58:49', '2025-03-28 23:58:49'),
(1860, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 09:13:17', '2025-03-29 09:13:17'),
(1861, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 09:16:09', '2025-03-29 09:16:09'),
(1862, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 2, NULL, '2025-03-29 09:21:18', '2025-03-29 09:21:18'),
(1863, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 09:21:30', '2025-03-29 09:21:30'),
(1864, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 2, NULL, '2025-03-29 09:22:54', '2025-03-29 09:22:54'),
(1865, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 09:23:31', '2025-03-29 09:23:32'),
(1866, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 09:27:24', '2025-03-29 09:27:24'),
(1867, 1, 1, '管理员', '*************', '中国|0|香港|0|电讯盈科', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 10:15:58', '2025-03-29 10:15:58'),
(1868, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 10:36:38', '2025-03-29 10:36:38'),
(1869, 1, 1, '管理员', '**************', '中国|0|福建省|福州市|联通', 'Mozilla/5.0 (Linux; Android 14; V2162A Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.103 Mobile Safari/537.36 XWEB/1300473 MMWEBSDK/20250201 MMWEBID/3743 MicroMessenger/8.0.57.2820(0x28003990) WeChat/arm64 Weixin NetType/ Language/zh_CN ABI/arm64', 1, '密码错误', '2025-03-29 10:42:16', '2025-03-29 10:42:17'),
(1870, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Linux; Android 14; V2162A Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.103 Mobile Safari/537.36 XWEB/1300473 MMWEBSDK/20250201 MMWEBID/3743 MicroMessenger/8.0.57.2820(0x28003990) WeChat/arm64 Weixin NetType/ Language/zh_CN ABI/arm64', 1, '密码错误', '2025-03-29 10:42:40', '2025-03-29 10:42:40'),
(1871, 76, 1, '内勤测试账号1', '**************', '中国|0|福建省|福州市|联通', 'Mozilla/5.0 (Linux; Android 14; V2162A Build/UP1A.231005.007; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/130.0.6723.103 Mobile Safari/537.36 XWEB/1300473 MMWEBSDK/20250201 MMWEBID/3743 MicroMessenger/8.0.57.2820(0x28003990) WeChat/arm64 Weixin NetType/ Language/zh_CN ABI/arm64', 0, '电脑端', '2025-03-29 10:43:00', '2025-03-29 10:43:00'),
(1872, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-29 10:52:54', '2025-03-29 10:52:54'),
(1873, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 10:53:42', '2025-03-29 10:53:42'),
(1874, 1, 1, '管理员', '*************', '中国|0|云南省|昭通市|移动', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x1800392a) NetType/WIFI Language/zh_CN', 1, '密码错误', '2025-03-29 10:58:47', '2025-03-29 10:58:48'),
(1875, 1, 1, '管理员', '*************', '中国|0|云南省|昭通市|移动', 'Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.57(0x1800392a) NetType/WIFI Language/zh_CN', 1, '密码错误', '2025-03-29 10:59:08', '2025-03-29 10:59:09'),
(1876, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:01:35', '2025-03-29 11:01:36'),
(1877, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:04:11', '2025-03-29 11:04:11'),
(1878, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:08:13', '2025-03-29 11:08:13'),
(1879, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:08:42', '2025-03-29 11:08:42'),
(1880, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:09:16', '2025-03-29 11:09:16'),
(1881, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:10:01', '2025-03-29 11:10:02'),
(1882, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:10:42', '2025-03-29 11:10:42'),
(1883, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:17:50', '2025-03-29 11:17:51'),
(1884, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 11:19:29', '2025-03-29 11:19:30'),
(1885, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15', 0, '电脑端', '2025-03-29 11:28:03', '2025-03-29 11:28:04'),
(1886, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 13:21:51', '2025-03-29 13:21:52'),
(1887, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 15:19:21', '2025-03-29 15:19:22'),
(1888, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 15:35:14', '2025-03-29 15:35:15'),
(1889, 76, 1, '内勤测试账号1', '39.144.252.214', '中国|0|0|0|移动', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-29 15:42:33', '2025-03-29 15:42:33'),
(1890, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 15:46:56', '2025-03-29 15:46:56'),
(1891, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 15:51:31', '2025-03-29 15:51:32'),
(1892, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-29 15:54:37', '2025-03-29 15:54:38'),
(1893, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0', 0, '电脑端', '2025-03-29 15:55:34', '2025-03-29 15:55:34'),
(1894, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-03-29 21:58:26', '2025-03-29 21:58:27'),
(1895, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-03-29 21:58:38', '2025-03-29 21:58:38'),
(1896, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-30 08:33:58', '2025-03-30 08:33:59'),
(1897, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-30 08:34:26', '2025-03-30 08:34:27'),
(1898, 1, 1, '管理员', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 1, '密码错误', '2025-03-30 08:37:17', '2025-03-30 08:37:18'),
(1899, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:50:24', '2025-03-30 08:50:25'),
(1900, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15', 0, '电脑端', '2025-03-30 08:50:41', '2025-03-30 08:50:41'),
(1901, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:50:52', '2025-03-30 08:50:53'),
(1902, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:51:22', '2025-03-30 08:51:22'),
(1903, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:53:00', '2025-03-30 08:53:00'),
(1904, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:53:17', '2025-03-30 08:53:17'),
(1905, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:53:51', '2025-03-30 08:53:52'),
(1906, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:53:56', '2025-03-30 08:53:56'),
(1907, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 08:55:55', '2025-03-30 08:55:55'),
(1908, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 10:22:06', '2025-03-30 10:22:06'),
(1909, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 10:44:13', '2025-03-30 10:44:14'),
(1910, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 11:18:36', '2025-03-30 11:18:36'),
(1911, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-30 15:40:32', '2025-03-30 15:40:32'),
(1912, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-31 16:21:59', '2025-03-31 16:21:59'),
(1913, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-31 16:24:28', '2025-03-31 16:24:28'),
(1914, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-31 16:27:17', '2025-03-31 16:27:17'),
(1915, 76, 1, '内勤测试账号1', '*************', '中国|0|福建省|福州市|电信', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 0, '电脑端', '2025-03-31 16:32:35', '2025-03-31 16:32:35'),
(1916, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-04-05 12:13:55', '2025-04-05 12:13:55'),
(1917, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-04-06 21:21:18', '2025-04-06 21:21:18'),
(1918, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-04-09 11:19:26', '2025-04-09 11:19:26'),
(1919, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 2, NULL, '2025-04-09 13:55:45', '2025-04-09 13:55:46'),
(1920, 76, 1, '内勤测试账号1', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-04-09 13:55:58', '2025-04-09 13:55:58'),
(1921, 1, 1, '管理员', '127.0.0.1', '0|0|0|内网IP|内网IP', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 0, '电脑端', '2025-04-09 14:05:27', '2025-04-09 14:05:28');

-- Set sequence value
-- The original AUTO_INCREMENT was 1922, and the max inserted login_log_id is 1921.
-- Set the next value to 1922 to match the original schema behavior.
SELECT setval('t_login_log_login_log_id_seq', 1922, false);

-- ============================================================
--   End of Script
-- ============================================================
-- ============================================================
--   Table Name: t_mail_template
-- ============================================================

-- Drop table
DROP TABLE IF EXISTS t_mail_template;

-- Create table
CREATE TABLE t_mail_template (
  template_code VARCHAR(200) PRIMARY KEY,
  template_subject VARCHAR(100) NOT NULL,
  template_content TEXT NOT NULL, -- Changed from longtext to TEXT
  template_type VARCHAR(50) NOT NULL,
  disable_flag BOOLEAN NOT NULL DEFAULT false, -- Mapped tinyint(1) to boolean
  update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to the table and columns
COMMENT ON TABLE t_mail_template IS '邮件模板表'; -- Added table comment based on context
COMMENT ON COLUMN t_mail_template.template_code IS '模板编码'; -- Added column comment based on context
COMMENT ON COLUMN t_mail_template.template_subject IS '模板名称';
COMMENT ON COLUMN t_mail_template.template_content IS '模板内容';
COMMENT ON COLUMN t_mail_template.template_type IS '解析类型 string，freemarker';
COMMENT ON COLUMN t_mail_template.disable_flag IS '是否禁用';
COMMENT ON COLUMN t_mail_template.update_time IS '更新时间'; -- Changed comment from '创建时间'
COMMENT ON COLUMN t_mail_template.create_time IS '创建时间';

-- Unique constraint (already covered by PRIMARY KEY)
-- ALTER TABLE t_mail_template ADD CONSTRAINT uq_t_mail_template_template_code UNIQUE (template_code);

-- Create trigger for update_time
CREATE TRIGGER trg_t_mail_template_set_update_time
BEFORE UPDATE ON t_mail_template
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp('update_time');

-- Insert data
-- Note: Converted disable_flag (0->false). Unescaped \r\n and \" in template_content.
INSERT INTO t_mail_template (template_code, template_subject, template_content, template_type, disable_flag, update_time, create_time) VALUES
('login_verification_code', '登录验证码', '<!DOCTYPE HTML>
<html>
<head>
  <title>登录提醒</title>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <style>
      * {
          font-family: SimSun;
          /* 4号字体 */
          font-size: 18px;
          /* 22磅行间距 */
          line-height: 29px;
      }

      .main_font_size {
          font-size: 12.0pt;
      }

      .mainContent {
          line-height: 28px;
      }

      p {
          margin: 0 auto;
          text-align: justify;
      }
  </style>

</head>
<body>
<div>
  <div style="margin: 0px auto;width: 690px;">
    <div class="mainContent">
      <h1>验证码</h1>
      <p>请在验证页面输入此验证码</p>
      <p><b>${code}</b></p>
      <p>验证码将于此电子邮件发出 5 分钟后过期。</p>
      <p>如果你未曾提出此请求，可以忽略这封电子邮件。</p>
    </div>

  </div>
</div>
</body>
</html>', 'freemarker', false, '2024-08-06 09:13:08', '2024-07-28 13:56:06');

-- ============================================================
--   End of Script
-- ============================================================
-- ===========================================================
-- Converted PostgreSQL DDL/DML for table: t_menu
-- ===========================================================

-- 删除可能存在的旧表
DROP TABLE IF EXISTS t_menu;

-- 创建表结构: t_menu
CREATE TABLE t_menu (
    menu_id BIGSERIAL PRIMARY KEY,
    menu_name VARCHAR(200) NOT NULL,
    menu_type INTEGER NOT NULL,
    parent_id BIGINT NOT NULL,
    sort INTEGER DEFAULT NULL,
    path VARCHAR(255) DEFAULT NULL,
    component VARCHAR(255) DEFAULT NULL,
    perms_type INTEGER DEFAULT NULL,
    api_perms TEXT DEFAULT NULL,
    web_perms TEXT DEFAULT NULL,
    icon VARCHAR(100) DEFAULT NULL,
    context_menu_id BIGINT DEFAULT NULL,
    frame_flag BOOLEAN NOT NULL DEFAULT false,
    frame_url TEXT DEFAULT NULL,
    cache_flag BOOLEAN NOT NULL DEFAULT false,
    visible_flag BOOLEAN NOT NULL DEFAULT true,
    disabled_flag BOOLEAN NOT NULL DEFAULT false,
    deleted_flag BOOLEAN NOT NULL DEFAULT false,
    create_user_id BIGINT NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_user_id BIGINT DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- ON UPDATE 由触发器处理
);

-- 添加表和列的注释
COMMENT ON TABLE t_menu IS '菜单表';
COMMENT ON COLUMN t_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN t_menu.menu_name IS '菜单名称';
COMMENT ON COLUMN t_menu.menu_type IS '类型';
COMMENT ON COLUMN t_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN t_menu.sort IS '显示顺序';
COMMENT ON COLUMN t_menu.path IS '路由地址';
COMMENT ON COLUMN t_menu.component IS '组件路径';
COMMENT ON COLUMN t_menu.perms_type IS '权限类型';
COMMENT ON COLUMN t_menu.api_perms IS '后端权限字符串';
COMMENT ON COLUMN t_menu.web_perms IS '前端权限字符串';
COMMENT ON COLUMN t_menu.icon IS '菜单图标';
COMMENT ON COLUMN t_menu.context_menu_id IS '功能点关联菜单ID';
COMMENT ON COLUMN t_menu.frame_flag IS '是否为外链';
COMMENT ON COLUMN t_menu.frame_url IS '外链地址';
COMMENT ON COLUMN t_menu.cache_flag IS '是否缓存';
COMMENT ON COLUMN t_menu.visible_flag IS '显示状态';
COMMENT ON COLUMN t_menu.disabled_flag IS '禁用状态';
COMMENT ON COLUMN t_menu.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_menu.create_user_id IS '创建人';
COMMENT ON COLUMN t_menu.create_time IS '创建时间';
COMMENT ON COLUMN t_menu.update_user_id IS '更新人';
COMMENT ON COLUMN t_menu.update_time IS '更新时间';

-- 创建触发器 (假设 trigger_set_timestamp() 函数已存在)
-- 如果函数不存在，你需要先创建它，例如：
-- CREATE OR REPLACE FUNCTION trigger_set_timestamp()
-- RETURNS TRIGGER AS $$
-- BEGIN
--   NEW.update_time = NOW();
--   RETURN NEW;
-- END;
-- $$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp_t_menu
BEFORE UPDATE ON t_menu
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 插入数据: t_menu
-- 注意: MySQL 的 tinyint(1) 在这里被转换为 boolean (0 -> false, 1 -> true)
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(26,'菜单管理',2,50,1,'/menu/list','/system/menu/menu-list.vue',NULL,NULL,NULL,'CopyOutlined',NULL,false,NULL,true,true,false,false,2,'2021-08-09 15:04:35',1,'2023-12-01 19:39:03'),
(40,'删除',3,26,NULL,NULL,NULL,1,'system:menu:batchDelete','system:menu:batchDelete',NULL,26,false,NULL,false,true,false,false,1,'2021-08-12 09:45:56',1,'2023-10-07 18:15:50'),
(45,'组织架构',1,0,3,'/organization',NULL,NULL,NULL,NULL,'UserSwitchOutlined',NULL,false,NULL,false,true,false,false,1,'2021-08-12 16:13:27',1,'2025-03-16 19:44:31'),
(46,'员工管理',2,45,3,'/organization/employee','/system/employee/BoxSelect.vue',NULL,NULL,NULL,'AuditOutlined',NULL,false,NULL,false,true,false,false,1,'2021-08-12 16:21:50',1,'2024-07-02 20:15:23'),
(47,'商品管理',2,138,1,'/erp/goods/list','/business/erp/goods/goods-list.vue',NULL,NULL,NULL,'AliwangwangOutlined',NULL,false,NULL,true,true,false,false,1,'2021-08-12 17:58:39',1,'2025-03-30 20:55:55'),
(50,'系统设置',1,0,6,'/setting',NULL,NULL,NULL,NULL,'SettingOutlined',NULL,false,NULL,false,true,false,false,1,'2021-08-13 16:41:33',1,'2023-12-01 19:38:03'),
(76,'角色管理',2,45,4,'/organization/role','/system/role/BoxSelect.vue',NULL,NULL,NULL,'SlidersOutlined',NULL,false,NULL,false,true,false,false,1,'2021-08-26 10:31:00',1,'2024-07-02 20:15:28'),
(78,'商品分类',2,48,2,'/erp/catalog/goods','/business/erp/catalog/goods-catalog.vue',NULL,NULL,NULL,'ApartmentOutlined',NULL,false,NULL,true,true,false,false,1,'2022-05-18 23:34:14',1,'2023-12-01 19:33:13'),
(79,'自定义分组',2,48,3,'/erp/catalog/custom','/business/erp/catalog/custom-catalog.vue',NULL,NULL,NULL,'AppstoreAddOutlined',NULL,false,NULL,false,true,false,false,1,'2022-05-18 23:37:53',1,'2023-12-01 19:33:16'),
(81,'用户操作记录',2,213,6,'/support/operate-log/operate-log-list','/support/operate-log/operate-log-list.vue',NULL,NULL,NULL,'VideoCameraOutlined',NULL,false,NULL,false,true,false,false,1,'2022-05-20 12:37:24',44,'2024-08-13 14:34:10'),
(85,'组件演示',2,84,NULL,'/demonstration/index','/support/demonstration/BoxSelect.vue',NULL,NULL,NULL,'ClearOutlined',NULL,false,NULL,false,true,false,false,1,'2022-05-20 23:16:46',NULL,'2022-05-20 23:16:46'),
(86,'添加部门',3,46,1,NULL,NULL,1,'system:department:add','system:department:add',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-26 23:33:37',1,'2023-10-07 18:26:35'),
(87,'修改部门',3,46,2,NULL,NULL,1,'system:department:update','system:department:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-26 23:34:11',1,'2023-10-07 18:26:44'),
(88,'删除部门',3,46,3,NULL,NULL,1,'system:department:delete','system:department:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-26 23:34:49',1,'2023-10-07 18:26:49'),
(91,'添加员工',3,46,NULL,NULL,NULL,1,'system:employee:add','system:employee:add',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:11:38',1,'2023-10-07 18:27:46'),
(92,'编辑员工',3,46,NULL,NULL,NULL,1,'system:employee:update','system:employee:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:12:10',1,'2023-10-07 18:27:49'),
(93,'禁用启用员工',3,46,NULL,NULL,NULL,1,'system:employee:disabled','system:employee:disabled',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:12:37',1,'2023-10-07 18:27:53'),
(94,'调整员工部门',3,46,NULL,NULL,NULL,1,'system:employee:department:update','system:employee:department:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:12:59',1,'2023-10-07 18:27:34'),
(95,'重置密码',3,46,NULL,NULL,NULL,1,'system:employee:password:reset','system:employee:password:reset',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:13:30',1,'2023-10-07 18:27:57'),
(96,'删除员工',3,46,NULL,NULL,NULL,1,'system:employee:delete','system:employee:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:14:08',1,'2023-10-07 18:28:01'),
(97,'添加角色',3,76,NULL,NULL,NULL,1,'system:role:add','system:role:add',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:34:00',1,'2023-10-07 18:42:31'),
(98,'删除角色',3,76,NULL,NULL,NULL,1,'system:role:delete','system:role:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:34:19',1,'2023-10-07 18:42:35'),
(99,'编辑角色',3,76,NULL,NULL,NULL,1,'system:role:update','system:role:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:34:55',1,'2023-10-07 18:42:44'),
(100,'更新数据范围',3,76,NULL,NULL,NULL,1,'system:role:dataScope:update','system:role:dataScope:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:37:03',1,'2023-10-07 18:41:49'),
(101,'批量移除员工',3,76,NULL,NULL,NULL,1,'system:role:employee:batch:delete','system:role:employee:batch:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:39:05',1,'2023-10-07 18:43:32'),
(102,'移除员工',3,76,NULL,NULL,NULL,1,'system:role:employee:delete','system:role:employee:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:39:21',1,'2023-10-07 18:43:37'),
(103,'添加员工',3,76,NULL,NULL,NULL,1,'system:role:employee:add','system:role:employee:add',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:39:38',1,'2023-10-07 18:44:05'),
(104,'修改权限',3,76,NULL,NULL,NULL,1,'system:role:menu:update','system:role:menu:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-05-27 00:41:55',1,'2023-10-07 18:44:11'),
(105,'添加',3,26,NULL,NULL,NULL,1,'system:menu:add','system:menu:add',NULL,26,false,NULL,false,true,false,false,1,'2022-05-27 00:44:37',1,'2023-10-07 17:35:35'),
(106,'编辑',3,26,NULL,NULL,NULL,1,'system:menu:update','system:menu:update',NULL,26,false,NULL,false,true,false,false,1,'2022-05-27 00:44:59',1,'2023-10-07 17:35:48'),
(109,'参数配置',2,50,3,'/config/config-list','/support/config/config-list.vue',NULL,NULL,NULL,'AntDesignOutlined',NULL,false,NULL,false,true,false,false,1,'2022-05-27 13:34:41',1,'2022-06-23 16:24:16'),
(110,'数据字典',2,50,4,'/setting/dict','/support/dict/BoxSelect.vue',NULL,NULL,NULL,'BarcodeOutlined',NULL,false,NULL,false,true,false,false,1,'2022-05-27 17:53:00',1,'2022-05-27 18:09:14'),
(111,'监控服务',1,0,100,'/monitor',NULL,NULL,NULL,NULL,'BarChartOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-17 11:13:23',1,'2025-03-16 19:53:37'),
(113,'查询',3,112,NULL,NULL,NULL,NULL,NULL,'ad',NULL,NULL,false,NULL,false,true,false,false,1,'2022-06-17 11:31:36',NULL,'2022-06-17 11:31:36'),
(114,'运维工具',1,0,200,NULL,NULL,NULL,NULL,NULL,'NodeCollapseOutlined',NULL,false,NULL,false,true,false,true,1,'2022-06-20 10:09:16',1,'2023-12-01 19:36:18'),
(117,'Reload',2,50,12,'/hook','/support/reload/reload-list.vue',NULL,NULL,NULL,'ReloadOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-20 10:16:49',1,'2023-12-01 19:39:17'),
(122,'数据库监控',2,111,4,'/support/druid/index',NULL,NULL,NULL,NULL,'ConsoleSqlOutlined',NULL,true,'http://localhost:1024/druid',true,true,false,false,1,'2022-06-20 14:49:33',1,'2023-02-16 19:15:58'),
(130,'单号管理',2,50,6,'/support/serial-number/serial-number-list','/support/serial-number/serial-number-list.vue',NULL,NULL,NULL,'NumberOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-24 14:45:22',1,'2022-06-28 16:23:41'),
(132,'公告管理',2,138,2,'/oa/notice/notice-list','/business/oa/notice/notice-list.vue',NULL,NULL,NULL,'SoundOutlined',NULL,false,NULL,true,true,false,false,1,'2022-06-24 18:23:09',1,'2024-07-08 13:58:51'),
(133,'缓存管理',2,50,11,'/support/cache/cache-list','/support/cache/cache-list.vue',NULL,NULL,NULL,'BorderInnerOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-24 18:52:25',1,'2023-12-01 19:39:13'),
(138,'功能Demo',1,0,6,NULL,NULL,NULL,NULL,NULL,'BankOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-24 20:09:18',1,'2025-03-16 19:58:45'),
(142,'公告详情',2,132,NULL,'/oa/notice/notice-detail','/business/oa/notice/notice-detail.vue',NULL,NULL,NULL,NULL,NULL,false,NULL,false,false,false,false,1,'2022-06-25 16:38:47',1,'2022-09-14 19:46:17'),
(143,'登录登出记录',2,213,5,'/support/login-log/login-log-list','/support/login-log/login-log-list.vue',NULL,NULL,NULL,'LoginOutlined',NULL,false,NULL,false,true,false,false,1,'2022-06-28 15:01:38',44,'2024-08-13 14:33:49'),
(144,'企业管理',2,138,1,'/oa/enterprise/enterprise-list','/business/oa/enterprise/enterprise-list.vue',NULL,NULL,NULL,'ShopOutlined',NULL,false,NULL,false,true,false,false,1,'2022-09-14 17:00:07',1,'2025-03-18 22:54:50'),
(145,'企业详情',2,138,NULL,'/oa/enterprise/enterprise-detail','/business/oa/enterprise/enterprise-detail.vue',NULL,NULL,NULL,NULL,NULL,false,NULL,false,false,false,false,1,'2022-09-14 18:52:52',1,'2022-11-22 10:39:07'),
(147,'帮助文档',2,218,1,'/help-doc/help-doc-manage-list','/support/help-doc/management/help-doc-manage-list.vue',NULL,NULL,NULL,'FolderViewOutlined',NULL,false,NULL,false,true,false,false,1,'2022-09-14 19:59:01',1,'2023-12-01 19:38:23'),
(148,'意见反馈',2,218,2,'/feedback/feedback-list','/support/feedback/feedback-list.vue',NULL,NULL,NULL,'CoffeeOutlined',NULL,false,NULL,false,true,false,false,1,'2022-09-14 19:59:52',1,'2023-12-01 19:38:40'),
(149,'我的通知',2,132,NULL,'/oa/notice/notice-employee-list','/business/oa/notice/notice-employee-list.vue',NULL,NULL,NULL,NULL,NULL,false,NULL,false,false,false,false,1,'2022-09-14 20:29:41',1,'2022-09-14 20:31:23'),
(150,'我的通知公告详情',2,132,NULL,'/oa/notice/notice-employee-detail','/business/oa/notice/notice-employee-detail.vue',NULL,NULL,NULL,NULL,NULL,false,NULL,false,false,false,false,1,'2022-09-14 20:30:25',1,'2022-09-14 20:31:38'),
(151,'代码生成',2,310,600,'/support/code-generator','/support/code-generator/code-generator-list.vue',NULL,NULL,NULL,'CoffeeOutlined',NULL,false,NULL,false,true,false,false,1,'2022-09-21 18:25:05',1,'2025-03-16 19:57:01'),
(152,'更新日志',2,218,3,'/support/change-log/change-log-list','/support/change-log/change-log-list.vue',NULL,NULL,NULL,'HeartOutlined',NULL,false,NULL,false,true,false,false,44,'2022-10-10 10:31:20',1,'2023-12-01 19:38:51'),
(153,'清除缓存',3,133,NULL,NULL,NULL,1,'support:cache:delete','support:cache:delete',NULL,133,false,NULL,false,true,true,false,1,'2022-10-15 22:45:13',1,'2023-10-07 16:22:29'),
(154,'获取缓存key',3,133,NULL,NULL,NULL,1,'support:cache:keys','support:cache:keys',NULL,133,false,NULL,false,true,true,false,1,'2022-10-15 22:45:48',1,'2023-10-07 16:22:35'),
(156,'查看结果',3,117,NULL,NULL,NULL,1,'support:reload:result','support:reload:result',NULL,117,false,NULL,false,true,false,false,1,'2022-10-15 23:17:23',1,'2023-10-07 14:31:47'),
(157,'单号生成',3,130,NULL,NULL,NULL,1,'support:serialNumber:generate','support:serialNumber:generate',NULL,130,false,NULL,false,true,false,false,1,'2022-10-15 23:21:06',1,'2023-10-07 18:22:46'),
(158,'生成记录',3,130,NULL,NULL,NULL,1,'support:serialNumber:record','support:serialNumber:record',NULL,130,false,NULL,false,true,false,false,1,'2022-10-15 23:21:34',1,'2023-10-07 18:22:55'),
(159,'新建',3,110,NULL,NULL,NULL,1,'support:dict:add','support:dict:add',NULL,110,false,NULL,false,true,false,false,1,'2022-10-15 23:23:51',1,'2023-10-07 18:18:24'),
(160,'编辑',3,110,NULL,NULL,NULL,1,'support:dict:edit','support:dict:edit',NULL,110,false,NULL,false,true,false,false,1,'2022-10-15 23:24:05',1,'2023-10-07 18:19:17'),
(161,'批量删除',3,110,NULL,NULL,NULL,1,'support:dict:delete','support:dict:delete',NULL,110,false,NULL,false,true,false,false,1,'2022-10-15 23:24:34',1,'2023-10-07 18:19:39'),
(162,'刷新缓存',3,110,NULL,NULL,NULL,1,'support:dict:refresh','support:dict:refresh',NULL,110,false,NULL,false,true,false,false,1,'2022-10-15 23:24:55',1,'2023-10-07 18:18:37'),
(163,'新建',3,109,NULL,NULL,NULL,1,'support:config:add','support:config:add',NULL,109,false,NULL,false,true,false,false,1,'2022-10-15 23:26:56',1,'2023-10-07 18:16:17'),
(164,'编辑',3,109,NULL,NULL,NULL,1,'support:config:update','support:config:update',NULL,109,false,NULL,false,true,false,false,1,'2022-10-15 23:27:07',1,'2023-10-07 18:16:24'),
(165,'查询',3,47,NULL,NULL,NULL,1,'goods:query','goods:query',NULL,47,false,NULL,false,true,false,false,1,'2022-10-16 19:55:39',1,'2023-10-07 13:58:28'),
(166,'新建',3,47,NULL,NULL,NULL,1,'goods:add','goods:add',NULL,47,false,NULL,false,true,false,false,1,'2022-10-16 19:56:00',1,'2023-10-07 13:58:32'),
(167,'批量删除',3,47,NULL,NULL,NULL,1,'goods:batchDelete','goods:batchDelete',NULL,47,false,NULL,false,true,false,false,1,'2022-10-16 19:56:15',1,'2023-10-07 13:58:35'),
(168,'查询',3,147,11,NULL,NULL,1,'support:helpDoc:query','support:helpDoc:query',NULL,147,false,NULL,false,true,false,false,1,'2022-10-16 20:12:13',1,'2023-10-07 14:05:49'),
(169,'新建',3,147,12,NULL,NULL,1,'support:helpDoc:add','support:helpDoc:add',NULL,147,false,NULL,false,true,false,false,1,'2022-10-16 20:12:37',1,'2023-10-07 14:05:56'),
(170,'新建目录',3,147,1,NULL,NULL,1,'support:helpDocCatalog:addCategory','support:helpDocCatalog:addCategory',NULL,147,false,NULL,false,true,false,false,1,'2022-10-16 20:12:57',1,'2023-10-07 14:06:38'),
(171,'修改目录',3,147,2,NULL,NULL,1,'support:helpDocCatalog:update','support:helpDocCatalog:update',NULL,147,false,NULL,false,true,false,false,1,'2022-10-16 20:13:46',1,'2023-10-07 14:06:49'),
(173,'新建',3,78,NULL,NULL,NULL,1,'category:add','category:add',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:02',1,'2023-10-07 13:54:01'),
(174,'查询',3,78,NULL,NULL,NULL,1,'category:tree','category:tree',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:22',1,'2023-10-07 13:54:33'),
(175,'编辑',3,78,NULL,NULL,NULL,1,'category:update','category:update',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:38',1,'2023-10-07 13:54:18'),
(176,'删除',3,78,NULL,NULL,NULL,1,'category:delete','category:delete',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:50',1,'2023-10-07 13:54:27'),
(177,'新建',3,79,NULL,NULL,NULL,1,'category:add','custom:category:add',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:02',1,'2023-10-07 13:57:32'),
(178,'查询',3,79,NULL,NULL,NULL,1,'category:tree','custom:category:tree',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:22',1,'2023-10-07 13:57:50'),
(179,'编辑',3,79,NULL,NULL,NULL,1,'category:update','custom:category:update',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:38',1,'2023-10-07 13:58:02'),
(180,'删除',3,79,NULL,NULL,NULL,1,'category:delete','custom:category:delete',NULL,78,false,NULL,false,true,false,false,1,'2022-10-16 20:17:50',1,'2023-10-07 13:58:12'),
(181,'查询',3,144,NULL,NULL,NULL,1,'oa:enterprise:query','oa:enterprise:query',NULL,144,false,NULL,false,true,false,false,1,'2022-10-16 20:25:14',1,'2023-10-07 12:00:09'),
(182,'新建',3,144,NULL,NULL,NULL,1,'oa:enterprise:add','oa:enterprise:add',NULL,144,false,NULL,false,true,false,false,1,'2022-10-16 20:25:25',1,'2023-10-07 12:00:17'),
(183,'编辑',3,144,NULL,NULL,NULL,1,'oa:enterprise:update','oa:enterprise:update',NULL,144,false,NULL,false,true,false,false,1,'2022-10-16 20:25:36',1,'2023-10-07 12:00:38'),
(184,'删除',3,144,NULL,NULL,NULL,1,'oa:enterprise:delete','oa:enterprise:delete',NULL,144,false,NULL,false,true,false,false,1,'2022-10-16 20:25:53',1,'2023-10-07 12:00:46'),
(185,'查询',3,132,NULL,NULL,NULL,1,'oa:notice:query','oa:notice:query',NULL,132,false,NULL,false,true,false,false,1,'2022-10-16 20:26:38',1,'2023-10-07 11:43:01'),
(186,'新建',3,132,NULL,NULL,NULL,1,'oa:notice:add','oa:notice:add',NULL,132,false,NULL,false,true,false,false,1,'2022-10-16 20:27:04',1,'2023-10-07 11:43:07'),
(187,'编辑',3,132,NULL,NULL,NULL,1,'oa:notice:update','oa:notice:update',NULL,132,false,NULL,false,true,false,false,1,'2022-10-16 20:27:15',1,'2023-10-07 11:43:12'),
(188,'删除',3,132,NULL,NULL,NULL,1,'oa:notice:delete','oa:notice:delete',NULL,132,false,NULL,false,true,false,false,1,'2022-10-16 20:27:23',1,'2023-10-07 11:43:18'),
(190,'查询',3,152,NULL,NULL,NULL,1,'','support:changeLog:query',NULL,152,false,NULL,false,true,false,false,1,'2022-10-16 20:28:33',1,'2023-10-07 14:25:05'),
(191,'新建',3,152,NULL,NULL,NULL,1,'support:changeLog:add','support:changeLog:add',NULL,152,false,NULL,false,true,false,false,1,'2022-10-16 20:28:46',1,'2023-10-07 14:24:15'),
(192,'批量删除',3,152,NULL,NULL,NULL,1,'support:changeLog:batchDelete','support:changeLog:batchDelete',NULL,152,false,NULL,false,true,false,false,1,'2022-10-16 20:29:10',1,'2023-10-07 14:24:22'),
(193,'文件管理',2,50,20,'/support/file/file-list','/support/file/file-list.vue',NULL,NULL,NULL,'FolderOpenOutlined',NULL,false,NULL,false,true,false,false,1,'2022-10-21 11:26:11',1,'2022-10-22 11:29:22'),
(194,'删除',3,47,NULL,NULL,NULL,1,'goods:delete','goods:delete',NULL,47,false,NULL,false,true,false,false,1,'2022-10-21 20:00:12',1,'2023-10-07 13:58:39'),
(195,'修改',3,47,NULL,NULL,NULL,1,'goods:update','goods:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 20:05:23',1,'2023-10-07 13:58:42'),
(196,'查看详情',3,145,NULL,NULL,NULL,1,'oa:enterprise:detail','oa:enterprise:detail',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 20:16:47',1,'2023-10-07 11:48:59'),
(198,'删除',3,152,NULL,NULL,NULL,1,'support:changeLog:delete','support:changeLog:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 20:42:34',1,'2023-10-07 14:24:32'),
(199,'查询',3,109,NULL,NULL,NULL,1,'support:config:query','support:config:query',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 20:45:14',1,'2023-10-07 18:16:27'),
(200,'查询',3,193,NULL,NULL,NULL,1,'support:file:query','support:file:query',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 20:47:23',1,'2023-10-07 18:24:43'),
(201,'删除',3,147,14,NULL,NULL,1,'support:helpDoc:delete','support:helpDoc:delete',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 21:03:20',1,'2023-10-07 14:07:02'),
(202,'更新',3,147,13,NULL,NULL,1,'support:helpDoc:update','support:helpDoc:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 21:03:32',1,'2023-10-07 14:06:56'),
(203,'查询',3,143,NULL,NULL,NULL,1,'support:loginLog:query','support:loginLog:query',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-21 21:05:11',1,'2023-10-07 14:27:23'),
(204,'查询',3,81,NULL,NULL,NULL,1,'support:operateLog:query','support:operateLog:query',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-22 10:33:31',1,'2023-10-07 14:27:56'),
(205,'详情',3,81,NULL,NULL,NULL,1,'support:operateLog:detail','support:operateLog:detail',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-22 10:33:49',1,'2023-10-07 14:28:04'),
(206,'心跳监控',2,111,1,'/support/heart-beat/heart-beat-list','/support/heart-beat/heart-beat-list.vue',1,NULL,NULL,'FallOutlined',NULL,false,NULL,false,true,false,false,1,'2022-10-22 10:47:03',1,'2022-10-22 18:32:52'),
(207,'更新',3,152,NULL,NULL,NULL,1,'support:changeLog:update','support:changeLog:update',NULL,NULL,false,NULL,false,true,false,false,1,'2022-10-22 11:51:32',1,'2023-10-07 14:24:39'),
(212,'查询',3,117,NULL,NULL,NULL,1,'support:reload:query','support:reload:query',NULL,NULL,false,NULL,true,true,true,false,1,'2023-10-07 14:31:36',NULL,'2023-10-07 14:31:36'),
(213,'网络安全',1,0,5,NULL,NULL,1,NULL,NULL,'SafetyCertificateOutlined',NULL,false,NULL,true,true,false,false,1,'2023-10-17 19:03:08',1,'2025-03-16 19:53:33'),
(214,'登录失败锁定',2,213,4,'/support/login-fail','/support/login-fail/login-fail-list.vue',1,NULL,NULL,'LockOutlined',NULL,false,NULL,true,true,false,false,1,'2023-10-17 19:04:24',44,'2024-08-13 14:16:26'),
(215,'接口加解密',2,213,2,'/support/api-encrypt','/support/api-encrypt/api-encrypt-BoxSelect.vue',1,NULL,NULL,'CodepenCircleOutlined',NULL,false,NULL,true,true,false,false,1,'2023-10-24 11:49:28',44,'2024-08-13 12:00:14'),
(216,'导出',3,47,NULL,NULL,NULL,1,'goods:exportGoods','goods:exportGoods',NULL,NULL,false,NULL,true,true,false,false,1,'2023-12-01 19:34:03',NULL,'2023-12-01 19:34:03'),
(217,'导入',3,47,3,NULL,NULL,1,'goods:importGoods','goods:importGoods',NULL,NULL,false,NULL,true,true,false,false,1,'2023-12-01 19:34:22',NULL,'2023-12-01 19:34:22'),
(218,'文档中心',1,0,4,NULL,NULL,1,NULL,NULL,'FileSearchOutlined',NULL,false,NULL,true,true,false,false,1,'2023-12-01 19:37:28',1,'2025-03-16 19:53:23'),
(219,'部门管理',2,45,1,'/organization/department','/system/department/department-list.vue',1,NULL,NULL,'ApartmentOutlined',NULL,false,NULL,false,true,false,false,1,'2024-06-22 16:40:21',1,'2024-07-02 20:15:17'),
(221,'定时任务',2,50,25,'/job/list','/support/job/job-list.vue',1,NULL,NULL,'AppstoreOutlined',NULL,false,NULL,true,true,false,false,2,'2024-06-25 17:57:40',2,'2024-06-25 19:49:21'),
(228,'职务管理',2,45,2,'/organization/position','/system/position/position-list.vue',1,NULL,NULL,'ApartmentOutlined',NULL,false,NULL,true,true,false,false,1,'2024-06-29 11:11:09',1,'2024-07-02 20:15:11'),
(229,'查询任务',3,221,NULL,NULL,NULL,1,'support:job:query','support:job:query',NULL,221,false,NULL,true,true,false,false,2,'2024-06-29 11:14:15',2,'2024-06-29 11:15:00'),
(230,'更新任务',3,221,NULL,NULL,NULL,1,'support:job:update','support:job:update',NULL,221,false,NULL,true,true,false,false,2,'2024-06-29 11:15:40',NULL,'2024-06-29 11:15:40'),
(231,'执行任务',3,221,NULL,NULL,NULL,1,'support:job:execute','support:job:execute',NULL,221,false,NULL,true,true,false,false,2,'2024-06-29 11:16:03',NULL,'2024-06-29 11:16:03'),
(232,'查询记录',3,221,NULL,NULL,NULL,1,'support:job:log:query','support:job:log:query',NULL,221,false,NULL,true,true,false,false,2,'2024-06-29 11:16:37',NULL,'2024-06-29 11:16:37'),
(233,'knife4j文档',2,218,4,'/knife4j',NULL,1,NULL,NULL,'FileWordOutlined',NULL,true,'http://localhost:10244/doc.html',true,true,false,false,1,'2024-07-02 20:23:50',1,'2025-03-16 20:55:08'),
(234,'swagger文档',2,218,5,'/swagger','http://localhost:1024/swagger-ui/index.html',1,NULL,NULL,'ApiOutlined',NULL,true,'http://localhost:10244/swagger-ui/index.html',true,true,false,false,1,'2024-07-02 20:35:43',1,'2025-03-17 21:49:06'),
(250,'三级等保设置',2,213,1,'/support/level3protect/level3-protect-config-index','/support/level3protect/level3-protect-config-BoxSelect.vue',1,NULL,NULL,'SafetyOutlined',NULL,false,NULL,true,true,false,false,44,'2024-08-13 11:41:02',44,'2024-08-13 11:58:12'),
(251,'敏感数据脱敏',2,213,3,'/support/level3protect/data-masking-list','/support/level3protect/data-masking-list.vue',1,NULL,NULL,'FileProtectOutlined',NULL,false,NULL,true,true,false,false,44,'2024-08-13 11:58:00',44,'2024-08-13 11:59:49'),
(252,'供应商管理',2,0,NULL,'/supplier/list','/business/erp/supplier/supplier-list.vue',1,NULL,NULL,NULL,NULL,false,NULL,false,true,false,true,1,'2025-03-12 20:43:11',1,'2025-03-12 23:21:40'),
(253,'查询',3,252,NULL,NULL,NULL,1,'supplier:query',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:12',1,'2025-03-12 23:21:40'),
(254,'添加',3,252,NULL,NULL,NULL,1,'supplier:add',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:12',1,'2025-03-12 23:21:40'),
(255,'更新',3,252,NULL,NULL,NULL,1,'supplier:update',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:12',1,'2025-03-12 23:21:40'),
(256,'删除',3,252,NULL,NULL,NULL,1,'supplier:delete',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:12',1,'2025-03-12 23:21:40'),
(257,'审批',3,252,NULL,NULL,NULL,1,'supplier:approve',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:12',1,'2025-03-12 23:21:40'),
(258,'状态管理',3,252,NULL,NULL,NULL,1,'supplier:status',NULL,NULL,252,false,NULL,true,true,false,true,1,'2025-03-12 20:43:13',1,'2025-03-12 23:21:40'),
(260,'供应商',2,0,1,'/supplier/list','/business/erp/supplier/SupplierList.vue',1,NULL,NULL,'SolutionOutlined',NULL,false,NULL,false,true,false,false,1,'2025-03-12 23:28:38',1,'2025-03-23 23:32:14'),
(261,'查询',3,260,NULL,NULL,NULL,1,'supplier:query','supplier:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:38',1,'2025-03-24 10:41:41'),
(262,'新增',3,260,NULL,NULL,NULL,1,'supplier:add','supplier:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:38',1,'2025-03-24 10:42:52'),
(263,'编辑',3,260,NULL,NULL,NULL,1,'supplier:update','supplier:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:38',1,'2025-03-24 10:42:56'),
(264,'删除',3,260,NULL,NULL,NULL,1,'supplier:delete','supplier:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:39',1,'2025-03-24 10:42:59'),
(266,'导入',3,260,NULL,NULL,NULL,1,'supplier:import','supplier:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:39',1,'2025-03-24 10:43:07'),
(267,'导出',3,260,NULL,NULL,NULL,1,'supplier:export','supplier:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-12 23:28:39',1,'2025-03-24 10:43:11'),
(269,'审批',3,260,NULL,NULL,NULL,1,'supplier:approve','supplier:approve',NULL,260,false,NULL,true,true,false,true,1,'2025-03-12 23:28:40',1,'2025-03-24 10:43:22'),
(270,'计算器',2,0,444,'/pricecalculator','/business/erp/box-quotation-calculator/BoxSelect.vue',1,NULL,NULL,'CalculatorOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-13 00:44:22',1,'2025-04-02 00:21:55'),
(272,'客户管理',2,0,NULL,'/customer/list','/business/erp/customer/CustomerList.vue',1,NULL,NULL,'WechatOutlined',NULL,false,NULL,false,true,false,false,1,'2025-03-14 17:18:30',1,'2025-03-23 23:31:02'),
(273,'纸张报价',2,259,NULL,'/paper/quotation','/business/erp/paper/PaperQuotationList.vue',1,NULL,NULL,'money',NULL,false,NULL,false,true,false,true,1,'2025-03-14 18:20:48',1,'2025-03-14 18:28:03'),
(274,'纸张报价',2,259,NULL,'/paper/quotation','/business/erp/paper/PaperQuotationList.vue',1,NULL,NULL,'money',NULL,false,NULL,false,true,false,true,1,'2025-03-14 18:27:16',1,'2025-03-14 18:40:16'),
(275,'查询',3,274,NULL,NULL,NULL,1,'paper:quotation:query',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:16',1,'2025-03-14 18:40:16'),
(276,'新增',3,274,NULL,NULL,NULL,1,'paper:quotation:add',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:16',1,'2025-03-14 18:40:16'),
(277,'编辑',3,274,NULL,NULL,NULL,1,'paper:quotation:update',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:16',1,'2025-03-14 18:40:16'),
(278,'删除',3,274,NULL,NULL,NULL,1,'paper:quotation:delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:16',1,'2025-03-14 18:40:16'),
(279,'批量删除',3,274,NULL,NULL,NULL,1,'paper:quotation:batch-delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:17',1,'2025-03-14 18:40:16'),
(280,'导入',3,274,NULL,NULL,NULL,1,'paper:quotation:import',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:17',1,'2025-03-14 18:40:16'),
(281,'导出',3,274,NULL,NULL,NULL,1,'paper:quotation:export',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 18:27:17',1,'2025-03-14 18:40:16'),
(282,'纸张报价',2,259,NULL,'/paper-quotation/list','/business/erp/paper-quotation/PaperQuotationList.vue',1,NULL,NULL,NULL,NULL,false,NULL,false,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(283,'查询',3,282,NULL,NULL,NULL,1,'paperQuotation:query',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(284,'新增',3,282,NULL,NULL,NULL,1,'paperQuotation:add',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(285,'编辑',3,282,NULL,NULL,NULL,1,'paperQuotation:update',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(286,'删除',3,282,NULL,NULL,NULL,1,'paperQuotation:delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(287,'批量删除',3,282,NULL,NULL,NULL,1,'paperQuotation:batch-delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(288,'导入',3,282,NULL,NULL,NULL,1,'paperQuotation:import',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(289,'导出',3,282,NULL,NULL,NULL,1,'paperQuotation:export',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(290,'审批',3,282,NULL,NULL,NULL,1,'paperQuotation:approve',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 18:53:39'),
(291,'纸张报价',2,259,NULL,'/paper-quotation/list','/business/erp/paper/PaperQuotationList.vue',1,NULL,NULL,NULL,NULL,false,NULL,false,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(292,'查询',3,291,NULL,NULL,NULL,1,'paperQuotation:query',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(293,'新增',3,291,NULL,NULL,NULL,1,'paperQuotation:add',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(294,'编辑',3,291,NULL,NULL,NULL,1,'paperQuotation:update',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(295,'删除',3,291,NULL,NULL,NULL,1,'paperQuotation:delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(296,'批量删除',3,291,NULL,NULL,NULL,1,'paperQuotation:batch-delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(297,'导入',3,291,NULL,NULL,NULL,1,'paperQuotation:import',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(298,'导出',3,291,NULL,NULL,NULL,1,'paperQuotation:export',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(299,'审批',3,291,NULL,NULL,NULL,1,'paperQuotation:approve',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-14 20:11:48'),
(300,'供应商纸种及价格',2,0,2,'/paper-quotation/list','/business/erp/paper-quotation/PaperQuotationList.vue',1,NULL,NULL,'MoneyCollectOutlined',NULL,false,NULL,false,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-23 23:31:30'),
(301,'查询',3,300,NULL,NULL,NULL,1,'paperQuotation:query','paperQuotation:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-24 10:52:00'),
(302,'新增',3,300,NULL,NULL,NULL,1,'paperQuotation:add','paperQuotation:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-24 10:52:04'),
(303,'编辑',3,300,NULL,NULL,NULL,1,'paperQuotation:update','paperQuotation:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-24 10:52:07'),
(304,'删除',3,300,NULL,NULL,NULL,1,'paperQuotation:delete','paperQuotation:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-24 10:52:10'),
(305,'批量删除',3,300,NULL,NULL,NULL,1,'paperQuotation:batch-delete',NULL,NULL,260,false,NULL,true,true,false,true,1,'2025-03-14 16:30:00',1,'2025-03-18 23:01:18'),
(306,'导入',3,300,NULL,NULL,NULL,1,'paperQuotation:import',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',NULL,'2025-03-14 16:30:00'),
(307,'导出',3,300,NULL,NULL,NULL,1,'paperQuotation:export',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',NULL,'2025-03-14 16:30:00'),
(308,'审批',3,300,NULL,NULL,NULL,1,'paperQuotation:approve','paperQuotation:approve',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 16:30:00',1,'2025-03-24 10:53:28'),
(309,'开发工具',1,0,5,NULL,NULL,1,NULL,NULL,'AntDesignOutlined',NULL,false,NULL,true,true,false,true,1,'2025-03-16 19:45:54',1,'2025-03-24 00:15:35'),
(310,'test',1,138,NULL,NULL,NULL,1,NULL,NULL,'AlertOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-16 19:56:48',NULL,'2025-03-16 19:56:48'),
(316,'查询',3,315,NULL,NULL,NULL,1,'paper:query',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:49',NULL,'2025-03-18 19:06:49'),
(317,'新增',3,315,NULL,NULL,NULL,1,'paper:add',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:49',NULL,'2025-03-18 19:06:49'),
(318,'编辑',3,315,NULL,NULL,NULL,1,'paper:update',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:49',NULL,'2025-03-18 19:06:49'),
(319,'删除',3,315,NULL,NULL,NULL,1,'paper:delete',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:49',NULL,'2025-03-18 19:06:49'),
(320,'批量删除',3,315,NULL,NULL,NULL,1,'paper:batch-delete',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:50',NULL,'2025-03-18 19:06:50'),
(321,'导入',3,315,NULL,NULL,NULL,1,'paper:import',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:50',NULL,'2025-03-18 19:06:50'),
(322,'导出',3,315,NULL,NULL,NULL,1,'paper:export',NULL,NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 19:06:50',NULL,'2025-03-18 19:06:50'),
(323,'全部纸质资料',2,0,NULL,'/paper/list','/business/erp/paper/PaperList.vue',1,NULL,NULL,'PaperClipOutlined',NULL,false,NULL,false,true,false,false,1,'2025-03-18 19:07:11',1,'2025-03-27 22:51:21'),
(331,'查询',3,323,NULL,NULL,NULL,1,'paper:query','paper:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:35',NULL,'2025-03-18 20:29:30'),
(332,'新增',3,323,NULL,NULL,NULL,1,'paper:add','paper:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:35',NULL,'2025-03-18 20:29:30'),
(333,'编辑',3,323,NULL,NULL,NULL,1,'paper:update','paper:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:35',NULL,'2025-03-18 20:29:30'),
(334,'删除',3,323,NULL,NULL,NULL,1,'paper:delete','paper:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:36',NULL,'2025-03-18 20:29:30'),
(336,'导入',3,323,NULL,NULL,NULL,1,'paper:import','paper:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:36',NULL,'2025-03-18 20:29:31'),
(337,'导出',3,323,NULL,NULL,NULL,1,'paper:export','paper:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-18 20:04:36',NULL,'2025-03-18 20:29:30'),
(338,'查询',3,272,NULL,NULL,NULL,1,'customer:query','customer:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:38',NULL,'2025-03-14 23:28:38'),
(339,'新增',3,272,NULL,NULL,NULL,1,'customer:add','customer:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:38',NULL,'2025-03-14 23:28:38'),
(340,'编辑',3,272,NULL,NULL,NULL,1,'customer:update','customer:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:38',NULL,'2025-03-14 23:28:38'),
(341,'删除',3,272,NULL,NULL,NULL,1,'customer:delete','customer:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:39',NULL,'2025-03-14 23:28:39'),
(342,'导入',3,272,NULL,NULL,NULL,1,'customer:import','customer:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:39',NULL,'2025-03-14 23:28:39'),
(343,'导出',3,272,NULL,NULL,NULL,1,'customer:export','customer:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-14 23:28:39',NULL,'2025-03-14 23:28:39'),
(353,'纸箱报价',2,0,NULL,'/box-quotation/list','/business/erp/box-quotation/BoxQuotationList.vue',1,NULL,NULL,'InboxOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-19 02:15:13',1,'2025-03-23 23:33:10'),
(354,'查询',3,353,NULL,NULL,NULL,1,'box-quotation:query','boxQuotation:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:13',1,'2025-03-29 15:44:17'),
(355,'新增',3,353,NULL,NULL,NULL,1,'box-quotation:add','boxQuotation:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:13',1,'2025-03-29 15:45:19'),
(356,'编辑',3,353,NULL,NULL,NULL,1,'box-quotation:query','boxQuotation:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:13',1,'2025-03-29 15:44:34'),
(357,'删除',3,353,NULL,NULL,NULL,1,'box-quotation:delete','boxQuotation:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:13',1,'2025-03-29 15:45:26'),
(358,'批量删除',3,353,NULL,NULL,NULL,1,'boxQuotation:batch-delete','boxQuotation:batch-delete',NULL,260,false,NULL,true,true,false,true,1,'2025-03-19 02:15:13',1,'2025-03-19 10:42:54'),
(359,'导入',3,353,NULL,NULL,NULL,1,'box-quotation:import','boxQuotation:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:14',1,'2025-03-29 15:45:31'),
(360,'导出',3,353,NULL,NULL,NULL,1,'box-quotation:export','boxQuotation:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:14',1,'2025-03-29 15:45:36'),
(361,'审批',3,353,NULL,NULL,NULL,1,'box-quotation:approve','boxQuotation:approve',NULL,260,false,NULL,true,true,false,false,1,'2025-03-19 02:15:14',1,'2025-03-29 15:44:54'),
(362,'fast-crud',2,138,0,'/demo/fast-crud','/business/erp/box-quotation-beta/BoxSelect.vue',1,NULL,NULL,'TableOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-19 13:03:38',1,'2025-03-19 13:04:35'),
(363,'商品',2,310,NULL,'/goods/list','/business/erp/goods/goods-list.vue',1,NULL,NULL,'AlibabaOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-23 12:29:48',1,'2025-03-25 21:42:27'),
(364,'测试2',1,138,NULL,NULL,NULL,1,NULL,NULL,'AlertOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-25 01:02:46',NULL,'2025-03-25 01:02:46'),
(365,'测试1',2,364,NULL,'test1','/business/erp/box-quotation-calculator/BoxSelect.vue',1,NULL,NULL,'AliwangwangOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-25 01:03:40',NULL,'2025-03-25 01:03:40'),
(366,'印刷工艺查询',3,367,NULL,NULL,NULL,1,'printing-process:query',NULL,NULL,NULL,false,NULL,true,true,false,false,1,'2025-03-29 15:38:57',1,'2025-03-29 15:39:55'),
(367,'权限',1,0,NULL,NULL,NULL,1,NULL,NULL,NULL,NULL,false,NULL,true,false,false,false,1,'2025-03-29 15:39:31',1,'2025-03-30 19:45:24'),
(368,'商品管理',2,0,NULL,'/product/list','/business/product/ProductList.vue',1,NULL,NULL,'shopping',NULL,false,NULL,false,true,false,false,1,'2025-03-30 20:41:17',1,'2025-03-30 20:55:55'),
(369,'查询',3,368,NULL,NULL,NULL,1,'product:query','product:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:17',NULL,'2025-03-30 20:41:17'),
(370,'新增',3,368,NULL,NULL,NULL,1,'product:add','product:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:17',NULL,'2025-03-30 20:41:17'),
(371,'编辑',3,368,NULL,NULL,NULL,1,'product:edit','product:edit',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:17',NULL,'2025-03-30 20:41:17'),
(372,'删除',3,368,NULL,NULL,NULL,1,'product:delete','product:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:18',NULL,'2025-03-30 20:41:18'),
(373,'导入',3,368,NULL,NULL,NULL,1,'product:import','product:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:18',NULL,'2025-03-30 20:41:18'),
(374,'导出',3,368,NULL,NULL,NULL,1,'product:export','product:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:18',NULL,'2025-03-30 20:41:18'),
(375,'查看',3,368,NULL,NULL,NULL,1,'product:view','product:view',NULL,260,false,NULL,true,true,false,false,1,'2025-03-30 20:41:18',NULL,'2025-03-30 20:41:18'),
(376,'实体参数管理',2,392,NULL,'/entity-param/list','/business/erp/entity-param/EntityParamList.vue',1,NULL,NULL,NULL,NULL,false,NULL,false,true,false,false,1,'2025-03-31 09:35:00',1,'2025-03-31 21:39:44'),
(377,'查询',3,376,NULL,NULL,NULL,1,'entityParam:query','entityParam:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:01',NULL,'2025-03-31 09:35:01'),
(378,'新增',3,376,NULL,NULL,NULL,1,'entityParam:add','entityParam:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:01',NULL,'2025-03-31 09:35:01'),
(379,'编辑',3,376,NULL,NULL,NULL,1,'entityParam:update','entityParam:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:01',NULL,'2025-03-31 09:35:01'),
(380,'删除',3,376,NULL,NULL,NULL,1,'entityParam:delete','entityParam:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:02',NULL,'2025-03-31 09:35:02'),
(381,'导入',3,376,NULL,NULL,NULL,1,'entityParam:import','entityParam:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:02',NULL,'2025-03-31 09:35:02'),
(382,'导出',3,376,NULL,NULL,NULL,1,'entityParam:export','entityParam:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:03',NULL,'2025-03-31 09:35:03'),
(383,'查看',3,376,NULL,NULL,NULL,1,'entityParam:view','entityParam:view',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 09:35:03',NULL,'2025-03-31 09:35:03'),
(384,'实体参数类型',2,392,4,'/business/entity-param-type','business/erp/entity-param-type/EntityParamTypeList.vue',1,'entityParamType:query','entityParamType:query','',NULL,false,NULL,false,true,false,false,1,'2025-03-31 19:28:23',1,'2025-03-31 21:40:05'),
(385,'查询',3,384,NULL,NULL,NULL,1,'entityParamType:query','entityParamType:query',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:24',NULL,'2025-03-31 19:28:24'),
(386,'新增',3,384,NULL,NULL,NULL,1,'entityParamType:add','entityParamType:add',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:25',NULL,'2025-03-31 19:28:25'),
(387,'编辑',3,384,NULL,NULL,NULL,1,'entityParamType:update','entityParamType:update',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:25',NULL,'2025-03-31 19:28:25'),
(388,'删除',3,384,NULL,NULL,NULL,1,'entityParamType:delete','entityParamType:delete',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:25',NULL,'2025-03-31 19:28:25'),
(389,'导入',3,384,NULL,NULL,NULL,1,'entityParamType:import','entityParamType:import',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:26',NULL,'2025-03-31 19:28:26'),
(390,'导出',3,384,NULL,NULL,NULL,1,'entityParamType:export','entityParamType:export',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:26',NULL,'2025-03-31 19:28:26'),
(391,'查看',3,384,NULL,NULL,NULL,1,'entityParamType:view','entityParamType:view',NULL,260,false,NULL,true,true,false,false,1,'2025-03-31 19:28:26',NULL,'2025-03-31 19:28:26'),
(392,'系统参数',1,0,NULL,NULL,NULL,1,NULL,NULL,'AntDesignOutlined',NULL,false,NULL,true,true,false,false,1,'2025-03-31 21:39:35',NULL,'2025-03-31 21:39:35');

-- 重置序列 (根据原 MySQL DDL 中的 AUTO_INCREMENT=393)
-- 注意: 使用 select setval 时，第二个参数是序列的 *当前* 值，所以设置为 392，下一个生成的值将是 393。
-- 第三个参数 'true' 表示序列已经被使用过。如果设置为 'false'，则下一个生成的值将是 392。
-- 如果表为空，或者想从 1 开始，可以省略这个 setval 调用。
SELECT setval('t_menu_menu_id_seq', 392, true);


-- 注意:
-- 1. 'type' 列的 tinyint 被转换为 integer，假设它存储的是类别值 (1, 2, 3) 而非布尔值。
-- 2. 'cache', 'visible', 'disabled' 列的 tinyint 被转换为 boolean。
-- 3. 自增ID 'id' 使用 BIGSERIAL 实现。
-- 4. 移除了 MySQL 特定的 ENGINE, CHARSET, COLLATE 等子句。
-- 5. COMMENT 语法已调整为 PostgreSQL 格式。
-- 文件: t_message.sql
-- 表: t_message 通知消息

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_message;

-- 创建表结构
CREATE TABLE t_message (
    message_id BIGSERIAL PRIMARY KEY,
    message_type smallint NOT NULL,
    receiver_user_type integer NOT NULL,
    receiver_user_id bigint NOT NULL,
    data_id varchar(500) DEFAULT '',
    title varchar(1000) NOT NULL,
    content text NOT NULL,
    read_flag boolean NOT NULL DEFAULT false,
    read_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_message IS '通知消息';
COMMENT ON COLUMN t_message.message_id IS '消息id';
COMMENT ON COLUMN t_message.message_type IS '消息类型';
COMMENT ON COLUMN t_message.receiver_user_type IS '接收者用户类型';
COMMENT ON COLUMN t_message.receiver_user_id IS '接收者用户id';
COMMENT ON COLUMN t_message.data_id IS '相关数据id';
COMMENT ON COLUMN t_message.title IS '标题';
COMMENT ON COLUMN t_message.content IS '内容';
COMMENT ON COLUMN t_message.read_flag IS '是否已读';
COMMENT ON COLUMN t_message.read_time IS '已读时间';
COMMENT ON COLUMN t_message.create_time IS '创建时间';
COMMENT ON COLUMN t_message.update_time IS '更新时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_message_msg ON t_message (message_type, receiver_user_type, receiver_user_id);

-- 插入数据
INSERT INTO t_message (message_id, message_type, receiver_user_type, receiver_user_id, data_id, title, content, read_flag, read_time, create_time, update_time) VALUES
(1, 1, 1, 1, 'null', '张三的对公付款单 【3000元】', E'尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市', true, '2025-03-13 01:20:42', '2024-06-27 01:14:07', '2025-03-13 01:20:42'),
(2, 2, 1, 1, '234', '刘备的请假单【本周四】', E'尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市', true, '2025-03-13 01:20:40', '2024-07-04 16:09:49', '2025-03-13 01:20:40'),
(3, 1, 1, 1, '23', '武松的物资采购单【Macbook Pro】', E'尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市', true, '2025-03-13 01:20:38', '2024-07-07 22:03:14', '2025-03-13 01:20:38'),
(4, 1, 1, 1, '23', '孙悟空的出差申请【出差洛阳】', E'尊敬的各位技术大佬：\r\n\r\n1024创新实验室技术分享即将隆重举行\r\n\r\n现将有关会议事宜通知如下：\r\n\r\n一、会议内容\r\n\r\n1、研究探讨SmartAdmin的技术体系\r\n\r\n二、会议形式\r\n\r\n大会专题小会分组讨论;\r\n\r\n三、会议时间及地点\r\n\r\n会议报到时间：xxx1年6月14日\r\n\r\n会议报到地点：洛阳市', true, '2025-03-11 20:36:00', '2024-07-07 22:03:14', '2025-03-11 20:36:00');

-- 重置序列（如果需要从特定值开始）
-- SELECT setval('t_message_message_id_seq', (SELECT MAX(message_id) FROM t_message));
-- 如果原 AUTO_INCREMENT=5 表示下一个值是 5，则使用:
SELECT setval('t_message_message_id_seq', 4, true); -- 第三个参数 true 表示下一个值是 5


-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_message
BEFORE UPDATE ON t_message
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_notice.sql
-- 表: t_notice 通知

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_notice;

-- 创建表结构
CREATE TABLE t_notice (
    notice_id BIGSERIAL PRIMARY KEY,
    notice_type_id bigint NOT NULL,
    title varchar(200) NOT NULL,
    all_visible_flag boolean NOT NULL,
    scheduled_publish_flag boolean NOT NULL,
    publish_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    content_text text NOT NULL,
    content_html text NOT NULL,
    attachment varchar(1000) DEFAULT NULL,
    page_view_count integer NOT NULL DEFAULT 0,
    user_view_count integer NOT NULL DEFAULT 0,
    source varchar(1000) DEFAULT NULL,
    author varchar(1000) DEFAULT NULL,
    document_number varchar(1000) DEFAULT NULL,
    deleted_flag boolean NOT NULL DEFAULT false,
    create_user_id bigint DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_notice IS '通知';
COMMENT ON COLUMN t_notice.notice_id IS '通知ID';
COMMENT ON COLUMN t_notice.notice_type_id IS '类型ID (例如: 1公告 2动态)';
COMMENT ON COLUMN t_notice.title IS '标题';
COMMENT ON COLUMN t_notice.all_visible_flag IS '是否全部可见';
COMMENT ON COLUMN t_notice.scheduled_publish_flag IS '是否定时发布';
COMMENT ON COLUMN t_notice.publish_time IS '发布时间';
COMMENT ON COLUMN t_notice.content_text IS '文本内容';
COMMENT ON COLUMN t_notice.content_html IS 'html内容';
COMMENT ON COLUMN t_notice.attachment IS '附件';
COMMENT ON COLUMN t_notice.page_view_count IS '页面浏览量 (PV)';
COMMENT ON COLUMN t_notice.user_view_count IS '用户浏览量 (UV)';
COMMENT ON COLUMN t_notice.source IS '来源';
COMMENT ON COLUMN t_notice.author IS '作者';
COMMENT ON COLUMN t_notice.document_number IS '文号';
COMMENT ON COLUMN t_notice.deleted_flag IS '删除标志';
COMMENT ON COLUMN t_notice.create_user_id IS '创建人ID';
COMMENT ON COLUMN t_notice.update_time IS '更新时间';
COMMENT ON COLUMN t_notice.create_time IS '创建时间';

-- 插入数据
-- 注意: content_text 中的 \r\n 已使用 E'' 处理，content_html 中的 \" 已替换为 "
INSERT INTO t_notice (notice_id, notice_type_id, title, all_visible_flag, scheduled_publish_flag, publish_time, content_text, content_html, attachment, page_view_count, user_view_count, source, author, document_number, deleted_flag, create_user_id, update_time, create_time) VALUES
(49, 1, 'Spring Boot 3.0.0 首个 RC 发布', true, false, '2024-01-01 20:22:23', E'Spring Boot 3.0.0 首个 RC 发布\r\nSpring Boot 3.0 首个 RC 已发布，此外还为两个分支发布了更新：2.7.5 & 2.6.13。\r\n3.0.0-RC1\r\n发布公告写道，此版本包含 135 项功能增强、文档改进、依赖升级和 Bugfix。\r\nSpring Boot 3.0 的开发工作始于实验性的 Spring Native，旨在为 GraalVM 原生镜像提供支持。在该版本中，开发者现在可以使用标准 Spring Boot Maven 或 Gradle 插件将 Spring Boot 应用程序转换为原生可执行文件，而无需任何特殊配置。\r\n此版本还在参考文档中添加新内容来解释 AOT 处理背后的概念以及如何开始生成第一个 GraalVM 原生镜像。\r\n除此之外，Spring Boot 3.0 还完成了迁移到 JakartaEE 9 的工作，以及将使用的 Java 版本升级到 Java 17。\r\n其他新特性：\r\n为 Spring Data JDBC 提供更灵活的自动配置为 Prometheus 示例提供自动配置增强 Log4j2 功能，包括配置文件支持和环境属性查找\r\n详情查看 Release Note。\r\nSpring Boot 2.7.5 和 2.6.13 的更新内容主要是修复错误，优化文档和升级依赖，详情查看 Release Note (2.7.5、2.6.13)。\r\n相关链接\r\nSpring Boot 的详细介绍：点击查看Spring Boot 的下载地址：点击下载', '<h1 style="text-indent: 0px; text-align: start;"><a href="https://www.oschina.net/news/214401/spring-boot-3-0-0-rc1-released" target="_blank">Spring&nbsp;Boot&nbsp;3.0.0&nbsp;首个&nbsp;RC&nbsp;发布</a></h1><p>Spring&nbsp;Boot&nbsp;3.0 首个&nbsp;RC 已发布，此外还为两个分支发布了更新：2.7.5 & 2.6.13。</p><p>3.0.0-RC1</p><p>发布公告写道，此版本包含 135&nbsp;项功能增强、文档改进、依赖升级和&nbsp;Bugfix。</p><p>Spring&nbsp;Boot&nbsp;3.0&nbsp;的开发工作始于实验性的&nbsp;Spring&nbsp;Native，旨在为&nbsp;GraalVM&nbsp;原生镜像提供支持。在该版本中，开发者现在可以使用标准&nbsp;Spring&nbsp;Boot&nbsp;Maven&nbsp;或&nbsp;Gradle&nbsp;插件将&nbsp;Spring&nbsp;Boot&nbsp;应用程序转换为原生可执行文件，而无需任何特殊配置。</p><p>此版本还在参考文档中添加新内容来解释 AOT&nbsp;处理背后的概念以及如何开始生成第一个&nbsp;GraalVM&nbsp;原生镜像。</p><p>除此之外，Spring&nbsp;Boot&nbsp;3.0&nbsp;还完成了迁移到 JakartaEE&nbsp;9&nbsp;的工作，以及将使用的&nbsp;Java&nbsp;版本升级到&nbsp;Java&nbsp;17。</p><p>其他新特性：</p><p>为&nbsp;Spring&nbsp;Data&nbsp;JDBC&nbsp;提供更灵活的自动配置为&nbsp;Prometheus&nbsp;示例提供自动配置增强&nbsp;Log4j2&nbsp;功能，包括配置文件支持和环境属性查找</p><p>详情查看&nbsp;Release&nbsp;Note。</p><p>Spring&nbsp;Boot&nbsp;2.7.5&nbsp;和&nbsp;2.6.13&nbsp;的更新内容主要是修复错误，优化文档和升级依赖，详情查看&nbsp;Release&nbsp;Note&nbsp;(2.7.5、2.6.13)。</p><p>相关链接</p><p>Spring&nbsp;Boot&nbsp;的详细介绍：点击查看Spring&nbsp;Boot&nbsp;的下载地址：点击下载</p>', '', 0, 0, '开源中国', '卓大', NULL, false, 1, '2024-03-02 18:53:26', '2022-10-22 14:27:33'),
(50, 1, 'Oracle 推出 JDK 8 的直接替代品', true, false, '2024-01-01 20:22:23', E'Oracle 推出 JDK 8 的直接替代品\r\n来源: OSCHINA\r\n编辑: 白开水不加糖\r\n2022-10-20 08:14:29\r\n 0\r\n为了向传统的 Java 8 服务器工作负载提供 Java 17 级别的性能，Oracle 宣布推出 Java SE Subscription Enterprise Performance Pack (Enterprise Performance Pack)。并声称这是 JDK 8 的直接替代品，现已在 MyOracleSupport 上面向所有 Java SE 订阅客户和 Oracle 云基础设施 (OCI) 用户免费提供。\r\n“Enterprise Performance Pack 为 JDK 8 用户提供了在 JDK 8 和 JDK 17 发布之间的 7 年时间里，为 Java 带来的重大内存管理和性能改进。这些改进包括：现代垃圾回收算法、紧凑字符串、增强的可观察性和数十种其他优化。”\r\nJava 8 发布于 2014 年，和 Java 17 一样都是长期支持 (LTS) 版本；尽管发布距今已有近九年的历史，但仍被很多开发人员和组织所广泛应用。New Relic 发布的一份 “2022 年 Java 生态系统状况报告” 数据表明，Java 8 仍被 46.45% 的 Java 应用程序在生产中使用。\r\n根据介绍，Enterprise Performance Pack 在 Intel 和基于 Arm 的系统（如 Ampere Altra）上支持 headless Linux 64 位工作负载。\r\nOracle 方面称，使用 Enterprise Performance Pack 的客户将可以立即看到以或接近内存或 CPU 容量运行的 JDK 8 工作负载的好处。在 Oracle 自己的产品和云服务进行的测试表明，高负载应用程序的内存和性能都提高了大约 40%。即使没有接近容量运行的 JDK 8 应用程序，也可以会看到高达 5% 的性能提升。\r\n虽然 Enterprise Performance Pack 中包含的许多改进可以通过默认选项获得，但 Oracle 建议用户还是自己研究文档，以最大限度地提高性能并最大限度地降低内存使用率。例如，通过启用可扩展的低延迟 ZGC 垃圾收集器来提高应用程序响应能力，需要通过 -XX:+UseZGC 选项。', '<h3>Oracle&nbsp;推出&nbsp;JDK&nbsp;8&nbsp;的直接替代品</h3><p>来源:&nbsp;OSCHINA</p><p>编辑: 白开水不加糖</p><p>2022-10-20&nbsp;08:14:29</p><p> 0</p><p>为了向传统的&nbsp;Java&nbsp;8&nbsp;服务器工作负载提供&nbsp;Java&nbsp;17&nbsp;级别的性能，Oracle 宣布推出&nbsp;Java&nbsp;SE&nbsp;Subscription&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;(Enterprise&nbsp;Performance&nbsp;Pack)。并声称这是 JDK&nbsp;8&nbsp;的直接替代品，现已在 MyOracleSupport 上面向所有&nbsp;Java&nbsp;SE&nbsp;订阅客户和&nbsp;Oracle&nbsp;云基础设施&nbsp;(OCI)&nbsp;用户免费提供。</p><p>“Enterprise&nbsp;Performance&nbsp;Pack&nbsp;为&nbsp;JDK&nbsp;8&nbsp;用户提供了在&nbsp;JDK&nbsp;8&nbsp;和&nbsp;JDK&nbsp;17&nbsp;发布之间的&nbsp;7&nbsp;年时间里，为&nbsp;Java&nbsp;带来的重大内存管理和性能改进。这些改进包括：现代垃圾回收算法、紧凑字符串、增强的可观察性和数十种其他优化。”</p><p>Java&nbsp;8&nbsp;发布于&nbsp;2014&nbsp;年，和&nbsp;Java&nbsp;17&nbsp;一样都是长期支持&nbsp;(LTS)&nbsp;版本；尽管发布距今已有近九年的历史，但仍被很多开发人员和组织所广泛应用。New&nbsp;Relic&nbsp;发布的一份 “2022&nbsp;年&nbsp;Java&nbsp;生态系统状况报告”&nbsp;数据表明，Java&nbsp;8&nbsp;仍被&nbsp;46.45%&nbsp;的&nbsp;Java&nbsp;应用程序在生产中使用。</p><p>根据介绍，Enterprise&nbsp;Performance&nbsp;Pack&nbsp;在&nbsp;Intel&nbsp;和基于&nbsp;Arm&nbsp;的系统（如&nbsp;Ampere&nbsp;Altra）上支持 headless&nbsp;Linux&nbsp;64&nbsp;位工作负载。</p><p>Oracle 方面称，使用&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;的客户将可以立即看到以或接近内存或&nbsp;CPU&nbsp;容量运行的&nbsp;JDK&nbsp;8&nbsp;工作负载的好处。在&nbsp;Oracle&nbsp;自己的产品和云服务进行的测试表明，高负载应用程序的内存和性能都提高了大约&nbsp;40%。即使没有接近容量运行的&nbsp;JDK&nbsp;8&nbsp;应用程序，也可以会看到高达&nbsp;5%&nbsp;的性能提升。</p><p>虽然&nbsp;Enterprise&nbsp;Performance&nbsp;Pack&nbsp;中包含的许多改进可以通过默认选项获得，但 Oracle 建议用户还是自己研究文档，以最大限度地提高性能并最大限度地降低内存使用率。例如，通过启用可扩展的低延迟&nbsp;ZGC&nbsp;垃圾收集器来提高应用程序响应能力，需要通过&nbsp;-XX:+UseZGC&nbsp;选项。</p>', '', 0, 0, 'OSChina', '卓大', NULL, false, 1, '2024-01-08 19:02:12', '2022-10-22 14:29:56'),
(51, 1, 'Spring Framework 6.0.0 RC2 发布', true, false, '2024-01-01 20:22:23', E'Spring Framework 6.0.0 RC2 发布\r\nSpring Framework 6.0.0 发布了第二个 RC 版本。\r\n新特性\r\n确保可以在构建时评估 classpath 检查 #29352为 JPA 持久化回调引入 Register 反射提示 #29348检查 @RegisterReflectionForBinding 是否至少指定一个类 #29346为 AOT 引擎设置引入 builder API #29341支持检测正在进行的 AOT 处理 #29340重新组织 HTTP Observation 类型 #29334支持在没有 java.beans.Introspector 的前提下，执行基本属性判断 #29320为BindingReflectionHintsRegistrar 添加 Kotlin 数据类组件支持 #29316将 HttpServiceFactory 和 RSocketServiceProxyFactory 切换到 builder 模型，以便优先进行可编程配置 #29296引入基于 GraalVM FieldValueTransformer API 的 PreComputeFieldFeature#29081在 TestContext 框架中引入 SPI 来处理 ApplicationContext 故障 #28826SimpleEvaluationContext 支持禁用 array 分配 #28808DateTimeFormatterRegistrar 支持默认回退到 ISO 解析 #26985\r\nSpring Framework 6.0 作为重大更新，要求使用 Java 17 或更高版本，并且已迁移到 Jakarta EE 9+（在 jakarta 命名空间中取代了以前基于 javax 的 EE API），以及对其他基础设施的修改。基于这些变化，Spring Framework 6.0 支持最新 Web 容器，如 Tomcat 10 / Jetty 11，以及最新的持久性框架 Hibernate ORM 6.1。这些特性仅可用于 Servlet API 和 JPA 的 jakarta 命名空间变体。\r\n值得一提的是，开发者可通过此版本在基于 Spring 的应用中体验 “虚拟线程”（JDK 19 中的预览版 “Project Loom”），查看此文章了解更多细节。现在提供了自定义选项来插入基于虚拟线程的 Executor 实现，目标是在 Project Loom 正式可用时提供 “一等公民” 的配置选项。\r\n除了上述的变化，Spring Framework 6.0 还包含许多其他改进和特性，例如：\r\n提供基于 @HttpExchange 服务接口的 HTTP 接口客户端对 RFC 7807 问题详细信息的支持Spring HTTP 客户端提供基于 Micrometer 的可观察性……\r\n详情查看 Release Note。\r\n按照发布计划，Spring Framework 6.0 将于 11 月正式 GA。', '<h1 style="text-indent: 0px; text-align: start;"><a href="https://www.oschina.net/news/214472/spring-framework-6-0-0-rc2-released" target="_blank">Spring&nbsp;Framework&nbsp;6.0.0&nbsp;RC2&nbsp;发布</a></h1><p style="text-indent: 0px; text-align: left;">Spring&nbsp;Framework&nbsp;6.0.0&nbsp;发布了<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fspring.io%2Fblog%2F2022%2F10%2F20%2Fspring-framework-6-0-0-rc2-available-now" target="_blank">第二个&nbsp;RC&nbsp;版本</a>。</p><p style="text-indent: 0px; text-align: left;"><strong>新特性</strong></p><ul style="text-indent: 0px; text-align: left;"><li>确保可以在构建时评估&nbsp;classpath&nbsp;检查&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29352" target="_blank">#29352</a></li><li>为&nbsp;JPA&nbsp;持久化回调引入&nbsp;Register&nbsp;反射提示&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29348" target="_blank">#29348</a></li><li>检查&nbsp;<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>@RegisterReflectionForBinding</code></span>&nbsp;是否至少指定一个类&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29346" target="_blank">#29346</a></li><li>为&nbsp;AOT&nbsp;引擎设置引入&nbsp;builder&nbsp;API&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29341" target="_blank">#29341</a></li><li>支持检测正在进行的&nbsp;AOT&nbsp;处理&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29340" target="_blank">#29340</a></li><li>重新组织&nbsp;HTTP&nbsp;Observation&nbsp;类型&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29334" target="_blank">#29334</a></li><li>支持在没有&nbsp;java.beans.Introspector&nbsp;的前提下，执行基本属性判断&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29320" target="_blank">#29320</a></li><li>为<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>BindingReflectionHintsRegistrar</code></span>&nbsp;添加&nbsp;Kotlin&nbsp;数据类组件支持&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29316" target="_blank">#29316</a></li><li>将&nbsp;HttpServiceFactory&nbsp;和&nbsp;RSocketServiceProxyFactory&nbsp;切换到&nbsp;builder&nbsp;模型，以便优先进行可编程配置&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29296" target="_blank">#29296</a></li><li>引入基于&nbsp;GraalVM&nbsp;<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>FieldValueTransformer</code></span>&nbsp;API&nbsp;的&nbsp;<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>PreComputeFieldFeature</code></span><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F29081" target="_blank">#29081</a></li><li>在&nbsp;TestContext&nbsp;框架中引入&nbsp;SPI&nbsp;来处理&nbsp;ApplicationContext&nbsp;故障&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F28826" target="_blank">#28826</a></li><li>SimpleEvaluationContext&nbsp;支持禁用&nbsp;array&nbsp;分配&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F28808" target="_blank">#28808</a></li><li>DateTimeFormatterRegistrar&nbsp;支持默认回退到&nbsp;ISO&nbsp;解析&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Fissues%2F26985" target="_blank">#26985</a></li></ul><p style="text-indent: 0px; text-align: left;"><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">Spring&nbsp;Framework&nbsp;6.0&nbsp;作为重大更新，要求</span><span style="color: rgb(51, 51, 51);"><strong>使用&nbsp;Java&nbsp;17&nbsp;或更高版本</strong></span><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">，并且已迁移到&nbsp;Jakarta&nbsp;EE&nbsp;9+（在&nbsp;</span><span style="color: rgb(51, 51, 51); font-size: 13px;"><code>jakarta</code></span><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">&nbsp;命名空间中取代了以前基于&nbsp;</span><span style="color: rgb(51, 51, 51); font-size: 13px;"><code>javax</code></span><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">&nbsp;的&nbsp;EE&nbsp;API），以及对其他基础设施的修改。基于这些变化，Spring&nbsp;Framework&nbsp;6.0&nbsp;支持最新&nbsp;Web&nbsp;容器，如&nbsp;</span><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Ftomcat.apache.org%2Fwhichversion.html" target="_blank">Tomcat&nbsp;10</a><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">&nbsp;/&nbsp;</span><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.eclipse.org%2Fjetty%2Fdownload.php" target="_blank">Jetty&nbsp;11</a><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">，以及最新的持久性框架&nbsp;</span><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fhibernate.org%2Form%2Freleases%2F6.1%2F" target="_blank">Hibernate&nbsp;ORM&nbsp;6.1</a><span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">。这些特性仅可用于&nbsp;Servlet&nbsp;API&nbsp;和&nbsp;JPA&nbsp;的&nbsp;jakarta&nbsp;命名空间变体。</span></p><p style="text-indent: 0px; text-align: left;">值得一提的是，开发者可通过此版本在基于&nbsp;Spring&nbsp;的应用中体验&nbsp;“虚拟线程”（JDK&nbsp;19&nbsp;中的预览版&nbsp;“Project&nbsp;Loom”），<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fspring.io%2Fblog%2F2022%2F10%2F11%2Fembracing-virtual-threads" target="_blank">查看此文章</a>了解更多细节。现在提供了自定义选项来插入基于虚拟线程的&nbsp;<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>Executor</code></span>&nbsp;实现，目标是在&nbsp;Project&nbsp;Loom&nbsp;正式可用时提供&nbsp;“一等公民”&nbsp;的配置选项。</p><p style="text-indent: 0px; text-align: left;">除了上述的变化，Spring&nbsp;Framework&nbsp;6.0&nbsp;还包含许多其他改进和特性，例如：</p><ul style="text-indent: 0px; text-align: left;"><li>提供基于&nbsp;<span style="color: rgb(51, 51, 51); font-size: 13px;"><code>@HttpExchange</code></span>&nbsp;服务接口的&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdocs.spring.io%2Fspring-framework%2Fdocs%2F6.0.0-RC1%2Freference%2Fhtml%2Fintegration.html%23rest-http-interface" target="_blank">HTTP&nbsp;接口客户端</a></li><li>对&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdocs.spring.io%2Fspring-framework%2Fdocs%2F6.0.0-RC1%2Freference%2Fhtml%2Fweb.html%23mvc-ann-rest-exceptions" target="_blank">RFC&nbsp;7807&nbsp;问题详细信息</a>的支持</li><li>Spring&nbsp;HTTP&nbsp;客户端提供基于&nbsp;Micrometer&nbsp;的可观察性</li><li>……</li></ul><p style="text-indent: 0px; text-align: left;"><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fspring-projects%2Fspring-framework%2Freleases%2Ftag%2Fv6.0.0-RC2" target="_blank">详情查看&nbsp;Release&nbsp;Note</a>。</p><p style="text-indent: 0px; text-align: left;">按照发布计划，Spring&nbsp;Framework&nbsp;6.0&nbsp;将于&nbsp;11&nbsp;月正式&nbsp;GA。</p>', '', 0, 0, 'CSDN', '罗伊', NULL, false, 1, '2024-01-08 19:02:12', '2022-10-22 14:30:45'),
(52, 1, 'Windows Terminal 正式成为 Windows 11 默认终端', true, false, '2024-01-01 20:22:23', E'今年 7 月 ，微软在 Windows 11 的 Beta 版本测试了将系统默认终端设置为 Windows Terminal 。如今该设置已登录稳定版本，从 Windows 11 22H2 版本开始，Windows Terminal 将正式成为 Windows 11 的默认设置。\r\n默认终端是在打开命令行应用程序时默认启动的终端模拟器。从 Windows 诞生之日起，其默认终端一直是 Windows 控制台主机 conhost.exe。此次更新则意味着，以后 Windows 11 的所有命令行应用程序都将在 Windows Terminal 中自动打开。\r\nWindows Terminal 拥有非常多现代化的功能，毕竟它很新（ 2019 年 5 月在 Microsoft Build 上首次发布），吸取了很多现代终端的灵感。它支持多选项卡和窗格、命令面板等现代化的 UI 和操作方式，以及大量的自定义选项，比如目录、配置文件图标、自定义背景图像、配色方案、字体和透明度。\r\n当然，如果不想用 Windows Terminal，用户也可以在 Windows 设置中的 隐私和安全 > 开发人员页面和 Windows 终端设置 中调整默认终端设置，（此更新使用 “让 Windows 决定” 作为默认选择，即默认采用 Windows Terminal） 。\r\n此外，如果在更新之前就已设置其他默认终端，此次更新不会覆盖你的偏好。\r\n关于 Windows 11 默认终端的更多详情可查看微软博客。', '<p style="text-indent: 0px; text-align: left;">今年&nbsp;7&nbsp;月&nbsp;，微软在&nbsp;Windows&nbsp;11&nbsp;的&nbsp;Beta&nbsp;版本<a href="https://www.oschina.net/news/204429/wt-default-terminal-in-win11-beta-channel" target="">测试</a>了将系统默认终端设置为&nbsp;Windows&nbsp;Terminal&nbsp;。如今该设置已登录稳定版本，从&nbsp;Windows&nbsp;11&nbsp;22H2&nbsp;版本开始，Windows&nbsp;Terminal&nbsp;将<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdevblogs.microsoft.com%2Fcommandline%2Fwindows-terminal-is-now-the-default-in-windows-11%2F" target="_blank">正式成为</a>&nbsp;Windows&nbsp;11&nbsp;的默认设置。</p><p style="text-indent: 0px; text-align: left;">默认终端是在打开命令行应用程序时默认启动的终端模拟器。从&nbsp;Windows&nbsp;诞生之日起，其默认终端一直是&nbsp;Windows&nbsp;控制台主机&nbsp;conhost.exe。此次更新则意味着，以后&nbsp;Windows&nbsp;11&nbsp;的所有命令行应用程序都将在&nbsp;Windows&nbsp;Terminal&nbsp;中自动打开。</p><p style="text-indent: 0px; text-align: left;">Windows&nbsp;Terminal&nbsp;拥有非常多现代化的功能，毕竟它<span style="color: rgb(51, 51, 51); background-color: rgb(255, 255, 255);">很新（&nbsp;2019&nbsp;年&nbsp;5&nbsp;月在&nbsp;Microsoft&nbsp;Build&nbsp;上首次发布），吸取了很多现代终端的灵感。它支持多</span>选项卡和窗格、命令面板等现代化的&nbsp;UI&nbsp;和操作方式，以及大量的自定义选项，比如目录、配置文件图标、自定义背景图像、配色方案、字体和透明度。</p><p style="text-indent: 0px; text-align: left;">当然，如果不想用&nbsp;Windows&nbsp;Terminal，用户也可以在&nbsp;Windows&nbsp;设置中的&nbsp;<em>隐私和安全&nbsp;&gt;&nbsp;开发人员页面和&nbsp;Windows&nbsp;终端设置&nbsp;</em>中调整默认终端设置，（此更新使用&nbsp;“让&nbsp;Windows&nbsp;决定”&nbsp;作为默认选择，即默认采用&nbsp;Windows&nbsp;Terminal）&nbsp;。</p><p style="text-indent: 0px; text-align: left;">此外，如果在更新之前就已设置其他默认终端，此次更新<strong>不会覆盖</strong>你的偏好。</p><p style="text-indent: 0px; text-align: left;">关于&nbsp;Windows&nbsp;11&nbsp;默认终端的更多详情可查看<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fdevblogs.microsoft.com%2Fcommandline%2Fwindows-terminal-is-now-the-default-in-windows-11%2F" target="_blank">微软博客</a>。</p>', '', 0, 0, '开源中国', '善逸', NULL, false, 1, '2024-01-08 19:02:12', '2022-10-22 14:33:03'),
(53, 1, 'TypeScript 诞生 10 周年', true, false, '2024-01-01 20:22:23', E'TypeScript 已经诞生 10 年了。10 年前 ——2012 年 10 月 1 日，TypeScript 首次公开亮相。当时主导 TypeScript 开发的 Anders Hejlsberg 这样描述 TypeScript：\r\n它是 JavaScript 的类型化超集，可被编译成常用的 JavaScript。TypeScript 还可以通过启用丰富的工具体验来极大地帮助提升生产力，与此同时开发者保持不变维护现有的代码，并继续使用喜爱的 JavaScript 库。TypeScript is a typed superset of JavaScript that compiles to idiomatic (normal) JavaScript, can dramatically improve your productivity by enabling rich tooling experiences, all while maintaining your existing code and continuing to use the same JavaScript libraries you already love.\r\n微软在博客中回顾了 TypeScript 刚亮相时受到的评价，大多数人对它都是持怀疑态度，毕竟这对于许多 JavaScript 开发者来说，试图将静态类型引入 JavaScript 是一个笑话 —— 或是邪恶的阴谋。反对者则直言这是十分愚蠢的想法，他们认为当时已存在可以编译为 JavaScript 的强类型语言，例如 C#、Java 和 C++。他们还吐槽主导 TypeScript 开发的 Anders Hejlsberg 对静态类型有 “迷之执着”。\r\n当时微软意识到 JavaScript 未来将会被应用到无数场景，而且他们公司内部团队在处理复杂的 JavaScript 代码库时面临着巨大的挑战，所以他们觉得有必要创造强大的工具来帮助编写 JavaScript—— 尤其是针对大型 JavaScript 项目。基于此需求，TypeScript 也确定了自己的定位和特性，它是 JavaScript 的超集，将类型检查和静态分析、显式接口和最佳实践结合到单一语言和编译器中。通过在 JavaScript 上构建，TypeScript 能够更接近目标运行时，同时仅添加支持大型应用程序和大型团队所需的语法糖。\r\n团队还坚持 TypeScript 要能够与现有的 JavaScript 无缝交互，与 JavaScript 共同进化，并且看上去也和 JavaScript 类似。\r\nTypeScript 诞生之初的部分设计目标：\r\n不会对已有的程序增加运行时开销与当前和未来的 ECMAScript 提案保持一致保留所有 JavaScript 代码的运行时行为避免添加表达式类型的语法 (expression-level syntax)使用一致、完全可擦除的结构化类型系统……\r\n这些目标指导着 TypeScript 的发展方向：关注类型系统，成为 JavaScript 的类型检查器，只添加类型检查所需的语法，避免添加新的运行时语法和行为。\r\n微软提到，TypeScript 拥有如今的繁荣生态离不开一个重要属性：开源。TypeScript 一开始就是免费且开源 —— 语言规范和编译器都是开源项目，并且以真正开放的方式来运作。事实上，微软当时对外展现出的姿态并不是现在的 “拥抱开源”，所以他们内部并没真正认识到 TypeScript 的开源是如何帮助它走向成功。因此有人认为，TypeScript 在很大程度上引导微软开始更多地转向开源。\r\n现在，TypeScript 仍在积极发展和迭代改进，并被全球数百万开发者使用。在诸多编程语言排名、指数或开发者调查中，TypeScript 一直位居前列，也是最受欢迎和最常用的编程语言。', '<p style="text-indent: 0px; text-align: start;">TypeScript&nbsp;已经诞生&nbsp;10&nbsp;年了。10&nbsp;年前&nbsp;——2012&nbsp;年&nbsp;10&nbsp;月&nbsp;1&nbsp;日，TypeScript&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fweb.archive.org%2Fweb%2F20121003001910%2Fhttps%3A%2F%2Fblogs.msdn.com%2Fb%2Fsomasegar%2Farchive%2F2012%2F10%2F01%2Ftypescript-javascript-development-at-application-scale.aspx" target="_blank"><strong>首次公开亮相</strong></a>。当时主导&nbsp;TypeScript&nbsp;开发的&nbsp;Anders&nbsp;Hejlsberg&nbsp;这样描述&nbsp;TypeScript：</p><blockquote style="text-indent: 0px; text-align: left;">它是&nbsp;JavaScript&nbsp;的类型化超集，可被编译成常用的&nbsp;JavaScript。TypeScript&nbsp;还可以通过启用丰富的工具体验来极大地帮助提升生产力，与此同时开发者保持不变维护现有的代码，并继续使用喜爱的&nbsp;JavaScript&nbsp;库。TypeScript&nbsp;is&nbsp;a&nbsp;typed&nbsp;superset&nbsp;of&nbsp;JavaScript&nbsp;that&nbsp;compiles&nbsp;to&nbsp;idiomatic&nbsp;(normal)&nbsp;JavaScript,&nbsp;can&nbsp;dramatically&nbsp;improve&nbsp;your&nbsp;productivity&nbsp;by&nbsp;enabling&nbsp;rich&nbsp;tooling&nbsp;experiences,&nbsp;all&nbsp;while&nbsp;maintaining&nbsp;your&nbsp;existing&nbsp;code&nbsp;and&nbsp;continuing&nbsp;to&nbsp;use&nbsp;the&nbsp;same&nbsp;JavaScript&nbsp;libraries&nbsp;you&nbsp;already&nbsp;love.</blockquote><p style="text-indent: 0px; text-align: left;">微软在博客中回顾了&nbsp;TypeScript&nbsp;刚亮相时受到的评价，大多数人对它都是持怀疑态度，毕竟这对于许多&nbsp;JavaScript&nbsp;开发者来说，试图将静态类型引入&nbsp;JavaScript&nbsp;是一个笑话&nbsp;——&nbsp;或是邪恶的阴谋。反对者则直言这是十分愚蠢的想法，他们认为当时已存在可以编译为&nbsp;JavaScript&nbsp;的强类型语言，例如&nbsp;C#、Java&nbsp;和&nbsp;C++。他们还吐槽主导&nbsp;TypeScript&nbsp;开发的&nbsp;Anders&nbsp;Hejlsberg&nbsp;对静态类型有&nbsp;“迷之执着”。</p><p style="text-indent: 0px; text-align: start;">当时微软意识到&nbsp;JavaScript&nbsp;未来将会被应用到无数场景，而且他们公司内部团队在处理复杂的&nbsp;JavaScript&nbsp;代码库时面临着巨大的挑战，所以他们觉得有必要创造强大的工具来帮助编写&nbsp;JavaScript——&nbsp;尤其是针对大型&nbsp;JavaScript&nbsp;项目。基于此需求，TypeScript&nbsp;也确定了自己的定位和特性，它是&nbsp;JavaScript&nbsp;的超集，将类型检查和静态分析、显式接口和最佳实践结合到单一语言和编译器中。通过在&nbsp;JavaScript&nbsp;上构建，TypeScript&nbsp;能够更接近目标运行时，同时仅添加支持大型应用程序和大型团队所需的语法糖。</p><p style="text-indent: 0px; text-align: start;">团队还坚持&nbsp;TypeScript&nbsp;要能够与现有的&nbsp;JavaScript&nbsp;无缝交互，与&nbsp;JavaScript&nbsp;共同进化，并且看上去也和&nbsp;JavaScript&nbsp;类似。</p><p style="text-indent: 0px; text-align: start;">TypeScript&nbsp;诞生之初的部分<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fgithub.com%2Fmicrosoft%2FTypeScript%2Fwiki%2FTypeScript-Design-Goals%2F53ffa9b1802cd8e18dfe4b2cd4e9ef5d4182df10" target="_blank"><strong>设计目标</strong></a>：</p><ul style="text-indent: 0px; text-align: left;"><li>不会对已有的程序增加运行时开销</li><li>与当前和未来的&nbsp;ECMAScript&nbsp;提案保持一致</li><li>保留所有&nbsp;JavaScript&nbsp;代码的运行时行为</li><li>避免添加表达式类型的语法&nbsp;(expression-level&nbsp;syntax)</li><li>使用一致、完全可擦除的结构化类型系统</li><li>……</li></ul><p style="text-indent: 0px; text-align: start;">这些目标指导着&nbsp;TypeScript&nbsp;的发展方向：关注类型系统，成为&nbsp;JavaScript&nbsp;的类型检查器，只添加类型检查所需的语法，避免添加新的运行时语法和行为。</p><p style="text-indent: 0px; text-align: start;">微软提到，TypeScript&nbsp;拥有如今的繁荣生态离不开一个重要属性：<strong>开源</strong>。TypeScript&nbsp;一开始就是免费且开源&nbsp;——<span style="color: rgb(51, 51, 51);">&nbsp;语言规范和编译器都是开源项目，</span>并且以真正开放的方式来运作。事实上，微软当时对外展现出的姿态并不是现在的&nbsp;“拥抱开源”，所以他们内部并没真正认识到&nbsp;TypeScript&nbsp;的开源是如何帮助它走向成功。因此有人认为，TypeScript&nbsp;在很大程度上引导微软开始更多地转向开源。</p><p style="text-indent: 0px; text-align: start;">现在，TypeScript&nbsp;仍在积极发展和迭代改进，并被全球数百万开发者使用。在诸多编程语言排名、指数或开发者调查中，TypeScript&nbsp;一直位居前列，也是最受欢迎和最常用的编程语言。</p>', '', 0, 0, '开源中国', '开云', NULL, false, 1, '2024-01-08 19:02:12', '2022-10-22 14:34:56'),
(54, 1, 'JetBrains Fleet 公测，下一代 IDE', true, false, '2024-01-01 20:22:23', E'JetBrains 宣布首次公共预览 Fleet，所有人都可以使用。Fleet 是由 JetBrains 打造的下一代 IDE，于 2021 年首次正式推出。它是一个新的分布式多语言编辑器和 IDE，基于 JetBrains 在后端的 IntelliJ 平台，采用了全新的用户界面和分布式架构从头开始构建。\r\n下载 Fleet：https://www.jetbrains.com.cn/fleet/download/\r\n\r\n公告表示，自从最初宣布 Fleet 以来，有超过 137,000 人报名参加私人预览；官方最初之所以决定从封闭式预览开始，是为了能够以渐进的方式处理反馈。现如今，JetBrains Fleet 仍处于起步阶段，还有大量的工作要做。其向公众开放预览的原因有两个方面：“首先，我们认为让所有注册者再等下去是不对的，但单独邀请这么多人对我们来说也缺乏意义。面向公众开放预览对我们来说更容易。第二，也是最重要的，我们一直是一家以开放态度打造产品的公司。我们不希望 Fleet 在这方面有任何不同。”\r\nJetBrains 方面提供了一个图表，以显示 Fleet 目前提供支持的语言和技术，以及每个技术的状态。但值得注意的是，Fleet 仍处于早期阶段，有些事情可能无法按预期工作；所以即使有些东西被列为受支持的，也有可能存在问题。\r\n同时 JetBrains 也强调称，他们并不打算取代其现有的 IDE。\r\n因此，请不要期望在 Fleet 中看到与我们的 IDE（如 IntelliJ IDEA）完全相同的功能。尽管我们会继续开发 Fleet，我们 IDE 的所有功能也不会出现在其中。Fleet 是我们为开发者提供不同用户体验的一个机会。话虽如此，我们确实希望听到你认为 Fleet 还缺少什么功能的反馈，例如特定的重构选项、工具集成等。我们现有的 IDE 将继续发展。我们对其有很多计划，包括性能改进、新的用户界面、远程开发等等。最后，Fleet 还在底层采用了我们现有工具的智慧，所以这些工具都不会消失。\r\nJetBrains 透露，在未来几个月他们将致力于稳定 Fleet，并尽可能地解决得到的反馈。同时，将在以下领域开展工作：\r\n为插件作者提供 API 支持和 SDK–鉴于 Fleet 有一个分布式架构，我们需要努力为插件作者简化工作。 虽然我们保证会为扩展 Fleet 提供一个平台，但也请求大家在这方面多一点耐心。 性能 – 我们希望 Fleet 不仅在内存占用方面，而且在响应时间方面都能表现出色。 有很多地方我们仍然可以提高性能，我们将在这些方面努力。 主题和键盘地图 – 我们知道许多开发者已经习惯了他们现有的编辑器和 IDE，当他们转移到新的 IDE 时，往往会想念他们以前的键盘绑定和主题。 我们将致力于增加对更多主题和键盘映射的支持。 我们当然也会致力于 Vim 的模拟。\r\n更多详情可查看官方博客。', '<p style="text-indent: 0px; text-align: left;">JetBrains&nbsp;<a href="https://my.oschina.net/u/5494143/blog/5584325" target="">宣布</a>首次公共预览&nbsp;<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.jetbrains.com.cn%2Ffleet%2F" target="_blank">Fleet</a>，所有人都可以使用。Fleet&nbsp;是由&nbsp;JetBrains&nbsp;打造的下一代&nbsp;IDE，于&nbsp;2021&nbsp;年首次正式<a href="https://my.oschina.net/u/5494143/blog/5332934" target="">推出</a>。它是一个新的分布式多语言编辑器和&nbsp;IDE，基于&nbsp;JetBrains&nbsp;在后端的&nbsp;IntelliJ&nbsp;平台，采用了全新的用户界面和分布式架构从头开始构建。</p><p style="text-indent: 0px; text-align: left;"><strong>下载&nbsp;Fleet：</strong><a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fwww.jetbrains.com.cn%2Ffleet%2Fdownload%2F" target="_blank">https://www.jetbrains.com.cn/fleet/download/</a></p><p style="text-indent: 0px; text-align: left;"><br></p><p style="text-indent: 0px; text-align: left;">公告表示，自从最初宣布&nbsp;Fleet&nbsp;以来，有超过&nbsp;137,000&nbsp;人报名参加私人预览；官方最初之所以决定从封闭式预览开始，是为了能够以渐进的方式处理反馈。现如今，JetBrains&nbsp;Fleet&nbsp;仍处于起步阶段，还有大量的工作要做。其向公众开放预览的原因有两个方面：“首先，我们认为让所有注册者再等下去是不对的，但单独邀请这么多人对我们来说也缺乏意义。面向公众开放预览对我们来说更容易。第二，也是最重要的，我们一直是一家以开放态度打造产品的公司。我们不希望&nbsp;Fleet&nbsp;在这方面有任何不同。”</p><p style="text-indent: 0px; text-align: left;">JetBrains&nbsp;方面提供了一个<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fjb.gg%2Ffleet-feature-matrix" target="_blank">图表</a>，以显示&nbsp;Fleet&nbsp;目前提供支持的语言和技术，以及每个技术的状态。但值得注意的是，Fleet&nbsp;仍处于早期阶段，有些事情可能无法按预期工作；所以即使有些东西被列为受支持的，也有可能存在问题。</p><p style="text-indent: 0px; text-align: left;">同时&nbsp;JetBrains&nbsp;也强调称，他们并不打算取代其现有的&nbsp;IDE。</p><blockquote style="text-indent: 0px; text-align: left;">因此，请不要期望在&nbsp;Fleet&nbsp;中看到与我们的&nbsp;IDE（如&nbsp;IntelliJ&nbsp;IDEA）完全相同的功能。尽管我们会继续开发&nbsp;Fleet，我们&nbsp;IDE&nbsp;的所有功能也不会出现在其中。Fleet&nbsp;是我们为开发者提供不同用户体验的一个机会。话虽如此，我们确实希望听到你认为&nbsp;Fleet&nbsp;还缺少什么功能的反馈，例如特定的重构选项、工具集成等。我们现有的&nbsp;IDE&nbsp;将继续发展。我们对其有很多计划，包括性能改进、新的用户界面、远程开发等等。最后，Fleet&nbsp;还在底层采用了我们现有工具的智慧，所以这些工具都不会消失。</blockquote><p style="text-indent: 0px; text-align: start;">JetBrains&nbsp;透露，在未来几个月他们将致力于稳定&nbsp;Fleet，并尽可能地解决得到的反馈。同时，将在以下领域开展工作：</p><ul style="text-indent: 0px; text-align: left;"><li><strong>为插件作者提供&nbsp;API&nbsp;支持和&nbsp;SDK</strong>–鉴于&nbsp;Fleet&nbsp;有一个<a href="https://www.oschina.net/action/GoToLink?url=https%3A%2F%2Fblog.jetbrains.com%2Fzh-hans%2Ffleet%2F2022%2F01%2Ffleet-below-deck-part-i-architecture-overview%2F" target="_blank">分布式架构</a>，我们需要努力为插件作者简化工作。&nbsp;虽然我们保证会为扩展&nbsp;Fleet&nbsp;提供一个平台，但也请求大家在这方面多一点耐心。&nbsp;</li><li><strong>性能</strong>&nbsp;–&nbsp;我们希望&nbsp;Fleet&nbsp;不仅在内存占用方面，而且在响应时间方面都能表现出色。&nbsp;有很多地方我们仍然可以提高性能，我们将在这些方面努力。&nbsp;</li><li><strong>主题和键盘地图</strong>&nbsp;–&nbsp;我们知道许多开发者已经习惯了他们现有的编辑器和&nbsp;IDE，当他们转移到新的&nbsp;IDE&nbsp;时，往往会想念他们以前的键盘绑定和主题。&nbsp;我们将致力于增加对更多主题和键盘映射的支持。&nbsp;我们当然也会致力于&nbsp;Vim&nbsp;的模拟。</li></ul><p style="text-indent: 0px; text-align: left;">更多详情可<a href="https://my.oschina.net/u/5494143/blog/5584325" target="">查看官方博客</a>。</p>', '', 0, 0, 'CSDN', '开云', NULL, false, 1, '2024-01-08 19:02:12', '2022-10-22 14:36:10'),
(55, 2, '1024创新实验室 十一放假通知', true, false, '2024-01-01 20:22:23', E'国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：10月1日至7日放假调休，共7天。\r\n衷心预祝\r\n国庆快乐，阖家幸福！', '<p style="text-indent: 0px; text-align: justify;">国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：<strong>10月1日至7日放假调休</strong>，共7天。</p><p style="text-indent: 0px; text-align: justify;"><strong>衷心预祝</strong></p><p style="text-indent: 0px; text-align: justify;"><strong>国庆快乐，阖家幸福！</strong></p>', '', 0, 0, '人力行政部', '卓大', '1024创新实验室发〔2022〕字第36号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:37:57'),
(56, 2, '十月份技术分享会议', true, false, '2024-01-01 20:22:23', E'尊敬的各位技术大佬：\r\n1024创新实验室技术分享即将隆重举行\r\n现将有关会议事宜通知如下：\r\n一、会议内容\r\n1、研究探讨SmartAdmin的技术体系\r\n二、会议形式\r\n大会专题小会分组讨论;\r\n三、会议时间及地点\r\n会议报到时间：xxx1年6月14日\r\n会议报到地点：洛阳市', '<p style="text-indent: 0px; text-align: start;">尊敬的各位技术大佬：</p><p style="text-indent: 0px; text-align: start;">1024创新实验室技术分享即将隆重举行</p><p style="text-indent: 0px; text-align: start;">现将有关会议事宜通知如下：</p><p style="text-indent: 0px; text-align: start;"><strong>一、会议内容</strong></p><p style="text-indent: 0px; text-align: start;">1、研究探讨SmartAdmin的技术体系</p><p style="text-indent: 0px; text-align: start;"><strong>二、会议形式</strong></p><p style="text-indent: 0px; text-align: start;">大会专题小会分组讨论;</p><p style="text-indent: 0px; text-align: start;"><strong>三、会议时间及地点</strong></p><p style="text-indent: 0px; text-align: start;">会议报到时间：xxx1年6月14日</p><p style="text-indent: 0px; text-align: start;">会议报到地点：洛阳市</p>', '', 0, 0, '技术部', '开云', '1024创新实验室发〔2022〕字第33号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:40:45'),
(57, 2, '关于疫情防控上班通知', true, false, '2024-01-01 20:22:23', E'近期，国内部分地区疫情频发，多地疫情出现外溢，为有效降低我市疫情输入和传播风险，洛阳市疾病预防控制中心发布疫情防控公众提示：\r\n一、所有入（返）洛阳人员均需提前3天向目的地社区（村居）、酒店宾馆、接待单位等所属网格进行报备，或通过“洛阳即时通系统”进行自主报备，配合做好健康码和行程码查验、核酸检测、隔离观察和健康监测等相关疫情防控措施。\r\n二、倡导广大群众减少跨地市出行，避免人群大范围流动引发的疫情传播扩散风险。\r\n三、对7天内有高风险区旅居史的人员，采取7天集中隔离医学观察；对7天内有中风险区旅居史的人员，采取7天居家隔离医学观察，如不具备居家隔离医学观察条件的，采取集中隔离医学观察。\r\n四、对疫情发生地出现一定范围社区传播或已实施大范围社区管控措施，基于对疫情输入风险研判结果，对近7天内来自疫情发生地所在县（市、区）的流入人员，参照中风险区旅居史人员的防控要求采取相应措施。\r\n五、对所有省外入（返）洛阳人员，须持有48小时内核酸检测阴性证明，抵达后进行“5天3检”，每次检测间隔24小时。推广“落地检”，按照“自愿免费即采即走，不限制流动”的原则，抵达我市后，立即进行1次核酸检测。\r\n六、加强重点机构场所疫情防控，坚持非必要不举办，对确需举办的培训、会展、文艺演出等大型聚集性活动，查验48小时内核酸检测阴性证明；建筑工地等人员密集型单位，查验外省（区、市）返岗人员48小时内核酸检测阴性证明；养老机构、儿童福利机构等查验探访人员48小时内核酸检测阴性证明；对进入宾馆、酒店和旅游景区等人流密集场所时，查验48小时内核酸检测阴性证明。\r\n七、近期有外出旅行史的人员，请密切关注疫情发生地区公布的病例和无症状感染者流调轨迹信息和中高风险区信息。有涉疫风险的人员要立即向社区（村）、住宿宾馆和单位报告，配合落实隔离医学观察。\r\n八、发热病人、健康码“黄码”等人员要履行个人防护责任，主动配合健康监测和核酸检测，在未排除感染风险前不出行。\r\n', '<p style="text-indent: 0px; text-align: justify;">近期，国内部分地区疫情频发，多地疫情出现外溢，为有效降低我市疫情输入和传播风险，洛阳市疾病预防控制中心发布疫情防控公众提示：</p><p style="text-indent: 0px; text-align: justify;">一、所有入（返）洛阳人员均需提前3天向目的地社区（村居）、酒店宾馆、接待单位等所属网格进行报备，或通过“洛阳即时通系统”进行自主报备，配合做好健康码和行程码查验、核酸检测、隔离观察和健康监测等相关疫情防控措施。</p><p style="text-indent: 0px; text-align: justify;">二、倡导广大群众减少跨地市出行，避免人群大范围流动引发的疫情传播扩散风险。</p><p style="text-indent: 0px; text-align: justify;">三、对7天内有高风险区旅居史的人员，采取7天集中隔离医学观察；对7天内有中风险区旅居史的人员，采取7天居家隔离医学观察，如不具备居家隔离医学观察条件的，采取集中隔离医学观察。</p><p style="text-indent: 0px; text-align: justify;">四、对疫情发生地出现一定范围社区传播或已实施大范围社区管控措施，基于对疫情输入风险研判结果，对近7天内来自疫情发生地所在县（市、区）的流入人员，参照中风险区旅居史人员的防控要求采取相应措施。</p><p style="text-indent: 0px; text-align: justify;">五、对所有省外入（返）洛阳人员，须持有48小时内核酸检测阴性证明，抵达后进行“5天3检”，每次检测间隔24小时。推广“落地检”，按照“自愿免费即采即走，不限制流动”的原则，抵达我市后，立即进行1次核酸检测。</p><p style="text-indent: 0px; text-align: justify;">六、加强重点机构场所疫情防控，坚持非必要不举办，对确需举办的培训、会展、文艺演出等大型聚集性活动，查验48小时内核酸检测阴性证明；建筑工地等人员密集型单位，查验外省（区、市）返岗人员48小时内核酸检测阴性证明；养老机构、儿童福利机构等查验探访人员48小时内核酸检测阴性证明；对进入宾馆、酒店和旅游景区等人流密集场所时，查验48小时内核酸检测阴性证明。</p><p style="text-indent: 0px; text-align: justify;">七、近期有外出旅行史的人员，请密切关注疫情发生地区公布的病例和无症状感染者流调轨迹信息和中高风险区信息。有涉疫风险的人员要立即向社区（村）、住宿宾馆和单位报告，配合落实隔离医学观察。</p><p style="text-indent: 0px; text-align: justify;">八、发热病人、健康码“黄码”等人员要履行个人防护责任，主动配合健康监测和核酸检测，在未排除感染风险前不出行。</p><p style="text-indent: 0px; text-align: justify;"><br></p>', '', 0, 0, '行政部', '卓大', '1024创新实验室发〔2022〕字第40号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:46:00'),
(58, 2, '办公室消杀关键位置通知', true, false, '2024-01-01 20:22:23', E'开展消毒消杀是杀灭病源、切断疫情传播的有效手段，是防控疫情的重要措施。为了切实将新型冠状病毒肺炎疫情防控工作落到实处，守护好辖区居民及工作人员的身体健康和生命安全，青山镇高度重视新型冠状病毒肺炎的消杀工作，将采购的防护服，防护面罩，一次性手套，口罩，84消毒液，酒精消毒液以及喷雾工具等消毒消杀物资，分发到镇级各站所各村（社区），全镇开展消杀工作。', '<p><span style="color: rgb(93, 93, 93); background-color: rgb(247, 247, 247);">开展消毒消杀是杀灭病源、切断疫情传播的有效手段，是防控疫情的重要措施。为了切实将新型冠状病毒肺炎疫情防控工作落到实处，守护好辖区居民及工作人员的身体健康和生命安全，青山镇高度重视新型冠状病毒肺炎的消杀工作，将采购的防护服，防护面罩，一次性手套，口罩，84消毒液，酒精消毒液以及喷雾工具等消毒消杀物资，分发到镇级各站所各村（社区），全镇开展消杀工作。</span></p>', '', 0, 0, '行政部', '卓大', '1024创新实验室发〔2022〕字第26号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:47:12'),
(59, 2, '十月份人事任命通知', true, false, '2024-01-01 20:22:23', E'1024创新实验室发〔2022〕字第36号\r\n1024创新实验室发〔2022〕字第36号\r\n1024创新实验室发〔2022〕字第36号\r\n1024创新实验室发〔2022〕字第36号\r\n1024创新实验室发〔2022〕字第36号\r\n1024创新实验室发〔2022〕字第36号', '<p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p><p>1024创新实验室发〔2022〕字第36号</p>', '', 0, 0, '销售部', '卓大', '1024创新实验室发〔2022〕字第30号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:50:11'),
(60, 2, '1024创新实验室 春节放假通知', true, false, '2024-01-01 20:22:23', E'春节假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：10月1日至7日放假调休，共7天。\r\n衷心预祝\r\n国庆快乐，阖家幸福！', '<p style="text-indent: 0px; text-align: justify;">国庆假期即将来临，根据国务院办公厅关于国庆节的放假安排，废纸信息网安排如下：<strong>10月1日至7日放假调休</strong>，共7天。</p><p style="text-indent: 0px; text-align: justify;"><strong>衷心预祝</strong></p><p style="text-indent: 0px; text-align: justify;"><strong>国庆快乐，阖家幸福！</strong></p>', '', 0, 0, '人力行政部', '卓大', '1024创新实验室发〔2022〕字第36号', false, 1, '2024-01-08 19:02:12', '2022-10-22 14:37:57');

-- 重置序列以匹配原始 AUTO_INCREMENT=65
SELECT setval('t_notice_notice_id_seq', 64, true); -- 设置当前值为 64，下一个值为 65

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_notice
BEFORE UPDATE ON t_notice
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_notice_type.sql
-- 表: t_notice_type 通知类型

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_notice_type;

-- 创建表结构
CREATE TABLE t_notice_type (
    notice_type_id BIGSERIAL PRIMARY KEY,
    notice_type_name varchar(1000) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_notice_type IS '通知类型';
COMMENT ON COLUMN t_notice_type.notice_type_id IS '通知类型ID';
COMMENT ON COLUMN t_notice_type.notice_type_name IS '类型名称';
COMMENT ON COLUMN t_notice_type.create_time IS '创建时间';
COMMENT ON COLUMN t_notice_type.update_time IS '更新时间';

-- 插入数据
INSERT INTO t_notice_type (notice_type_id, notice_type_name, create_time, update_time) VALUES
(1, '新闻', '2022-08-16 20:29:15', '2024-09-03 21:44:42'),
(2, '通知', '2022-08-16 20:29:20', '2022-08-16 20:29:20');

-- 重置序列以匹配原始 AUTO_INCREMENT=4
SELECT setval('t_notice_type_notice_type_id_seq', 3, true); -- 设置当前值为 3，下一个值为 4

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_notice_type
BEFORE UPDATE ON t_notice_type
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_notice_view_record.sql
-- 表: t_notice_view_record 通知查看记录

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_notice_view_record;

-- 创建表结构
CREATE TABLE t_notice_view_record (
    notice_id bigint NOT NULL,
    employee_id bigint NOT NULL,
    page_view_count integer DEFAULT 0,
    first_ip varchar(255) DEFAULT NULL,
    first_user_agent varchar(1000) DEFAULT NULL,
    last_ip varchar(255) DEFAULT NULL,
    last_user_agent varchar(1000) DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (notice_id, employee_id)
);

-- 添加注释
COMMENT ON TABLE t_notice_view_record IS '通知查看记录';
COMMENT ON COLUMN t_notice_view_record.notice_id IS '通知公告id';
COMMENT ON COLUMN t_notice_view_record.employee_id IS '员工id';
COMMENT ON COLUMN t_notice_view_record.page_view_count IS '查看次数';
COMMENT ON COLUMN t_notice_view_record.first_ip IS '首次ip';
COMMENT ON COLUMN t_notice_view_record.first_user_agent IS '首次用户设备等标识';
COMMENT ON COLUMN t_notice_view_record.last_ip IS '最后一次ip';
COMMENT ON COLUMN t_notice_view_record.last_user_agent IS '最后一次用户设备等标识';
COMMENT ON COLUMN t_notice_view_record.create_time IS '创建时间';
COMMENT ON COLUMN t_notice_view_record.update_time IS '更新时间';

-- 创建唯一索引 (PostgreSQL会自动为主键创建唯一索引，所以单独的UNIQUE KEY可以省略)
-- 如果仍需显式创建唯一约束（与主键作用相同，但可有不同名称），可以使用:
-- ALTER TABLE t_notice_view_record ADD CONSTRAINT uk_notice_employee UNIQUE (notice_id, employee_id);
-- COMMENT ON CONSTRAINT uk_notice_employee ON t_notice_view_record IS '资讯员工';

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_notice_view_record
BEFORE UPDATE ON t_notice_view_record
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 此文件没有 INSERT 数据
-- 文件: t_notice_visible_range.sql
-- 表: t_notice_visible_range 通知可见范围

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_notice_visible_range;

-- 创建表结构
CREATE TABLE t_notice_visible_range (
    notice_id bigint NOT NULL,
    data_type smallint NOT NULL, -- 1员工 2部门
    data_id bigint NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_t_notice_visible_range_notice_data UNIQUE (notice_id, data_type, data_id)
);

-- 添加注释
COMMENT ON TABLE t_notice_visible_range IS '通知可见范围';
COMMENT ON COLUMN t_notice_visible_range.notice_id IS '资讯id';
COMMENT ON COLUMN t_notice_visible_range.data_type IS '数据类型 (1:员工, 2:部门)';
COMMENT ON COLUMN t_notice_visible_range.data_id IS '员工 or 部门 id';
COMMENT ON COLUMN t_notice_visible_range.create_time IS '创建时间';
COMMENT ON CONSTRAINT uk_t_notice_visible_range_notice_data ON t_notice_visible_range IS '通知范围唯一约束';

-- 插入数据
INSERT INTO t_notice_visible_range (notice_id, data_type, data_id, create_time) VALUES
(63, 1, 63, '2024-08-09 10:40:32');

-- 注意: 此表没有 update_time 列，因此不需要创建触发器
-- 文件: t_oa_bank.sql
-- 表: t_oa_bank OA银行信息

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_oa_bank;

-- 创建表结构
CREATE TABLE t_oa_bank (
    bank_id BIGSERIAL PRIMARY KEY,
    bank_name varchar(255) NOT NULL,
    account_name varchar(255) NOT NULL,
    account_number varchar(255) NOT NULL,
    remark varchar(255) DEFAULT NULL,
    business_flag boolean NOT NULL,
    enterprise_id bigint NOT NULL,
    disabled_flag boolean NOT NULL DEFAULT false,
    deleted_flag boolean NOT NULL DEFAULT false,
    create_user_id bigint NOT NULL,
    create_user_name varchar(50) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_oa_bank IS 'OA银行信息';
COMMENT ON COLUMN t_oa_bank.bank_id IS '银行信息ID';
COMMENT ON COLUMN t_oa_bank.bank_name IS '开户银行';
COMMENT ON COLUMN t_oa_bank.account_name IS '账户名称';
COMMENT ON COLUMN t_oa_bank.account_number IS '账号';
COMMENT ON COLUMN t_oa_bank.remark IS '备注';
COMMENT ON COLUMN t_oa_bank.business_flag IS '是否对公';
COMMENT ON COLUMN t_oa_bank.enterprise_id IS '企业ID';
COMMENT ON COLUMN t_oa_bank.disabled_flag IS '禁用状态';
COMMENT ON COLUMN t_oa_bank.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_oa_bank.create_user_id IS '创建人ID';
COMMENT ON COLUMN t_oa_bank.create_user_name IS '创建人';
COMMENT ON COLUMN t_oa_bank.create_time IS '创建时间';
COMMENT ON COLUMN t_oa_bank.update_time IS '更新时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_oa_bank_enterprise_id ON t_oa_bank (enterprise_id);

-- 插入数据
INSERT INTO t_oa_bank (bank_id, bank_name, account_name, account_number, remark, business_flag, enterprise_id, disabled_flag, deleted_flag, create_user_id, create_user_name, create_time, update_time) VALUES
(26, '工商银行', '1024创新实验室', '1024', '基本户', true, 2, false, false, 1, '管理员', '2022-10-22 17:58:43', '2022-10-22 17:58:43'),
(27, '建设银行', '1024创新实验室', '10241', '其他户', false, 2, false, false, 1, '管理员', '2022-10-22 17:59:19', '2022-10-22 17:59:19');

-- 重置序列以匹配原始 AUTO_INCREMENT=29
SELECT setval('t_oa_bank_bank_id_seq', 28, true); -- 设置当前值为 28，下一个值为 29

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_oa_bank
BEFORE UPDATE ON t_oa_bank
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_oa_enterprise.sql
-- 表: t_oa_enterprise OA企业模块

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_oa_enterprise;

-- 创建表结构
CREATE TABLE t_oa_enterprise (
    enterprise_id BIGSERIAL PRIMARY KEY,
    enterprise_name varchar(255) NOT NULL,
    enterprise_logo varchar(255) DEFAULT NULL,
    type integer NOT NULL DEFAULT 1, -- 1:有限公司;2:合伙公司
    unified_social_credit_code varchar(255) NOT NULL,
    contact varchar(100) NOT NULL,
    contact_phone varchar(100) NOT NULL,
    email varchar(100) DEFAULT NULL,
    province varchar(100) DEFAULT NULL,
    province_name varchar(100) DEFAULT NULL,
    city varchar(100) DEFAULT NULL,
    city_name varchar(100) DEFAULT NULL,
    district varchar(100) DEFAULT NULL,
    district_name varchar(100) DEFAULT NULL,
    address varchar(255) DEFAULT NULL,
    business_license varchar(255) DEFAULT NULL,
    disabled_flag boolean NOT NULL DEFAULT false,
    deleted_flag boolean NOT NULL DEFAULT false,
    create_user_id bigint NOT NULL,
    create_user_name varchar(50) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_oa_enterprise IS 'OA企业模块';
COMMENT ON COLUMN t_oa_enterprise.enterprise_id IS '企业ID';
COMMENT ON COLUMN t_oa_enterprise.enterprise_name IS '企业名称';
COMMENT ON COLUMN t_oa_enterprise.enterprise_logo IS '企业logo';
COMMENT ON COLUMN t_oa_enterprise.type IS '类型（1:有限公司;2:合伙公司）';
COMMENT ON COLUMN t_oa_enterprise.unified_social_credit_code IS '统一社会信用代码';
COMMENT ON COLUMN t_oa_enterprise.contact IS '联系人';
COMMENT ON COLUMN t_oa_enterprise.contact_phone IS '联系人电话';
COMMENT ON COLUMN t_oa_enterprise.email IS '邮箱';
COMMENT ON COLUMN t_oa_enterprise.province IS '省份编码';
COMMENT ON COLUMN t_oa_enterprise.province_name IS '省份名称';
COMMENT ON COLUMN t_oa_enterprise.city IS '市编码';
COMMENT ON COLUMN t_oa_enterprise.city_name IS '城市名称';
COMMENT ON COLUMN t_oa_enterprise.district IS '区县编码';
COMMENT ON COLUMN t_oa_enterprise.district_name IS '区县名称';
COMMENT ON COLUMN t_oa_enterprise.address IS '详细地址';
COMMENT ON COLUMN t_oa_enterprise.business_license IS '营业执照';
COMMENT ON COLUMN t_oa_enterprise.disabled_flag IS '禁用状态';
COMMENT ON COLUMN t_oa_enterprise.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_oa_enterprise.create_user_id IS '创建人ID';
COMMENT ON COLUMN t_oa_enterprise.create_user_name IS '创建人';
COMMENT ON COLUMN t_oa_enterprise.create_time IS '创建时间';
COMMENT ON COLUMN t_oa_enterprise.update_time IS '更新时间';

-- 插入数据
INSERT INTO t_oa_enterprise (enterprise_id, enterprise_name, enterprise_logo, type, unified_social_credit_code, contact, contact_phone, email, province, province_name, city, city_name, district, district_name, address, business_license, disabled_flag, deleted_flag, create_user_id, create_user_name, create_time, update_time) VALUES
(1, '1024创新区块链实验室', 'public/common/34f5ac0fc097402294aea75352c128f0_20240306112435.png', 1, '1024lab_block', '开云', '***********', NULL, '410000', '河南省', '410300', '洛阳市', '410311', '洛龙区', '区块链大楼', 'public/common/1d89055e5680426280446aff1e7e627c_20240306112451.jpeg', false, false, 1, '管理员', '2021-10-22 17:03:35', '2022-10-22 17:04:18'),
(2, '1024创新实验室', '', 2, '1024lab', '卓大', '***********', '<EMAIL>', '410000', '河南省', '410300', '洛阳市', '410311', '洛龙区', '1024大楼', 'public/common/59b1ca99b7fe45d78678e6295798a699_20231201200459.jpg', false, false, 44, '卓大', '2022-10-22 14:57:36', '2022-10-22 17:03:57');

-- 重置序列以匹配原始 AUTO_INCREMENT=127
SELECT setval('t_oa_enterprise_enterprise_id_seq', 126, true); -- 设置当前值为 126，下一个值为 127

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_oa_enterprise
BEFORE UPDATE ON t_oa_enterprise
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_oa_enterprise_employee.sql
-- 表: t_oa_enterprise_employee 企业关联的员工

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_oa_enterprise_employee;

-- 创建表结构
CREATE TABLE t_oa_enterprise_employee (
    enterprise_employee_id BIGSERIAL PRIMARY KEY,
    enterprise_id varchar(100) NOT NULL, -- 根据注释和数据，这里是企业ID，但类型是varchar
    employee_id varchar(100) NOT NULL,   -- 根据注释和数据，这里是员工ID，但类型是varchar
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_oa_enterprise_employee IS '企业关联的员工';
COMMENT ON COLUMN t_oa_enterprise_employee.enterprise_employee_id IS '主键ID';
COMMENT ON COLUMN t_oa_enterprise_employee.enterprise_id IS '企业ID'; -- 原注释是'订单ID'，但表名和上下文暗示是企业ID
COMMENT ON COLUMN t_oa_enterprise_employee.employee_id IS '员工ID'; -- 原注释是'货物名称'，但表名和上下文暗示是员工ID
COMMENT ON COLUMN t_oa_enterprise_employee.update_time IS '更新时间';
COMMENT ON COLUMN t_oa_enterprise_employee.create_time IS '创建时间';

-- 创建唯一约束 (添加表名前缀)
ALTER TABLE t_oa_enterprise_employee ADD CONSTRAINT uk_t_oa_enterprise_employee_enterprise_employee UNIQUE (enterprise_id, employee_id);

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_oa_enterprise_employee_employee_id ON t_oa_enterprise_employee (employee_id);
CREATE INDEX idx_t_oa_enterprise_employee_enterprise_id ON t_oa_enterprise_employee (enterprise_id);

-- 插入数据
INSERT INTO t_oa_enterprise_employee (enterprise_employee_id, enterprise_id, employee_id, update_time, create_time) VALUES
(154, '2', '2', '2022-10-22 17:57:50', '2022-10-22 17:57:50'),
(155, '2', '44', '2022-10-22 17:57:50', '2022-10-22 17:57:50');

-- 重置序列以匹配原始 AUTO_INCREMENT=159
SELECT setval('t_oa_enterprise_employee_enterprise_employee_id_seq', 158, true); -- 设置当前值为 158，下一个值为 159

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_oa_enterprise_employee
BEFORE UPDATE ON t_oa_enterprise_employee
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_oa_invoice.sql
-- 表: t_oa_invoice OA发票信息

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_oa_invoice;

-- 创建表结构
CREATE TABLE t_oa_invoice (
    invoice_id BIGSERIAL PRIMARY KEY,
    invoice_heads varchar(255) NOT NULL,
    taxpayer_identification_number varchar(255) NOT NULL,
    account_number varchar(255) NOT NULL,
    bank_name varchar(255) NOT NULL,
    remark varchar(255) DEFAULT NULL,
    enterprise_id bigint NOT NULL,
    disabled_flag boolean NOT NULL DEFAULT false,
    deleted_flag boolean NOT NULL DEFAULT false,
    create_user_id bigint NOT NULL,
    create_user_name varchar(50) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_oa_invoice IS 'OA发票信息';
COMMENT ON COLUMN t_oa_invoice.invoice_id IS '发票信息ID';
COMMENT ON COLUMN t_oa_invoice.invoice_heads IS '开票抬头';
COMMENT ON COLUMN t_oa_invoice.taxpayer_identification_number IS '纳税人识别号';
COMMENT ON COLUMN t_oa_invoice.account_number IS '银行账户';
COMMENT ON COLUMN t_oa_invoice.bank_name IS '开户行';
COMMENT ON COLUMN t_oa_invoice.remark IS '备注';
COMMENT ON COLUMN t_oa_invoice.enterprise_id IS '企业ID';
COMMENT ON COLUMN t_oa_invoice.disabled_flag IS '禁用状态';
COMMENT ON COLUMN t_oa_invoice.deleted_flag IS '删除状态';
COMMENT ON COLUMN t_oa_invoice.create_user_id IS '创建人ID';
COMMENT ON COLUMN t_oa_invoice.create_user_name IS '创建人';
COMMENT ON COLUMN t_oa_invoice.create_time IS '创建时间';
COMMENT ON COLUMN t_oa_invoice.update_time IS '更新时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_oa_invoice_enterprise_id ON t_oa_invoice (enterprise_id);

-- 插入数据
INSERT INTO t_oa_invoice (invoice_id, invoice_heads, taxpayer_identification_number, account_number, bank_name, remark, enterprise_id, disabled_flag, deleted_flag, create_user_id, create_user_name, create_time, update_time) VALUES
(15, '1024创新实验室', '1024lab', '1024lab', '中国银行', '123', 2, false, false, 1, '管理员', '2022-10-22 17:59:35', '2023-09-27 16:26:07');

-- 重置序列以匹配原始 AUTO_INCREMENT=17
SELECT setval('t_oa_invoice_invoice_id_seq', 16, true); -- 设置当前值为 16，下一个值为 17

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_oa_invoice
BEFORE UPDATE ON t_oa_invoice
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_operate_log.sql
-- 表: t_operate_log 操作记录

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_operate_log;

-- 创建表结构
CREATE TABLE t_operate_log (
    operate_log_id BIGSERIAL PRIMARY KEY,
    operate_user_id bigint NOT NULL,
    operate_user_type integer NOT NULL,
    operate_user_name varchar(50) NOT NULL,
    module varchar(50) DEFAULT NULL,
    content varchar(500) DEFAULT NULL,
    url varchar(100) DEFAULT NULL,
    method varchar(100) DEFAULT NULL,
    param text DEFAULT NULL,
    ip varchar(255) DEFAULT NULL,
    ip_region varchar(1000) DEFAULT NULL,
    user_agent text DEFAULT NULL,
    success_flag boolean DEFAULT NULL, -- 0失败 1成功
    fail_reason text DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_operate_log IS '操作记录';
COMMENT ON COLUMN t_operate_log.operate_log_id IS '主键';
COMMENT ON COLUMN t_operate_log.operate_user_id IS '用户id';
COMMENT ON COLUMN t_operate_log.operate_user_type IS '用户类型';
COMMENT ON COLUMN t_operate_log.operate_user_name IS '用户名称';
COMMENT ON COLUMN t_operate_log.module IS '操作模块';
COMMENT ON COLUMN t_operate_log.content IS '操作内容';
COMMENT ON COLUMN t_operate_log.url IS '请求路径';
COMMENT ON COLUMN t_operate_log.method IS '请求方法';
COMMENT ON COLUMN t_operate_log.param IS '请求参数';
COMMENT ON COLUMN t_operate_log.ip IS '请求ip';
COMMENT ON COLUMN t_operate_log.ip_region IS '请求ip地区';
COMMENT ON COLUMN t_operate_log.user_agent IS '请求user-agent';
COMMENT ON COLUMN t_operate_log.success_flag IS '请求结果 (false:失败, true:成功)';
COMMENT ON COLUMN t_operate_log.fail_reason IS '失败原因';
COMMENT ON COLUMN t_operate_log.update_time IS '更新时间';
COMMENT ON COLUMN t_operate_log.create_time IS '创建时间';

-- 重置序列以匹配原始 AUTO_INCREMENT=5022
SELECT setval('t_operate_log_operate_log_id_seq', 5021, true); -- 设置当前值为 5021，下一个值为 5022

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_operate_log
BEFORE UPDATE ON t_operate_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 注意: 此文件没有 INSERT 数据 (从原始文件中省略了)
-- 文件: t_password_log.sql
-- 表: t_password_log 密码修改记录

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_password_log;

-- 创建表结构
CREATE TABLE t_password_log (
    id BIGSERIAL PRIMARY KEY,
    user_id bigint NOT NULL,
    user_type smallint NOT NULL, -- 假设 user_type 是类别值，用 smallint
    old_password varchar(255) NOT NULL,
    new_password varchar(255) DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_password_log IS '密码修改记录';
COMMENT ON COLUMN t_password_log.id IS '主键';
COMMENT ON COLUMN t_password_log.user_id IS '用户id';
COMMENT ON COLUMN t_password_log.user_type IS '用户类型';
COMMENT ON COLUMN t_password_log.old_password IS '旧密码';
COMMENT ON COLUMN t_password_log.new_password IS '新密码';
COMMENT ON COLUMN t_password_log.update_time IS '更新时间'; -- 原注释是'创建时间'，但列定义是更新时间
COMMENT ON COLUMN t_password_log.create_time IS '创建时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_password_log_user_and_type ON t_password_log (user_id, user_type);

-- 重置序列以匹配原始 AUTO_INCREMENT=12
SELECT setval('t_password_log_id_seq', 11, true); -- 设置当前值为 11，下一个值为 12

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_password_log
BEFORE UPDATE ON t_password_log
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 注意: 此文件没有 INSERT 数据
-- 文件: t_position.sql
-- 表: t_position 职务表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_position;

-- 创建表结构
CREATE TABLE t_position (
    position_id BIGSERIAL PRIMARY KEY,
    position_name varchar(200) NOT NULL,
    level varchar(200) DEFAULT NULL,
    sort integer DEFAULT 0,
    remark varchar(200) DEFAULT NULL,
    deleted_flag boolean DEFAULT false,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_position IS '职务表';
COMMENT ON COLUMN t_position.position_id IS '职务ID';
COMMENT ON COLUMN t_position.position_name IS '职务名称';
COMMENT ON COLUMN t_position.level IS '职级';
COMMENT ON COLUMN t_position.sort IS '排序';
COMMENT ON COLUMN t_position.remark IS '备注';
COMMENT ON COLUMN t_position.deleted_flag IS '删除标志';
COMMENT ON COLUMN t_position.create_time IS '创建时间';
COMMENT ON COLUMN t_position.update_time IS '更新时间';

-- 插入数据
INSERT INTO t_position (position_id, position_name, level, sort, remark, deleted_flag, create_time, update_time) VALUES
(3, '技术P7', 'L1', 3, '', false, '2024-06-29 15:57:07', '2024-07-15 23:34:35'),
(4, '技术P8', 'L2', 1, NULL, false, '2024-07-15 23:34:14', '2024-07-15 23:34:23'),
(5, '管理M5', 'L1', 4, NULL, false, '2024-07-15 23:34:48', '2024-07-15 23:34:48'),
(6, '管理M6', 'L2', 5, NULL, false, '2024-07-15 23:35:00', '2024-07-15 23:35:00');

-- 重置序列以匹配原始 AUTO_INCREMENT=7
SELECT setval('t_position_position_id_seq', 6, true); -- 设置当前值为 6，下一个值为 7

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_position
BEFORE UPDATE ON t_position
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_reload_item.sql
-- 表: t_reload_item reload项目

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_reload_item;

-- 创建表结构
CREATE TABLE t_reload_item (
    tag varchar(255) PRIMARY KEY,
    args varchar(255) DEFAULT NULL,
    identification varchar(255) NOT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL, -- 没有默认值
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_reload_item IS 'reload项目';
COMMENT ON COLUMN t_reload_item.tag IS '项名称';
COMMENT ON COLUMN t_reload_item.args IS '参数 可选';
COMMENT ON COLUMN t_reload_item.identification IS '运行标识';
COMMENT ON COLUMN t_reload_item.update_time IS '更新时间';
COMMENT ON COLUMN t_reload_item.create_time IS '创建时间';

-- 插入数据
INSERT INTO t_reload_item (tag, args, identification, update_time, create_time) VALUES
('system_config', '1', 'x', '2025-03-11 19:02:11', '2019-04-18 11:48:27');

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_reload_item
BEFORE UPDATE ON t_reload_item
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_reload_result.sql
-- 表: t_reload_result reload结果

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_reload_result;

-- 创建表结构
CREATE TABLE t_reload_result (
    tag varchar(255) NOT NULL,
    identification varchar(255) NOT NULL,
    args varchar(255) DEFAULT NULL,
    result boolean NOT NULL, -- 是否成功
    exception text DEFAULT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- 注意：此表似乎没有主键或唯一约束，但在实际应用中可能需要添加
);

-- 添加注释
COMMENT ON TABLE t_reload_result IS 'reload结果';
COMMENT ON COLUMN t_reload_result.tag IS '项名称';
COMMENT ON COLUMN t_reload_result.identification IS '运行标识';
COMMENT ON COLUMN t_reload_result.args IS '参数';
COMMENT ON COLUMN t_reload_result.result IS '是否成功 (true:成功, false:失败)';
COMMENT ON COLUMN t_reload_result.exception IS '异常信息';
COMMENT ON COLUMN t_reload_result.create_time IS '创建时间';

-- 插入数据
INSERT INTO t_reload_result (tag, identification, args, result, exception, create_time) VALUES
('system_config', 'x', '1', true, NULL, '2025-03-11 19:03:49');

-- 注意: 此表没有 update_time 列，因此不需要创建触发器
-- 文件: t_role.sql
-- 表: t_role 角色表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_role;

-- 创建表结构
CREATE TABLE t_role (
    role_id BIGSERIAL PRIMARY KEY,
    role_name varchar(20) NOT NULL,
    role_code varchar(500) DEFAULT NULL,
    remark varchar(255) DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 原注释是'创建时间'，但列定义是更新时间
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_t_role_role_code UNIQUE (role_code) -- 添加表名前缀
);

-- 添加注释
COMMENT ON TABLE t_role IS '角色表';
COMMENT ON COLUMN t_role.role_id IS '主键';
COMMENT ON COLUMN t_role.role_name IS '角色名称';
COMMENT ON COLUMN t_role.role_code IS '角色编码';
COMMENT ON COLUMN t_role.remark IS '角色描述';
COMMENT ON COLUMN t_role.update_time IS '更新时间';
COMMENT ON COLUMN t_role.create_time IS '创建时间';
COMMENT ON CONSTRAINT uk_t_role_role_code ON t_role IS '角色编码唯一约束';

-- 插入数据
INSERT INTO t_role (role_id, role_name, role_code, remark, update_time, create_time) VALUES
(1, '技术总监', NULL, '', '2022-10-19 20:24:09', '2019-06-21 12:09:34'),
(34, '销售总监', 'cto', '', '2023-09-06 19:10:34', '2019-08-30 09:30:50'),
(35, '总经理', NULL, '', '2019-08-30 09:31:05', '2019-08-30 09:31:05'),
(36, '董事长', NULL, '', '2019-08-30 09:31:11', '2019-08-30 09:31:11'),
(37, '财务', NULL, '', '2019-08-30 09:31:16', '2019-08-30 09:31:16'),
(59, '内勤', '123', NULL, '2025-03-24 10:38:02', '2025-03-24 10:38:02');

-- 重置序列以匹配原始 AUTO_INCREMENT=60
SELECT setval('t_role_role_id_seq', 59, true); -- 设置当前值为 59，下一个值为 60

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_role
BEFORE UPDATE ON t_role
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_role_data_scope.sql
-- 表: t_role_data_scope 角色的数据范围

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_role_data_scope;

-- 创建表结构
CREATE TABLE t_role_data_scope (
    id BIGSERIAL PRIMARY KEY,
    data_scope_type integer NOT NULL,
    view_type integer NOT NULL,
    role_id bigint NOT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_role_data_scope IS '角色的数据范围';
COMMENT ON COLUMN t_role_data_scope.id IS '主键ID'; -- 假设 id 是主键
COMMENT ON COLUMN t_role_data_scope.data_scope_type IS '数据范围id'; -- 对应 data_scope 表的 ID？
COMMENT ON COLUMN t_role_data_scope.view_type IS '数据范围类型'; -- 例如：1全部，2自定义，3本部门，4本部门及以下，5仅本人
COMMENT ON COLUMN t_role_data_scope.role_id IS '角色id';
COMMENT ON COLUMN t_role_data_scope.update_time IS '更新时间';
COMMENT ON COLUMN t_role_data_scope.create_time IS '创建时间';

-- 插入数据
INSERT INTO t_role_data_scope (id, data_scope_type, view_type, role_id, update_time, create_time) VALUES
(67, 1, 2, 1, '2024-03-18 20:41:00', '2024-03-18 20:41:00'),
(73, 1, 1, 59, '2025-03-24 11:43:45', '2025-03-24 11:43:45');

-- 重置序列以匹配原始 AUTO_INCREMENT=74
SELECT setval('t_role_data_scope_id_seq', 73, true); -- 设置当前值为 73，下一个值为 74

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_role_data_scope
BEFORE UPDATE ON t_role_data_scope
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_role_employee.sql
-- 表: t_role_employee 角色员工关联表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_role_employee;

-- 创建表结构
CREATE TABLE t_role_employee (
    id BIGSERIAL PRIMARY KEY,
    role_id bigint NOT NULL,
    employee_id bigint NOT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_t_role_employee_role_employee UNIQUE (role_id, employee_id) -- 添加表名前缀
);

-- 添加注释
COMMENT ON TABLE t_role_employee IS '角色员工关联表'; -- 修改注释使其更准确
COMMENT ON COLUMN t_role_employee.id IS '主键ID';
COMMENT ON COLUMN t_role_employee.role_id IS '角色id';
COMMENT ON COLUMN t_role_employee.employee_id IS '员工id';
COMMENT ON COLUMN t_role_employee.update_time IS '更新时间';
COMMENT ON COLUMN t_role_employee.create_time IS '创建时间';
COMMENT ON CONSTRAINT uk_t_role_employee_role_employee ON t_role_employee IS '角色员工唯一约束';

-- 插入数据
INSERT INTO t_role_employee (id, role_id, employee_id, update_time, create_time) VALUES
(325, 36, 63, '2022-10-19 20:25:26', '2022-10-19 20:25:26'),
(329, 34, 72, '2022-11-05 10:56:54', '2022-11-05 10:56:54'),
(330, 36, 72, '2022-11-05 10:56:54', '2022-11-05 10:56:54'),
(333, 1, 44, '2023-10-07 18:53:29', '2023-10-07 18:53:29'),
(334, 1, 47, '2023-10-07 18:55:00', '2023-10-07 18:55:00'),
(341, 1, 48, '2024-09-02 23:03:28', '2024-09-02 23:03:28'),
(343, 59, 76, '2025-03-24 10:44:36', '2025-03-24 10:44:36'),
(344, 37, 75, '2025-03-25 16:45:19', '2025-03-25 16:45:19');

-- 重置序列以匹配原始 AUTO_INCREMENT=345
SELECT setval('t_role_employee_id_seq', 344, true); -- 设置当前值为 344，下一个值为 345

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_role_employee
BEFORE UPDATE ON t_role_employee
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_role_menu.sql
-- 表: t_role_menu 角色-菜单关联表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_role_menu;

-- 创建表结构
CREATE TABLE t_role_menu (
    role_menu_id BIGSERIAL PRIMARY KEY,
    role_id bigint NOT NULL,
    menu_id bigint NOT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_role_menu IS '角色-菜单关联表'; -- 修改注释，移除换行符
COMMENT ON COLUMN t_role_menu.role_menu_id IS '主键id';
COMMENT ON COLUMN t_role_menu.role_id IS '角色id';
COMMENT ON COLUMN t_role_menu.menu_id IS '菜单id';
COMMENT ON COLUMN t_role_menu.update_time IS '更新时间';
COMMENT ON COLUMN t_role_menu.create_time IS '创建时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_role_menu_role_id ON t_role_menu (role_id);
CREATE INDEX idx_t_role_menu_menu_id ON t_role_menu (menu_id);

-- 插入数据
INSERT INTO t_role_menu VALUES (820,34,259,'2025-03-15 14:18:57','2025-03-15 14:18:57'),(821,34,260,'2025-03-15 14:18:57','2025-03-15 14:18:57'),(822,34,261,'2025-03-15 14:18:57','2025-03-15 14:18:57'),(1423,1,165,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1424,1,47,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1425,1,48,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1426,1,137,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1427,1,166,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1428,1,194,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1429,1,78,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1430,1,173,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1431,1,174,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1432,1,175,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1433,1,176,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1434,1,50,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1435,1,26,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1436,1,40,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1437,1,105,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1438,1,106,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1439,1,109,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1440,1,163,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1441,1,164,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1442,1,199,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1443,1,110,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1444,1,159,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1445,1,160,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1446,1,161,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1447,1,162,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1448,1,130,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1449,1,157,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1450,1,158,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1451,1,133,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1452,1,117,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1453,1,156,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1454,1,193,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1455,1,200,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1456,1,220,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1457,1,45,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1458,1,219,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1459,1,46,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1460,1,88,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1461,1,76,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1462,1,97,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1463,1,98,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1464,1,99,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1465,1,100,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1466,1,101,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1467,1,102,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1468,1,103,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1469,1,104,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1470,1,213,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1471,1,214,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1472,1,143,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1473,1,203,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1474,1,215,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1475,1,218,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1476,1,147,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1477,1,170,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1478,1,171,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1479,1,168,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1480,1,169,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1481,1,202,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1482,1,201,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1483,1,148,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1484,1,152,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1485,1,190,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1486,1,191,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1487,1,192,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1488,1,198,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1489,1,207,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1490,1,111,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1491,1,206,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1492,1,81,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1493,1,204,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1494,1,205,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1495,1,122,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1496,1,259,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1497,1,138,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1498,1,145,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1499,1,196,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1500,1,144,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1501,1,181,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1502,1,182,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1503,1,183,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1504,1,184,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1505,1,132,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1506,1,142,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1507,1,149,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1508,1,150,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1509,1,185,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1510,1,186,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1511,1,187,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1512,1,188,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1513,1,260,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1514,1,261,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1515,1,323,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1516,1,324,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1517,1,325,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1518,1,326,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1519,1,327,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1520,1,329,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1521,1,330,'2025-03-18 20:16:04','2025-03-18 20:16:04'),(1522,37,138,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1523,37,144,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1524,37,181,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1525,37,328,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1526,37,326,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1527,37,325,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1528,37,260,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1529,37,324,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1530,37,323,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1531,37,331,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1532,37,332,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1533,37,333,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1534,37,334,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1535,37,335,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1536,37,336,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1537,37,337,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1538,37,261,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1539,37,262,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1540,37,263,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1541,37,264,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1542,37,266,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1543,37,267,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1544,37,269,'2025-03-18 20:19:47','2025-03-18 20:19:47'),(1879,59,323,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1880,59,331,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1881,59,332,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1882,59,333,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1883,59,334,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1884,59,336,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1885,59,337,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1886,59,272,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1887,59,338,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1888,59,339,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1889,59,340,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1890,59,341,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1891,59,342,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1892,59,343,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1893,59,353,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1894,59,354,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1895,59,355,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1896,59,356,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1897,59,357,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1898,59,359,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1899,59,360,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1900,59,361,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1901,59,260,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1902,59,261,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1903,59,262,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1904,59,263,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1905,59,264,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1906,59,266,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1907,59,267,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1908,59,269,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1909,59,300,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1910,59,301,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1911,59,302,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1912,59,303,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1913,59,304,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1914,59,306,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1915,59,307,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1916,59,308,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1917,59,46,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1918,59,91,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1919,59,92,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1920,59,93,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1921,59,94,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1922,59,95,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1923,59,96,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1924,59,86,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1925,59,87,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1926,59,88,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1927,59,45,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1928,59,270,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1929,59,50,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1930,59,109,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1931,59,163,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1932,59,164,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1933,59,199,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1934,59,110,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1935,59,159,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1936,59,160,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1937,59,161,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1938,59,162,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1939,59,366,'2025-03-29 15:54:26','2025-03-29 15:54:26'),(1940,59,367,'2025-03-29 15:54:26','2025-03-29 15:54:26');

-- 重置序列以匹配原始 AUTO_INCREMENT=1941
SELECT setval('t_role_menu_role_menu_id_seq', 1940, true); -- 设置当前值为 1940，下一个值为 1941

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_role_menu
BEFORE UPDATE ON t_role_menu
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_serial_number.sql
-- 表: t_serial_number 单号生成器定义表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_serial_number;

-- 创建表结构
CREATE TABLE t_serial_number (
    serial_number_id integer PRIMARY KEY, -- MySQL 是 int NOT NULL，没有 AUTO_INCREMENT，直接用 integer 作为主键
    business_name varchar(50) NOT NULL,
    format varchar(50) DEFAULT NULL,
    rule_type varchar(20) NOT NULL,
    init_number integer NOT NULL, -- unsigned 在 PG 中通常就是 integer
    step_random_range integer NOT NULL, -- unsigned 在 PG 中通常就是 integer
    remark varchar(255) DEFAULT NULL,
    last_number bigint DEFAULT NULL,
    last_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_t_serial_number_business_name UNIQUE (business_name) -- 添加表名前缀
);

-- 添加注释
COMMENT ON TABLE t_serial_number IS '单号生成器定义表';
COMMENT ON COLUMN t_serial_number.serial_number_id IS '单号生成器ID'; -- 修改注释
COMMENT ON COLUMN t_serial_number.business_name IS '业务名称';
COMMENT ON COLUMN t_serial_number.format IS '格式 ([yyyy],[mm],[dd],[nnnnn]等)';
COMMENT ON COLUMN t_serial_number.rule_type IS '规则格式 (none, year, month, day)';
COMMENT ON COLUMN t_serial_number.init_number IS '初始值';
COMMENT ON COLUMN t_serial_number.step_random_range IS '步长随机范围';
COMMENT ON COLUMN t_serial_number.remark IS '备注';
COMMENT ON COLUMN t_serial_number.last_number IS '上次产生的单号';
COMMENT ON COLUMN t_serial_number.last_time IS '上次产生的单号时间';
COMMENT ON COLUMN t_serial_number.update_time IS '更新时间';
COMMENT ON COLUMN t_serial_number.create_time IS '创建时间';
COMMENT ON CONSTRAINT uk_t_serial_number_business_name ON t_serial_number IS '业务名称唯一约束';

-- 插入数据
INSERT INTO t_serial_number (serial_number_id, business_name, format, rule_type, init_number, step_random_range, remark, last_number, last_time, update_time, create_time) VALUES
(1, '订单编号', 'DK[yyyy][mm][dd]NO[nnnnn]', 'day', 1000, 10, 'DK20201101NO321', 1, '2023-12-04 09:16:42', '2024-01-08 19:24:46', '2021-02-19 14:37:50'),
(2, '合同编号', 'HT[yyyy][mm][dd][nnnnn]-CX', 'none', 1, 1, '', 8, '2023-12-04 09:54:53', '2023-12-04 09:54:52', '2021-08-12 20:40:37');

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_serial_number
BEFORE UPDATE ON t_serial_number
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_serial_number_record.sql
-- 表: t_serial_number_record serial_number记录表

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_serial_number_record;

-- 创建表结构
CREATE TABLE t_serial_number_record (
    serial_number_id integer NOT NULL,
    record_date date NOT NULL,
    last_number bigint NOT NULL DEFAULT 0,
    last_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    count bigint NOT NULL DEFAULT 0,
    update_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    create_time TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
    -- 注意：原始表没有主键，但有一个 KEY(serial_number_id, record_date)
    -- 在 PostgreSQL 中，可以考虑将其设为复合主键或添加唯一约束
    -- PRIMARY KEY (serial_number_id, record_date)
);

-- 添加注释
COMMENT ON TABLE t_serial_number_record IS 'serial_number记录表';
COMMENT ON COLUMN t_serial_number_record.serial_number_id IS '单号生成器ID';
COMMENT ON COLUMN t_serial_number_record.record_date IS '记录日期';
COMMENT ON COLUMN t_serial_number_record.last_number IS '最后更新值';
COMMENT ON COLUMN t_serial_number_record.last_time IS '最后更新时间';
COMMENT ON COLUMN t_serial_number_record.count IS '更新次数';
COMMENT ON COLUMN t_serial_number_record.update_time IS '更新时间';
COMMENT ON COLUMN t_serial_number_record.create_time IS '创建时间';

-- 创建索引 (模仿 MySQL KEY 的行为，添加表名前缀)
CREATE INDEX idx_t_serial_number_record_generator ON t_serial_number_record (serial_number_id, record_date);
-- 如果需要唯一性，可以创建唯一索引或约束：
-- ALTER TABLE t_serial_number_record ADD CONSTRAINT uk_t_serial_number_record_generator UNIQUE (serial_number_id, record_date);

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_serial_number_record
BEFORE UPDATE ON t_serial_number_record
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 注意: 此文件没有 INSERT 数据
-- 文件: t_smart_job.sql
-- 表: t_smart_job 定时任务配置

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_smart_job;

-- 创建表结构
CREATE TABLE t_smart_job (
    job_id SERIAL PRIMARY KEY, -- int AUTO_INCREMENT -> SERIAL
    job_name varchar(50) NOT NULL,
    job_class varchar(200) NOT NULL,
    trigger_type varchar(30) NOT NULL,
    trigger_value varchar(200) NOT NULL,
    enabled_flag boolean NOT NULL DEFAULT false,
    param varchar(1000) DEFAULT NULL,
    last_execute_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
    last_execute_log_id integer DEFAULT NULL, -- int -> integer
    sort integer NOT NULL DEFAULT 0, -- int -> integer
    remark varchar(255) DEFAULT NULL,
    deleted_flag boolean NOT NULL DEFAULT false,
    update_name varchar(50) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加注释
COMMENT ON TABLE t_smart_job IS '定时任务配置 @listen';
COMMENT ON COLUMN t_smart_job.job_id IS '任务id';
COMMENT ON COLUMN t_smart_job.job_name IS '任务名称';
COMMENT ON COLUMN t_smart_job.job_class IS '任务执行类';
COMMENT ON COLUMN t_smart_job.trigger_type IS '触发类型 (e.g., cron, fixed_delay)';
COMMENT ON COLUMN t_smart_job.trigger_value IS '触发配置 (e.g., cron表达式, 延迟毫秒数)';
COMMENT ON COLUMN t_smart_job.enabled_flag IS '是否开启 (true:是, false:否)';
COMMENT ON COLUMN t_smart_job.param IS '参数';
COMMENT ON COLUMN t_smart_job.last_execute_time IS '最后一次执行时间';
COMMENT ON COLUMN t_smart_job.last_execute_log_id IS '最后一次执行记录id';
COMMENT ON COLUMN t_smart_job.sort IS '排序';
COMMENT ON COLUMN t_smart_job.remark IS '描述';
COMMENT ON COLUMN t_smart_job.deleted_flag IS '删除状态 (true:是, false:否)';
COMMENT ON COLUMN t_smart_job.update_name IS '更新人';
COMMENT ON COLUMN t_smart_job.create_time IS '创建时间';
COMMENT ON COLUMN t_smart_job.update_time IS '更新时间';

-- 插入数据
INSERT INTO t_smart_job (job_id, job_name, job_class, trigger_type, trigger_value, enabled_flag, param, last_execute_time, last_execute_log_id, sort, remark, deleted_flag, update_name, create_time, update_time) VALUES
(1, '示例任务1', 'net.lab1024.sa.system.support.job.sample.SmartJobSample1', 'cron', '10 15 0/1 * * *', false, '1', '2025-03-12 20:15:10', 8191, 1, '测试测试', false, '管理员', '2024-06-17 20:00:46', '2025-03-12 21:21:10'),
(2, '示例任务2', 'net.lab1024.sa.system.support.job.sample.SmartJobSample2', 'fixed_delay', '120', false, '一路春光啊一路荆棘呀惊鸿一般短暂如夏花一样绚烂这是一个不能停留太久的世界，一路春光啊一路荆棘呀惊鸿一般短暂如夏花一样绚烂这是一个不能停留太久的世界啊', '2025-03-12 21:21:04', 8214, 2, '一个不能停留太久的世界啊', false, '管理员', '2024-06-18 20:45:35', '2025-03-12 21:21:10');

-- 重置序列以匹配原始 AUTO_INCREMENT=4
SELECT setval('t_smart_job_job_id_seq', 3, true); -- 设置当前值为 3，下一个值为 4

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_smart_job
BEFORE UPDATE ON t_smart_job
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();
-- 文件: t_smart_job_log.sql
-- 表: t_smart_job_log 定时任务-执行记录

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_smart_job_log;

-- 创建表结构
CREATE TABLE t_smart_job_log (
    log_id SERIAL PRIMARY KEY, -- int AUTO_INCREMENT -> SERIAL
    job_id integer NOT NULL, -- int -> integer
    job_name varchar(100) NOT NULL,
    param varchar(2000) DEFAULT NULL,
    success_flag boolean NOT NULL, -- tinyint(1) -> boolean
    execute_start_time TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    execute_time_millis integer DEFAULT NULL, -- int -> integer
    execute_end_time TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL,
    execute_result varchar(2000) DEFAULT NULL,
    ip varchar(50) NOT NULL,
    process_id varchar(50) NOT NULL,
    program_path varchar(255) NOT NULL,
    create_name varchar(100) NOT NULL,
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
    -- 注意: 此表没有 update_time 列
);

-- 添加注释
COMMENT ON TABLE t_smart_job_log IS '定时任务-执行记录 @listen';
COMMENT ON COLUMN t_smart_job_log.log_id IS '日志ID';
COMMENT ON COLUMN t_smart_job_log.job_id IS '任务id';
COMMENT ON COLUMN t_smart_job_log.job_name IS '任务名称';
COMMENT ON COLUMN t_smart_job_log.param IS '执行参数';
COMMENT ON COLUMN t_smart_job_log.success_flag IS '是否成功 (true:是, false:否)';
COMMENT ON COLUMN t_smart_job_log.execute_start_time IS '执行开始时间';
COMMENT ON COLUMN t_smart_job_log.execute_time_millis IS '执行时长 (毫秒)';
COMMENT ON COLUMN t_smart_job_log.execute_end_time IS '执行结束时间';
COMMENT ON COLUMN t_smart_job_log.execute_result IS '执行结果';
COMMENT ON COLUMN t_smart_job_log.ip IS '执行任务的服务器ip';
COMMENT ON COLUMN t_smart_job_log.process_id IS '执行任务的进程id';
COMMENT ON COLUMN t_smart_job_log.program_path IS '执行任务的程序目录';
COMMENT ON COLUMN t_smart_job_log.create_name IS '创建人 (通常是 "system")';
COMMENT ON COLUMN t_smart_job_log.create_time IS '创建时间';

-- 创建索引 (添加表名前缀)
CREATE INDEX idx_t_smart_job_log_job_id ON t_smart_job_log (job_id);

-- 插入数据
-- 注意: success_flag 的值 1/0 已转换为 true/false
-- 注意: program_path 中的 \\ 已替换为 / (PostgreSQL 通常使用 / 作为路径分隔符)


-- 重置序列以匹配原始 AUTO_INCREMENT=8215
SELECT setval('t_smart_job_log_log_id_seq', 8214, true); -- 设置当前值为 8214，下一个值为 8215

-- 注意: 此表没有 update_time 列，因此不需要创建触发器
-- 文件: t_table_column.sql
-- 表: t_table_column 表格的自定义列存储

-- 如果表已存在，则删除
DROP TABLE IF EXISTS t_table_column;

-- 创建表结构
CREATE TABLE t_table_column (
    table_column_id BIGSERIAL PRIMARY KEY,
    user_id bigint NOT NULL,
    user_type integer NOT NULL,
    table_id integer NOT NULL,
    columns jsonb DEFAULT NULL, -- text 存储 JSON -> jsonb
    create_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_t_table_column_employee_table UNIQUE (user_id, table_id) -- 添加表名前缀
);

-- 添加注释
COMMENT ON TABLE t_table_column IS '表格的自定义列存储';
COMMENT ON COLUMN t_table_column.table_column_id IS '主键ID';
COMMENT ON COLUMN t_table_column.user_id IS '用户id';
COMMENT ON COLUMN t_table_column.user_type IS '用户类型';
COMMENT ON COLUMN t_table_column.table_id IS '表格id';
COMMENT ON COLUMN t_table_column.columns IS '具体的表格列 (JSONB 格式)'; -- 明确为 JSONB
COMMENT ON COLUMN t_table_column.create_time IS '创建时间';
COMMENT ON COLUMN t_table_column.update_time IS '更新时间';
COMMENT ON CONSTRAINT uk_t_table_column_employee_table ON t_table_column IS '用户和表格唯一约束';

-- 重置序列以匹配原始 AUTO_INCREMENT=6
SELECT setval('t_table_column_table_column_id_seq', 5, true); -- 设置当前值为 5，下一个值为 6

-- 创建触发器，用于在更新时自动设置 update_time
CREATE TRIGGER set_timestamp_t_table_column
BEFORE UPDATE ON t_table_column
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- 注意: 此文件没有 INSERT 数据
commit ;