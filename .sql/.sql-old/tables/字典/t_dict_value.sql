create table txy.t_dict_value
(
    dict_value_id bigint auto_increment
        primary key,
    dict_key_id   bigint                             not null,
    value_code    varchar(50)                        not null comment '编码',
    value_name    varchar(50)                        not null comment '名称',
    remark        varchar(500)                       null comment '备注',
    sort          int      default 0                 not null comment '排序',
    deleted_flag  tinyint  default 0                 not null comment '删除状态',
    update_time   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    create_time   datetime default CURRENT_TIMESTAMP not null,
    constraint t_dict_value_dict_key_id_value_code_uindex
        unique (dict_key_id, value_code)
)
    comment '字典的值' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;

alter table t_dict_value
    add value_color varchar(6) null comment '字典值颜色' after value_name;

