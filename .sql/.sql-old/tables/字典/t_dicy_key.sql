create table txy.t_dict_key
(
    dict_key_id  bigint auto_increment
        primary key,
    key_code     varchar(50)                        not null comment '编码',
    key_name     varchar(50)                        not null comment '名称',
    remark       varchar(500)                       null comment '备注',
    deleted_flag tinyint  default 0                 not null comment '删除状态',
    update_time  datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    create_time  datetime default CURRENT_TIMESTAMP not null,
    constraint t_dict_key_pk
        unique (key_code)
)
    comment '字典key' collate = utf8mb4_general_ci
                      row_format = DYNAMIC;


