-- 删除重复的纸箱报价管理菜单数据
DELETE FROM t_menu WHERE menu_name = '纸箱报价管理' AND deleted_flag = 0;
DELETE FROM t_menu WHERE api_perms LIKE 'boxQuotation:%' AND deleted_flag = 0;

-- 纸箱报价管理菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, perms_type, icon, visible_flag, disabled_flag, deleted_flag, create_user_id)
VALUES ('纸箱报价管理', 2, 0, '/box-quotation/list', '/business/erp/box-quotation/BoxQuotationList.vue', 1, 'icon-file', 1, 0, 0, 1);

-- 获取纸箱报价管理菜单ID
SET @box_quotation_menu_id = LAST_INSERT_ID();

-- 批量插入纸箱报价管理权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, perms_type, api_perms, context_menu_id, visible_flag, disabled_flag, deleted_flag, create_user_id)
VALUES 
('查询', 3, @box_quotation_menu_id, 1, 'boxQuotation:query', 260, 1, 0, 0, 1),
('查看', 3, @box_quotation_menu_id, 1, 'boxQuotation:view', 260, 1, 0, 0, 1),
('删除', 3, @box_quotation_menu_id, 1, 'boxQuotation:delete', 260, 1, 0, 0, 1),
('导出', 3, @box_quotation_menu_id, 1, 'boxQuotation:export', 260, 1, 0, 0, 1);
