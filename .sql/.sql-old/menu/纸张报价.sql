-- 删除重复的纸张报价菜单数据
DELETE FROM t_menu WHERE menu_name = '纸张报价' AND deleted_flag = 0;
DELETE FROM t_menu WHERE api_perms LIKE 'paperQuotation:%' AND deleted_flag = 0;

-- 纸张报价菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, path, component, perms_type, visible_flag, disabled_flag, deleted_flag, create_user_id)
VALUES ('纸张报价', 2, 0, '/paper-quotation/list', '/business/erp/paper-quotation/PaperQuotationList.vue', 1, 1, 0, 0, 1);

-- 获取纸张报价菜单ID
SET @paper_quotation_menu_id = LAST_INSERT_ID();

-- 批量插入纸张报价权限菜单
INSERT INTO t_menu (menu_name, menu_type, parent_id, perms_type, api_perms, context_menu_id, visible_flag, disabled_flag, deleted_flag, create_user_id)
VALUES 
('查询', 3, @paper_quotation_menu_id, 1, 'paperQuotation:query', 260, 1, 0, 0, 1),
('新增', 3, @paper_quotation_menu_id, 1, 'paperQuotation:add', 260, 1, 0, 0, 1),
('编辑', 3, @paper_quotation_menu_id, 1, 'paperQuotation:update', 260, 1, 0, 0, 1),
('删除', 3, @paper_quotation_menu_id, 1, 'paperQuotation:delete', 260, 1, 0, 0, 1),
('导入', 3, @paper_quotation_menu_id, 1, 'paperQuotation:import', 260, 1, 0, 0, 1),
('导出', 3, @paper_quotation_menu_id, 1, 'paperQuotation:export', 260, 1, 0, 0, 1),
('审批', 3, @paper_quotation_menu_id, 1, 'paperQuotation:approve', 260, 1, 0, 0, 1);
