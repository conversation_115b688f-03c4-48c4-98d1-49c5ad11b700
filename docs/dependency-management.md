# TXY-ERP 依赖管理与冲突解决指南 🔧

## 概述

TXY-ERP项目使用了先进的Gradle依赖冲突自动解决插件，能够智能处理常见的依赖冲突问题，确保项目构建的稳定性和一致性。

> **📝 配置简化说明**: 配置文件已从300+行简化到130行，保留最核心和最有效的功能，提升维护性和可读性。

## 使用的插件

### JVM Dependency Conflict Resolution Plugin
- **插件ID**: `org.gradlex.jvm-dependency-conflict-resolution`
- **版本**: 2.4
- **功能**: 自动检测和解决JVM生态系统中的依赖冲突
- **官方文档**: https://gradlex.org/jvm-dependency-conflict-resolution/
- **GitHub**: https://github.com/gradlex-org/jvm-dependency-conflict-resolution

## 配置文件结构

```
txy-erp/
├── build.gradle                           # 主构建文件，应用插件
├── gradle/
│   └── dependency-conflicts.gradle        # 详细的冲突解决配置
└── docs/
    └── dependency-management.md           # 本文档
```

## 主要功能

### 🔍 自动冲突检测
插件会自动检测以下类型的冲突：
- **版本冲突**: 同一个库的不同版本
- **能力冲突**: 提供相同功能的不同库
- **日志框架冲突**: SLF4J、Log4j、Logback等
- **API兼容性冲突**: javax vs jakarta等

### ⚙️ 智能冲突解决
- **自动选择最高版本**: 对于兼容的版本冲突
- **强制特定版本**: 对于有安全漏洞的版本
- **依赖替换**: 将旧版本替换为新版本
- **元数据修补**: 修复发布库的错误元数据

### 📝 日志框架统一
项目统一使用 **Log4j2** 作为日志实现：
- 自动排除 `spring-boot-starter-logging`
- 自动添加 `spring-boot-starter-log4j2`
- 处理 SLF4J、JCL、JUL 等日志桥接

## 常用命令

### 🔍 全面依赖冲突检测
```bash
# 🎯 一次性检测所有依赖冲突（推荐）
./gradlew --no-configuration-cache detectAllConflicts

# ⚡ 快速检测关键依赖版本统一情况
./gradlew --no-configuration-cache quickConflictCheck

# 🛡️ 检测安全漏洞依赖
./gradlew --no-configuration-cache securityVulnerabilityCheck
```

### 📊 依赖分析
```bash
# 显示依赖树
./gradlew showDependencyTree

# 生成依赖报告
./gradlew generateDependencyReport

# 查看依赖解析结果
./gradlew dependencies
```

### 查看特定模块依赖
```bash
# 查看ax-server模块的依赖
./gradlew :ax-server:dependencies

# 查看运行时依赖
./gradlew :ax-server:dependencies --configuration runtimeClasspath

# 查看依赖洞察
./gradlew :ax-server:dependencyInsight --dependency jackson-core
```

## 🔍 依赖冲突检测工具详解

### 1. detectAllConflicts - 全面冲突检测
**功能**: 一次性检测项目中所有模块的依赖冲突
- ✅ 检测版本冲突（同一依赖的不同版本）
- ✅ 检测能力冲突（提供相同功能的不同库）
- ✅ 生成详细报告文件
- ✅ 统计冲突影响范围

**输出示例**:
```
🔍 开始全面检测依赖冲突...
📦 检测模块: ax-server
  ✅ 无冲突
📊 冲突检测汇总报告
检测模块数: 5
发现冲突数: 0
🎉 恭喜！未发现依赖冲突！
```

### 2. quickConflictCheck - 快速关键依赖检测
**功能**: 快速检测关键依赖的版本统一情况
- 🎯 专注检测最重要的依赖（Guava、Jackson、Spring等）
- ⚡ 快速执行，适合日常检查
- 📋 清晰显示版本统一状态

**输出示例**:
```
🔍 检测: com.google.guava:guava
  ✅ 版本统一: 32.1.3-jre
🔍 检测: commons-codec:commons-codec
  ✅ 版本统一: 1.17.2
```

### 3. securityVulnerabilityCheck - 安全漏洞检测
**功能**: 检测项目中是否存在有安全漏洞的依赖版本
- 🛡️ 检测已知安全漏洞版本
- 🚨 包含Jackson、Log4j、Spring等常见漏洞
- 📍 精确定位漏洞依赖和影响模块

**检测的漏洞包括**:
- Jackson 2.9.x/2.10.x 反序列化漏洞
- Log4j 2.16.x CVE-2021-44228 漏洞
- Spring Framework 早期版本漏洞
- Commons Collections 3.x 反序列化漏洞

## 配置说明

### 日志框架配置
```groovy
logging {
    // 强制使用Log4j2
    enforceLog4J2()
    
    // 其他选项：
    // enforceLogback()     // 使用Logback
    // enforceSlf4JSimple() // 使用SLF4J Simple
}
```

### 冲突解决策略
```groovy
conflictResolution {
    // 选择最高版本
    selectHighestVersion("com.fasterxml.jackson.core:jackson-core")
    
    // 选择特定模块
    select("org.gradlex:bouncycastle-bcprov", "org.bouncycastle:bcprov-jdk18on")
    
    // 宽松选择（如果模块不存在则选择第一个找到的）
    selectLenient("some.capability", "preferred.module")
}
```

### 依赖修补
```groovy
patch {
    module("org.springframework.boot:spring-boot-starter") {
        // 移除依赖
        removeDependency("org.springframework.boot:spring-boot-starter-logging")
        
        // 添加运行时依赖
        addRuntimeOnlyDependency("org.springframework.boot:spring-boot-starter-log4j2")
        
        // 降级依赖范围
        reduceToCompileOnlyApiDependency("some.dependency")
    }
    
    // 版本对齐
    align("group:artifact1", "group:artifact2", "group:artifact3")
}
```

## 常见问题解决

### 1. 日志冲突
**问题**: 出现多个日志实现的警告
```
SLF4J: Class path contains multiple SLF4J bindings.
```

**解决**: 插件会自动处理，确保只使用Log4j2实现。

### 2. Jackson版本冲突
**问题**: Jackson组件版本不一致导致序列化问题

**解决**: 插件自动对齐所有Jackson组件到同一版本。

### 3. BouncyCastle JDK版本冲突
**问题**: 同时存在JDK15和JDK18版本的BouncyCastle

**解决**: 插件强制使用JDK18版本，确保与Java 17+兼容。

### 4. Spring Boot日志配置
**问题**: Spring Boot默认使用Logback，但项目需要Log4j2

**解决**: 插件自动排除Logback，添加Log4j2依赖。

### 5. Jakarta Servlet API能力冲突
**问题**: Spring Boot应用中出现Servlet API能力冲突

**解决**: 插件选择Tomcat嵌入式版本，确保与Spring Boot兼容。

### 6. Validation API新旧版本冲突
**问题**: 项目中同时存在新的Jakarta和旧的javax版本

**解决**: 插件自动选择Jakarta版本，移除旧的javax.validation依赖。

## 自定义配置

### 添加新的冲突解决规则
编辑 `gradle/dependency-conflicts.gradle` 文件：

```groovy
conflictResolution {
    // 添加你的自定义规则
    selectHighestVersion("your.group:your-artifact")
}
```

### 修补特定依赖
```groovy
patch {
    module("problematic.group:problematic-artifact") {
        removeDependency("unwanted.dependency")
        addApiDependency("better.dependency")
    }
}
```

### 排除特定版本
```groovy
configurations.configureEach {
    resolutionStrategy.componentSelection {
        withModule('group:artifact') { selection ->
            if (selection.candidate.version == 'bad.version') {
                selection.reject("存在已知问题")
            }
        }
    }
}
```

## 最佳实践

### 1. 定期检查
```bash
# 每次添加新依赖后运行
./gradlew checkDependencyConflicts
```

### 2. 版本升级
- 优先通过 `ax-dependencies` 平台管理版本
- 避免在子模块中硬编码版本号
- 使用 `selectHighestVersion` 而不是 `force`

### 3. 安全考虑
- 插件会自动拒绝有已知安全漏洞的版本
- 定期更新依赖版本
- 关注安全公告

### 4. 性能优化
- 启用依赖缓存（已配置）
- 使用并行构建（已配置）
- 避免使用快照版本

## 故障排除

### 构建失败
1. 清理构建缓存：`./gradlew clean`
2. 刷新依赖：`./gradlew --refresh-dependencies`
3. 检查冲突：`./gradlew checkDependencyConflicts`

### 依赖解析问题
1. 查看详细日志：`./gradlew build --info`
2. 分析依赖树：`./gradlew showDependencyTree`
3. 检查特定依赖：`./gradlew dependencyInsight --dependency <name>`

### Jakarta Servlet API冲突 🔥
**问题症状**:
```
Cannot select module with conflict on capability 'org.gradlex:jakarta-servlet-api:6.0.0' 
also provided by [org.apache.tomcat.embed:tomcat-embed-core:10.1.40(runtime)]
```

**原因**: `jakarta.servlet:jakarta.servlet-api` 与 `org.apache.tomcat.embed:tomcat-embed-core` 都提供相同的Servlet API能力

**解决方案**: 在 `gradle/dependency-conflicts.gradle` 中配置：
```groovy
conflictResolution {
    // 选择Tomcat嵌入式版本（Spring Boot推荐）
    select("org.gradlex:jakarta-servlet-api", "org.apache.tomcat.embed:tomcat-embed-core")
}

patch {
    // 移除SA-Token中的独立servlet-api依赖
    module("cn.dev33:sa-token-jakarta-servlet") {
        removeDependency("jakarta.servlet:jakarta.servlet-api")
    }
}
```

### 配置缓存序列化错误
**问题**: `Configuration cache state could not be cached`

**解决方案**:
1. 跳过有问题的任务：`./gradlew build -x spotlessJava`
2. 禁用配置缓存：`./gradlew build --no-configuration-cache`
3. 或在 `gradle.properties` 中设置：`org.gradle.configuration-cache=false`

### IDEA依赖冲突显示问题 💡
**问题**: IDEA仍然显示依赖冲突，但Gradle检测无冲突

**解决方案**:
```bash
# 1. 运行全面冲突检测确认状态
./gradlew --no-configuration-cache detectAllConflicts

# 2. 刷新Gradle项目
# 在IDEA中: Gradle面板 -> 刷新按钮

# 3. 重新导入项目
# File -> Reload Gradle Project

# 4. 清理IDEA缓存
# File -> Invalidate Caches and Restart
```

### 插件问题
1. 确认插件版本兼容性
2. 查看插件文档获取最新配置
3. 在GitHub上报告问题

## 参考资源

- [Gradle依赖管理官方文档](https://docs.gradle.org/current/userguide/dependency_management.html)
- [JVM Dependency Conflict Resolution插件文档](https://gradlex.org/jvm-dependency-conflict-resolution/)
- [Maven Sympathy插件](https://github.com/GradleUp/maven-sympathy)
- [Gradle最佳实践](https://docs.gradle.org/current/userguide/best_practices.html)

---

💡 **提示**: 如果遇到问题，可以先尝试运行 `./gradlew analyzeDependencyVersions` 来分析具体的冲突情况。 