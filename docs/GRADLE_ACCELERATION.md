# Gradle 加速配置说明 🚀

## 已配置的加速优化

### 1. Gradle Wrapper 镜像加速
- **文件**: `gradle/wrapper/gradle-wrapper.properties`
- **优化**: 使用腾讯云镜像下载 Gradle 发行版
- **效果**: Gradle 本体下载速度提升 5-10 倍

```properties
# 腾讯云镜像（推荐）
distributionUrl=https\://mirrors.cloud.tencent.com/gradle/gradle-8.14.2-all.zip

# 备用镜像
# distributionUrl=https\://mirrors.huaweicloud.com/gradle/gradle-8.14.2-all.zip
```

### 2. Maven 仓库镜像加速
- **文件**: `settings.gradle`
- **优化**: 配置阿里云、腾讯云、华为云镜像
- **效果**: 依赖下载速度提升 3-5 倍

### 3. 性能优化配置
- **文件**: `gradle.properties`
- **优化项**:
  - 并行构建: `org.gradle.parallel=true`
  - 守护进程: `org.gradle.daemon=true`
  - 构建缓存: `org.gradle.caching=true`
  - 并行下载: `systemProp.org.gradle.internal.repository.max.concurrent.downloads=8`
  - JVM 优化: G1GC + 4GB 堆内存

### 4. 全局初始化脚本
- **文件**: `gradle/init.gradle`
- **功能**: 自动替换官方仓库为国内镜像
- **效果**: 确保所有项目都使用加速镜像

## 使用方法

### 首次使用
```bash
# 清理现有缓存（可选）
./gradlew clean --refresh-dependencies

# 重新下载依赖
./gradlew build
```

### 验证加速效果
```bash
# 查看使用的仓库
./gradlew dependencies --configuration compileClasspath

# 构建时间对比
./gradlew clean build --profile
```

## 镜像源优先级

1. **阿里云** (最快，推荐)
   - Central: `https://maven.aliyun.com/repository/central`
   - Public: `https://maven.aliyun.com/repository/public`
   - Spring: `https://maven.aliyun.com/repository/spring`

2. **腾讯云** (备用)
   - `https://mirrors.cloud.tencent.com/nexus/repository/maven-public/`

3. **华为云** (备用)
   - `https://mirrors.huaweicloud.com/repository/maven/`

4. **官方仓库** (兜底)
   - Maven Central

## 故障排除

### 如果下载仍然很慢
1. 检查网络连接
2. 尝试切换镜像源
3. 清理 Gradle 缓存：
   ```bash
   rm -rf ~/.gradle/caches/
   rm -rf .gradle/
   ```

### 如果出现依赖冲突
1. 查看依赖树：`./gradlew dependencies`
2. 强制刷新：`./gradlew build --refresh-dependencies`
3. 检查版本冲突并在 `build.gradle` 中强制指定版本

## 性能监控

### 构建性能报告
```bash
./gradlew build --profile --scan
```

### 依赖下载统计
```bash
./gradlew build --info | grep "Downloaded"
```

## 注意事项

1. **首次构建**可能仍需要较长时间下载依赖
2. **网络环境**不同，镜像速度可能有差异
3. **企业网络**可能需要配置代理
4. **构建缓存**会随时间积累，定期清理可保持最佳性能

## 企业网络代理配置

如果在企业网络环境下，需要配置代理：

```properties
# gradle.properties 中添加
systemProp.http.proxyHost=your-proxy-host
systemProp.http.proxyPort=your-proxy-port
systemProp.https.proxyHost=your-proxy-host
systemProp.https.proxyPort=your-proxy-port
```

---

**配置完成后，Gradle 构建速度应该有显著提升！** 🎉 