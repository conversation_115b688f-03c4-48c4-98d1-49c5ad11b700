# 2025-03-28 Pacdora API 指南（中文翻译）

## 开始之前

**我们建议先阅读 [Pacdora 编辑器 API 常见问题](https://ifconfiger.feishu.cn/wiki/WqE1ww8yuixLZ2kLUeZcSuVRn0f) 以获取更多信息。**

## 关键概念

为了高效集成 Pacdora API，需理解以下关键概念：

*   **`data-pacdora-ui`**：指 Pacdora 模型的一个实例。一个项目包含生成 3D 预览和设计所需的全部数据。Pacdora API 提供的一切都与具体项目相关。
*   **模型（Model）**：即 pacdora.com 上的某种特定包装盒模型，每个模型有唯一的 `modelId`，如 `100010` 或 `150010`。你可以用 `modelId` 初始化 Pacdora API，让客户从零开始设计。
*   **模板（Template）**：项目中包含你为客户预设的设计或尺寸。

## 教程与演示

*   [https://cdn.pacdora.com/pacdora-api-intro.mp4](https://cdn.pacdora.com/pacdora-api-intro.mp4)
*   [https://apidemo.pacdora.com/index.html](https://apidemo.pacdora.com/index.html)

## 最新 API 版本

*   [https://cdn.pacdora.com/Pacdora-v1.1.6.js](https://cdn.pacdora.com/Pacdora-v1.1.6.js)

## Github

*   [https://github.com/Pacdora](https://github.com/Pacdora)

## 快速开始

Pacdora 提供两种集成方式：

*   **HTML 组件**：如果你主要使用 HTML，可以通过自定义属性 `data-pacdora-ui` 直接嵌入 Pacdora 模块。
*   **JavaScript 函数**：如果你更习惯 JS，可以通过 `Pacdora` 对象在应用中创建和自定义 Pacdora 模块。

## 通过 CDN 使用 Pacdora

在 HTML 文档中引入以下 `<script>` 标签（建议放在文档末尾，确保 HTML 加载完毕后执行）：

```html
<script src="https://cdn.pacdora.com/Pacdora-v*.*.*.js"></script>
```

## 初始化 Pacdora

*   联系 [<EMAIL>](mailto:<EMAIL>) 获取 App ID 和 App Key。
*   访问 Pacdora 官网查找所需的模型 ID 或模板 ID。
*   使用 `Pacdora.init` 方法进行初始化。

```javascript
await Pacdora.init({
    userId: '你的客户ID',
    appId: '你的App ID',
    isDelay: true, // 请始终设为 true
    theme: '#3300FF',
});
```

`Pacdora.init` 方法是异步的，初始化前可展示加载动画，初始化完成后结束动画。

### 参数说明：

| 属性 | 说明 | 类型 | 默认值 |
| :--------- | :---------------------------------------------------------------- | :------ | :------------ |
| `userId`   | 可选。你平台的客户ID。 | string  | /             |
| `appId`    | 必填。你的签名 APP ID。 | string  | /             |
| `isDelay`  | 兼容旧版，**请始终设为 true**。 | boolean | `false`       |
| `width`    | 可选。空白盒型时指定宽度（mm）。 | number  | /             |
| `height`   | 可选。空白盒型时指定高度（mm）。 | number  | /             |
| `length`   | 可选。空白盒型时指定长度（mm）。 | number  | /             |
| `theme`    | 可选。所有模块主题色。 | color   | `#36B37E`     |
| `doneBtn`  | 可选。完成按钮提示文本。不传则不显示。 | string  | /             |

## 创建场景

此方法创建一个场景并返回所创建项目的项目 ID。请确保在调用此方法之前 HTML DOM 已完全挂载。

```javascript
await Pacdora.createScene({
    doneBtn: '保存',
    modelId: '100010'
});
```

### 示例：设置包装颜色选项

```javascript
await Pacdora.createScene({
    doneBtn: '保存',
    modelId: '100010',
    packagingColors: ['#ff0000', '#00ff00', '#0000ff', '#893829']
});
```

### 参数说明：

| 属性        | 说明                                                                                                                                                                                                                                                                                                                                                                                                                                                    | 类型                                                                                                                                      | 默认值 | 
| :---------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------- | :------------ | 
| `id`              | 可选。用户创建的项目 ID，可与 `boxId` 或 `modelId` 一起选择。                                                                                                                                                                                                                                                                                                                                                                                   | number                                                                                                                                    | /             | 
| `templateId`      | 可选。在 Pacdora 官网上创建的模板 ID。                                                                                                                                                                                                                                                                                                                                                                                                      | number                                                                                                                                    | /             | 
| `modelId`         | 可选。空白盒型 ID。                                                                                                                                                                                                                                                                                                                                                                                                           | number                                                                                                                                    | /             | 
| `width`           | 可选。选择空白盒型时指定盒子的宽度（毫米）。                                                                                                                                                                                                                                                                                                                                                                                                         | number                                                                                                                                    | /             | 
| `height`          | 可选。选择空白盒型时指定盒子的高度（毫米）。                                                                                                                                                                                                                                                                                                                                                                                                         | number                                                                                                                                    | /             | 
| `length`          | 可选。选择空白盒型时指定盒子的长度（毫米）。                                                                                                                                                                                                                                                                                                                                                                                                         | number                                                                                                                                    | /             | 
| `doneBtn`         | 可选。完成按钮的文本。如果不提供，则不显示。                                                                                                                                                                                                                                                                                                                                                                                                     | string                                                                                                                                    | /             | 
| `localeResource`  | 可选。你想要在 Pacdora 组件中替换的文本资源。                                                                                                                                                                                                                                                                                                                                                                                              | object                                                                                                                                    | `{}`          | 
| `packagingColors` | 可选。推荐的包装颜色列表。你的自定义颜色将附加到现有列表中。                                                                                                                                                                                                                                                                                                                                                                                        | array                                                                                                                                     | `[]`          | 
| `showSize`        | 可选。编辑页面显示尺寸修改组件。                                                                                                                                                                                                                                                                                                                                                                                                       | boolean                                                                                                                                   | `true`        | 
| `showMaterial`    | 可选。编辑页面显示材质修改组件。                                                                                                                                                                                                                                                                                                                                                                                                       | boolean                                                                                                                                   | `true`        | 
| `isCreatePreview` | 是否以预览模式创建项目，默认为 true。如果用户未执行任何操作（即处于预览状态，此用户不会记录在付费计划中），你将无法获取项目 ID。只有在用户执行操作后，你才能使用 `Pacdora.getBoxInfo()` 检索项目 ID。当然，你也可以在 `createScene` 中强制设置 `isCreatePreview: false` 以切换到非预览状态，在这种情况下，用户将被记录在付费计划中。 | boolean                                                                                                                                   | `true`        | 
| `clips`           | 指定画布的可设计区域。<br/> `inside` 和 `outside` 的类型是数组，允许你指定多个可设计区域                                                                                                                                                                                                                                                                                                                                                            | `{`<br/>`inside: { x: number, y: number, w: number, h: number }[];`<br/>`outside: { x: number, y: number, w: number, h: number }[];`<br/>`}` | /             | 

## 获取用户信息

```javascript
Pacdora.getUserInfo(): Promise<{ userId: string /*, ...其他属性 */ }>
```

如果配置了用户收集信息，此方法将检索用户信息。成功执行后，它会返回一个包含用户 ID 和其他相关详细信息的对象。

```html
<script>
    (async () => {
        await Pacdora.init({
           userId: '你的客户ID',
            appId: '你的App ID',
            isDelay: true,
            theme: '#3300FF',
        });
        const userInfo = await Pacdora.getUserInfo();
        console.log('用户信息:', JSON.stringify(userInfo));
        // 输出: 用户信息: {"userId": "你的客户ID", "email": "<EMAIL>", ...}
    })();
</script>
```

## 设置用户信息

```javascript
Pacdora.setUserInfo({data}): Promise<>
```

**重要：** 在使用此功能之前，请联系我们确定参数（例如昵称、电子邮件等）。

```html
<script>
    (async () => {
        // 假设 userInfo 已获取
        // console.log('用户信息:', JSON.stringify(userInfo));
        await Pacdora.setUserInfo({
            nickname: "用户昵称",
            email: "<EMAIL>"
            // ... 其他约定的参数
        });
    })();
</script>
```

## 重置用户 ID：此 API 已弃用

```javascript
Pacdora.resetUserId(newUserId: string)
```

此方法允许你重置当前用户的用户 ID。两个 ID 对应的项目将被合并。

```html
<script>
    (async () => {
        await Pacdora.init({
           userId: '你的客户ID',
            appId: '你的App ID',
            isDelay: true,
            theme: '#3300FF',
        });
        await Pacdora.resetUserId('新的客户ID');
    })();
</script>
```

## 本地化

此 API 允许你修改 Pacdora 组件内的文本。你可以通过向 `Pacdora.init` 传递 `localeResource` 对象来自定义文本。例如，要将 `data-pacdora-ui="design-btn"` 弹出窗口标题中的默认"Upload & Design"文本更改为"在线设计"，可以使用以下代码：

```javascript
await Pacdora.init({
    userId: '你的自定义ID',
    appId: '你的App ID',
    isDelay: true,
    localeResource: {
        'Upload & Design': '在线设计' // 示例更改
        // 为其他文本添加更多键值对
    }
 });
```

要支持你的本地语言，请将 Pacdora 组件中的文本替换为你所需的翻译。你可以从 [locale-v1.0.1.json](https://example.com/locale-v1.0.1.json)（注意：需要实际 URL）下载所有文本资源。

## 打开和关闭盒子

如果你通过 `init` 或 `createScene` 函数创建了一个可折叠的盒子，你可以使用 `Pacdora.collapse` 方法切换其状态。

```html
<div data-pacdora-ui="3d" data-pacdora-id="d3" style="width:300px;height:300px"></div>
<button onclick="collapse(1)">打开</button>
<button onclick="collapse(0)">关闭</button>
<script>
    function collapse(open) {
        // 调用前确保 Pacdora 已初始化并创建了场景
        Pacdora.collapse("d3", open); // open: 0 (折叠) 或 1 (展开)
    }
</script>
```

在上面的示例中，使用 `data-pacdora-id` 指定要控制的 3D 预览图像。`open` 值可以是 `0`（折叠）或 `1`（展开）。

## 编辑盒子

Pacdora 提供了一系列方法来编辑盒子的属性。

### `Pacdora.setSize(option)`

设置盒子的大小。

```javascript
await Pacdora.setSize({
    width: 100,
    height: 100,
    length: 100,
    type: 'id', // 'id' (内尺寸), 'ed' (外尺寸), 'dm' (制造尺寸)
    async: true // 立即更新
});
```

#### 参数说明：

| 属性 | 说明 | 类型 | 默认值 |
| :--------- | :----------------------------------------------------- | :----------- | :------------ |
| `width`    | 可选。盒子的宽度（毫米）。 | number       | /             |
| `height`   | 可选。盒子的高度（毫米）。 | number       | /             |
| `length`   | 可选。盒子的长度（毫米）。 | number       | /             |
| `type`     | 可选。尺寸模式：`id` \| `ed` \| `dm` | string       | `'id'`        |
| `async`    | 可选。如果设置为 true，盒子尺寸将立即更新。 | boolean      | `false`       |

### `Pacdora.setThickness(option)`

设置盒子的厚度。

```javascript
await Pacdora.setThickness({
    value: 2, // 厚度（毫米）
    async: true
});
```

#### 参数说明：

| 属性 | 说明 | 类型 | 默认值 |
| :--------- | :------------------------------------------------------------------ | :------ | :------------ |
| `value`    | 可选。盒子的厚度（毫米）。 | number  | /             |
| `async`    | 可选。如果设置为 true，盒子厚度将立即更新。 | boolean | `false`       |

### `Pacdora.setMaterial(option)`

设置盒子的材质。

```javascript
await Pacdora.setMaterial({
    name: '纸板',
    image: 'https://example.com/material.jpg', // 材质图片的 URL
    side: 'https://example.com/edge.jpg', // 可选的边缘材质图片
    insideName: '内层纸板', // 可选的内部材质名称
    insideImage: 'https://example.com/inside_material.jpg', // 可选的内部材质纹理
    async: true
});
```

#### 参数说明：

| 属性    | 说明 | 类型 | 默认值 |
| :------------ | :---------------------------------------------------------------------------------------- | :------ | :------------ |
| `name`        | 可选。材质名称。 | string  | /             |
| `image`       | 可选。材质图片的 URL。 | string  | /             |
| `async`       | 可选。如果设置为 true，材质将立即更新。 | boolean | `false`       |
| `side`        | 可选。边缘材质的图片。 | string  | /             |
| `insideName`  | 可选。内部材质名称。 | string  | /             |
| `insideImage` | 可选。内部材质纹理，如果未设置，将应用 `image` 字段的值。 | string  | /             |

### `Pacdora.updateEditor(pacdoraId)`

当你打开设计编辑器（通过点击 `"design-btn"` 触发），并且同时使用 `Pacdora.setSize` 或 `Pacdora.setThickness` 更改了刀模线形状时，编辑对话框中的刀模线不会自动更新。在这种情况下，你需要使用 `Pacdora.updateEditor` 来通知对话框更改。

```html
<div
    data-pacdora-ui="design-btn"
    class="btn"
    data-pacdora-id="design"
    data-user-collect="true"
    data-user-collect-cancel="true"
>
    在线设计
</div>
<script>
    // 调用 setSize 或 setThickness 后...
    Pacdora.updateEditor("design"); // 使用 design-btn 的 data-pacdora-id
</script>
```

### `Pacdora.rename(name, id?)`

更改项目名称。

```javascript
await Pacdora.rename('我的新项目名称', projectId); // 如果在 createScene 后调用，projectId 是可选的
```

#### 参数说明：

| 属性 | 说明 | 类型 | 默认值 |
| :--------- | :------------------------------------------------------------------------------------------------------------------------------------- | :------ | :------------ |
| `name`     | 必填。项目名称。 | string  | /             |
| `id`       | 可选。`Pacdora.createScene` 函数返回的 `projectId` 或通过 `/open/v1/user/projects` 获取的列表中的 `id`。 | number  | /             |

### `Pacdora.getBoxInfo()`

返回盒子的 `width`、`height`、`length`、`thickness`、`materialName`、`materialImage`、`screenshot`、`packageInsideColor`、`packageOutsideColor`、`designArea`、`widthOfArea` 和 `heightOfArea` 属性。

#### 返回内容

| 属性           | 说明 | 类型 | 
| :------------------ | :------------------------------------------------------------------------------------------------------------------------------------- | :----- | 
| `width`             | 盒子的宽度 | number | 
| `height`            | 盒子的高度 | number | 
| `length`            | 盒子的长度 | number | 
| `thickness`         | 盒子的厚度 | number | 
| `materialName`      | 盒子的材质名称。 | string | 
| `materialImage`     | 材质图片的 URL。 | string | 
| `screenshot`        | 用户设计的 3D 预览图（base64）；如果用户尚未开始设计，预览图将显示盒子刀模线图。 | string | 
| `packageInsideColor`| 用户为盒子内部设置的颜色。 | string | 
| `packageOutsideColor`| 用户为盒子外部设置的颜色。 | string | 
| `designArea`        | 刀模线的设计区域。 | string | 
| `widthOfArea`       | 设计区域的宽度 | number | 
| `heightOfArea`      | 设计区域的高度 | number | 

```javascript
const { width, height, length, /* ... 其他属性 */ } = await Pacdora.getBoxInfo();
console.log(width, height, length);
```

## 事件

Pacdora 提供了 `$on` 和 `$off` 方法来监听和取消注册交互。`Pacdora.$on(event, callback, scope)` 函数需要三个参数，其中 `scope` 指定当前的监听上下文。例如，以下代码设置了一个保存事件，仅当在弹出窗口中保存设计时，才会在 `"test"` 范围内触发：

```html
<div data-pacdora-ui="design-btn" class="btn" data-scope="test">在线设计</div>
<script>
    Pacdora.$on(
        'design:save',
        (data) => {
            console.log('在 "test" 范围内保存设计');
            const {
                screenshot, // base64 格式
                maskImages: {
                    inside: [],
                    outside: []
                },
                collapse, // 盒子展开状态 0-100
                rotation: { x, y, z } // 盒子旋转角度
            } = data;
            console.log('截图:', screenshot.substring(0, 50) + '...'); // 记录部分截图数据
        },
        'test' // Scope 与 data-scope 属性匹配
    );

    // 取消注册示例 (可选)
    // function unregisterSave() {
    //     Pacdora.$off('design:save', null, 'test');
    // }
</script>
```

### 事件列表

| 名称                   | 描述                                                              | 关联组件                     | 返回数据示例 (在回调中)                                                                                                                                                                                                                                                                                                             | 
| :--------------------- | :--------------------------------------------------------------- | :------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | 
| `download:start`       | 点击下载 PDF 按钮时触发                                           | `data-pacdora-ui="download"` | `(id: string)`                                                                                                                                                                                                                                                                                                                                         | 
| `download:success`     | PDF 下载完成                                                     | `data-pacdora-ui="download"` | `(id: string)`                                                                                                                                                                                                                                                                                                                                         | 
| `download:fail`        | PDF 下载失败                                                     | `data-pacdora-ui="download"` | `(id: string, error?: any)`                                                                                                                                                                                                                                                                                                                        | 
| `before:editor:change` | 在编辑器弹出窗口中修改材质和尺寸之前                               | `data-pacdora-ui="design-btn"` | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `after:editor:change`  | 编辑器弹出窗口更改材质和尺寸保存后                               | `data-pacdora-ui="design-btn"` | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `project:change`       | 项目发生更改时触发                                               | 所有                       | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `design:opened`        | 编辑器弹出窗口出现后触发                                           | `data-pacdora-ui="design-btn"` | `(pacdoraId: string)`                                                                                                                                                                                                                                                                                                                                  | 
| `before:design`        | 编辑器弹出窗口出现之前触发                                           | `data-pacdora-ui="design-btn"` | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `design:save`          | 在编辑器弹出窗口中点击保存按钮后触发                               | `data-pacdora-ui="design-btn"` | `(data: { screenshot: string, maskImages: { inside: string[], outside: string[] }, collapse: number, rotation: { x: number, y: number, z: number } })`                                                                                                                                                                                          | 
| `design:cancel`        | 在编辑器弹出窗口中点击关闭图标后触发                               | `data-pacdora-ui="design-btn"` | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `update:canvas`        | Canva 画布更新后触发                                             | `data-pacdora-ui="canva"`    | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `canva:element:selected`| 在 Canva 画布中选择元素时触发                                      | `data-pacdora-ui="canva"`    | `(elementInfo: any)`                                                                                                                                                                                                                                                                                                                               | 
| `canva:face:selected`  | 在 Canva 画布中选择刀模线表面后触发                                | `data-pacdora-ui="canva"`    | `(faceInfo: any)`                                                                                                                                                                                                                                                                                                                                  | 
| `3d:change`            | 3D 模型发生更改时触发                                            | `data-pacdora-ui="3d"`       | `()`                                                                                                                                                                                                                                                                                                                                                 | 
| `3d:init`              | 3D 模型初始化后触发                                              | `data-pacdora-ui="3d"`       | `(id: string)`                                                                                                                                                                                                                                                                                                                                         | 

## 创建盒子的基础结构（刀模线）

你可以使用属性 `data-pacdora-ui="dieline"` 为相应的盒子创建刀模线插图，或使用 `Pacdora.renderDieline` 构建盒子的模板图像。

```html
<div data-pacdora-ui="dieline" data-pacdora-id="myDieline" style="width:300px;height:300px; border: 1px solid #ccc;"></div>
```

## 生成 2D 预览

要显示带有刀模线的用户设计预览图像，请添加属性 `data-pacdora-ui="preview"`。

```html
<div data-pacdora-ui="preview" data-pacdora-id="myPreview" style="width:300px;height:300px; border: 1px solid #ccc;"></div>
```

## 生成 3D 预览

使用属性 `data-pacdora-ui="3d"` 来启用 3D 预览功能。

```html
<div
    data-pacdora-ui="3d"
    class="d3 pb"
    data-pacdora-id="d3"
    data-light-intensity="1.5"
    data-init-rotation="true"
    data-use-shadow="false"
    style="width: 500px; height: 400px; border: 1px solid #ccc;"
></div>
```

### 属性

| 属性名称           | 描述                                       | 默认值 | 
| :--------------------- | :----------------------------------------- | :------------ | 
| `data-width`           | 组件宽度                                   | -             | 
| `data-height`          | 组件高度                                   | -             | 
| `data-light-intensity` | 场景中的光照强度。                           | `1.5`         | 
| `data-init-rotation`   | 加载期间是否有旋转动画？                     | `true`        | 
| `data-use-shadow`      | 是否显示阴影？                             | `false`       | 

### 事件

| 事件名称 | 描述                   | 返回值         | 
| :--------- | :--------------------- | :--------------- | 
| `3d:init`  | 3D 预览已初始化       | `(id: string)` | 

### `Pacdora.get3DRotation(pacdoraId: string)`

获取模型的旋转角度。

```html
<div
    data-pacdora-ui="3d"
    class="d3 pb"
    data-pacdora-id="d3"
    data-light-intensity="2"
    data-init-rotation="true"
    style="width: 500px; height: 400px; border: 1px solid #ccc;"
></div>
<script>
    // 调用前确保 3d:init 事件已触发
    Pacdora.$on('3d:init', (id) => {
        if (id === 'd3') {
            const rotation = Pacdora.get3DRotation('d3');
            if (rotation) {
               console.log('模型的旋转角度是 x: %d, y: %d, z: %d', rotation.x, rotation.y, rotation.z);
            }
        }
    });
</script>
```

### `Pacdora.enable3DRotate(pacdoraId: string, bool: boolean)`

是否允许 `data-pacdora-ui='3d'` 的组件旋转？

### `Pacdora.enable3DDrag(pacdoraId: string, bool: boolean)`

是否允许 `data-pacdora-ui='3d'` 的组件右键移动？

### `Pacdora.enable3DScale(pacdoraId: string, bool: boolean)`

是否允许 `data-pacdora-ui='3d'` 的组件缩放？

```html
<div
    data-pacdora-ui="3d"
    class="d3 pb"
    data-pacdora-id="d3"
    data-light-intensity="2"
    data-init-rotation="true"
    style="width: 500px; height: 400px; border: 1px solid #ccc;"
></div>
<script>
     // 禁止 data-pacdora-id='d3' 且 data-pacdora-ui='3d' 的组件旋转、缩放和平移。
    (async () => {
        // 如果针对特定实例，则在调用前确保 3d:init 已触发
        await Pacdora.enable3DRotate("d3", false);
        await Pacdora.enable3DDrag("d3", false);
        await Pacdora.enable3DScale("d3", false);
    })();
</script>
```

### 入场旋转动画

要为 3D 预览添加入场旋转动画，请包含属性 `data-init-rotation="true"`：

```html
<div
    data-pacdora-ui="3d"
    class="d3 pb"
    data-pacdora-id="d3"
    data-light-intensity="2"
    data-init-rotation="true"
    style="width: 500px; height: 400px; border: 1px solid #ccc;"
></div>
```

### `Pacdora.rotate(pacdoraId, angle)`

3D 模型**从当前位置**旋转的角度。

#### 属性

| 属性       | 描述                                                       | 类型                                     | 默认值 | 
| :-------- | :------------------------------------------------------------- | :--------------------------------------- | :------------ | 
| `pacdoraId` | 组件 ID。`data-pacdora-id` 属性值                          | string                                   | /             | 
| `angle`   | 3D 模型从当前位置旋转的角度                                  | `{ x?: number, y?: number, z?: number }` | /             | 

### `Pacdora.rotateTo(pacdoraId, angle)`

将 3D 盒子旋转**到某个角度**。

#### 属性

| 属性       | 描述                                                       | 类型                                     | 默认值 | 
| :-------- | :------------------------------------------------------------- | :--------------------------------------- | :------------ | 
| `pacdoraId` | 组件 ID。`data-pacdora-id` 属性值                          | string                                   | /             | 
| `angle`   | 3D 模型旋转的目标角度                                        | `{ x?: number, y?: number, z?: number }` | /             | 

### 调整 3D 容器大小

初始化 `data-pacdora-ui="3d"` 后（例如，浏览器缩放后），要重置 3D 预览窗口的大小，请使用以下方法：

`Pacdora.resize3D(pacdoraId, size)`

```html
<div data-pacdora-ui="3d" class="d3" data-pacdora-id="d3" style="width: 500px; height: 400px; border: 1px solid #ccc;"></div>
<script>
window.addEventListener('resize', (e) => {
    // 示例：窗口调整大小时调整为 300x300
    Pacdora.resize3D('d3', { width: 300, height: 300 });
});
</script>
```

请注意，必须使用 `data-pacdora-id` 属性来指定要调整大小的 3D 预览窗口。

### 设置 3D 预览图像背景

使用 `Pacdora.set3DBackground` 设置 3D 预览图像的背景颜色。

```html
<div data-pacdora-ui="3d" class="d3" data-pacdora-id="d3" style="width: 500px; height: 400px; border: 1px solid #ccc;"></div>
<script>
// 示例：设置渐变背景
Pacdora.set3DBackground('d3', 'gradient', {
    direction: 180, // 角度（度）
    colors: [
      { color: '#C4C5C7', step: 0 }, // 步骤 0 处的起始颜色
      { color: '#EBEBEB', step: 1 }  // 步骤 1 处的结束颜色
    ]
});

// 示例：设置纯色背景
// Pacdora.set3DBackground('d3', 'color', '#FF0000');

// 示例：设置透明背景
// Pacdora.set3DBackground('d3', 'transparent');
</script>
```

#### 参数说明：

| 属性       | 描述                                                                                                                                               | 类型                           | 
| :---------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------- | 
| `pacdoraId` | 由 `data-pacdora-id` 指定的 3D 预览组件                                                                                                                              | string                         | 
| `colorType` | 颜色类型。                                                                                                                                                     | `gradient`\|`color`\|`transparent` | 
| `color`     | 要设置的颜色。当 `colorType` 为 'color' 时，传入颜色 RGB 字符串。如果 `colorType` 为 'gradient'，则传入如示例所示的对象值。                                                  | string \| object               | 

### 在 3D 预览图像上显示尺寸信息

使用 `Pacdora.set3DDimensionView` 在 3D 预览图像上显示刀模线的尺寸。

```javascript
// 确保目标 pacdoraId 的 3d:init 事件已触发
Pacdora.set3DDimensionView('d3', true); // true 显示, false 隐藏
```

#### 属性

| 属性       | 描述                                             | 类型    | 
| :---------- | :------------------------------------------------------ | :------ | 
| `pacdoraId` | 由 `data-pacdora-id` 指定的 3D 预览组件             | string  | 
| `visible`   | 是否显示                                            | boolean | 

### `Pacdora.get2DScreenshot(side, option, dpiMode)`

获取 2D 设计图纸。

```javascript
const base64Image = await Pacdora.get2DScreenshot(
    'outside', // 'outside' 或 'inside'
    {
        showDieline: false,
        showMaterial: false,
    },
    'render' // 'performance' | 'preview' | 'render'
);
console.log('2D 截图 (base64): ', base64Image.substring(0, 50) + '...');
```

#### 属性

| 属性               | 描述                                                       | 类型                           | 
| :------------------ | :--------------------------------------------------------- | :----------------------------- | 
| `side`              | 获取盒子内部或外部的 2D 设计图纸。                           | `outside` \| `inside`          | 
| `option.showDieline`| 2D 设计图纸是否包含刀模线。                                | boolean                        | 
| `option.showMaterial`| 2D 设计图纸是否包含材质纹理。                              | boolean                        | 
| `dpiMode`           | 图像质量，`render` > `preview` > `performance`             | `performance`\|`preview`\|`render` | 

### `Pacdora.get3DShot(pacdoraId, width)`

获取 3D 预览截图。

```javascript
// 确保目标 pacdoraId 的 3d:init 事件已触发
const base64Screenshot = await Pacdora.get3DShot('d3', 800); // 指定 pacdoraId 和所需宽度
console.log('3D 截图 (base64): ', base64Screenshot.substring(0, 50) + '...');
```

#### 属性

| 属性       | 描述                                             | 类型   | 
| :---------- | :------------------------------------------------------ | :----- | 
| `pacdoraId` | 由 `data-pacdora-id` 指定的 3D 预览组件             | string | 
| `width`     | 返回图像的宽度。                                        | number | 

### 销毁 3D 预览

由于 3D 预览图像性能消耗较高，建议在关闭时使用 `Pacdora.destroy3d` 方法销毁 3D 预览图像。

`Pacdora.destroy3d(pacdoraId)`

```html
<div data-pacdora-ui="3d" data-pacdora-id="d3" class="d3" style="width: 500px; height: 400px; border: 1px solid #ccc;"></div>
<button onclick="destroyPreview()">销毁预览</button>
<script>
function destroyPreview() {
    Pacdora.destroy3d('d3'); // 指定 data-pacdora-id
}
</script>
```

在上面的示例中，你需要使用 `"data-pacdora-id"` 来指定需要控制的 3D 预览。

## 捕获 3D 预览

你可以使用 `data-pacdora-ui="3d-preview"` 属性将 3D 设计预览添加到页面中。当通过 `data-pacdora-ui="design-btn"` 弹出的设计编辑器中的保存按钮被点击时，它将自动更新为用户设计的 3D 预览。你可以使用 `data-screenshot-width` 属性控制预览图像的大小和精度。如果你想将截图更新为 `getBoxInfo` 中的当前设计，你需要在 `design-btn` 上设置 `data-save-screenshot="true"`。

```html
<div data-pacdora-ui="3d-preview" data-pacdora-id="my3dPreview" style="width: 300px; height: 300px; border: 1px solid #ccc;"></div>

<div
    data-pacdora-ui="design-btn"
    data-pacdora-id="design"
    class="btn"
    data-scope="test"
    data-screenshot-width="1000"
    data-save-screenshot="true"
>
    在线设计
</div>
```

## 促进编辑对话框的创建

通过使用属性 `data-pacdora-ui="design-btn"` 创建用于图像提交和编辑的弹出按钮。

```html
<div data-pacdora-ui="design-btn" class="upload-btn"> 在线设计</div>
<style>
.upload-btn {
    display: inline-block; /* 添加以实现正确的样式 */
    padding: 8px 16px; /* 添加内边距 */
    width: 120px;
    font-size: 16px;
    line-height: 1.5; /* 调整行高 */
    height: auto; /* 将高度更改为 auto */
    border-radius: 4px; /* 调整边框半径 */
    text-align: center;
    border: solid 1px #323232;
    cursor: pointer; /* 添加光标指针 */
    background-color: white; /* 添加背景色 */
    color: #323232; /* 添加颜色 */
    transition: background-color 0.3s, color 0.3s; /* 添加过渡效果 */
}
.upload-btn:hover { /* 将选择器更改为 .upload-btn */
    background: #323232;
    color: #fff;
}
</style>
```

### `data-pacdora-ui='design-btn'` 的自定义属性

| 属性                     | 描述                                                               | 类型          | 默认值                                                          | 
| :------------------------ | :----------------------------------------------------------------- | :------------ | :-------------------------------------------------------------- | 
| `data-pacdora-id`         | 由 `data-pacdora-id` 指定的组件 ID                                 | string        | /                                                               | 
| `data-scope`              | 事件监听范围。                                                     | string        | /                                                               | 
| `data-screenshot-width`   | 点击保存按钮时保存的截图的图像宽度。                               | number        | /                                                               | 
| `data-save-screenshot`    | 是否保存截图。                                                     | `true`\|`false` | /                                                               | 
| `data-background`         | 3D 预览窗口的背景颜色。                                            | color(#ff0000)| /                                                               | 
| `data-light-intensity`    | 3D 预览窗口的光照强度。                                            | number        | `0.5`                                                           | 
| `data-user-collect-cancel`| 用户信息收集弹出窗口是否可以关闭。                                   | `true`\|`false` | /                                                               | 
| `data-user-collect`       | 弹出窗口以收集用户信息。                                           | `true`\|`false` | /                                                               | 
| `data-user-collect-title` | 弹出窗口标题                                                       | string        | "嘿，请输入你的详细信息以保存你自己的设计！"                      | 
| `data-disable-opacity`    | 禁用包装颜色的透明选项。                                           | `true`\|`false` | `false`                                                         | 
| `data-z-index`            | 3D 预览窗口的 CSS 属性 `z-index`。                                 | number        | /                                                               | 
| `data-show-side`          | 是否显示侧边选项。                                                 | `true`\|`false` | `true`                                                          | 
| `data-unit`（beta）       | 显示的尺寸单位                                                     | `mm` \| `in`  | /                                                               | 
| `data-disable-color-customize`(beta)| 是否自定义包装的颜色                                               | `true`\|`false` | /                                                               | 

```html
<div
    data-pacdora-ui="design-btn"
    data-pacdora-id="design"
    data-user-collect="true"
    data-user-collect-cancel="false"
    data-user-collect-title="你的收集用户弹出窗口标题"
    class="btn upload-btn" <!-- 添加 upload-btn 类 -->
    data-scope="test"
    data-screenshot-width="1000"
    data-save-screenshot="true"
    data-background="#FF0000"
    data-light-intensity="0.4"
    data-z-index="999"
    data-show-side="false"
>
    在线设计
</div>
```

### 事件 (针对 `design-btn`)

| 事件名称      | 描述                                                                       | 返回值                                                                                                                                                                                                              | 
| :-------------- | :------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | 
| `before:design` | 打开设计对话框之前                                                           | `()`                                                                                                                                                                                                                | 
| `design:save`   | 保存按钮点击                                                               | `(data: { screenshot: string, maskImages: { inside: string[], outside: string[] }, collapse: number, rotation: { x: number, y: number, z: number } })`                                                                    | 
| `design:cancel` | 对话框关闭                                                               | `()`                                                                                                                                                                                                                | 
| `design:opened` | 编辑弹出窗口打开后。                                                       | `(pacdoraId: string)`                                                                                                                                                                                                           | 

### `Pacdora.getEditor3DShot(pacdoraId, width): Promise<base64String | null>`

获取**编辑器中**的 3D 图形截图。

#### 属性

| 属性       | 描述                                             | 类型   | 
| :---------- | :------------------------------------------------------ | :----- | 
| `pacdoraId` | 由 `data-pacdora-id` 指定的编辑器组件                 | string | 
| `width`     | 返回图像的宽度。                                        | number | 

### 关闭编辑对话框

Pacdora 提供了 `Pacdora.destroyDesignBtn(pacdoraId: string)` 方法来关闭和销毁编辑弹出窗口。

```html
<div
    data-pacdora-ui="design-btn"
    data-pacdora-id="design"
    class="btn upload-btn"
>
    在线设计
</div>
<button onclick="onClose()">手动关闭编辑器</button>
<script>
    function onClose() {
        Pacdora.destroyDesignBtn('design'); // 指定 data-pacdora-id
    }
</script>
```

在上面的示例中，你需要使用 `"data-pacdora-id"` 来指定需要控制的编辑对话框。

### 通过代码打开编辑器

你可以使用 `Pacdora.openEditor` 方法主动调用 Pacdora 编辑器。

```html
<button onclick="openEditor()">以编程方式打开编辑器</button>
<script>
function openEditor() {
    Pacdora.openEditor(
        (data) => { // saveCallback
            console.log('以编程方式保存编辑器', data);
        },
        () => { // cancelCallback
            console.log('以编程方式取消编辑器');
        },
        { // option: 编辑器选项，与 design-btn 的 data-* 属性相同
            saveScreenshot: false,
            // 添加其他选项，如 data-background, data-light-intensity 等。
        },
        'design_scope' // 可选范围
    );
}
</script>
```

#### 属性 (针对 `openEditor`)

| 参数           | 描述                                                         | 类型     | 
| :------------- | :----------------------------------------------------------- | :------- | 
| `saveCallback` | 编辑器成功保存后的回调函数。                               | function | 
| `cancelCallback`| 编辑器关闭后的回调函数。                                     | function | 
| `option`       | 编辑器选项，与 `design-btn` 的 `data-*` 属性相同。          | object   | 
| `scope`        | 受事件影响的范围。                                           | string   | 

## 设置模型的材质属性

在 Pacdora 官网上，你可以修改模型特定部分的颜色、金属度、粗糙度、透射和不透明度。Pacdora API 也提供了 `Pacdora.setCustomMaterial` 来允许你修改这些属性。

```javascript
await Pacdora.setCustomMaterial({
    "组件名称 A": { // 从客服处获取组件名称
      color: '#ffffff',
      metalness: 1,
      opacity: 0,
      // roughness: 0.5,
      // transmission: 0.8,
    },
    "组件名称 B": {
      color: '#ff0000',
      metalness: 0.5,
      opacity: 1,
    },
});
```

你可以通过咨询客服来获取组件名称。

## 自动上传图片到设计编辑器

```javascript
await Pacdora.addDesign(
  path: string, // 图片的 url，支持 png/jpg/svg
  side: 'outside' | 'inside',
  option?: {
      x?: number,
      y?: number, // 图片的偏移量（相对于设计区域的左上角）
      width: number, // 画布上图片的期望宽度
      height: number, // 画布上图片的期望高度
      fixRatio: boolean // 默认为 true。当图片和设计编辑器的宽高比不匹配时，是否改变图片的宽高比
  }
);

// 示例
await Pacdora.addDesign(
    'https://example.com/logo.png',
    'outside',
    { x: 10, y: 10, width: 50, height: 50, fixRatio: true }
);
```

## 下载组件

Pacdora 提供了一个导出组件，允许用户点击下载 PDF/DXF/AI 文件。**请注意，此组件需要暴露你的 App key，请谨慎使用。**

```html
 <div
    class="btn" <!-- 根据需要设置样式 -->
    id="downloadBtn"
    data-pacdora-ui="download"
    data-pacdora-id="download"
    data-ext="dxf|ai|pdf" <!-- 指定允许的扩展名 -->
    data-app-key="你的 app key" <!-- !!! 暴露 APP KEY !!! -->
    style="display: inline-block; padding: 8px 16px; border: 1px solid #ccc; cursor: pointer;"
>
    下载
</div>
<script>
  const downloadBtn = document.getElementById('downloadBtn');

  Pacdora.$on('download:start', (id) => {
    if (id === 'download' && downloadBtn) {
      downloadBtn.innerHTML = '加载中...';
      downloadBtn.style.pointerEvents = 'none'; // 下载期间禁用点击
    }
  });

  Pacdora.$on('download:success', (id) => {
    if (id === 'download' && downloadBtn) {
      downloadBtn.innerHTML = '成功';
      setTimeout(() => {
        downloadBtn.innerHTML = '下载';
        downloadBtn.style.pointerEvents = 'auto'; // 重新启用点击
      }, 2000); // 2 秒后重置
    }
  });

  Pacdora.$on('download:fail', (id, error) => {
    if (id === 'download' && downloadBtn) {
      downloadBtn.innerHTML = '失败';
      console.error('下载失败:', error);
      setTimeout(() => {
        downloadBtn.innerHTML = '下载';
        downloadBtn.style.pointerEvents = 'auto'; // 重新启用点击
      }, 3000); // 3 秒后重置
    }
  });
</script>
```

## 设置包装的背景颜色

你可以使用 `Pacdora.setPackageColor` 来设置包装的背景颜色。

```javascript
Pacdora.setPackageColor('outside', '#ff0000'); // 将外部颜色设置为红色
Pacdora.setPackageColor('inside', '#00ff00'); // 将内部颜色设置为绿色
```

### 属性

| 属性   | 描述                               | 类型                | 
| :----- | :--------------------------------- | :------------------ | 
| `side` | 包装的内部或外部。                   | `inside` \| `outside` | 
| `color`| 需要设置的颜色。                     | string              | 

## 根据提供的图像设置图案。

### `Pacdora.setPattern(options)`

你可以使用 `setPattern(options)` 方法来设置图案图像。

```javascript
  Pacdora.setPattern({
    patternUrl: 'https://cloud.pacdora.com/effect/pattern/2000009.svg', // 图案图像的 URL
    size: 50, // 缩放值 (0-100)
    side: 'outside' // 'inside' 或 'outside'
  });
```

#### 属性

| 属性       | 描述                                                                                                                                   | 类型                | 
| :----------- | :------------------------------------------------------------------------------------------------------------------------------------- | :------------------ | 
| `patternUrl` | 图像 URL。                                                                                                                             | string              | 
| `size`       | 控制图案的缩放值 (0-100)，其中值为 0 表示最小缩放，图案仅水平平铺一次。                                                                      | number              | 
| `side`       | 包装的内部或外部，默认为 `outside`。                                                                                                       | `inside` \| `outside` | 

### `Pacdora.clearPattern(side)`

你可以使用 `clearPattern(side)` 方法来清除先前设置的图案。

```javascript
Pacdora.clearPattern('outside'); // 清除外部图案
Pacdora.clearPattern('inside'); // 清除内部图案
```

#### 属性

| 属性   | 描述                                                    | 类型                | 
| :----- | :------------------------------------------------------- | :------------------ | 
| `side` | 包装的内部或外部，默认为 `outside`。                     | `inside` \| `outside` | 

## 自由布局编辑器

如果你需要高度定制化的 UI 或者需要更多对设计编辑器的控制，请查看此部分。

### 编辑器画布

编辑器画布显示刀模线图，你可以在上面绘制你的设计。你可以将属性 `data-pacdora-ui="canva"` 添加到 HTML 元素，将其变成画布组件。

```html
<div data-pacdora-ui="canva" data-pacdora-id="myCanvas" style="width: 500px; height: 500px; border: 1px solid #ccc;"></div>
```

### 设置画布背景颜色

你可以使用 `Pacdora.setCanvaBackground` 设置画布背景颜色。

```html
<div data-pacdora-ui="canva" style="width: 500px;height:500px;" data-pacdora-id="canva-id"></div>
<script>
    const pacdoraId = "canva-id";
    // 确保 init 和 createScene 已完成
    Pacdora.setCanvaBackground(pacdoraId, "#ff0000"); // 将背景设置为红色
</script>
```

### 缩放画布

你可以使用 `Pacdora.scaleCanva` 缩放画布。

```html
<div data-pacdora-ui="canva" style="width: 500px;height:500px;" data-pacdora-id="canva-id">
</div>
<script>
    // ...Pacdora.init 和 createScene
    const pacdoraId = 'canva-id';
    // 将画布缩放到其原始大小的 0.9 倍。
    Pacdora.scaleCanva(pacdoraId, 0.9);
</script>
```

### 将画布操作更改为拖动画布

你可以使用 `Pacdora.enableDragCanva` 将画布操作更改为拖动画布。

```html
<div data-pacdora-ui="canva" style="width: 500px;height:500px;" data-pacdora-id="canva-id"> <!-- 注意：将 ui 更改为 canva -->
</div>
<script>
    // ...Pacdora.init 和 createScene
    const pacdoraId = 'canva-id';
    Pacdora.enableDragCanva(pacdoraId, true); // true 启用拖动, false 禁用
</script>
```

### 如何将你的元素添加到画布

你可以使用 `Pacdora.enableDragImage` 将 HTML 元素变成可拖动的图像组件，使用 `Pacdora.enableDragText` 将 HTML 元素变成可拖动的文本组件。当它被拖放到画布上时，它将被添加到项目中。

#### `Pacdora.enableDragImage`

将你的 HTML 元素变成可拖动的图像组件。

```html
<img
    src="https://cdn.pacdora.com/user-materials-mockup_mockup/9c3f7880-2521-4e82-997c-df66ea8c7ecf.jpeg"
    id="image"
    style="width: 100px; cursor: grab;"
/>

<div data-pacdora-ui="canva" style="width: 500px;height:500px; border: 1px solid #ccc;" data-pacdora-id="myCanvas"> <!-- 注意：将 ui 更改为 canva -->
</div>
<script>
    // ...Pacdora.init 和 createScene
    const canvasId = 'myCanvas';
    const imageElement = document.getElementById('image');
    const imageUrl = imageElement.src; // 从图像元素获取 URL

    Pacdora.enableDragImage(
         canvasId, // 目标画布的 ID
         imageElement, // 要使其可拖动的 HTML 元素
         imageUrl // 拖放时要添加到画布的图像的 URL
      );
</script>
```