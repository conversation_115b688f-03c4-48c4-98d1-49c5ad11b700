package com.anxys.system.login.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.anxys.framework.common.annoation.NoNeedLogin;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.utils.RequestUtil;
import com.anxys.framework.constant.AdminSwaggerTagConst;
import com.anxys.framework.constant.RequestHeaderConst;
import com.anxys.system.login.domain.LoginForm;
import com.anxys.system.login.domain.LoginResultVO;
import com.anxys.system.login.service.LoginService;
import com.anxys.system.support.captcha.domain.CaptchaVO;
import com.anxys.system.support.securityprotect.service.Level3ProtectConfigService;
import com.anxys.system.utils.AdminRequestUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/** 员工登录 */
@RestController
@Tag(name = AdminSwaggerTagConst.System.SYSTEM_LOGIN)
public class LoginController {

  @Resource private LoginService loginService;

  @Resource private Level3ProtectConfigService level3ProtectConfigService;

  @NoNeedLogin
  @PostMapping("/login")
  @Operation(summary = "登录 ")
  public HttpResponse<LoginResultVO> login(
      @Valid @RequestBody LoginForm loginForm, HttpServletRequest request) {
    String ip = JakartaServletUtil.getClientIP(request);
    String userAgent =
        JakartaServletUtil.getHeaderIgnoreCase(request, RequestHeaderConst.USER_AGENT);
    return loginService.login(loginForm, ip, userAgent);
  }

  @GetMapping("/login/getLoginInfo")
  @Operation(summary = "获取登录结果信息  ")
  public HttpResponse<LoginResultVO> getLoginInfo() {
    String tokenValue = StpUtil.getTokenValue();
    LoginResultVO loginResult =
        loginService.getLoginResult(AdminRequestUtil.getRequestUser(), tokenValue);
    loginResult.setToken(tokenValue);
    return HttpResponse.ok(loginResult);
  }

  @Operation(summary = "退出登陆  ")
  @GetMapping("/login/logout")
  public HttpResponse<String> logout() {
    return loginService.logout(RequestUtil.getRequestUser());
  }

  @Operation(summary = "获取验证码  ")
  @GetMapping("/login/getCaptcha")
  @NoNeedLogin
  public HttpResponse<CaptchaVO> getCaptcha() {
    return loginService.getCaptcha();
  }

  @NoNeedLogin
  @GetMapping("/login/sendEmailCode/{loginName}")
  @Operation(summary = "获取邮箱登录验证码 ")
  public HttpResponse<String> sendEmailCode(@PathVariable String loginName) {
    return loginService.sendEmailCode(loginName);
  }

  @NoNeedLogin
  @GetMapping("/login/getTwoFactorLoginFlag")
  @Operation(summary = "获取双因子登录标识 ")
  public HttpResponse<Boolean> getTwoFactorLoginFlag() {
    // 双因子登录
    boolean twoFactorLoginEnabled = level3ProtectConfigService.isTwoFactorLoginEnabled();
    return HttpResponse.ok(twoFactorLoginEnabled);
  }
}
