package com.anxys.system.support.file.service;

import org.springframework.web.multipart.MultipartFile;

import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.system.support.file.domain.vo.FileDownloadVO;
import com.anxys.system.support.file.domain.vo.FileUploadVO;

/** 接口 */
public interface IFileStorageService {

  /**
   * 文件上传
   *
   * @param file 文件
   * @param path 路径
   * @return 文件上传结果
   */
  HttpResponse<FileUploadVO> upload(MultipartFile file, String path);

  /**
   * 获取文件url
   *
   * @param fileKey 文件key
   * @return 文件url
   */
  HttpResponse<String> getFileUrl(String fileKey);

  /**
   * 流式下载（名称为原文件）
   *
   * @param key 文件key
   * @return 文件下载结果
   */
  HttpResponse<FileDownloadVO> download(String key);

  /**
   * 单个删除文件 根据文件key删除
   *
   * @param fileKey 文件key
   * @return 删除结果
   */
  HttpResponse<String> delete(String fileKey);

  /**
   * 获取文件类型
   *
   * @param fileExt 文件后缀
   * @return 文件类型
   */
  default String getContentType(String fileExt) {
    // 文件的后缀名
    if ("bmp".equalsIgnoreCase(fileExt)) {
      return "image/bmp";
    }
    if ("gif".equalsIgnoreCase(fileExt)) {
      return "image/gif";
    }
    if ("jpeg".equalsIgnoreCase(fileExt) || "jpg".equalsIgnoreCase(fileExt)) {
      return "image/jpeg";
    }
    if ("png".equalsIgnoreCase(fileExt)) {
      return "image/png";
    }
    if ("html".equalsIgnoreCase(fileExt)) {
      return "text/html";
    }
    if ("txt".equalsIgnoreCase(fileExt)) {
      return "text/plain";
    }
    if ("vsd".equalsIgnoreCase(fileExt)) {
      return "application/vnd.visio";
    }
    if ("ppt".equalsIgnoreCase(fileExt) || "pptx".equalsIgnoreCase(fileExt)) {
      return "application/vnd.ms-powerpoint";
    }
    if ("doc".equalsIgnoreCase(fileExt) || "docx".equalsIgnoreCase(fileExt)) {
      return "application/msword";
    }
    if ("pdf".equalsIgnoreCase(fileExt)) {
      return "application/pdf";
    }
    if ("xml".equalsIgnoreCase(fileExt)) {
      return "text/xml";
    }
    return "";
  }
}
