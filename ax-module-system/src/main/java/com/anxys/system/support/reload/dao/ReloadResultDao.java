package com.anxys.system.support.reload.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.anxys.system.support.reload.domain.ReloadResultEntity;
import com.anxys.system.support.reload.domain.ReloadResultVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/** t_reload_result 数据表dao */
@Mapper
public interface ReloadResultDao extends BaseMapper<ReloadResultEntity> {

  List<ReloadResultVO> query(@Param("tag") String tag);
}
