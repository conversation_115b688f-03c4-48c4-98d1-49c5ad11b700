package com.anxys.system.support.config;

import com.anxys.system.support.config.domain.ConfigEntity;
import com.anxys.system.support.config.domain.ConfigQueryForm;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/** 系统参数配置 t_config Dao层 */
@Mapper
public interface ConfigDao extends BaseMapper<ConfigEntity> {

  /** 分页查询系统配置 */
  List<ConfigEntity> queryByPage(
      Page<ConfigEntity> page, @Param("query") ConfigQueryForm queryForm);

  /** 根据key查询获取数据 */
  ConfigEntity selectByKey(String key);
}
