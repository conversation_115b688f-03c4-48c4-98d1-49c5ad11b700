package com.anxys.system.support.reload.core;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.anxys.system.support.reload.core.domain.ReloadItem;
import com.anxys.system.support.reload.core.domain.ReloadObject;
import com.anxys.system.support.reload.core.domain.ReloadResult;

import lombok.Getter;
import lombok.Setter;

/** 检测是否 Reload 的类 */
public abstract class AbstractReloadCommand {

  /**
   * 当前ReloadItem的存储器 -- GETTER -- 获取本地缓存tag标识
   *
   * @return
   */
  @Getter private ConcurrentHashMap<String, String> tagIdentifierMap = new ConcurrentHashMap<>();

  /** -- SETTER -- */
  @Setter private ReloadManager reloadManager;

  public void init() {
    List<ReloadItem> reloadItems = this.readReloadItem();
    if (reloadItems != null) {
      for (ReloadItem reloadItem : reloadItems) {
        tagIdentifierMap.put(reloadItem.getTag(), reloadItem.getIdentification());
      }
    }
  }

  /**
   * 该方法返回一个List<ReloadItem></>:<br>
   * ReloadItem对象的tagIdentify为：该tag的 状态（状态其实就是个字符串，如果该字符串跟上次有变化则进行reload操作）<br>
   * ReloadItem对象的args为： reload操作需要的参数<br>
   * <br>
   *
   * @return List<ReloadItem>
   */
  public abstract List<ReloadItem> readReloadItem();

  /**
   * 处理Reload结果
   *
   * @param reloadResult
   */
  public abstract void handleReloadResult(ReloadResult reloadResult);

  /**
   * 设置新的缓存标识
   *
   * @param tag
   * @param identification
   */
  public void putIdentifierMap(String tag, String identification) {
    tagIdentifierMap.put(tag, identification);
  }

  /**
   * 获取重载对象
   *
   * @return
   */
  public ReloadObject reloadObject(String tag) {
    if (this.reloadManager == null) {
      return null;
    }
    Map<String, ReloadObject> reloadObjectMap = reloadManager.reloadObjectMap();
    return reloadObjectMap.get(tag);
  }
}
