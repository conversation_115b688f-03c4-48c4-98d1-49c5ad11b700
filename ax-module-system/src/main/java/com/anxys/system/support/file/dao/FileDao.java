package com.anxys.system.support.file.dao;

import java.util.Collection;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.anxys.system.support.file.domain.entity.FileEntity;
import com.anxys.system.support.file.domain.form.FileQueryForm;
import com.anxys.system.support.file.domain.vo.FileVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/** 文件服务 */
@Mapper
public interface FileDao extends BaseMapper<FileEntity> {

  /**
   * 文件key单个查询
   *
   * @param fileKey
   * @return
   */
  FileVO getByFileKey(@Param("fileKey") String fileKey);

  /** 批量获取 */
  List<FileVO> selectByFileKeyList(@Param("fileKeyList") Collection<String> fileKeyList);

  /**
   * 分页 查询
   *
   * @param page
   * @param queryForm
   * @return
   */
  List<FileVO> queryPage(Page<FileVO> page, @Param("queryForm") FileQueryForm queryForm);
}
