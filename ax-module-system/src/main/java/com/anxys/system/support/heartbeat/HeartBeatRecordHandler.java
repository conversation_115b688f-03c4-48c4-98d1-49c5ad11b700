package com.anxys.system.support.heartbeat;

import org.springframework.stereotype.Service;

import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.system.support.heartbeat.core.HeartBeatRecord;
import com.anxys.system.support.heartbeat.core.IHeartBeatRecordHandler;
import com.anxys.system.support.heartbeat.domain.HeartBeatRecordEntity;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/** 心跳记录 */
@Slf4j
@Service
public class HeartBeatRecordHandler implements IHeartBeatRecordHandler {

  @Resource private HeartBeatRecordDao heartBeatRecordDao;

  /**
   * 心跳日志处理方法
   *
   * @param heartBeatRecord
   */
  @Override
  public void handler(HeartBeatRecord heartBeatRecord) {
    HeartBeatRecordEntity heartBeatRecordEntity =
        BeanUtil.copy(heartBeatRecord, HeartBeatRecordEntity.class);
    HeartBeatRecordEntity heartBeatRecordOld = heartBeatRecordDao.query(heartBeatRecordEntity);
    if (heartBeatRecordOld == null) {
      heartBeatRecordDao.insert(heartBeatRecordEntity);
    } else {
      heartBeatRecordDao.updateHeartBeatTimeById(
          heartBeatRecordOld.getHeartBeatRecordId(), heartBeatRecordEntity.getHeartBeatTime());
    }
  }
}
