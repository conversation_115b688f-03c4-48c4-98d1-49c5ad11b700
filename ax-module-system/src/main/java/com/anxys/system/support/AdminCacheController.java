package com.anxys.system.support;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.constant.SwaggerTagConst;
import com.anxys.framework.controller.SupportBaseController;
import com.anxys.system.support.cache.CacheService;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

/** 缓存 */
@RestController
@Tag(name = SwaggerTagConst.Support.CACHE)
public class AdminCacheController extends SupportBaseController {

  @Resource private CacheService cacheService;

  @Operation(summary = "获取所有缓存 ")
  @GetMapping("/cache/names")
  @SaCheckPermission("support:cache:keys")
  public HttpResponse<List<String>> cacheNames() {
    return HttpResponse.ok(cacheService.cacheNames());
  }

  @Operation(summary = "移除某个缓存 ")
  @GetMapping("/cache/remove/{cacheName}")
  @SaCheckPermission("support:cache:delete")
  public HttpResponse<String> removeCache(@PathVariable String cacheName) {
    cacheService.removeCache(cacheName);
    return HttpResponse.ok();
  }

  @Operation(summary = "获取某个缓存的所有key ")
  @GetMapping("/cache/keys/{cacheName}")
  @SaCheckPermission("support:cache:keys")
  public HttpResponse<List<String>> cacheKeys(@PathVariable String cacheName) {
    return HttpResponse.ok(cacheService.cacheKey(cacheName));
  }
}
