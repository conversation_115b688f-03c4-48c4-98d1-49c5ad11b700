package com.anxys.system.support;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.constant.SwaggerTagConst;
import com.anxys.framework.controller.SupportBaseController;
import com.anxys.system.support.config.ConfigKeyEnum;
import com.anxys.system.support.config.ConfigService;
import com.anxys.system.support.securityprotect.domain.Level3ProtectConfigForm;
import com.anxys.system.support.securityprotect.domain.LoginFailQueryForm;
import com.anxys.system.support.securityprotect.domain.LoginFailVO;
import com.anxys.system.support.securityprotect.service.Level3ProtectConfigService;
import com.anxys.system.support.securityprotect.service.SecurityLoginService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/** 网络安全 */
@RestController
@Tag(name = SwaggerTagConst.Support.PROTECT)
public class AdminProtectController extends SupportBaseController {

  @Resource private SecurityLoginService securityLoginService;

  @Resource private Level3ProtectConfigService level3ProtectConfigService;

  @Resource private ConfigService configService;

  @Operation(summary = "分页查询 ")
  @PostMapping("/protect/loginFail/queryPage")
  public HttpResponse<PageResult<LoginFailVO>> queryPage(
      @RequestBody @Valid LoginFailQueryForm queryForm) {
    return HttpResponse.ok(securityLoginService.queryPage(queryForm));
  }

  @Operation(summary = "批量删除 ")
  @PostMapping("/protect/loginFail/batchDelete")
  public HttpResponse<String> batchDelete(@RequestBody ValidateList<Long> idList) {
    return securityLoginService.batchDelete(idList);
  }

  @Operation(summary = "更新三级等保配置 ")
  @PostMapping("/protect/level3protect/updateConfig")
  public HttpResponse<String> updateConfig(@RequestBody @Valid Level3ProtectConfigForm configForm) {
    return level3ProtectConfigService.updateLevel3Config(configForm);
  }

  @Operation(summary = "查询 三级等保配置 ")
  @GetMapping("/protect/level3protect/getConfig")
  public HttpResponse<String> getConfig() {
    return HttpResponse.ok(configService.getConfigValue(ConfigKeyEnum.LEVEL3_PROTECT_CONFIG));
  }
}
