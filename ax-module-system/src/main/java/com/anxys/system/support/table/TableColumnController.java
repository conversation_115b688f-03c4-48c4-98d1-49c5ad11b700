package com.anxys.system.support.table;

import org.springframework.web.bind.annotation.*;

import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.utils.RequestUtil;
import com.anxys.framework.constant.SwaggerTagConst;
import com.anxys.framework.controller.SupportBaseController;
import com.anxys.system.support.repeatsubmit.annoation.RepeatSubmit;
import com.anxys.system.support.table.domain.TableColumnUpdateForm;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/** 表格自定义列（前端用户自定义表格列，并保存到数据库里） */
@RestController
@Tag(name = SwaggerTagConst.Support.TABLE_COLUMN)
public class TableColumnController extends SupportBaseController {

  @Resource private TableColumnService tableColumnService;

  @Operation(summary = "修改表格列 ")
  @PostMapping("/tableColumn/update")
  @RepeatSubmit
  public HttpResponse<String> updateTableColumn(
      @RequestBody @Valid TableColumnUpdateForm updateForm) {
    return tableColumnService.updateTableColumns(RequestUtil.getRequestUser(), updateForm);
  }

  @Operation(summary = "恢复默认（删除） ")
  @GetMapping("/tableColumn/delete/{tableId}")
  @RepeatSubmit
  public HttpResponse<String> deleteTableColumn(@PathVariable Integer tableId) {
    return tableColumnService.deleteTableColumn(RequestUtil.getRequestUser(), tableId);
  }

  @Operation(summary = "查询表格列 ")
  @GetMapping("/tableColumn/getColumns/{tableId}")
  public HttpResponse<String> getColumns(@PathVariable Integer tableId) {
    return HttpResponse.ok(
        tableColumnService.getTableColumns(RequestUtil.getRequestUser(), tableId));
  }
}
