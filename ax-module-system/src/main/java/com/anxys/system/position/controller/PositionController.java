package com.anxys.system.position.controller;

import java.util.List;

import org.springframework.web.bind.annotation.*;

import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.constant.AdminSwaggerTagConst;
import com.anxys.system.position.domain.form.PositionAddForm;
import com.anxys.system.position.domain.form.PositionQueryForm;
import com.anxys.system.position.domain.form.PositionUpdateForm;
import com.anxys.system.position.domain.vo.PositionVO;
import com.anxys.system.position.service.PositionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/** 职务表 Controller */
@RestController
@Tag(name = AdminSwaggerTagConst.System.SYSTEM_POSITION)
public class PositionController {

  @Resource private PositionService positionService;

  @Operation(summary = "分页查询 ")
  @PostMapping("/position/queryPage")
  public HttpResponse<PageResult<PositionVO>> queryPage(
      @RequestBody @Valid PositionQueryForm queryForm) {
    return HttpResponse.ok(positionService.queryPage(queryForm));
  }

  @Operation(summary = "添加 ")
  @PostMapping("/position/add")
  public HttpResponse<String> add(@RequestBody @Valid PositionAddForm addForm) {
    return positionService.add(addForm);
  }

  @Operation(summary = "更新 ")
  @PostMapping("/position/update")
  public HttpResponse<String> update(@RequestBody @Valid PositionUpdateForm updateForm) {
    return positionService.update(updateForm);
  }

  @Operation(summary = "批量删除 ")
  @PostMapping("/position/batchDelete")
  public HttpResponse<String> batchDelete(@RequestBody ValidateList<Long> idList) {
    return positionService.batchDelete(idList);
  }

  @Operation(summary = "单个删除 ")
  @GetMapping("/position/delete/{positionId}")
  public HttpResponse<String> batchDelete(@PathVariable Long positionId) {
    return positionService.delete(positionId);
  }

  @Operation(summary = "不分页查询 ")
  @GetMapping("/position/queryList")
  public HttpResponse<List<PositionVO>> queryList() {
    return HttpResponse.ok(positionService.queryList());
  }
}
