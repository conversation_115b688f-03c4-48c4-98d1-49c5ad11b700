package com.anxys.erp.order.salesOrderItem.domain.form;

import com.anxys.erp.order.salesOrderItem.domain.SalesOrderItem;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单明细更新表单
 *
 * @since 2025-06-09 16:38:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SalesOrderItemUpdateParam extends SalesOrderItem {

  @Schema(description = "ID")
  @NotNull(message = "ID不能为空") private Long id;
}
