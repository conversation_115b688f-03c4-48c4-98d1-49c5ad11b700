package com.anxys.erp.order.salesOrder.domain.form;

import com.anxys.framework.common.domain.PageParam;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单信息分页查询表单
 *
 * @since 2025-06-07 16:01:33
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class SalesOrderPageParam extends SalesOrderQueryParam {

  @Schema(description = "分页参数")
  @Valid
  @NotNull(message = "分页参数不能为空") PageParam pageParam = new PageParam();
}
