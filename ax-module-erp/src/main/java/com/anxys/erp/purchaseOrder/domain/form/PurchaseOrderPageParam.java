package com.anxys.erp.purchaseOrder.domain.form;

import com.anxys.framework.common.domain.PageParam;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购订单信息分页查询表单
 *
 * @since 2025-06-10 11:06:16
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class PurchaseOrderPageParam extends PurchaseOrderQueryParam {

  @Schema(description = "分页参数")
  @Valid
  @NotNull(message = "分页参数不能为空") PageParam pageParam = new PageParam();
}
