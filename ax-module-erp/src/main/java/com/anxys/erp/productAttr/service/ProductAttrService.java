package com.anxys.erp.productAttr.service;

import java.io.IOException;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.anxys.erp.productAttr.dao.ProductAttrMapper;
import com.anxys.erp.productAttr.domain.ProductAttr;
import com.anxys.erp.productAttr.domain.excel.ProductAttrExcel;
import com.anxys.erp.productAttr.domain.form.ProductAttrAddParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrPageParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrQueryParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrUpdateForm;
import com.anxys.erp.productAttr.domain.vo.ProductAttrVO;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.idev.excel.FastExcel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品属性服务层
 *
 * @since 2025-03-30 14:41:45
 */
@Slf4j
@Service
public class ProductAttrService {

  @Resource private ProductAttrMapper productAttrMapper;

  /**
   * 分页查询
   *
   * @param queryForm 查询参数
   * @return 分页结果
   */
  public PageResult<ProductAttrVO> page(ProductAttrPageParam queryForm) {
    Page<ProductAttrVO> page = PageUtil.convert2PageQuery(queryForm.getPageParam());
    List<ProductAttrVO> list = productAttrMapper.page(page, queryForm);
    return PageUtil.convert2PageResult(page, list);
  }

  /**
   * 列表查询
   *
   * @param queryForm 查询参数
   * @return 列表结果
   */
  public List<ProductAttrVO> list(ProductAttrQueryParam queryForm) {
    return productAttrMapper.list(queryForm);
  }

  /**
   * 添加
   *
   * @param addForm 添加参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void insert(ProductAttrAddParam addForm) {
    ProductAttr productAttr = BeanUtil.copy(addForm, ProductAttr.class);
    productAttrMapper.insert(productAttr);
  }

  /**
   * 更新
   *
   * @param updateForm 更新参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void update(ProductAttrUpdateForm updateForm) {
    ProductAttr productAttr = BeanUtil.copy(updateForm, ProductAttr.class);
    productAttrMapper.updateById(productAttr);
  }

  /**
   * 批量删除
   *
   * @param idList ID列表
   */
  @Transactional(rollbackFor = Exception.class)
  public void batchDelete(List<Long> idList) {
    if (!idList.isEmpty()) {
      productAttrMapper.batchUpdateDeleted(idList, true);
    }
  }

  /**
   * 单个删除
   *
   * @param id ID
   */
  @Transactional(rollbackFor = Exception.class)
  public void delete(Long id) {
    productAttrMapper.deleteById(id);
  }

  /**
   * 通过ID查询
   *
   * @param id ID
   * @return 详情
   */
  public ProductAttrVO queryById(Long id) {
    ProductAttr productAttr = productAttrMapper.selectById(id);
    if (productAttr == null) {
      throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
    }
    return BeanUtil.copy(productAttr, ProductAttrVO.class);
  }

  /**
   * 导出Excel
   *
   * @param queryForm 查询参数
   * @return Excel数据列表
   */
  public List<ProductAttrExcel> exportExcel(ProductAttrQueryParam queryForm) {
    return productAttrMapper.exportExcel(queryForm);
  }

  /**
   * 导入Excel
   *
   * @param file Excel文件
   * @return 导入的数据条数
   */
  @Transactional(rollbackFor = Exception.class)
  public int importExcel(MultipartFile file) {
    List<ProductAttrExcel> dataList;
    try {
      dataList =
          FastExcel.read(file.getInputStream()).head(ProductAttrExcel.class).sheet().doReadSync();
    } catch (IOException e) {
      log.error("Excel导入失败", e);
      throw new BusinessException("数据格式存在问题，无法读取");
    }

    if (CollectionUtils.isEmpty(dataList)) {
      throw new BusinessException("数据为空");
    }

    // 处理导入数据
    log.info("开始处理导入数据，共 {} 条", dataList.size());
    List<ProductAttr> entityList =
        dataList.stream()
            .map(
                excel -> {
                  log.info("处理数据: {}", excel);
                  return BeanUtil.copy(excel, ProductAttr.class);
                })
            .toList();

    productAttrMapper.batchInsert(entityList);

    return dataList.size();
  }
}
