package com.anxys.erp.productAttr.controller;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.anxys.erp.productAttr.domain.excel.ProductAttrExcel;
import com.anxys.erp.productAttr.domain.form.ProductAttrAddParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrPageParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrQueryParam;
import com.anxys.erp.productAttr.domain.form.ProductAttrUpdateForm;
import com.anxys.erp.productAttr.domain.vo.ProductAttrVO;
import com.anxys.erp.productAttr.service.ProductAttrService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品属性控制器
 *
 * @since 2025-03-30 14:41:45
 */
@Slf4j
@RestController
@RequestMapping("/productAttr")
@Tag(name = "商品属性接口")
public class ProductAttrController {

  @Resource private ProductAttrService productAttrService;

  @Operation(summary = "商品属性分页查询")
  @PostMapping("/productAttrPage")
  @SaCheckPermission("product-attr:query")
  public HttpResponse<PageResult<ProductAttrVO>> query(
      @RequestBody @Valid ProductAttrPageParam queryForm) {
    PageResult<ProductAttrVO> pageResult = productAttrService.page(queryForm);
    return HttpResponse.ok(pageResult);
  }

  @Operation(summary = "商品属性列表查询")
  @PostMapping("/productAttrList")
  @SaCheckPermission("product-attr:query")
  public HttpResponse<List<ProductAttrVO>> list(
      @RequestBody @Valid ProductAttrQueryParam queryForm) {
    List<ProductAttrVO> list = productAttrService.list(queryForm);
    return HttpResponse.ok(list);
  }

  @Operation(summary = "获取商品属性详情")
  @GetMapping("/productAttrDetail/{id}")
  @SaCheckPermission("product-attr:query")
  public HttpResponse<ProductAttrVO> detail(@PathVariable Long id) {
    ProductAttrVO productAttrVO = productAttrService.queryById(id);
    return HttpResponse.ok(productAttrVO);
  }

  @Operation(summary = "添加商品属性")
  @PostMapping("/addProductAttr")
  @SaCheckPermission("product-attr:add")
  public HttpResponse<String> insert(@RequestBody @Valid ProductAttrAddParam addForm) {
    productAttrService.insert(addForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "更新商品属性")
  @PostMapping("/updateProductAttr")
  @SaCheckPermission("product-attr:update")
  public HttpResponse<String> update(@RequestBody @Valid ProductAttrUpdateForm updateForm) {
    productAttrService.update(updateForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "删除商品属性")
  @GetMapping("/deleteProductAttr/{id}")
  @SaCheckPermission("product-attr:delete")
  public HttpResponse<String> delete(@PathVariable Long id) {
    productAttrService.delete(id);
    return HttpResponse.ok();
  }

  @Operation(summary = "批量删除商品属性")
  @PostMapping("/batchDeleteProductAttr")
  @SaCheckPermission("product-attr:delete")
  public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
    productAttrService.batchDelete(idList);
    return HttpResponse.ok();
  }

  @Operation(summary = "导出商品属性Excel")
  @GetMapping("/exportProductAttrExcel")
  @SaCheckPermission("product-attr:export")
  public void exportExcel(
      @Parameter(description = "查询参数") ProductAttrQueryParam queryForm,
      HttpServletResponse response)
      throws IOException {
    List<ProductAttrExcel> list = productAttrService.exportExcel(queryForm);
    // 添加水印
    String watermark = AdminRequestUtil.getRequestUser().getActualName();
    watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

    ExcelUtil.exportExcelWithWatermark(
        response, "商品属性列表.xlsx", "商品属性", ProductAttrExcel.class, list, watermark);
  }

  @Operation(summary = "导入商品属性Excel")
  @PostMapping("/importProductAttrExcel")
  @SaCheckPermission("product-attr:import")
  public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
    int count = productAttrService.importExcel(file);
    return HttpResponse.okMsg("成功导入" + count + "条数据");
  }
}
