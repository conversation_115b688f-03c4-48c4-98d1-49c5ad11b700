package com.anxys.erp.skuSpecRelation.dao;

import com.anxys.erp.skuSpecRelation.domain.SkuSpecRelation;
import com.anxys.erp.skuSpecRelation.domain.excel.SkuSpecRelationExcel;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationPageParam;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationQueryParam;
import com.anxys.erp.skuSpecRelation.domain.vo.SkuSpecRelationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SKU规格值关联数据库访问层
 *
 * @since 2025-03-30 14:40:34
 */
@Mapper
public interface SkuSpecRelationMapper extends BaseMapper<SkuSpecRelation> {

  /**
   * 分页查询
   *
   * @param page 分页参数
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SkuSpecRelationVO> page(
      Page<SkuSpecRelationVO> page, @Param("param") SkuSpecRelationPageParam queryForm);

  /**
   * 查询列表
   *
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SkuSpecRelationVO> list(@Param("param") SkuSpecRelationQueryParam queryForm);

  /** 批量更新删除状态 */
  void batchUpdateDeleted(
      @Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);


  /**
   * 导出Excel
   *
   * @param queryForm 查询条件
   * @return 对象列表
   */
  List<SkuSpecRelationExcel> exportExcel(@Param("query") SkuSpecRelationQueryParam queryForm);

  void batchInsert(@Param("list") List<SkuSpecRelation> entityList);
}
