package com.anxys.erp.skuSpecRelation.controller;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.anxys.erp.skuSpecRelation.domain.excel.SkuSpecRelationExcel;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationAddParam;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationPageParam;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationQueryParam;
import com.anxys.erp.skuSpecRelation.domain.form.SkuSpecRelationUpdateForm;
import com.anxys.erp.skuSpecRelation.domain.vo.SkuSpecRelationVO;
import com.anxys.erp.skuSpecRelation.service.SkuSpecRelationService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * SKU规格值关联控制器
 *
 * @since 2025-03-30 14:40:34
 */
@Slf4j
@RestController
@RequestMapping("/skuSpecRelation")
@Tag(name = "SKU规格值关联接口")
public class SkuSpecRelationController {

  @Resource private SkuSpecRelationService skuSpecRelationService;

  @Operation(summary = "SKU规格值关联分页查询")
  @PostMapping("/skuSpecRelationPage")
  @SaCheckPermission("sku-spec-relation:query")
  public HttpResponse<PageResult<SkuSpecRelationVO>> query(
      @RequestBody @Valid SkuSpecRelationPageParam queryForm) {
    PageResult<SkuSpecRelationVO> pageResult = skuSpecRelationService.page(queryForm);
    return HttpResponse.ok(pageResult);
  }

  @Operation(summary = "SKU规格值关联列表查询")
  @PostMapping("/skuSpecRelationList")
  @SaCheckPermission("sku-spec-relation:query")
  public HttpResponse<List<SkuSpecRelationVO>> list(
      @RequestBody @Valid SkuSpecRelationQueryParam queryForm) {
    List<SkuSpecRelationVO> list = skuSpecRelationService.list(queryForm);
    return HttpResponse.ok(list);
  }

  @Operation(summary = "获取SKU规格值关联详情")
  @GetMapping("/skuSpecRelationDetail/{id}")
  @SaCheckPermission("sku-spec-relation:query")
  public HttpResponse<SkuSpecRelationVO> detail(@PathVariable Long id) {
    SkuSpecRelationVO skuSpecRelationVO = skuSpecRelationService.queryById(id);
    return HttpResponse.ok(skuSpecRelationVO);
  }

  @Operation(summary = "添加SKU规格值关联")
  @PostMapping("/addSkuSpecRelation")
  @SaCheckPermission("sku-spec-relation:add")
  public HttpResponse<String> insert(@RequestBody @Valid SkuSpecRelationAddParam addForm) {
    skuSpecRelationService.insert(addForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "更新SKU规格值关联")
  @PostMapping("/updateSkuSpecRelation")
  @SaCheckPermission("sku-spec-relation:update")
  public HttpResponse<String> update(@RequestBody @Valid SkuSpecRelationUpdateForm updateForm) {
    skuSpecRelationService.update(updateForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "删除SKU规格值关联")
  @GetMapping("/deleteSkuSpecRelation/{id}")
  @SaCheckPermission("sku-spec-relation:delete")
  public HttpResponse<String> delete(@PathVariable Long id) {
    skuSpecRelationService.delete(id);
    return HttpResponse.ok();
  }

  @Operation(summary = "批量删除SKU规格值关联")
  @PostMapping("/batchDeleteSkuSpecRelation")
  @SaCheckPermission("sku-spec-relation:delete")
  public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
    skuSpecRelationService.batchDelete(idList);
    return HttpResponse.ok();
  }

  @Operation(summary = "导出SKU规格值关联Excel")
  @GetMapping("/exportSkuSpecRelationExcel")
  @SaCheckPermission("sku-spec-relation:export")
  public void exportExcel(
      @Parameter(description = "查询参数") SkuSpecRelationQueryParam queryForm,
      HttpServletResponse response)
      throws IOException {
    List<SkuSpecRelationExcel> list = skuSpecRelationService.exportExcel(queryForm);
    // 添加水印
    String watermark = AdminRequestUtil.getRequestUser().getActualName();
    watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

    ExcelUtil.exportExcelWithWatermark(
        response, "SKU规格值关联列表.xlsx", "SKU规格值关联", SkuSpecRelationExcel.class, list, watermark);
  }

  @Operation(summary = "导入SKU规格值关联Excel")
  @PostMapping("/importSkuSpecRelationExcel")
  @SaCheckPermission("sku-spec-relation:import")
  public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
    int count = skuSpecRelationService.importExcel(file);
    return HttpResponse.okMsg("成功导入" + count + "条数据");
  }
}
