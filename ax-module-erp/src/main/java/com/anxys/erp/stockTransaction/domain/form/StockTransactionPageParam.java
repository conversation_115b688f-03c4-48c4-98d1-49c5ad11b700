package com.anxys.erp.stockTransaction.domain.form;


import com.anxys.framework.common.domain.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 库存流水分页查询表单
 *
 * @since 2025-06-09 16:59:21
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class StockTransactionPageParam extends StockTransactionQueryParam {

    @Schema(description = "分页参数")
    @Valid
    @NotNull(message = "分页参数不能为空")
    PageParam pageParam = new PageParam();

}

