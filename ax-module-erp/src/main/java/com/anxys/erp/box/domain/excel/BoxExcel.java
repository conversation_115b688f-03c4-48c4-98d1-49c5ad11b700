package com.anxys.erp.box.domain.excel;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 纸箱箱形Excel导出VO
 *
 * @since 2025-04-03 16:03:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BoxExcel implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @ExcelProperty("箱型编码")
  private String boxTypeCode;

  @ExcelProperty("箱型名称")
  private String boxTypeName;

  @ExcelProperty("箱型描述")
  private String boxTypeDesc;

  @ExcelProperty("箱型图片URL")
  private String imageUrl;

  @ExcelProperty("是否启用(1启用/0禁用)")
  private Boolean isActive;

  @ExcelProperty("参数JSON")
  private String paramsJson;

  @ExcelProperty("排序")
  private Integer sort;

  @ExcelProperty("创建人")
  private Long createdBy;

  @ExcelProperty("更新人")
  private Long updatedBy;

  @ExcelProperty("创建时间")
  private LocalDateTime createTime;

  @ExcelProperty("更新时间")
  private LocalDateTime updateTime;
}
