package com.anxys.erp.box.domain.form;

import java.time.LocalDateTime;

import com.anxys.erp.box.domain.Box;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 纸箱箱形查询表单
 *
 * @since 2025-04-03 16:03:21
 */
@EqualsAndHashCode(callSuper = false)
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BoxQueryParam extends Box {

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Schema(description = "创建时间始")
  private LocalDateTime createTimeFrom;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Schema(description = "创建时间至")
  private LocalDateTime createTimeTo;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Schema(description = "更新时间始")
  private LocalDateTime updateTimeFrom;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @Schema(description = "更新时间至")
  private LocalDateTime updateTimeTo;
}
