package com.anxys.erp.box.domain.form;

import com.anxys.framework.common.domain.PageParam;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 纸箱箱形分页查询表单
 *
 * @since 2025-04-03 16:03:21
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class BoxPageParam extends BoxQueryParam {

  @Schema(description = "分页参数")
  @Valid
  @NotNull(message = "分页参数不能为空")
  PageParam pageParam = new PageParam();
}
