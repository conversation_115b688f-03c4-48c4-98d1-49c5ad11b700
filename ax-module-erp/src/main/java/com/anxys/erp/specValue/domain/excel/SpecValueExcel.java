package com.anxys.erp.specValue.domain.excel;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品规格值Excel导出VO
 *
 * @since 2025-03-30 14:41:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpecValueExcel implements Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @ExcelProperty("规格ID | erp_spec的id")
  private Long specId;

  @ExcelProperty("规格值")
  private String value;

  @ExcelProperty("排序")
  private Integer sort;

  @ExcelProperty("字典 | 规格值状态：0-禁用，1-启用")
  private Boolean status;

  @ExcelProperty("创建时间")
  private LocalDateTime createTime;

  @ExcelProperty("更新时间")
  private LocalDateTime updateTime;

  @ExcelProperty("创建人")
  private Long createdBy;

  @ExcelProperty("更新人")
  private Long updatedBy;
}
