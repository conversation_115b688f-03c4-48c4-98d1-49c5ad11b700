package com.anxys.erp.stockTransactionItem.dao;

import com.anxys.erp.stockTransactionItem.domain.StockTransactionItem;
import com.anxys.erp.stockTransactionItem.domain.excel.StockTransactionItemExcel;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemPageParam;
import com.anxys.erp.stockTransactionItem.domain.form.StockTransactionItemQueryParam;
import com.anxys.erp.stockTransactionItem.domain.vo.StockTransactionItemVO;
import com.anxys.framework.mybatis.mapper.BaseMapperX;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 库存流水明细表数据库访问层
 * @since 2025-06-11 15:07:25
 *
 */
@Mapper
public interface StockTransactionItemMapper extends BaseMapperX<StockTransactionItem> {

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @param queryParam 查询参数
     * @return 对象列表
     */
    List<StockTransactionItemVO> page(Page page, @Param("param") StockTransactionItemPageParam queryParam);


    /**
    * 查询列表
    * @param queryParam 查询参数
    * @return 对象列表
    *
    */
    List<StockTransactionItemVO> list(@Param("param") StockTransactionItemQueryParam queryParam);

    /**
     * 批量更新删除状态
     */
    void batchUpdateDeleted(@Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量插入
     */
    void batchInsert(List<StockTransactionItem> entityList);


    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);


    /**
     * 导出Excel
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<StockTransactionItemExcel> exportExcel(@Param("query") StockTransactionItemQueryParam queryParam);

    /**
     * 根据流水主表ID查询明细列表
     *
     * @param transactionId 流水主表ID
     * @return 明细对象列表
     */
    List<StockTransactionItem> selectByTransactionId(@Param("transactionId") Long transactionId);
}

