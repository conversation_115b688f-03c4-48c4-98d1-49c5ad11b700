package com.anxys.erp.customer.domain.form;

import com.anxys.erp.customer.domain.Customer;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * 客户信息添加表单
 *
 * @since 2025-03-28 00:48:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerAddParam extends Customer {

  @NotBlank(message = "客户名称不能为空")
  private String customerName;

  @NotBlank(message = "字典|customer_type 客户类型：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商不能为空")
  private String customerType;

  @NotBlank(message = "字典|customer_status 客户状态：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户不能为空")
  private String status;

  @Schema(description = "地址信息列表")
  @NotEmpty(message = "地址信息列表不能为空")
  @Valid
  private List<Map<String, Object>> addressInfoList;

  @Schema(description = "支付信息列表")
  @NotEmpty(message = "支付信息列表不能为空")
  @Valid
  private List<Map<String, Object>> paymentInfoList;
}
