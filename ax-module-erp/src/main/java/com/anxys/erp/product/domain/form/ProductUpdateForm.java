package com.anxys.erp.product.domain.form;

import java.util.List;

import com.anxys.erp.product.domain.Product;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品基础信息更新表单
 *
 * @since 2025-03-30 14:36:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductUpdateForm extends Product {

  @Schema(description = "ID")
  @NotNull(message = "ID不能为空") private Long id;

  @NotBlank(message = "商品编码不能为空")
  @Size(max = 50, message = "商品编码最大长度为50")
  @Schema(description = "商品编码")
  private String productCode;

  @NotBlank(message = "商品名称不能为空")
  @Size(max = 100, message = "商品名称最大长度为100")
  @Schema(description = "商品名称")
  private String name;

  @Schema(description = "商品描述")
  @Size(max = 1000, message = "商品描述最大长度为1000")
  private String description;

  @NotBlank(message = "单位不能为空")
  @Size(max = 20, message = "单位最大长度为20")
  @Schema(description = "单位")
  private String unit;

  @NotNull(message = "主分类ID不能为空") @Schema(description = "主分类ID")
  private Long categoryId;

  @Schema(description = "品牌ID")
  private Long brandId;

  @NotNull(message = "商品状态不能为空") @Schema(description = "商品状态")
  private String status;

  @Schema(description = "商品来源")
  private String sourceType;

  @NotNull(message = "是否原材料不能为空") @Schema(description = "是否原材料")
  private Boolean isRawMaterial;

  @Min(value = 1, message = "最小起订量必须大于0")
  @Schema(description = "最小起订量")
  private Integer minOrderQty;

  @Schema(description = "额外分类ID列表")
  private List<Long> additionalCategoryIds;
}
