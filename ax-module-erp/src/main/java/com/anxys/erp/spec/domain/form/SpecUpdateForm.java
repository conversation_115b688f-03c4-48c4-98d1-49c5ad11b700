package com.anxys.erp.spec.domain.form;

import com.anxys.erp.spec.domain.Spec;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品规格更新表单
 *
 * @since 2025-03-30 14:41:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SpecUpdateForm extends Spec {

  @Schema(description = "ID")
  @NotNull(message = "ID不能为空") private Long id;
}
