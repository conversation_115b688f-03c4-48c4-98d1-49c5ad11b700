package com.anxys.erp.spec.dao;

import com.anxys.erp.spec.domain.Spec;
import com.anxys.erp.spec.domain.excel.SpecExcel;
import com.anxys.erp.spec.domain.form.SpecPageParam;
import com.anxys.erp.spec.domain.form.SpecQueryParam;
import com.anxys.erp.spec.domain.vo.SpecVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品规格数据库访问层
 *
 * @since 2025-03-30 14:41:29
 */
@Mapper
public interface SpecMapper extends BaseMapper<Spec> {

  /**
   * 分页查询
   *
   * @param page 分页参数
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SpecVO> page(Page<SpecVO> page, @Param("param") SpecPageParam queryForm);

  /**
   * 查询列表
   *
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SpecVO> list(@Param("param") SpecQueryParam queryForm);

  /** 批量更新删除状态 */
  void batchUpdateDeleted(
      @Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);


  /**
   * 批量插入
   *
   * @param entityList 实体列表
   * @return 影响行数
   */
  int batchInsert(@Param("entityList") List<Spec> entityList);

  /**
   * 导出Excel
   *
   * @param queryForm 查询条件
   * @return 对象列表
   */
  List<SpecExcel> exportExcel(@Param("query") SpecQueryParam queryForm);
}
