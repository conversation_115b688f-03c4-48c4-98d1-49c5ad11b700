package com.anxys.erp.boxQuotation.domain.vo;

import java.util.List;

import com.anxys.erp.boxQuotation.domain.BoxQuotation;
import com.anxys.erp.boxQuotation.domain.form.PaperBoard;
import com.anxys.erp.boxQuotation.domain.form.PrintingProcess;
import com.anxys.erp.boxQuotation.domain.form.RawPaper;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 纸箱报价VO
 *
 * @since 2025-03-25 12:58:48
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("erp_box_quotation")
public class BoxQuotationVO extends BoxQuotation {

  @Schema(description = "印刷工艺")
  private List<PrintingProcess> printingProcess;

  @Schema(description = "原纸")
  private List<RawPaper> rawPaper;

  @Schema(description = "纸板")
  private List<PaperBoard> paperBoard;
}
