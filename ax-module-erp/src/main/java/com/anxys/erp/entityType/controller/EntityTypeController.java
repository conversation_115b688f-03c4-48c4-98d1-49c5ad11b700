package com.anxys.erp.entityType.controller;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.anxys.erp.entityType.domain.excel.EntityTypeExcel;
import com.anxys.erp.entityType.domain.form.EntityTypeAddParam;
import com.anxys.erp.entityType.domain.form.EntityTypePageParam;
import com.anxys.erp.entityType.domain.form.EntityTypeQueryParam;
import com.anxys.erp.entityType.domain.form.EntityTypeUpdateParam;
import com.anxys.erp.entityType.domain.vo.EntityTypeVO;
import com.anxys.erp.entityType.service.EntityTypeService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;
import com.anxys.framework.common.utils.DateFormatterEnum;
import com.anxys.framework.common.utils.ExcelUtil;
import com.anxys.framework.common.utils.LocalDateUtil;
import com.anxys.system.utils.AdminRequestUtil;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 实体类型控制器
 *
 * @since 2025-03-31 10:27:04
 */
@Slf4j
@RestController
@RequestMapping("/entityType")
@Tag(name = "实体类型接口")
public class EntityTypeController {

  @Resource private EntityTypeService entityTypeService;

  @Operation(summary = "实体类型分页查询")
  @PostMapping("/entityTypePage")
  @SaCheckPermission("entity-type:query")
  public HttpResponse<PageResult<EntityTypeVO>> query(
      @RequestBody @Valid EntityTypePageParam queryParam) {
    PageResult<EntityTypeVO> pageResult = entityTypeService.page(queryParam);
    return HttpResponse.ok(pageResult);
  }

  @Operation(summary = "实体类型列表查询")
  @PostMapping("/entityTypeList")
  @SaCheckPermission("entity-type:query")
  public HttpResponse<List<EntityTypeVO>> list(
      @RequestBody @Valid EntityTypeQueryParam queryParam) {
    List<EntityTypeVO> list = entityTypeService.list(queryParam);
    return HttpResponse.ok(list);
  }

  @Operation(summary = "获取实体类型详情")
  @GetMapping("/entityTypeDetail/{id}")
  @SaCheckPermission("entity-type:query")
  public HttpResponse<EntityTypeVO> detail(@PathVariable Long id) {
    EntityTypeVO entityTypeVO = entityTypeService.queryById(id);
    return HttpResponse.ok(entityTypeVO);
  }

  @Operation(summary = "添加实体类型")
  @PostMapping("/addEntityType")
  @SaCheckPermission("entity-type:add")
  public HttpResponse<Long> insert(@RequestBody @Valid EntityTypeAddParam addParam) {
    return HttpResponse.ok(entityTypeService.insert(addParam));
  }

  @Operation(summary = "更新实体类型")
  @PostMapping("/updateEntityType")
  @SaCheckPermission("entity-type:update")
  public HttpResponse<String> update(@RequestBody @Valid EntityTypeUpdateParam updateParam) {
    entityTypeService.update(updateParam);
    return HttpResponse.ok();
  }

  @Operation(summary = "删除实体类型")
  @GetMapping("/deleteEntityType/{id}")
  @SaCheckPermission("entity-type:delete")
  public HttpResponse<String> delete(@PathVariable Long id) {
    entityTypeService.delete(id);
    return HttpResponse.ok();
  }

  @Operation(summary = "批量删除实体类型")
  @PostMapping("/batchDeleteEntityType")
  @SaCheckPermission("entity-type:delete")
  public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
    entityTypeService.batchDelete(idList);
    return HttpResponse.ok();
  }

  @Operation(summary = "导出实体类型Excel")
  @GetMapping("/exportEntityTypeExcel")
  @SaCheckPermission("entity-type:export")
  public void exportExcel(
      @Parameter(description = "查询参数") EntityTypeQueryParam queryParam,
      HttpServletResponse response)
      throws IOException {
    List<EntityTypeExcel> list = entityTypeService.exportExcel(queryParam);
    // 添加水印
    String watermark = AdminRequestUtil.getRequestUser().getActualName();
    watermark += LocalDateUtil.format(LocalDateTime.now(), DateFormatterEnum.YMD_HMS);

    ExcelUtil.exportExcelWithWatermark(
        response, "实体类型列表.xlsx", "实体类型", EntityTypeExcel.class, list, watermark);
  }

  @Operation(summary = "导入实体类型Excel")
  @PostMapping("/importEntityTypeExcel")
  @SaCheckPermission("entity-type:import")
  public HttpResponse<String> importExcel(@RequestParam("file") MultipartFile file) {
    int count = entityTypeService.importExcel(file);
    return HttpResponse.okMsg("成功导入" + count + "条数据");
  }
}
