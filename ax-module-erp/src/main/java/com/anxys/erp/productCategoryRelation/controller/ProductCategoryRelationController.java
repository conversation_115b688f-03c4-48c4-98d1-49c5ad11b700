package com.anxys.erp.productCategoryRelation.controller;

import java.util.List;

import org.springframework.web.bind.annotation.*;

import com.anxys.erp.productCategoryRelation.domain.form.ProductCategoryRelationAddParam;
import com.anxys.erp.productCategoryRelation.domain.form.ProductCategoryRelationPageParam;
import com.anxys.erp.productCategoryRelation.domain.form.ProductCategoryRelationQueryParam;
import com.anxys.erp.productCategoryRelation.domain.form.ProductCategoryRelationUpdateForm;
import com.anxys.erp.productCategoryRelation.domain.vo.ProductCategoryRelationVO;
import com.anxys.erp.productCategoryRelation.service.ProductCategoryRelationService;
import com.anxys.framework.common.domain.HttpResponse;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.domain.ValidateList;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品分类关联控制器
 *
 * @since 2025-03-30 14:42:00
 */
@Slf4j
@RestController
@RequestMapping("/productCategoryRelation")
@Tag(name = "商品分类关联接口")
public class ProductCategoryRelationController {

  @Resource private ProductCategoryRelationService productCategoryRelationService;

  @Operation(summary = "商品分类关联分页查询")
  @PostMapping("/productCategoryRelationPage")
  @SaCheckPermission("product-category-relation:query")
  public HttpResponse<PageResult<ProductCategoryRelationVO>> query(
      @RequestBody @Valid ProductCategoryRelationPageParam queryForm) {
    PageResult<ProductCategoryRelationVO> pageResult =
        productCategoryRelationService.page(queryForm);
    return HttpResponse.ok(pageResult);
  }

  @Operation(summary = "商品分类关联列表查询")
  @PostMapping("/productCategoryRelationList")
  @SaCheckPermission("product-category-relation:query")
  public HttpResponse<List<ProductCategoryRelationVO>> list(
      @RequestBody @Valid ProductCategoryRelationQueryParam queryForm) {
    List<ProductCategoryRelationVO> list = productCategoryRelationService.list(queryForm);
    return HttpResponse.ok(list);
  }

  @Operation(summary = "获取商品分类关联详情")
  @GetMapping("/productCategoryRelationDetail/{id}")
  @SaCheckPermission("product-category-relation:query")
  public HttpResponse<ProductCategoryRelationVO> detail(@PathVariable Long id) {
    ProductCategoryRelationVO productCategoryRelationVO =
        productCategoryRelationService.queryById(id);
    return HttpResponse.ok(productCategoryRelationVO);
  }

  @Operation(summary = "添加商品分类关联")
  @PostMapping("/addProductCategoryRelation")
  @SaCheckPermission("product-category-relation:add")
  public HttpResponse<String> insert(@RequestBody @Valid ProductCategoryRelationAddParam addForm) {
    productCategoryRelationService.insert(addForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "更新商品分类关联")
  @PostMapping("/updateProductCategoryRelation")
  @SaCheckPermission("product-category-relation:update")
  public HttpResponse<String> update(
      @RequestBody @Valid ProductCategoryRelationUpdateForm updateForm) {
    productCategoryRelationService.update(updateForm);
    return HttpResponse.ok();
  }

  @Operation(summary = "删除商品分类关联")
  @GetMapping("/deleteProductCategoryRelation/{id}")
  @SaCheckPermission("product-category-relation:delete")
  public HttpResponse<String> delete(@PathVariable Long id) {
    productCategoryRelationService.delete(id);
    return HttpResponse.ok();
  }

  @Operation(summary = "批量删除商品分类关联")
  @PostMapping("/batchDeleteProductCategoryRelation")
  @SaCheckPermission("product-category-relation:delete")
  public HttpResponse<String> batchDelete(@RequestBody @Valid ValidateList<Long> idList) {
    productCategoryRelationService.batchDelete(idList);
    return HttpResponse.ok();
  }
}
