package com.anxys.erp.productCategoryRelation.domain.form;

import com.anxys.erp.productCategoryRelation.domain.ProductCategoryRelation;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品分类关联更新表单
 *
 * @since 2025-03-30 14:42:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductCategoryRelationUpdateForm extends ProductCategoryRelation {

  @Schema(description = "ID")
  @NotNull(message = "ID不能为空") private Long id;
}
