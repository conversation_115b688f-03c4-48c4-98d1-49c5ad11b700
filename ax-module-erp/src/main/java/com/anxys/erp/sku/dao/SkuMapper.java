package com.anxys.erp.sku.dao;

import com.anxys.erp.sku.domain.Sku;
import com.anxys.erp.sku.domain.excel.SkuExcel;
import com.anxys.erp.sku.domain.form.SkuPageParam;
import com.anxys.erp.sku.domain.form.SkuQueryParam;
import com.anxys.erp.sku.domain.vo.SkuVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品SKU数据库访问层
 *
 * @since 2025-03-30 14:43:49
 */
@Mapper
public interface SkuMapper extends BaseMapper<Sku> {

  /**
   * 分页查询
   *
   * @param page 分页参数
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SkuVO> page(Page<SkuVO> page, @Param("param") SkuPageParam queryForm);

  /**
   * 查询列表
   *
   * @param queryForm 查询表单
   * @return 对象列表
   */
  List<SkuVO> list(@Param("param") SkuQueryParam queryForm);

  /** 批量更新删除状态 */
  void batchUpdateDeleted(
      @Param("idList") List<Long> idList, @Param("deletedFlag") Boolean deletedFlag);


  /**
   * 导出Excel
   *
   * @param queryForm 查询条件
   * @return 对象列表
   */
  List<SkuExcel> exportExcel(@Param("query") SkuQueryParam queryForm);

  void batchInsert(@Param("list") List<Sku> entityList);
}
