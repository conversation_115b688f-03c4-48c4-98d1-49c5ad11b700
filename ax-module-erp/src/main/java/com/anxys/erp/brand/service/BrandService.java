package com.anxys.erp.brand.service;

import java.io.IOException;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.anxys.erp.brand.dao.BrandMapper;
import com.anxys.erp.brand.domain.Brand;
import com.anxys.erp.brand.domain.excel.BrandExcel;
import com.anxys.erp.brand.domain.form.BrandAddParam;
import com.anxys.erp.brand.domain.form.BrandPageParam;
import com.anxys.erp.brand.domain.form.BrandQueryParam;
import com.anxys.erp.brand.domain.form.BrandUpdateForm;
import com.anxys.erp.brand.domain.vo.BrandVO;
import com.anxys.framework.common.code.UserErrorCode;
import com.anxys.framework.common.domain.PageResult;
import com.anxys.framework.common.exception.BusinessException;
import com.anxys.framework.common.utils.BeanUtil;
import com.anxys.framework.common.utils.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.idev.excel.FastExcel;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品品牌服务层
 *
 * @since 2025-03-30 14:35:47
 */
@Slf4j
@Service
public class BrandService {

  @Resource private BrandMapper brandMapper;

  /**
   * 分页查询
   *
   * @param queryForm 查询参数
   * @return 分页结果
   */
  public PageResult<BrandVO> page(BrandPageParam queryForm) {
    Page<BrandVO> page = PageUtil.convert2PageQuery(queryForm.getPageParam());
    List<BrandVO> list = brandMapper.page(page, queryForm);
    return PageUtil.convert2PageResult(page, list);
  }

  /**
   * 列表查询
   *
   * @param queryForm 查询参数
   * @return 列表结果
   */
  public List<BrandVO> list(BrandQueryParam queryForm) {
    return brandMapper.list(queryForm);
  }

  /**
   * 添加
   *
   * @param addForm 添加参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void insert(BrandAddParam addForm) {
    Brand brand = BeanUtil.copy(addForm, Brand.class);
    brandMapper.insert(brand);
  }

  /**
   * 更新
   *
   * @param updateForm 更新参数
   */
  @Transactional(rollbackFor = Exception.class)
  public void update(BrandUpdateForm updateForm) {
    Brand brand = BeanUtil.copy(updateForm, Brand.class);
    brandMapper.updateById(brand);
  }

  /**
   * 批量删除
   *
   * @param idList ID列表
   */
  @Transactional(rollbackFor = Exception.class)
  public void batchDelete(List<Long> idList) {
    if (!idList.isEmpty()) {
      brandMapper.batchUpdateDeleted(idList, true);
    }
  }

  /**
   * 单个删除
   *
   * @param id ID
   */
  @Transactional(rollbackFor = Exception.class)
  public void delete(Long id) {
    brandMapper.deleteById(id);
  }

  /**
   * 通过ID查询
   *
   * @param id ID
   * @return 详情
   */
  public BrandVO queryById(Long id) {
    Brand brand = brandMapper.selectById(id);
    if (brand == null) {
      throw new BusinessException(UserErrorCode.DATA_NOT_EXIST);
    }
    return BeanUtil.copy(brand, BrandVO.class);
  }

  /**
   * 导出Excel
   *
   * @param queryForm 查询参数
   * @return Excel数据列表
   */
  public List<BrandExcel> exportExcel(BrandQueryParam queryForm) {
    return brandMapper.exportExcel(queryForm);
  }

  /**
   * 导入Excel
   *
   * @param file Excel文件
   * @return 导入的数据条数
   */
  @Transactional(rollbackFor = Exception.class)
  public int importExcel(MultipartFile file) {
    List<BrandExcel> dataList;
    try {
      dataList = FastExcel.read(file.getInputStream()).head(BrandExcel.class).sheet().doReadSync();
    } catch (IOException e) {
      log.error("Excel导入失败", e);
      throw new BusinessException("数据格式存在问题，无法读取");
    }

    if (CollectionUtils.isEmpty(dataList)) {
      throw new BusinessException("数据为空");
    }

    // 处理导入数据
    log.info("开始处理导入数据，共 {} 条", dataList.size());
    List<Brand> entityList =
        dataList.stream()
            .map(
                excel -> {
                  log.info("处理数据: {}", excel);
                  return BeanUtil.copy(excel, Brand.class);
                })
            .toList();

    // 批量保存数据
    int insertCount = brandMapper.batchInsert(entityList);
    log.info("批量保存完成，成功插入 {} 条数据", insertCount);

    return dataList.size();
  }
}
