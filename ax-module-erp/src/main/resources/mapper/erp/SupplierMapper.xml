<?xml version="1.0" encoding="UTF-8"?>
            <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    <mapper namespace="com.anxys.erp.supplier.dao.SupplierMapper">
        <!-- 通用查询条件 -->

        <sql id="SupplierCondition">
            <!--@sql select * from erp_supplier t where deleted_flag-->
            <if test="param.id != null and param.id != ''">
                AND t.id = #{param.id}
            </if>
            <if test="param.supplierCode != null and param.supplierCode != ''">
                AND t.supplier_code LIKE CONCAT('%'
                    , #{param.supplierCode}
                    , '%')
            </if>
            <if test="param.supplierName != null and param.supplierName != ''">
                AND t.supplier_name LIKE CONCAT('%'
                    , #{param.supplierName}
                    , '%')
            </if>
            <if test="param.supplierType != null and param.supplierType != ''">
                AND t.supplier_type LIKE CONCAT('%'
                    , #{param.supplierType}
                    , '%')
            </if>
            <if test="param.businessLicense != null and param.businessLicense != ''">
                AND t.business_license LIKE CONCAT('%'
                    , #{param.businessLicense}
                    , '%')
            </if>
            <if test="param.taxId != null and param.taxId != ''">
                AND t.tax_id LIKE CONCAT('%'
                    , #{param.taxId}
                    , '%')
            </if>
            <if test="param.contactPerson != null and param.contactPerson != ''">
                AND t.contact_person LIKE CONCAT('%'
                    , #{param.contactPerson}
                    , '%')
            </if>
            <if test="param.contactPhone != null and param.contactPhone != ''">
                AND t.contact_phone LIKE CONCAT('%'
                    , #{param.contactPhone}
                    , '%')
            </if>
            <if test="param.contactEmail != null and param.contactEmail != ''">
                AND t.contact_email LIKE CONCAT('%'
                    , #{param.contactEmail}
                    , '%')
            </if>
            <if test="param.contactPosition != null and param.contactPosition != ''">
                AND t.contact_position LIKE CONCAT('%'
                    , #{param.contactPosition}
                    , '%')
            </if>
            <if test="param.alternativeContact != null and param.alternativeContact != ''">
                AND t.alternative_contact LIKE CONCAT('%'
                    , #{param.alternativeContact}
                    , '%')
            </if>
            <if test="param.alternativePhone != null and param.alternativePhone != ''">
                AND t.alternative_phone LIKE CONCAT('%'
                    , #{param.alternativePhone}
                    , '%')
            </if>
            <if test="param.addressInfoList != null and param.addressInfoList != ''">
                AND t.address_info_list = #{param.addressInfoList}
            </if>
            <if test="param.paymentInfoList != null and param.paymentInfoList != ''">
                AND t.payment_info_list = #{param.paymentInfoList}
            </if>
            <if test="param.creditLimit != null and param.creditLimit != ''">
                AND t.credit_limit = #{param.creditLimit}
            </if>
            <if test="param.creditPeriod != null and param.creditPeriod != ''">
                AND t.credit_period = #{param.creditPeriod}
            </if>
            <if test="param.cooperationStartDateFrom != null">
                AND t.cooperation_start_date &gt;= #{param.cooperationStartDateFrom}
            </if>
            <if test="param.cooperationStartDateTo != null">
                AND t.cooperation_start_date &lt;= #{param.cooperationStartDateTo}
            </if>
            <if test="param.contractExpiryDateFrom != null">
                AND t.contract_expiry_date &gt;= #{param.contractExpiryDateFrom}
            </if>
            <if test="param.contractExpiryDateTo != null">
                AND t.contract_expiry_date &lt;= #{param.contractExpiryDateTo}
            </if>
            <if test="param.lastOrderDateFrom != null">
                AND t.last_order_date &gt;= #{param.lastOrderDateFrom}
            </if>
            <if test="param.lastOrderDateTo != null">
                AND t.last_order_date &lt;= #{param.lastOrderDateTo}
            </if>
            <if test="param.totalOrderAmount != null and param.totalOrderAmount != ''">
                AND t.total_order_amount = #{param.totalOrderAmount}
            </if>
            <if test="param.approvalStatus != null and param.approvalStatus != ''">
                AND t.approval_status LIKE CONCAT('%'
                    , #{param.approvalStatus}
                    , '%')
            </if>
            <if test="param.approvalUserId != null and param.approvalUserId != ''">
                AND t.approval_user_id = #{param.approvalUserId}
            </if>
            <if test="param.approvalDateFrom != null">
                AND t.approval_date &gt;= #{param.approvalDateFrom}
            </if>
            <if test="param.approvalDateTo != null">
                AND t.approval_date &lt;= #{param.approvalDateTo}
            </if>
            <if test="param.approvalRemark != null and param.approvalRemark != ''">
                AND t.approval_remark LIKE CONCAT('%'
                    , #{param.approvalRemark}
                    , '%')
            </if>
            <if test="param.remark != null and param.remark != ''">
                AND t.remark LIKE CONCAT('%'
                    , #{param.remark}
                    , '%')
            </if>
            <if test="param.attachmentPath != null and param.attachmentPath != ''">
                AND t.attachment_path LIKE CONCAT('%'
                    , #{param.attachmentPath}
                    , '%')
            </if>
            <if test="param.createdBy != null and param.createdBy != ''">
                AND t.created_by = #{param.createdBy}
            </if>
            <if test="param.updatedBy != null and param.updatedBy != ''">
                AND t.updated_by = #{param.updatedBy}
            </if>
            <if test="param.deletedFlag != null and param.deletedFlag != ''">
                AND t.deleted_flag = #{param.deletedFlag}
            </if>
            <if test="param.createTimeFrom != null">
                AND t.create_time &gt;= #{param.createTimeFrom}
            </if>
            <if test="param.createTimeTo != null">
                AND t.create_time &lt;= #{param.createTimeTo}
            </if>
            <if test="param.updateTimeFrom != null">
                AND t.update_time &gt;= #{param.updateTimeFrom}
            </if>
            <if test="param.updateTimeTo != null">
                AND t.update_time &lt;= #{param.updateTimeTo}
            </if>
              AND t.deleted_flag = false

        </sql>

        <update id="batchUpdateDeleted">
            UPDATE erp_supplier
            SET deleted_flag = #{deletedFlag}
            WHERE id IN
            <foreach collection="idList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </update>

        <insert id="batchInsert">
            INSERT INTO erp_supplier
            (supplier_code            , supplier_name            , supplier_type            , business_license            , tax_id            , contact_person            , contact_phone            , contact_email            , contact_position            , alternative_contact            , alternative_phone            , address_info_list            , payment_info_list            , credit_limit            , credit_period            , cooperation_start_date            , contract_expiry_date            , last_order_date            , total_order_amount            , approval_status            , approval_user_id            , approval_date            , approval_remark            , remark            , attachment_path            , created_by            , updated_by            , deleted_flag            , create_time            , update_time)
            VALUES
            <foreach collection="list" item="entity" separator=",">
                (#{entity.supplierCode
                }, #{entity.supplierName
                }, #{entity.supplierType
                }, #{entity.businessLicense
                }, #{entity.taxId
                }, #{entity.contactPerson
                }, #{entity.contactPhone
                }, #{entity.contactEmail
                }, #{entity.contactPosition
                }, #{entity.alternativeContact
                }, #{entity.alternativePhone
                }, #{entity.addressInfoList
                }, #{entity.paymentInfoList
                }, #{entity.creditLimit
                }, #{entity.creditPeriod
                }, #{entity.cooperationStartDate
                }, #{entity.contractExpiryDate
                }, #{entity.lastOrderDate
                }, #{entity.totalOrderAmount
                }, #{entity.approvalStatus
                }, #{entity.approvalUserId
                }, #{entity.approvalDate
                }, #{entity.approvalRemark
                }, #{entity.remark
                }, #{entity.attachmentPath
                }, #{entity.createdBy
                }, #{entity.updatedBy
                }, #{entity.deletedFlag
                }, #{entity.createTime
                }, #{entity.updateTime
                })
            </foreach>
        </insert>



    </mapper>



