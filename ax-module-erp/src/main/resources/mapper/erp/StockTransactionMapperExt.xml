<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.anxys.erp.stockTransaction.dao.StockTransactionMapper">

    <sql id="StockTransactionConditionExt">
        <!--@sql select * from erp_product t -->
        <where>
            <include refid="StockTransactionCondition"/>
        </where>
    </sql>
    <!-- 分页查询 -->
    <select id="page" resultType="com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO">
        SELECT t.*
        FROM erp_stock_transaction t
            <include refid="StockTransactionConditionExt"/>
        ORDER BY t.create_time DESC
    </select>
    <!-- 查询列表 -->
    <select id="list" resultType="com.anxys.erp.stockTransaction.domain.vo.StockTransactionVO">
        SELECT t.*
        FROM erp_stock_transaction t
            <include refid="StockTransactionConditionExt"/>
        ORDER BY t.create_time DESC
    </select>
    <!-- 导出Excel -->
    <select id="exportExcel" resultType="com.anxys.erp.stockTransaction.domain.excel.StockTransactionExcel">
        SELECT t.*
        FROM erp_stock_transaction t
            <include refid="StockTransactionConditionExt"/>
        ORDER BY t.create_time DESC
    </select>

    <delete id="deleteById">
        UPDATE erp_stock_transaction
        SET deleted_flag = true
        WHERE id = #{id}
    </delete>

    <select id="selectByTransactionNo" resultType="com.anxys.erp.stockTransaction.domain.StockTransaction">
        SELECT * FROM erp_stock_transaction
        WHERE transaction_no = #{transactionNo}
          AND deleted_flag = false
    </select>
</mapper>

