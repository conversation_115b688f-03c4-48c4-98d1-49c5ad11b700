<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.anxys.erp.stockCheck.dao.StockCheckMapper">

    <sql id="StockCheckConditionExt">
        <!--@sql select * from erp_product t -->
        <where>
            <include refid="StockCheckCondition"/>
        </where>
    </sql>
    <!-- 分页查询 -->
    <select id="page" resultType="com.anxys.erp.stockCheck.domain.vo.StockCheckVO">
        SELECT t.*
        FROM erp_stock_check t
            <include refid="StockCheckConditionExt"/>
        ORDER BY t.create_time DESC
    </select>
    <!-- 查询列表 -->
    <select id="list" resultType="com.anxys.erp.stockCheck.domain.vo.StockCheckVO">
        SELECT t.*
        FROM erp_stock_check t
            <include refid="StockCheckConditionExt"/>
        ORDER BY t.create_time DESC
    </select>
    <!-- 导出Excel -->
    <select id="exportExcel" resultType="com.anxys.erp.stockCheck.domain.excel.StockCheckExcel">
        SELECT t.*
        FROM erp_stock_check t
            <include refid="StockCheckConditionExt"/>
        ORDER BY t.create_time DESC
    </select>

</mapper>

