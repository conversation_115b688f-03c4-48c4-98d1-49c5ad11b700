<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.anxys.erp.box.dao.BoxMapper">
    <!-- 通用查询条件 -->
    <sql id="BoxCondition">
        <!--@sql select * from erp_box t where deleted_flag-->
            <if test="param.id != null and param.id != ''">
                AND t.id = #{param.id}
            </if>
            <if test="param.boxTypeCode != null and param.boxTypeCode != ''">
                AND t.box_type_code LIKE CONCAT('%', #{param.boxTypeCode}, '%')
            </if>
            <if test="param.boxTypeName != null and param.boxTypeName != ''">
                AND t.box_type_name LIKE CONCAT('%', #{param.boxTypeName}, '%')
            </if>
            <if test="param.boxTypeDesc != null and param.boxTypeDesc != ''">
                AND t.box_type_desc LIKE CONCAT('%', #{param.boxTypeDesc}, '%')
            </if>
            <if test="param.imageUrl != null and param.imageUrl != ''">
                AND t.image_url LIKE CONCAT('%', #{param.imageUrl}, '%')
            </if>
            <if test="param.isActive != null and param.isActive != ''">
                AND t.is_active = #{param.isActive}
            </if>
            <if test="param.paramsJson != null and param.paramsJson != ''">
                AND t.params_json LIKE CONCAT('%', #{param.paramsJson}, '%')
            </if>
            <if test="param.sort != null and param.sort != ''">
                AND t.sort = #{param.sort}
            </if>
            <if test="param.createdBy != null and param.createdBy != ''">
                AND t.created_by = #{param.createdBy}
            </if>
            <if test="param.updatedBy != null and param.updatedBy != ''">
                AND t.updated_by = #{param.updatedBy}
            </if>
            <if test="param.createTimeFrom != null and param.createTimeFrom != ''">
                AND t.create_time &gt;= #{param.createTimeFrom}
            </if>
            <if test="param.createTimeTo != null and param.createTimeTo != ''">
                AND t.create_time &lt;= #{param.createTimeTo}
            </if>
            <if test="param.updateTimeFrom != null and param.updateTimeFrom != ''">
                AND t.update_time &gt;= #{param.updateTimeFrom}
            </if>
            <if test="param.updateTimeTo != null and param.updateTimeTo != ''">
                AND t.update_time &lt;= #{param.updateTimeTo}
            </if>
            <if test="param.deletedFlag != null and param.deletedFlag != ''">
                AND t.deleted_flag = #{param.deletedFlag}
            </if>
            and t.deleted_flag = false

    </sql>

    <update id="batchUpdateDeleted">
        UPDATE erp_box
        SET deleted_flag = #{deletedFlag}
        WHERE id IN
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <insert id="batchInsert">
        INSERT INTO erp_box
        (box_type_code, box_type_name, box_type_desc, image_url, is_active, params_json, sort, created_by, updated_by, create_time, update_time, deleted_flag)
        VALUES
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.boxTypeCode}, #{entity.boxTypeName}, #{entity.boxTypeDesc}, #{entity.imageUrl}, #{entity.isActive}, #{entity.paramsJson}, #{entity.sort}, #{entity.createdBy}, #{entity.updatedBy}, #{entity.createTime}, #{entity.updateTime}, #{entity.deletedFlag})
        </foreach>
    </insert>


</mapper>



