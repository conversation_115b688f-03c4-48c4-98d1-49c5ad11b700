<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.anxys.erp.oa.notice.dao.NoticeDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_notice.notice_id,
        t_notice.notice_type_id,
        t_notice.title,
        t_notice.all_visible_flag,
        t_notice.scheduled_publish_flag,
        t_notice.publish_time,
        t_notice.content_text,
        t_notice.content_html,
        t_notice.attachment,
        t_notice.page_view_count,
        t_notice.user_view_count,
        t_notice.source,
        t_notice.author,
        t_notice.document_number,
        t_notice.deleted_flag,
        t_notice.create_user_id,
        t_notice.update_time,
        t_notice.create_time
    </sql>

    <!-- ================================== 可见范围相关 ================================== -->

    <insert id="insertVisibleRange">
        insert into t_notice_visible_range
        (notice_id, data_type, data_id)
        values
        <foreach collection="visibleRangeFormList" separator="," item="item">
            ( #{noticeId} , #{item.dataType}, #{item.dataId} )
        </foreach>
    </insert>
    <delete id="deleteVisibleRange">
        delete
        from t_notice_visible_range
        where notice_id = #{noticeId}
    </delete>

    <select id="queryVisibleRange"
            resultType="com.anxys.erp.oa.notice.domain.vo.NoticeVisibleRangeVO">
        select *
        from t_notice_visible_range
        where notice_id = #{noticeId}
    </select>

    <!-- ================================== 通知公告【主表】相关 ================================== -->
    <update id="updateDeletedFlag">
        update t_notice
        set deleted_flag = true
        where notice_id = #{noticeId}
    </update>

    <!-- 后管分页查询资讯 -->
    <select id="query" resultType="com.anxys.erp.oa.notice.domain.vo.NoticeVO">
        SELECT
        <include refid="base_columns"/>,
        t_notice_type.notice_type_name as noticeTypeName,
        t_employee.actual_name as createUserName
        FROM t_notice
        LEFT JOIN t_notice_type on t_notice.notice_type_id = t_notice_type.notice_type_id
        LEFT JOIN t_employee on t_notice.create_user_id = t_employee.employee_id
        <where>
            <if test="query.noticeTypeId != null">
                AND t_notice_type.notice_type_id = #{query.noticeTypeId}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND ( INSTR(t_notice.title,#{query.keywords})
                OR INSTR(t_notice.author,#{query.keywords})
                OR INSTR(t_notice.source,#{query.keywords})
                )
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND INSTR(t_notice.document_number, #{query.documentNumber})
            </if>
            <if test="query.createUserName != null and query.createUserName != ''">
                AND t_employee.actual_name = #{query.createUserName}
            </if>
            <if test="query.deletedFlag != null">
                AND t_notice.deleted_flag = #{query.deletedFlag}
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE_FORMAT(t_notice.create_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.createTimeBegin},
                '%Y-%m-%d')
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE_FORMAT(t_notice.create_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.createTimeEnd}, '%Y-%m-%d')
            </if>
            <if test="query.publishTimeBegin != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.publishTimeBegin},
                '%Y-%m-%d')
            </if>
            <if test="query.publishTimeEnd != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.publishTimeEnd},
                '%Y-%m-%d')
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY t_notice.publish_time DESC, t_notice.notice_id DESC
        </if>
    </select>

    <!-- ================================== 通知公告【员工查看】相关 ================================== -->
    <select id="queryEmployeeNotice" resultType="com.anxys.erp.oa.notice.domain.vo.NoticeEmployeeVO">
        select
        <include refid="base_columns"/>,
        t_notice_type.notice_type_name,
        (select count(*) from t_notice_view_record where t_notice_view_record.employee_id = #{requestEmployeeId} and
        t_notice_view_record.notice_id = t_notice.notice_id) as viewFlag
        from t_notice
        left join t_notice_type on t_notice.notice_type_id = t_notice_type.notice_type_id
        <where>
            <if test="!administratorFlag">
            (
                t_notice.notice_id in
                (select t_notice_visible_range.notice_id
                from t_notice_visible_range
                where
                ( t_notice_visible_range.data_type = #{departmentDataType}
                    <if test="requestEmployeeDepartmentIdList != null and requestEmployeeDepartmentIdList.size > 0">
                        and
                        t_notice_visible_range.data_id
                        in
                        <foreach collection="requestEmployeeDepartmentIdList" open="(" close=")" separator="," item="item">
                            #{item}
                        </foreach>
                    </if>
                    )
                    or ( t_notice_visible_range.data_type = #{employeeDataType} and t_notice_visible_range.data_id = #{requestEmployeeId} )
                )
                or t_notice.all_visible_flag = true
            )
            </if>

            and t_notice.deleted_flag = #{deletedFlag}
            and t_notice.publish_time &lt; now()
            <if test="query.noticeTypeId != null">
                AND t_notice_type.notice_type_id = #{query.noticeTypeId}
            </if>
            <if test="query.keywords != null and query.keywords !=''">
                AND ( INSTR(t_notice.title,#{query.keywords})
                OR INSTR(t_notice.author,#{query.keywords})
                OR INSTR(t_notice.document_number,#{query.keywords})
                OR INSTR(t_notice.source,#{query.keywords})
                )
            </if>
            <if test="query.publishTimeBegin != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.publishTimeBegin},
                '%Y-%m-%d')
            </if>
            <if test="query.publishTimeEnd != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.publishTimeEnd},
                '%Y-%m-%d')
            </if>
            <if test="query.notViewFlag">
                AND viewFlag = 0
            </if>
        </where>
        order by t_notice.publish_time desc
    </select>
    <select id="queryEmployeeNotViewNotice" resultType="com.anxys.erp.oa.notice.domain.vo.NoticeEmployeeVO">
        select * from
        (
        select
        <include refid="base_columns"/>,
        t_notice_type.notice_type_name,
        (select count(*) from t_notice_view_record where t_notice_view_record.employee_id = #{requestEmployeeId} and
        t_notice_view_record.notice_id = t_notice.notice_id) as viewFlag
        from t_notice
        left join t_notice_type on t_notice.notice_type_id = t_notice_type.notice_type_id
        <where>
            <if test="!administratorFlag">
                t_notice.notice_id in
                (select t_notice_visible_range.notice_id
                from t_notice_visible_range
                where
                (t_notice_visible_range.data_type = #{departmentDataType}
                <if test="requestEmployeeDepartmentIdList != null and requestEmployeeDepartmentIdList.size > 0">
                    and
                    t_notice_visible_range.data_id
                    in
                    <foreach collection="requestEmployeeDepartmentIdList" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
                )
                or ( t_notice_visible_range.data_type = #{employeeDataType} and t_notice_visible_range.data_id =
                #{requestEmployeeId} )
                )
            </if>
            and t_notice.all_visible_flag = true
            and t_notice.deleted_flag = #{deletedFlag}
            and t_notice.publish_time &lt; now()
            <if test="query.noticeTypeId != null">
                AND t_notice_type.notice_type_id = #{query.noticeTypeId}
            </if>
            <if test="query.keywords != null and query.keywords !=''">
                AND ( INSTR(t_notice.title,#{query.keywords})
                OR INSTR(t_notice.author,#{query.keywords})
                OR INSTR(t_notice.documentNumber,#{query.keywords})
                OR INSTR(t_notice.source,#{query.keywords})
                )
            </if>
            <if test="query.publishTimeBegin != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &gt;= DATE_FORMAT(#{query.publishTimeBegin},
                '%Y-%m-%d')
            </if>
            <if test="query.publishTimeEnd != null">
                AND DATE_FORMAT(t_notice.publish_time, '%Y-%m-%d') &lt;= DATE_FORMAT(#{query.publishTimeEnd},
                '%Y-%m-%d')
            </if>
        </where>
        ) t where viewFlag = 0
        order by t.publish_time desc
    </select>
    <select id="queryNoticeViewRecordList" resultType="com.anxys.erp.oa.notice.domain.vo.NoticeViewRecordVO">
        select t_notice_view_record.*,
        t_employee.actual_name as employeeName,
        t_department.name as departmentName
        from t_notice_view_record
        left join t_employee on t_employee.employee_id = t_notice_view_record.employee_id
        left join t_department on t_department.department_id = t_employee.department_id
        where
        notice_id = #{queryForm.noticeId}
        <if test="queryForm.keywords != null and queryForm.keywords !=''">
            AND (
            INSTR(t_employee.actual_name,#{queryForm.keywords})
            OR INSTR(t_notice_view_record.first_ip,#{queryForm.keywords})
            OR INSTR(t_notice_view_record.first_user_agent,#{queryForm.keywords})
            OR INSTR(t_notice_view_record.last_ip,#{queryForm.keywords})
            OR INSTR(t_notice_view_record.last_user_agent,#{queryForm.keywords})
            )
        </if>
        <if test="queryForm.departmentId != null ">
            and t_department.department_id = #{queryForm.departmentId}
        </if>
        order by t_notice_view_record.update_time desc,t_notice_view_record.create_time desc
    </select>

    <!-- ================================== 通知公告【员工查看记录】相关 ================================== -->
    <select id="viewRecordCount" resultType="java.lang.Long">
        select count(*)
        from t_notice_view_record
        where notice_id = #{noticeId}
          and employee_id = #{employeeId}
    </select>
    <insert id="insertViewRecord">
        insert into t_notice_view_record (notice_id, employee_id, first_ip, first_user_agent, page_view_count)
        values (#{noticeId}, #{employeeId}, #{ip}, #{userAgent}, #{pageViewCount})
    </insert>
    <update id="updateViewRecord">
        update t_notice_view_record
        set page_view_count = page_view_count + 1,
            last_ip         = #{ip},
            last_user_agent = #{userAgent}
        where notice_id = #{noticeId}
          and employee_id = #{employeeId}
    </update>
    <update id="updateViewCount">
        update t_notice
        set page_view_count = page_view_count + #{pageViewCountIncrement},
            user_view_count = user_view_count + #{userViewCountIncrement}
        where notice_id = #{noticeId}
    </update>

</mapper>