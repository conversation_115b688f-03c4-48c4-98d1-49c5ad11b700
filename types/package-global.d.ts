import type dayjs from 'dayjs'
import type { EChartsOption as EChartsBaseOption } from 'echarts/types/dist/echarts'
import type { RouteRecordRaw as AliasRouteRecordRaw } from 'vue-router'
import 'ant-design-vue/typings/global'

declare global {
    type EChartsOption = EChartsBaseOption
    type Dayjs = dayjs.Dayjs
    type ConfigType = dayjs.ConfigType
    type RouteRecordRaw = AliasRouteRecordRaw
}
