/*
 * 枚举、图标、工具类
 */
import type * as lodash from 'lodash-es'

declare module '*.vue' {
  import type { Component } from 'vue'

  const component: Component
  export default component
}

// 对vue进行类型补充说明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    // 常量插件
    $smartEnumPlugin: SmartEnumPlugin
    // 常量图标
    $antIcons: Record<string, Component>
    // lodash工具类
    $lodash: lodash
  }
}
