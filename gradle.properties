# ========================================
# Gradle 8.14.2 ???????? ?
# ??????????
# ========================================

# === ???? ===
group=com.anxys
version=3.0.0

# ========================================
# ?????? ?
# ========================================

# ??Gradle???? - ??????????
org.gradle.daemon=true

# ???? - ??????CPU
org.gradle.parallel=true

# ???? - ??????
org.gradle.caching=true

# ???? - ?????????Gradle 6.6+?
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn
# ??IDEA??????????????????
org.gradle.configuration-cache.inputs.unsafe.ignore.system-properties=idea.gradle.debug.all

# ?????? - ??????
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# ========================================
# JVM???? - ??Java 17 ?
# ========================================

# Gradle JVM???? - ??G1GC??
org.gradle.jvmargs=-Xmx4g -Xms1g \
  -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:+UseStringDeduplication \
  -XX:+UseCompressedOops \
  -XX:+UseCompressedClassPointers \
  -XX:G1HeapRegionSize=16m \
  -XX:MaxGCPauseMillis=100 \
  -Dfile.encoding=UTF-8 \
  -Djava.awt.headless=true

# ???????
org.gradle.compiler.daemon=true

# ========================================
# ???? ??
# ========================================

# ??????
org.gradle.incremental=true

# Java?????????
org.gradle.java.compile-classpath-packaging=true

# ========================================
# ??????? ?
# ========================================

# ??HTTPS???????TLS??????
systemProp.https.protocols=TLSv1.2

# HTTP??????
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000

# ??HTTP????
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10

# ??????
systemProp.org.gradle.internal.repository.max.concurrent.downloads=8

# ??????
systemProp.org.gradle.internal.repository.initial.backoff=500
systemProp.org.gradle.internal.repository.max.backoff=10000

# ========================================
# ?????? ?
# ========================================

# ????????????
org.gradle.dependency.verification=off

# ========================================
# ??????? ?
# ========================================

# ???????
org.gradle.console=rich

# ???? - ???????
org.gradle.warning.mode=summary

# ?????API??
org.gradle.warning.incubating=false

# ========================================
# ???? ?
# ========================================

# ????????
org.gradle.test.parallel=true

# ========================================
# ?????? ?
# ========================================

# ????????
org.gradle.cache.local=true

# JVM????????????????
org.gradle.java.installations.auto-download=false

# ========================================
# Maven?????????? ??
# ========================================
