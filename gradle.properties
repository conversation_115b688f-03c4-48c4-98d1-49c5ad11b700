# ========================================
# Gradle 8.14.2 æè´æ§è½ä¼åéç½® ð
# åºäºææ°æä½³å®è·µä¼å
# ========================================

# === é¡¹ç®ä¿¡æ¯ ===
group=com.anxys
version=3.0.0

# ========================================
# æ ¸å¿æ§è½å¼å³ â¡
# ========================================

# å¯ç¨Gradleå®æ¤è¿ç¨ - æ¾èæååç»­æå»ºéåº¦
org.gradle.daemon=true

# å¹¶è¡æå»º - ååå©ç¨å¤æ ¸CPU
org.gradle.parallel=true

# æå»ºç¼å­ - é¿åéå¤ç¼è¯
org.gradle.caching=true

# éç½®ç¼å­ - å¤§å¹æåæå»ºéåº¦ï¼Gradle 6.6+ï¼
org.gradle.configuration-cache=true
org.gradle.configuration-cache.problems=warn
# å¿½ç¥IDEAè°è¯ç¸å³çç³»ç»å±æ§ï¼é¿åå½±åéç½®ç¼å­
org.gradle.configuration-cache.inputs.unsafe.ignore.system-properties=idea.gradle.debug.all

# æä»¶ç³»ç»çå¬ - æ¹åå¢éæå»º
org.gradle.vfs.watch=true
org.gradle.vfs.verbose=false

# ========================================
# JVMæ§è½ä¼å - ééJava 17 ð§
# ========================================

# Gradle JVMåå­éç½® - ä½¿ç¨G1GCä¼å
org.gradle.jvmargs=-Xmx4g -Xms1g \
  -XX:MaxMetaspaceSize=512m \
  -XX:+UseG1GC \
  -XX:+UseStringDeduplication \
  -XX:+UseCompressedOops \
  -XX:+UseCompressedClassPointers \
  -XX:G1HeapRegionSize=16m \
  -XX:MaxGCPauseMillis=100 \
  -Dfile.encoding=UTF-8 \
  -Djava.awt.headless=true

# ç¼è¯å¨å®æ¤è¿ç¨
org.gradle.compiler.daemon=true

# ========================================
# ç¼è¯ä¼å ð ï¸
# ========================================

# å¯ç¨å¢éç¼è¯
org.gradle.incremental=true

# Javaç¼è¯ç±»è·¯å¾æåä¼å
org.gradle.java.compile-classpath-packaging=true

# ========================================
# ç½ç»åä¸è½½ä¼å ð
# ========================================

# æå®HTTPSåè®®çæ¬ï¼è§£å³TLSæ¡æå¤±è´¥é®é¢
systemProp.https.protocols=TLSv1.2

# HTTPè¿æ¥è¶æ¶è®¾ç½®
org.gradle.internal.http.connectionTimeout=60000
org.gradle.internal.http.socketTimeout=60000

# å¯ç¨HTTPè¿æ¥ä¿æ
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10

# å¯ç¨å¹¶è¡ä¸è½½
systemProp.org.gradle.internal.repository.max.concurrent.downloads=8

# ç½ç»éè¯éç½®
systemProp.org.gradle.internal.repository.initial.backoff=500
systemProp.org.gradle.internal.repository.max.backoff=10000

# ========================================
# ä¾èµè§£æä¼å ð¦
# ========================================

# ç¦ç¨ä¾èµéªè¯ï¼å¼åç¯å¢ï¼
org.gradle.dependency.verification=off

# ========================================
# è¾åºåæ¥å¿ä¼å ð
# ========================================

# æ§å¶å°è¾åºä¼å
org.gradle.console=rich

# è­¦åæ¨¡å¼ - åªæ¾ç¤ºéè¦è­¦å
org.gradle.warning.mode=summary

# ========================================
# æµè¯ä¼å ð§ª
# ========================================

# æµè¯ä»»å¡å¹¶è¡æ§è¡
org.gradle.test.parallel=true

# ========================================
# å®å¨åç¨³å®æ§ ð
# ========================================

# å¯ç¨æ¬å°æå»ºç¼å­
org.gradle.cache.local=true

# JVMå®è£èªå¨ä¸è½½ï¼ç¦ç¨ä»¥æåå®å¨æ§ï¼
org.gradle.java.installations.auto-download=false

# ========================================
# Mavenä»åºä¼åï¼å¼åç¯å¢ï¼ ðï¸
# ========================================
