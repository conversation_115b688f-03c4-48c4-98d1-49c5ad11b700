pluginManagement {
    repositories {
        maven {
            name = "aliyun-gradle-plugin"
            url = uri("https://maven.aliyun.com/repository/gradle-plugin")
        }
        gradlePluginPortal()
    }
}

rootProject.name = "ax-admin"

// 启用类型安全project访问
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

// 依赖解析管理
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenLocal()
        maven {
            name = "aliyun-central"
            url = uri("https://maven.aliyun.com/repository/central")
        }
        maven {
            name = "aliyun-public"
            url = uri("https://maven.aliyun.com/repository/public")
        }
        maven {
            name = "aliyun-spring"
            url = uri("https://maven.aliyun.com/repository/spring")
        }
        mavenCentral()
        maven {
            name = "spring-milestones"
            url = uri("https://repo.spring.io/milestone")
        }
        maven {
            name = "spring-snapshots"
            url = uri("https://repo.spring.io/snapshot")
        }
    }
}

// 构建缓存配置
buildCache {
    local {
        isEnabled = true
        directory = File(rootDir, ".gradle/build-cache")
    }
}

// 子模块配置
include("ax-framework")
include("ax-server")
include("ax-module-system")
include("ax-module-erp")
include("ax-module-ai")

// 集成build-logic
includeBuild("build-logic") 