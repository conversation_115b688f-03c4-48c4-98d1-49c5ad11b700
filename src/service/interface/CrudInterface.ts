import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam, PageQueryParam } from '@/api/base-model/page-model'
import type { UploadChangeParam, UploadFile } from 'ant-design-vue'
import type { Key, SorterResult, TableCurrentDataSource, TablePaginationConfig } from 'ant-design-vue/es/table/interface'
import type { UnwrapNestedRefs } from 'vue'

// ==================== 基础能力接口：表单相关 ====================

/**
 * 表单基础状态接口
 */
export interface FormState<T extends BaseModel> {
  formType: 'add' | 'edit' | 'view'
  formTitle: string
  formData: UnwrapNestedRefs<T>
  formOpen: boolean
  detailData: UnwrapNestedRefs<Partial<T>>
  detailTitle: string
  detailOpen: boolean
  formLoading: boolean
  detailLoading: boolean
  submitLoading: boolean
}

/**
 * 表单提交接口
 */
export interface FormAction {
  closeDetail: () => void
  closeForm: () => void
  resetFormData: () => void
  submitForm: (callback?: () => void) => Promise<{ success: boolean, data: Record<string, unknown> | null }>
}

// ==================== 基础能力接口：列表相关 ====================

/**
 * 分页服务接口
 */
export interface Pagination {
  pageParam: PageParam
  total: number
  onPageChange: (page: number, pageSize?: number) => void
  queryPage: () => Promise<void>
  resetQuery: () => void
  onSearch: () => void
}

/**
 * 删除服务接口
 */
export interface Delete {
  deleteEntity: (id: number, confirmMessage?: string) => void
  batchDeleteEntities: (confirmMessageOrEvent?: string | Event) => void
}

/**
 * 排序服务接口
 */
export interface Sort {
  onChange: <R>(
    pagination: TablePaginationConfig,
    filters: Record<string, unknown[]>,
    sorter: SorterResult<R> | SorterResult<R>[],
    extra: TableCurrentDataSource<R>
  ) => void
}

// ==================== 组合接口 ====================

/**
 * 表单服务接口
 * 组合了所有表单相关的基础接口
 */
export interface Form<T extends BaseModel> extends
  FormState<T>,
  FormAction {
  // 组合接口，不需要额外定义方法
}

export interface Excel {
  importModalOpen: boolean
  importConfirmLoading: boolean
  uploadFile: File | null
  hasUploadFile: boolean
  handleUploadChange: (info: UploadChangeParam<UploadFile>) => void
  handleImportFile: (file: File) => void
  validateUploadFile: (file: File) => boolean
  beforeUpload: (file: File) => boolean
  downloadTemplate: () => void
  onImport: (event?: MouseEvent | File) => Promise<void>
  setImportModalOpen: (value: boolean) => void
  showImportModal: () => void
  onExport: () => Promise<void>

}

/**
 * 表格服务接口
 * 组合了所有列表相关的基础接口
 */
export interface PageList<
  Data extends BaseModel,
  QueryParam extends PageQueryParam,
> extends Pagination,
  Delete,
  Sort,
  Excel {
  queryParam: QueryParam
  tableData: Data[] // 表格数据
  tableLoading: boolean // 表格加载状态
  selectedRowKeyList: number[]
  columns: Record<string, unknown>[]
  rowKey: string // 行唯一标识字段
  scroll?: object // 表格滚动配置
  getRawTableData: () => Data[]
  onSelectChange: (selectedRowKeys: Key[], selectedRows: Data[]) => void

}

/**
 * 列表服务接口
 * 组合了所有列表相关的基础接口
 */
export interface List<
  Data extends BaseModel,
  QueryParam extends PageQueryParam,
> {
  listData: Data[] // 列表数据
  listLoading: boolean // 列表加载状态
  queryParam: QueryParam
  selectedKey?: string | number
  placeholder?: string
  getRawListData: () => Data[]
  getOptionValue: (item: Data) => string | number
  getOptionLabel: (item: Data) => string
}

// ==================== 最顶层接口 ====================

/**
 * CRUD服务接口
 * 组合了表单和列表服务接口，提供完整的CRUD功能
 */
export interface Crud<
  ListData extends BaseModel,
  FormData extends BaseModel,
  QueryParam extends { pageParam: PageParam },
> extends Form<FormData>, PageList<ListData, QueryParam>, List<ListData, QueryParam> {
  businessName: string
  openDetailView: (id: number) => void
  openEditForm: (id: number) => void
  openAddForm: () => void
  submitFormAndRefresh: (callback?: () => void) => Promise<{ success: boolean, data: Record<string, unknown> | null }>
}
