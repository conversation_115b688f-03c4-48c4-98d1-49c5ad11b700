import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam, PageResult } from '@/api/base-model/page-model'
import type { Key, SorterResult, TableCurrentDataSource, TablePaginationConfig } from 'ant-design-vue/es/table/interface'
import type { UploadChangeParam, UploadFile } from 'ant-design-vue/es/upload/interface'
import type { Crud } from './interface/CrudInterface.js'
import { FormProcessor } from './internal/FormProcessor'
import { ListProcessor } from './internal/ListProcessor'
import { TableProcessor } from './internal/TableProcessor'

// 将 CURD_KEY 定义移到类外部并导出，并添加类型提示
export const CRUD_KEY = Symbol('crudService')

/**
 * 基础CRUD服务类
 * 使用组合模式实现，通过FormProcessor和ListProcessor分别处理表单和列表相关的功能
 */
export class BaseCrudService<
  Model extends BaseModel,
  FormModel extends BaseModel,
  QueryParam extends { pageParam: PageParam },
> implements Crud<Model, FormModel, QueryParam> {
  provide(): void {
    provide(CRUD_KEY, this)
  }

  /**
   * 表单处理器
   */
  protected formProcessor: FormProcessor< FormModel>

  /**
   * 列表处理器
   */
  protected tableProcessor: TableProcessor<Model, QueryParam>

  /**
   * 列表处理器
   */
  protected listProcessor: ListProcessor<Model, QueryParam>

  /**
   * 业务名称
   */

  /**
   * 构造函数
   * @param businessNameValue
   * @param columns
   * @param api  API对象集合
   * @param api.queryPage 分页查询方法
   * @param api.getDetail 获取详情方法
   * @param api.add 添加方法
   * @param api.update 更新方法
   * @param api.delete 删除方法
   * @param api.batchDelete 批量删除方法
   * @param api.import 导入方法
   * @param api.export 导出方法
   * @param api.downloadTemplate 下载模板方法
   * @param initialQueryParam 初始查询参数
   */
  constructor(
    protected businessNameValue: string,
    columns: Record<string, unknown>[],
    api: {
      queryPage?: (param: QueryParam) => Promise<{ data?: PageResult<Model> }>
      getDetail?: (id: number) => Promise<{ data?: FormModel }>
      add?: (data: FormModel) => Promise<unknown>
      update?: (data: FormModel) => Promise<unknown>
      delete?: (id: number) => Promise<unknown>
      batchDelete?: (ids: number[]) => Promise<unknown>
      import?: (formData: FormData) => Promise<unknown>
      export?: (param: QueryParam) => void
      downloadTemplate?: () => void
    },
        initialQueryParam: Partial<QueryParam> = {},
  ) {
    // 确保属性有初始值
    this.formProcessor = {} as FormProcessor<FormModel>
    this.tableProcessor = {} as TableProcessor<Model, QueryParam>
    this.listProcessor = {} as ListProcessor<Model, QueryParam>
    try {
      // 创建表单处理器
      this.formProcessor = new FormProcessor<FormModel>(
        this.businessNameValue,
        {
          getDetail: api.getDetail,
          add: api.add,
          update: api.update,
        },
      )

      // 设置自定义默认表单数据
      this.formProcessor.setCustomDefaultFormData(() => this.getDefaultFormData())

      // 创建表格处理器
      this.tableProcessor = new TableProcessor<Model, QueryParam>(
        this.businessNameValue,
        ref(columns),
        {
          queryPage: api.queryPage,
          delete: api.delete,
          batchDelete: api.batchDelete,
          import: api.import,
          export: api.export,
          downloadTemplate: api.downloadTemplate,
        },
        initialQueryParam,
      )

      // 创建列表处理器
      this.listProcessor = new ListProcessor<Model, QueryParam>(
        this.businessNameValue,
        {
          queryPage: api.queryPage,
          delete: api.delete as (id: number | string) => Promise<unknown>,
        },
        initialQueryParam,
      )
    }
    catch (error) {
      console.error('BaseCrudService 初始化失败:', error)
    }
  }

  /**
   * 获取表格列配置
   */
  get columns(): Record<string, unknown>[] {
    return this.tableProcessor.columns
  }

  /**
   * 设置表格列配置
   */
  set columns(value: Record<string, unknown>[]) {
    this.tableProcessor.columns = value
  }

  /**
   * 获取行唯一标识
   */
  get rowKey(): string {
    return this.tableProcessor.rowKey
  }

  get scroll(): object | undefined {
    return this.tableProcessor.scroll
  }

  get hasUploadFile(): boolean {
    return this.tableProcessor.hasUploadFile
  }

  /**
   * 获取业务名称
   */
  get businessName(): string {
    return this.businessNameValue
  }

  // ==================== 表格数据相关属性 ====================

  /**
   * 获取表格数据
   */
  get tableData(): Model[] {
    return this.tableProcessor.tableData
  }

  /**
   * 获取表格加载状态
   */
  get tableLoading(): boolean {
    return this.tableProcessor.tableLoading
  }

  /**
   * 获取选中行键列表
   */
  get selectedRowKeyList(): number[] {
    return this.tableProcessor.selectedRowKeyList
  }

  /**
   * 获取导入模态框开启状态
   */
  get importModalOpen(): boolean {
    return this.tableProcessor.importModalOpen
  }

  /**
   * 设置导入模态框开启状态
   */
  set importModalOpen(value: boolean) {
    this.tableProcessor.setImportModalOpen(value)
  }

  /**
   * 获取导入确认加载状态
   */
  get importConfirmLoading(): boolean {
    return this.tableProcessor.importConfirmLoading
  }

  /**
   * 获取上传文件
   */
  get uploadFile(): File | null {
    return this.tableProcessor.uploadFile
  }

  // ==================== 表单相关属性 ====================

  /**
   * 获取表单类型
   */
  get formType(): 'add' | 'edit' | 'view' {
    return this.formProcessor.formType
  }

  /**
   * 获取表单标题
   */
  get formTitle(): string {
    return this.formProcessor.formTitle
  }

  /**
   * 获取表单开启状态
   */
  get formOpen(): boolean {
    return this.formProcessor.formOpen
  }

  /**
   * 获取详情开启状态
   */
  get detailOpen(): boolean {
    return this.formProcessor.detailOpen
  }

  /**
   * 获取表单加载状态
   */
  get formLoading(): boolean {
    return this.formProcessor.formLoading
  }

  /**
   * 获取详情加载状态
   */
  get detailLoading(): boolean {
    return this.formProcessor.detailLoading
  }

  /**
   * 获取提交加载状态
   */
  get submitLoading(): boolean {
    return this.formProcessor.submitLoading
  }

  // ==================== 表单相关方法 ====================

  get formData() {
    return this.formProcessor.formData
  }

  get detailData() {
    return this.formProcessor.detailData
  }

  get detailTitle(): string {
    return this.formProcessor.detailTitle
  }

  openAddForm(): void {
    this.formProcessor.openAddForm()
  }

  openEditForm(id: number): void {
    this.formProcessor.openEditForm(id)
  }

  openDetailView(id: number): void {
    this.formProcessor.openDetailView(id)
  }

  closeForm(): void {
    this.formProcessor.closeForm()
  }

  closeDetail(): void {
    this.formProcessor.closeDetail()
  }

  resetFormData(): void {
    this.formProcessor.resetFormData()
  }

  /**
   * 提供默认表单数据（供子类重写）
   * 这个方法可以被子类重写，以提供特定业务的默认表单数据
   */
  protected getDefaultFormData(): FormModel {
    return {} as FormModel
  }

  /**
   * 处理表单提交前的数据，子类可重写
   */
  protected beforeSubmit(formData: FormModel): FormModel {
    return { ...formData }
  }

  submitForm(callback?: () => void): Promise<{ success: boolean, data: Record<string, unknown> | null } > {
    return this.formProcessor.submitForm(callback)
  }

  // ==================== 列表相关属性和方法 ====================

  get total(): number {
    return this.tableProcessor.total
  }

  get queryParam(): QueryParam {
    return this.tableProcessor.queryParam
  }

  get pageParam(): PageParam {
    return this.tableProcessor.pageParam
  }

  getRawTableData(): Model[] {
    return this.tableProcessor.getRawTableData()
  }

  resetQuery = (): void => {
    this.tableProcessor.resetQuery()
  }

  onSearch = (): void => {
    this.tableProcessor.onSearch()
  }

  queryPage = (): Promise<void> => {
    return this.tableProcessor.queryPage()
  }

  onSelectChange = (selectedRowKeys: Key[], selectedRows: Model[]): void => {
    this.tableProcessor.onSelectChange(selectedRowKeys, selectedRows)
  }

  onChange = <R>(
    pagination: TablePaginationConfig,
    filters: Record<string, unknown[]>,
    sorter: SorterResult<R> | SorterResult<R>[],
    extra: TableCurrentDataSource<R>,
  ): void => {
    this.tableProcessor.onChange(pagination, filters, sorter, extra)
  }

  onPageChange = (page: number, pageSize?: number): void => {
    this.tableProcessor.onPageChange(page, pageSize)
  }

  /**
   * 处理分页组件的变化事件
   */
  onPaginationChange = (pagination: TablePaginationConfig): void => {
    this.tableProcessor.onPaginationChange(pagination)
  }

  deleteEntity = (id: number, confirmMessage?: string): void => {
    this.tableProcessor.deleteEntity(id, confirmMessage)
  }

  batchDeleteEntities = (confirmMessageOrEvent?: string | Event): void => {
    this.tableProcessor.batchDeleteEntities(confirmMessageOrEvent)
  }

  showImportModal = (): void => {
    this.tableProcessor.showImportModal()
  }

  handleUploadChange = (info: UploadChangeParam<UploadFile>): void => {
    this.tableProcessor.handleUploadChange(info)
  }

  handleImportFile = (file: File): void => {
    this.tableProcessor.handleImportFile(file)
  }

  validateUploadFile = (file: File): boolean => {
    return this.tableProcessor.validateUploadFile(file)
  }

  beforeUpload = (file: File): boolean => {
    return this.tableProcessor.beforeUpload(file)
  }

  /**
   * 导入数据
   * @param eventOrFile 事件对象或文件对象
   */
  onImport = (eventOrFile?: MouseEvent | File): Promise<void> => {
    return this.tableProcessor.onImport(eventOrFile)
  }

  downloadTemplate = (): void => {
    this.tableProcessor.downloadTemplate()
  }

  onExport = (): Promise<void> => {
    return this.tableProcessor.onExport()
  }

  // ==================== 协调多个处理器的方法 ====================

  /**
   * 提交表单并刷新列表
   * @param callback 成功后的额外回调函数
   */
  submitFormAndRefresh = async (callback?: () => void): Promise<{ success: boolean, data: Record<string, unknown> | null }> => {
    const { success, data } = await this.formProcessor.submitForm(() => {
      // 提交成功后刷新列表
      this.tableProcessor.queryPage()
      // 执行额外回调
      if (callback)
        callback()
    })
    return { success, data }
  }

  /**
   * 设置导入模态框的状态
   * @param value 导入模态框状态
   */
  setImportModalOpen = (value: boolean): void => {
    this.tableProcessor.setImportModalOpen(value)
  }

  // 自定义业务方法可以在子类中添加

  // ==================== 列表相关属性和方法 ====================
  get listData(): Model[] {
    return this.listProcessor.listData
  }

  get listLoading(): boolean {
    return this.listProcessor.listLoading
  }

  get listPlaceholder(): string {
    return this.listProcessor.placeholder
  }

  set selectedKey(value: string | number | undefined) {
    this.listProcessor.selectedKey = value
  }

  get selectedKey(): string | number | undefined {
    return this.listProcessor.selectedKey
  }

  getRawListData(): Model[] {
    return this.listProcessor.getRawListData()
  }

  getOptionValue = (item: Model): string | number => {
    return item.id || ''
  }

  getOptionLabel = (item: Model): string => {
    return item.id?.toString() || '' // 默认返回空字符串
  }

  resetListQuery = (): void => {
    this.listProcessor.resetQuery()
  }
}
