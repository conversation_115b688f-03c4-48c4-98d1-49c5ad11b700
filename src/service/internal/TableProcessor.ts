import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam, PageQueryParam, PageResult, SortItem } from '@/api/base-model/page-model'
import type { Key, SorterResult, TableCurrentDataSource, TablePaginationConfig } from 'ant-design-vue/es/table/interface'
import type { UploadChangeParam, UploadFile } from 'ant-design-vue/es/upload/interface'
import type { PageList } from '../interface/CrudInterface.js'
import { smartSentry } from '@/lib/smart-sentry'
import { message, Modal } from 'ant-design-vue'
import { reactive, ref, toRaw } from 'vue'
import { ActionExecutor } from './ActionExecutor'

/**
 * 表格处理器类
 * 负责处理表格相关的操作和状态管理
 */
export class TableProcessor<
  Result extends BaseModel,
  QueryParam extends PageQueryParam,
> implements PageList<Result, QueryParam> {
  /**
   * 行唯一标识，可被子类重写
   */
  get rowKey(): string {
    return 'id'
  }

  /**
   * 表格滚动配置，可被子类重写
   */
  get scroll(): object {
    return { x: 1300 }
  }

  /**
   * 表格列配置，必须被子类重写
   */
  get columns(): Record<string, unknown>[] {
    if (this._columns.value.length === 0) {
      console.warn('表格列未定义，请使用 setColumns 方法设置')
    }
    return this._columns.value
  }

  /**
   * 设置表格列配置
   */
  set columns(value: Record<string, unknown>[]) {
    this._columns.value = value
  }

  /**
   * 表格数据
   */
  private _tableData = ref<Result[]>([])
  /**
   * 分页结果
   */
  private _pageResult = ref<PageResult<Result>>({ list: [], total: 0 })

  /**
   * 总条数
   */
  get total(): number {
    return this._pageResult.value?.total || 0
  }

  /**
   * 选中的行Key表格
   */
  private _selectedRowKeyList = ref<number[]>([])
  /**
   * 选中的行对象表格
   */
  private _selectedRows = ref<Result[]>([])
  /**
   * 导入Modal是否可见
   */
  private _importModalOpen = ref<boolean>(false)
  /**
   * 导入确认加载状态
   */
  private _importConfirmLoading = ref<boolean>(false)
  /**
   * 上传的文件
   */
  private _uploadFile = ref<File | null>(null)
  /**
   * 查询参数
   */
  queryParam: QueryParam

  /**
   * 获取表格列配置
   */

  /**
   * 获取表格数据
   */
  get tableData(): Result[] {
    return this._tableData.value as Result[]
  }

  /**
   * 获取表格加载状态
   */
  get tableLoading(): boolean {
    return false
  }

  /**
   * 获取分页结果
   */
  get pageResult(): PageResult<Result> {
    return this._pageResult.value as PageResult<Result>
  }

  /**
   * 获取选中的行Key表格
   */
  get selectedRowKeyList(): number[] {
    return this._selectedRowKeyList.value
  }

  /**
   * 获取导入Modal是否可见
   */
  get importModalOpen(): boolean {
    return this._importModalOpen.value
  }

  /**
   * 获取导入确认加载状态
   */
  get importConfirmLoading(): boolean {
    return this._importConfirmLoading.value
  }

  /**
   * 获取上传的文件
   */
  get uploadFile(): File | null {
    return this._uploadFile.value
  }

  /**
   * 获取页面参数
   */
  get pageParam(): PageParam {
    return this.queryParam.pageParam
  }

  get hasUploadFile(): boolean {
    return !!this._uploadFile.value
  }

  /**
   * 构造函数
   * @param businessName 业务名称
   * @param _columns 表格列配置
   * @param api API对象
   * @param api.queryPage 查询分页数据
   * @param api.delete 删除数据
   * @param api.batchDelete 批量删除数据
   * @param api.import 导入数据
   * @param api.export 导出数据
   * @param api.downloadTemplate 下载模板
   * @param initialQueryParam 初始查询参数
   */
  constructor(
    private businessName: string,
    private _columns = ref<Record<string, unknown>[]>([]),
    private api: {
      queryPage: ((param: QueryParam) => Promise<{ data?: PageResult<Result> }>) | undefined
      delete: ((id: number) => Promise<unknown>) | undefined
      batchDelete: ((ids: number[]) => Promise<unknown>) | undefined
      import: ((formData: FormData) => Promise<unknown>) | undefined
      export: ((param: QueryParam) => void) | undefined
      downloadTemplate: (() => void) | undefined
    },
      initialQueryParam: Partial<QueryParam> = {},
  ) {
    // 创建初始查询参数
    this.queryParam = this.createDefaultQueryParam(initialQueryParam)
    // 自动加载数据
    this.queryPage()
  }

  /**
   * 获取表格数据的非响应式版本
   */
  getRawTableData = (): Result[] => {
    return toRaw(this._tableData.value) as Result[]
  }

  /**
   * 重置查询条件
   */
  resetQuery = (): void => {
    // 保留当前页大小
    const pageSize = this.pageParam.pageSize
    // 重置所有查询条件
    const defaultParam = this.createDefaultQueryParam({})
    Object.assign(this.queryParam, { ...defaultParam })
    // 恢复页大小
    this.pageParam.pageSize = pageSize
    // 重置为第一页
    this.pageParam.pageNum = 1
    // 查询数据
    this.queryPage()
  }

  /**
   * 搜索
   */
  onSearch = (): void => {
    this.pageParam.pageNum = 1
    this.queryPage()
  }

  /**
   * 查询分页数据
   */
  async queryPage(): Promise<void> {
    if (!this.api.queryPage) {
      message.error(`${this.businessName}服务缺少查询API`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        const queryResult = await this.api.queryPage!(this.queryParam as QueryParam)
        if (queryResult.data) {
          this._pageResult.value = queryResult.data
          this._tableData.value = queryResult.data.list || []
        }
      },
      `获取${this.businessName}表格失败`,
    )
  }

  /**
   * 选择行变化
   */
  onSelectChange = (selectedRowKeys: Key[], selectedRows: Result[]): void => {
    this._selectedRowKeyList.value = selectedRowKeys as number[]
    this._selectedRows.value = selectedRows
  }

  /**
   * 表格变化处理（排序、过滤）
   */
  onChange = <R>(
    _pagination: TablePaginationConfig,
    _filters: Record<string, unknown[]>,
    sorter: SorterResult<R> | SorterResult<R>[],
    extra: TableCurrentDataSource<R>,
  ): void => {
    if (extra.action === 'sort') {
      // 处理可能是数组的情况
      const currentSorter = Array.isArray(sorter) ? sorter[0] : sorter
      if (!currentSorter?.field || typeof currentSorter.field !== 'string')
        return

      const { order, field } = currentSorter

      // 初始化排序表格（如果不存在）
      if (!this.pageParam.sortItemList) {
        this.pageParam.sortItemList = []
      }

      const column = this.camelToUnderscore(field)
      const findIndex = this.pageParam.sortItemList.findIndex(e => e.column === column)

      if (findIndex !== -1) {
        this.pageParam.sortItemList.splice(findIndex, 1)
      }

      if (order) {
        const asc = order === 'ascend'
        this.pageParam.sortItemList.push({
          column,
          asc,
        } as SortItem)
      }
      this.queryPage()
    }
  }

  /**
   * 分页变化处理
   */
  onPageChange = (page: number, pageSize?: number): void => {
    this.pageParam.pageNum = page
    if (pageSize) {
      this.pageParam.pageSize = pageSize
    }
    this.queryPage()
  }

  /**
   * 直接处理分页控件的变化事件
   * @param pagination 分页控件参数
   */
  onPaginationChange = (pagination: TablePaginationConfig): void => {
    this.pageParam.pageNum = pagination.current || 1
    if (pagination.pageSize) {
      this.pageParam.pageSize = pagination.pageSize
    }
    this.queryPage()
  }

  /**
   * 删除实体
   */
  deleteEntity = (id: number, confirmMessage?: string): void => {
    if (!id || !this.api.delete) {
      message.error(`${this.businessName}服务缺少删除API或记录缺少ID`)
      return
    }

    // 如果没有传入确认消息，则根据业务名称生成
    const confirmText = confirmMessage || `确定要删除该${this.businessName}数据吗？`

    Modal.confirm({
      title: '提示',
      content: confirmText,
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        await this.singleDelete(id)
      },
      cancelText: '取消',
    })
  }

  /**
   * 单个删除实体
   */
  private async singleDelete(id: number): Promise<void> {
    if (!id || !this.api.delete) {
      return
    }

    // 确保record.id不为undefined
    const entityId = id

    await ActionExecutor.execute(
      async () => {
        await this.api.delete!(entityId)
        message.success(`删除${this.businessName}成功`)
        this.queryPage()
      },
      `删除${this.businessName}失败`,
    )
  }

  /**
   * 确认批量删除实体
   */
  batchDeleteEntities = (confirmMessageOrEvent?: string | Event): void => {
    if (this._selectedRowKeyList.value.length === 0) {
      message.warning('请选择要删除的数据')
      return
    }

    if (!this.api.batchDelete) {
      message.error(`${this.businessName}服务缺少批量删除API`)
      return
    }
    // 如果没有传参数或传入的是事件对象，则自动生成确认消息
    const confirmMessage = typeof confirmMessageOrEvent === 'string'
      ? confirmMessageOrEvent
      : `确定要删除选中的 ${this._selectedRowKeyList.value.length} 条${this.businessName}数据吗？`

    Modal.confirm({
      title: '提示',
      content: confirmMessage,
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        await this.batchDelete()
      },
      cancelText: '取消',
    })
  }

  /**
   * 批量删除实体
   */
  private async batchDelete(): Promise<void> {
    if (this._selectedRowKeyList.value.length === 0 || !this.api.batchDelete) {
      return
    }

    await ActionExecutor.execute(
      async () => {
        await this.api.batchDelete!(this._selectedRowKeyList.value)
        message.success(`批量删除${this.businessName}成功`)
        this._selectedRowKeyList.value = []
        this.queryPage()
      },
      `批量删除${this.businessName}失败`,
    )
  }

  /**
   * 显示导入模态框
   */
  showImportModal = (): void => {
    if (!this.api.import) {
      message.error(`${this.businessName}服务缺少导入API`)
      return
    }
    this._importModalOpen.value = true
    this._uploadFile.value = null
  }

  /**
   * 处理上传变化
   */
  handleUploadChange = (info: UploadChangeParam<UploadFile>): void => {
    if (info.file.status && info.file.status !== 'uploading') {
      this._uploadFile.value = info.file.originFileObj || null
    }
  }

  /**
   * 上传文件
   */
  handleImportFile = (file: File): void => {
    this._uploadFile.value = file
  }

  /**
   * 验证上传文件
   * @param file 待验证的文件
   * @returns 文件是否有效
   */
  validateUploadFile = (file: File): boolean => {
    // 接受的文件类型
    const accept = '.xls,.xlsx'
    // 文件大小限制(MB)
    const maxSize = 10

    const isAcceptType = accept.split(',').some((type) => {
      return file.name.toLowerCase().endsWith(type.replace('.', '').toLowerCase())
    })

    if (!isAcceptType) {
      message.error(`只能上传${accept}格式的文件!`)
      return false
    }

    const isLtMaxSize = file.size / 1024 / 1024 < maxSize
    if (!isLtMaxSize) {
      message.error(`文件大小不能超过${maxSize}MB!`)
      return false
    }

    return true
  }

  /**
   * 上传前检查（用于上传组件的beforeUpload属性）
   * @param file 待上传的文件
   * @returns 是否继续上传
   */
  beforeUpload = (file: File): boolean => {
    // 使用验证方法验证文件
    if (!this.validateUploadFile(file)) {
      return false
    }

    // 验证通过，处理文件
    this.handleImportFile(file)
    return false // 阻止组件默认上传行为
  }

  /**
   * 导入数据
   */
  async onImport(eventOrFile?: MouseEvent | File): Promise<void> {
    if (!this.api.import) {
      message.error(`${this.businessName}服务缺少导入API`)
      return
    }

    // 判断参数类型，忽略 MouseEvent
    let fileToUse: File | null

    if (eventOrFile instanceof File) {
      fileToUse = eventOrFile
    }
    else {
      fileToUse = this._uploadFile.value
    }

    if (!fileToUse) {
      message.warning('请选择要导入的文件')
      return
    }

    const formData = new FormData()
    formData.append('file', fileToUse)

    await ActionExecutor.execute(
      async () => {
        await this.api.import!(formData)
        message.success(`导入${this.businessName}成功`)
        this._importModalOpen.value = false
        await this.queryPage()
      },
      `导入${this.businessName}失败`,
    )
  }

  /**
   * 设置导入模态框显示状态
   * @param value 是否显示
   */
  setImportModalOpen = (value: boolean): void => {
    this._importModalOpen.value = value
  }

  /**
   * 下载模板
   */
  downloadTemplate = (): void => {
    if (!this.api.downloadTemplate) {
      message.error(`${this.businessName}服务缺少下载模板API`)
      return
    }

    try {
      this.api.downloadTemplate()
      message.success('正在下载模板')
    }
    catch (error) {
      smartSentry.captureError(error)
      message.error('下载模板失败')
    }
  }

  /**
   * 导出数据
   */
  onExport = async (): Promise<void> => {
    if (!this.api.export) {
      message.error(`${this.businessName}服务缺少导出API`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        this.api.export!(this.queryParam as QueryParam)
        message.success(`导出${this.businessName}成功`)
      },
      `导出${this.businessName}失败`,
    )
  }

  /**
   * 驼峰转下划线
   */
  private camelToUnderscore(str: string): string {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase()
  }

  /**
   * 创建默认查询参数
   */
  private createDefaultQueryParam(initialQueryParam: Partial<QueryParam>): QueryParam {
    // 创建默认查询参数
    const defaultQueryParam = {
      pageParam: {
        pageNum: 1,
        pageSize: 10,
      },
    } as QueryParam

    // 合并默认参数和传入的参数
    const queryParam = { ...defaultQueryParam, ...initialQueryParam } as QueryParam

    // 使用reactive保持响应性
    return reactive(queryParam) as QueryParam
  }
}
