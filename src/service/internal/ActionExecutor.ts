import { SmartLoading } from '@/components/base-old/smart-loading'
import { smartSentry } from '@/lib/smart-sentry'
import { message } from 'ant-design-vue'

/**
 * 通用的操作执行器
 * 提供统一的错误处理和全局加载状态管理
 */
export class ActionExecutor {
  /**
   * 统一错误处理包装方法
   * @param action 要执行的异步操作
   * @param errorMessage 出错时显示的错误消息
   * @returns 操作结果，若出错则返回null
   */
  static async execute<R>(
    action: () => Promise<R>,
    errorMessage: string,
  ): Promise<R | null> {
    SmartLoading.show()

    try {
      return await action()
    }
    catch (error) {
      smartSentry.captureError(error)
      message.error(errorMessage)
      return null
    }
    finally {
      SmartLoading.hide()
    }
  }
}
