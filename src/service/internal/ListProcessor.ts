import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam, PageQueryParam, PageResult } from '@/api/base-model/page-model'
import type { List } from '../interface/CrudInterface.js'
import { message, Modal } from 'ant-design-vue'
import { reactive, ref, toRaw } from 'vue'
import { ActionExecutor } from './ActionExecutor'

/**
 * 列表处理器类
 * 负责处理列表相关的操作和状态管理
 */
export class ListProcessor<
  Result extends BaseModel,
  QueryParam extends PageQueryParam,
> implements List<Result, QueryParam> {
  /**
   * 选中的Key列表
   */
  private _selectedKey = ref<string | number | undefined>(undefined)
  /**
   * 原始分页结果
   */
  private _rawPageResult = ref<Record<string, unknown> | PageResult<Result> | undefined >(undefined)
  /**
   * 分页结果
   */
  private _pageResult = ref<PageResult<Result>>({ list: [], total: 0 })
  /**
   * 列表数据
   */
  private _listData = ref<Result[]>([])
  /**
   * 查询参数
   */
  queryParam: QueryParam
  /**
   * 占位符
   */
  placeholder: string

  /**
   * 获取列表加载状态
   */
  get listLoading(): boolean {
    return false
  }

  set selectedKey(value: string | number | undefined) {
    this._selectedKey.value = value
  }

  /**
   * 获取选中的Key
   */
  get selectedKey(): string | number | undefined {
    return this._selectedKey.value
  }

  get rawPageResult(): Record<string, unknown> | PageResult<Result> | undefined {
    return this._rawPageResult.value
  }

  /**
   * 获取列表数据
   */
  get listData(): Result[] {
    return this._listData.value as Result[]
  }

  /**
   * 获取页面参数
   */
  get pageParam(): PageParam {
    return this.queryParam.pageParam
  }

  /**
   * 构造函数
   * @param businessName 业务名称
   * @param api API对象
   * @param initialQueryParam 初始查询参数
   */
  constructor(
    private businessName: string,
    private api: {
      queryPage?: (param: QueryParam) => Promise<{ data?: PageResult<Result> }>
      delete?: (id: number | string) => Promise<unknown>
    },
        initialQueryParam: Partial<QueryParam> = {},
  ) {
    // 创建初始查询参数
    this.queryParam = this.createDefaultQueryParam(initialQueryParam)
    // 自动加载数据
    this.queryPage()
    // 设置默认值
    this.placeholder = `请选择${this.businessName}`
  }

  /**
   * 获取列表数据的非响应式版本
   */
  getRawListData = (): Result[] => {
    return toRaw(this._listData.value) as Result[]
  }

  getOptionValue = (item: Result): string | number => {
    return item.id || ''
  }

  getOptionLabel = (item: Result): string => {
    return item.id?.toString() || '' // 默认返回空字符串
  }

  /**
   * 重置查询条件
   */
  resetQuery = (): void => {
    // 重置所有查询条件
    const defaultParam = this.createDefaultQueryParam({})
    Object.assign(this.queryParam, { ...defaultParam })
    // 重置为第一页
    this.queryParam.pageParam.pageNum = 1
    // 查询数据
    this.queryPage()
  }

  /**
   * 搜索
   */
  onSearch = (): void => {
    this.queryPage()
  }

  /**
   * 查询分页数据（暂用）
   */
  async queryPage(): Promise<void> {
    if (!this.api.queryPage) {
      message.error(`${this.businessName}服务缺少查询API`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        const queryResult = await this.api.queryPage!(this.queryParam)
        if (queryResult.data) {
          this._pageResult.value = queryResult.data
          this._listData.value = queryResult.data.list || []
          this._rawPageResult.value = queryResult.data || []
        }
      },
      `获取${this.businessName}列表失败`,
    )
  }

  /**
   * 删除实体
   */
  deleteEntity = (record: Result, confirmMessage?: string): void => {
    if (!record.id || !this.api.delete) {
      message.error(`${this.businessName}服务缺少删除API或记录缺少ID`)
      return
    }

    // 如果没有传入确认消息，则根据业务名称生成
    const confirmText = confirmMessage || `确定要删除该${this.businessName}数据吗？`

    Modal.confirm({
      title: '提示',
      content: confirmText,
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        await this.singleDelete(record)
      },
      cancelText: '取消',
    })
  }

  /**
   * 单个删除实体
   */
  private async singleDelete(record: Result): Promise<void> {
    if (!record.id || !this.api.delete) {
      return
    }

    // 确保record.id不为undefined
    const entityId = record.id

    await ActionExecutor.execute(
      async () => {
        await this.api.delete!(entityId)
        message.success(`删除${this.businessName}成功`)
        this.queryPage()
      },
      `删除${this.businessName}失败`,
    )
  }

  /**
   * 创建默认查询参数
   */
  private createDefaultQueryParam(initialQueryParam: Partial<QueryParam>): QueryParam {
    // 创建默认查询参数
    const defaultQueryParam = {
      pageParam: {
        pageNum: 1,
        pageSize: 10,
      },
    } as QueryParam

    // 合并默认参数和传入的参数
    const queryParam = { ...defaultQueryParam, ...initialQueryParam } as QueryParam

    // 使用reactive保持响应性
    return reactive(queryParam) as QueryParam
  }
}
