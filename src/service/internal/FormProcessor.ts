import type { BaseModel } from '@/api/base-model/base-model'
import type { Form } from '../interface/CrudInterface.js'
import { message } from 'ant-design-vue'
import { computed, reactive, ref, toRaw, type UnwrapNestedRefs } from 'vue'
import { ActionExecutor } from './ActionExecutor'

/**
 * 表单处理器类
 * 负责处理表单相关的操作和状态管理
 */
export class FormProcessor<FormModel extends BaseModel> implements Form<FormModel> {
  /**
   * 表单类型
   */
  private _formType = ref<'add' | 'edit' | 'view'>('add')
  /**
   * 表单实体ID
   */
  private _entityId = ref<number>(0)
  /**
   * 表单弹窗状态
   */
  private _formOpen = ref<boolean>(false)
  /**
   * 详情弹窗状态
   */
  private _detailOpen = ref<boolean>(false)
  /**
   * 表单数据 - 用于新增/编辑表单
   */
  formData: UnwrapNestedRefs<FormModel>
  /**
   * 详情数据 - 用于查看详情
   */
  detailData: UnwrapNestedRefs<Partial<FormModel>>
  /**
   * 表单标题
   */
  private _formTitle: ComputedRef<string>
  /**
   * 详情标题
   */
  detailTitle: string

  /**
   * 自定义默认表单数据提供函数
   */
  private _customDefaultFormDataProvider?: () => FormModel = undefined

  /**
   * 设置自定义默认表单数据提供函数
   * @param provider 自定义默认表单数据提供函数
   */
  setCustomDefaultFormData(provider: () => FormModel): void {
    this._customDefaultFormDataProvider = provider
    // 重置表单数据以应用新的默认值
    this.resetFormData()
  }

  /**
   * 获取表单类型
   */
  get formType(): 'add' | 'edit' | 'view' {
    return this._formType.value
  }

  /**
   * 获取表单弹窗状态
   */
  get formOpen(): boolean {
    return this._formOpen.value
  }

  /**
   * 获取详情弹窗状态
   */
  get detailOpen(): boolean {
    return this._detailOpen.value
  }

  /**
   * 获取表单标题
   */
  get formTitle(): string {
    return this._formTitle.value
  }

  /**
   * 获取当前表单处理的实体ID
   */
  get entityId(): number | string | undefined {
    return this._entityId.value === 0 ? undefined : this._entityId.value
  }

  // 兼容旧接口的属性
  get formLoading(): boolean { return false }
  get detailLoading(): boolean { return false }
  get submitLoading(): boolean { return false }

  /**
   * 构造函数
   * @param businessName 业务名称
   * @param api API对象
   */
  constructor(
    private businessName: string,
    private api: {
      getDetail?: (id: number) => Promise<{ data?: FormModel }>
      add?: (data: FormModel) => Promise<unknown>
      update?: (data: FormModel) => Promise<unknown>
    },
  ) {
    // 初始化响应式数据
    this.formData = reactive<FormModel>(this.getDefaultFormData())
    this.detailData = reactive<Partial<FormModel>>({})

    // 初始化表单标题计算属性
    this._formTitle = computed(() => {
      if (this._formType.value === 'view')
        return `${this.businessName}详情`
      else
        return this._formType.value === 'add' ? `新增${this.businessName}` : `编辑${this.businessName}`
    })

    // 初始化详情标题
    this.detailTitle = `${businessName}详情`
  }

  /**
   * 打开添加表单
   */
  openAddForm = (): void => {
    this._formType.value = 'add'
    this._entityId.value = 0
    this.resetFormData()
    this._formOpen.value = true
  }

  /**
   * 打开编辑表单
   */
  openEditForm = (id: number): void => {
    if (!id) {
      message.error(`无法编辑${this.businessName}：缺少ID`)
      return
    }
    this._formType.value = 'edit'
    this._entityId.value = id
    this._formOpen.value = true
    this.fetchEntityForEdit(id)
  }

  /**
   * 查看详情
   */
  openDetailView = (id: number): void => {
    if (!id) {
      message.error(`无法查看${this.businessName}：缺少ID`)
      return
    }

    if (this.api.getDetail) {
      this._entityId.value = id
      this._detailOpen.value = true
      this.fetchEntityForDetail(id)
    }
    else {
      // 如果没有详情API，则直接使用记录数据
      this.detailData = reactive<Partial<FormModel>>({})
      this._formType.value = 'view'
      this._formOpen.value = true
    }
  }

  /**
   * 重置表单数据
   */
  resetFormData = (): void => {
    // 先清空，再重新赋值
    const keys = Object.keys(this.formData)
    keys.forEach((key) => {
      delete (this.formData as Record<string, unknown>)[key]
    })
    // 将默认值复制到表单数据中
    Object.assign(this.formData, this.getDefaultFormData())
  }

  /**
   * 重置详情数据
   */
  resetDetailData = (): void => {
    // 先清空，再重新赋值
    const keys = Object.keys(this.detailData)
    keys.forEach((key) => {
      delete (this.detailData as Record<string, unknown>)[key]
    })
  }

  /**
   * 关闭表单
   */
  closeForm = (): void => {
    this._formOpen.value = false
  }

  /**
   * 关闭详情
   */
  closeDetail = (): void => {
    this._detailOpen.value = false
  }

  /**
   * 获取用于编辑的实体详情
   * @param entityId 实体ID
   */
  private fetchEntityForEdit = async (entityId: number): Promise<void> => {
    if (!entityId || !this.api.getDetail) {
      message.error(`缺少ID或API，无法获取${this.businessName}详情`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        const res = await this.api.getDetail!(entityId)
        if (res.data) {
          // 将实体数据转换为表单数据并更新
          this.resetFormData()
          const formData = this.convertToFormData(res.data)
          Object.assign(this.formData, formData)
        }
        else {
          message.error(`获取${this.businessName}详情失败`)
        }
      },
      `获取${this.businessName}详情失败`,
    )
  }

  /**
   * 获取用于查看的实体详情
   * @param entityId 实体ID
   */
  private fetchEntityForDetail = async (entityId: number): Promise<void> => {
    if (!entityId || !this.api.getDetail) {
      message.error(`缺少ID或API，无法获取${this.businessName}详情`)
      return
    }

    await ActionExecutor.execute(
      async () => {
        const res = await this.api.getDetail!(entityId)
        if (res.data) {
          // 更新详情数据
          this.resetDetailData()
          Object.assign(this.detailData, res.data)
        }
        else {
          message.error(`获取${this.businessName}详情失败`)
        }
      },
      `获取${this.businessName}详情失败`,
    )
  }

  /**
   * 提交表单
   * @param callback 成功后的回调函数
   */
  submitForm = async (callback?: () => void): Promise<{ success: boolean, data: Record<string, unknown> | null }> => {
    if (!this.api.add || !this.api.update) {
      message.error(`${this.businessName}服务缺少必要的API`)
      return { success: false, data: null }
    }

    let responseData: Record<string, unknown> | null = null

    const result = await ActionExecutor.execute(
      async () => {
        // 获取原始数据（非响应式）
        const rawFormData = toRaw(this.formData) as FormModel
        // 准备提交数据
        const submitData = this.beforeSubmit(rawFormData)

        // 根据表单类型选择添加或更新API
        if (this._formType.value === 'add') {
          // 新增模式，移除ID
          if ('id' in submitData) {
            delete submitData.id
          }
          responseData = await this.api.add!(submitData) as Record<string, unknown> | null
          message.success(`新增${this.businessName}成功`)
        }
        else if (this._formType.value === 'edit') {
          // 编辑模式，确保包含ID
          submitData.id = this._entityId.value
          responseData = await this.api.update!(submitData) as Record<string, unknown> | null
          message.success(`更新${this.businessName}成功`)
        }

        // 关闭表单
        this.closeForm()

        // 执行回调
        if (callback) {
          callback()
        }

        return true
      },
      this._formType.value === 'add' ? `新增${this.businessName}失败` : `更新${this.businessName}失败`,
    )

    return {
      success: result === true,
      data: responseData,
    }
  }

  /**
   * 获取默认的表单数据，子类可重写
   */
  protected getDefaultFormData(): FormModel {
    // 如果有自定义提供函数，则使用它
    if (this._customDefaultFormDataProvider) {
      return this._customDefaultFormDataProvider()
    }
    // 否则返回空对象
    return {} as FormModel
  }

  /**
   * 处理表单提交前的数据，子类可重写
   */
  protected beforeSubmit(formData: FormModel): FormModel {
    return { ...formData }
  }

  /**
   * 将实体数据转换为表单数据，子类可重写
   */
  protected convertToFormData(entityData: FormModel): FormModel {
    // 创建一个空的表单数据对象
    const formData = {} as FormModel

    // 自动复制所有在实体数据中存在且非空的字段
    for (const key in entityData) {
      if (Object.prototype.hasOwnProperty.call(entityData, key)) {
        // 将实体数据中的字段复制到表单数据中
        (formData as Record<string, unknown>)[key] = entityData[key]
      }
    }

    // 处理字符串字段的空值
    this.handleStringFields(formData, entityData)

    return formData
  }

  /**
   * 处理字符串字段的空值，子类可重写
   */
  protected handleStringFields(formData: FormModel, _entityData: FormModel): void {
    // 自动处理所有字符串类型的字段，将null/undefined替换为空字符串
    for (const key in formData) {
      if (Object.prototype.hasOwnProperty.call(formData, key)) {
        // 判断是否是字符串类型字段
        const originalValue = formData[key]

        // 检查值是否可能是字符串
        if (
          originalValue === '' || originalValue === null
          || originalValue === undefined
        ) {
          // 将null/undefined的字符串字段设为空字符串
          if (originalValue === null || originalValue === undefined) {
            (formData as Record<string, unknown>)[key] = ''
          }
        }
      }
    }
  }
}
