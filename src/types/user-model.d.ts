import type { MenuTreeVo } from '@/api/system/login/login-model.ts'
import type { LoginResultVo } from '@/api/system/login/model/login-result-vo'
/*
 * @LastEditTime: 2022-05-16 20:59:27
 * @LastEditors: <PERSON><PERSON>aiFan
 * @Description:
 * @FilePath: /typescript-ant-design-vue@/types/user.d.ts
 */
import type { MenuVo } from '@/api/system/menu/model/menu-vo'
import type { UserTagNav } from '@/store/modules/model/UserTagNav'

export interface UserState {
  /**
   * @description: token
   * @param {*}
   * @return {*}
   */
  token?: string
  /**
   * @description: 用户信息
   * @param {*}
   * @return {*}
   */
  userInfo: LoginResultVo
  /**
   * @description: 功能点权限列表
   * @param {*}
   * @return {*}
   */
  pointsList?: Array<string>
  /**
   * @description: 菜单树
   * @param {*}
   * @return {*}
   */
  menuTree?: Array<MenuTreeVo>
  /**
   * @description: 菜单列表 用于构建前端路由
   * @param {*}
   * @return {*}
   */
  menuList?: Array<MenuVo>
  /**
   * @description: tag列表
   * @param {*}
   * @return {*}
   */
  tagNav?: Array<UserTagNav>
  /**
   * @description: keep-alive缓存菜单
   * @param {*}
   * @return {*}
   */
  keepAliveIncludes?: string[]
}
