declare interface DictItem {
  valueCode: string | number
  code: string
  valueName: string
}

  declare type DictValueList = DictItem[]

/**
 *  语言 i18n
 */
// declare type LanguageType = 'zh_CN' | 'en_US' | 'ru' | 'ja' | 'ko'
declare type LanguageType = 'zh_CN' | 'en_US'

/**
 * 四种布局: 左侧、左侧展开、顶部、混合
 */
declare type LayoutType = 'side' | 'side-expand'

/**
 * 主题： 亮色，暗色，夜色
 */
declare type ThemeType = 'light' | 'dark'

/**
 * 应用信息配置
 */
declare interface AppConfig {
  // i18n 语言选择
  language: LanguageType
  // 布局
  layout: string
  // 主题
  sideMenuTheme: ThemeType
  // 侧边菜单宽度 ， 默认为256px
  sideMenuWidth: number
  // 主题颜色索引
  colorIndex: number
  // 顶部菜单页面宽度
  pageWidth: string
  // 圆角
  borderRadius: number
  // 标签页
  pageTagFlag: boolean
  // 标签页样式: default、 antd、naive
  pageTagStyle: string
  // 面包屑
  breadCrumbFlag: boolean
  // 页脚
  footerFlag: boolean
  // 帮助文档
  helpDocFlag: boolean
  // 水印
  watermarkFlag: boolean
  // 网站名称
  websiteName: string
  // 主题颜色
  primaryColor: string
  // 紧凑
  compactFlag: boolean
  // 帮助文档显示
  helpDocExpandFlag: boolean
  // 全屏
  fullScreenFlag: boolean
}

declare interface SmartEnumWrapper<T> {
  [key: string]: SmartEnum<T>
}

declare interface SmartEnum<T> {
  [key: string]: SmartEnumItem<T>
}

interface SmartEnumItem<T> {
  value: T
  desc: string
}

interface SmartEnumPlugin {
  getDescByValue: (constantName: string, value: string | number | undefined) => string

  getValueDescList: (constantName: string) => SmartEnumItem[]

  getValueDesc: (constantName: string) => { [key: string]: string }
}
