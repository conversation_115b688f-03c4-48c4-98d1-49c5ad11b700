/*
 * 所有路由入口
 */
import NoPrivilege from '@/views/system/40X/403.vue'
import NotFound from '@/views/system/40X/404.vue'
import { helpDocRouters } from './support/help-doc'
import { homeRouters } from './system/home'
import { loginRouters } from './system/login'

export const routerArray = [
  ...loginRouters,
  ...homeRouters,
  ...helpDocRouters,
  { path: '/:pathMatch(.*)*', name: '404', component: NotFound },
  { path: '/403', name: '403', component: NoPrivilege },
]
