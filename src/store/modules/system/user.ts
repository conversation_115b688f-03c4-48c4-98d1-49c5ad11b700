import type { RouteLocationNormalized, Router } from 'vue-router'
import { messageApi } from '@/api/support/message-api.js'
import localKey from '@/constants/local-storage-key-const'
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { MENU_TYPE_ENUM } from '@/constants/system/menu-const'
import { smartSentry } from '@/lib/smart-sentry.js'
import { localRead, localRemove, localSave } from '@/utils/local-util'
import { cloneDeep, isEmpty, isNull } from 'lodash-es'
import { defineStore } from 'pinia'

// ===================== 类型定义 =====================

/** 菜单项类型 */
interface MenuItem {
  menuId: number
  menuName: string
  menuTitle?: string
  parentId: number
  menuType: number
  path?: string
  frameUrl?: string
  visibleFlag: boolean
  disabledFlag: boolean
  children?: MenuItem[]
}

/** 标签页导航项 */
interface TagNavItem {
  menuName: string
  menuTitle: string
  menuQuery?: Record<string, any>
  fromMenuName?: string
  fromMenuQuery?: Record<string, any>
}

/** 待办事项 */
interface ToDoItem {
  doneFlag: boolean
  [key: string]: any
}

/** 父级菜单项 */
interface ParentMenuItem {
  name: string
  title: string
}

/** 用户登录信息 */
interface UserLoginInfo {
  token: string
  employeeId: string
  avatar: string
  loginName: string
  actualName: string
  phone: string
  departmentId: string
  departmentName: string
  needUpdatePwdFlag: boolean
  administratorFlag: boolean
  lastLoginIp: string
  lastLoginIpRegion: string
  lastLoginUserAgent: string
  lastLoginTime: string
  menuList: MenuItem[]
}

/** 用户状态 */
interface UserState {
  token: string
  employeeId: string
  avatar: string
  loginName: string
  actualName: string
  phone: string
  departmentId: string
  departmentName: string
  needUpdatePwdFlag: boolean
  administratorFlag: boolean
  lastLoginIp: string
  lastLoginIpRegion: string
  lastLoginUserAgent: string
  lastLoginTime: string
  menuTree: MenuItem[]
  menuRouterList: MenuItem[]
  menuRouterInitFlag: boolean
  menuParentIdListMap: Map<string, ParentMenuItem[]>
  pointsList: MenuItem[]
  tagNav: TagNavItem[] | null
  keepAliveIncludes: string[]
  unreadMessageCount: number
  toBeDoneCount: number
}

// ===================== Store定义 =====================

export const useUserStore = defineStore('userStore', {
  state: (): UserState => ({
    token: '',
    employeeId: '',
    avatar: '',
    loginName: '',
    actualName: '',
    phone: '',
    departmentId: '',
    departmentName: '',
    needUpdatePwdFlag: false,
    administratorFlag: true,
    lastLoginIp: '',
    lastLoginIpRegion: '',
    lastLoginUserAgent: '',
    lastLoginTime: '',
    menuTree: [],
    menuRouterList: [],
    menuRouterInitFlag: false,
    menuParentIdListMap: new Map(),
    pointsList: [],
    tagNav: null,
    keepAliveIncludes: [],
    unreadMessageCount: 0,
    toBeDoneCount: 0,
  }),

  getters: {
    getToken(state): string {
      if (state.token) {
        return state.token
      }
      return localRead(localKey.USER_TOKEN) || ''
    },

    getNeedUpdatePwdFlag(state): boolean {
      return state.needUpdatePwdFlag
    },

    getMenuRouterInitFlag(state): boolean {
      return state.menuRouterInitFlag
    },

    getMenuTree(state): MenuItem[] {
      return state.menuTree
    },

    getMenuRouterList(state): MenuItem[] {
      return state.menuRouterList
    },

    getMenuParentIdListMap(state): Map<string, ParentMenuItem[]> {
      return state.menuParentIdListMap
    },

    getPointList(state): MenuItem[] {
      if (isEmpty(state.pointsList)) {
        const localUserPoints = localRead(localKey.USER_POINTS) || ''
        state.pointsList = localUserPoints ? JSON.parse(localUserPoints) : []
      }
      return state.pointsList
    },

    getTagNav(state): TagNavItem[] {
      if (isNull(state.tagNav)) {
        const localTagNav = localRead(localKey.USER_TAG_NAV) || ''
        state.tagNav = localTagNav ? JSON.parse(localTagNav) : []
      }
      const tagNavList = cloneDeep(state.tagNav) || []

      tagNavList.unshift({
        menuName: HOME_PAGE_NAME,
        menuTitle: '首页',
      })
      return tagNavList
    },
  },

  actions: {
    logout(): void {
      this.token = ''
      this.tagNav = []
      this.unreadMessageCount = 0
      localRemove(localKey.USER_TOKEN)
      localRemove(localKey.USER_POINTS)
      localRemove(localKey.USER_TAG_NAV)
    },

    async queryUnreadMessageCount(): Promise<void> {
      try {
        const result = await messageApi.queryUnreadCount()
        this.unreadMessageCount = result.data
      }
      catch (e) {
        smartSentry.captureError(e)
      }
    },

    async queryToBeDoneList(): Promise<void> {
      try {
        const localToBeDoneList = localRead(localKey.TO_BE_DONE)
        if (localToBeDoneList) {
          const todoList: ToDoItem[] = JSON.parse(localToBeDoneList)
          this.toBeDoneCount = todoList.filter(e => !e.doneFlag).length
        }
      }
      catch (err) {
        smartSentry.captureError(err)
      }
    },

    setUserLoginInfo(data: UserLoginInfo): void {
      // 用户基本信息
      this.token = data.token
      this.employeeId = data.employeeId
      this.avatar = data.avatar
      this.loginName = data.loginName
      this.actualName = data.actualName
      this.phone = data.phone
      this.departmentId = data.departmentId
      this.departmentName = data.departmentName
      this.needUpdatePwdFlag = data.needUpdatePwdFlag
      this.administratorFlag = data.administratorFlag
      this.lastLoginIp = data.lastLoginIp
      this.lastLoginIpRegion = data.lastLoginIpRegion
      this.lastLoginUserAgent = data.lastLoginUserAgent
      this.lastLoginTime = data.lastLoginTime

      // 菜单权限
      this.menuTree = buildMenuTree(data.menuList)

      // 拥有路由的菜单
      this.menuRouterList = data.menuList.filter(e => e.path || e.frameUrl)

      // 父级菜单集合
      this.menuParentIdListMap = buildMenuParentIdListMap(this.menuTree)

      // 功能点
      this.pointsList = data.menuList.filter(
        menu => menu.menuType === MENU_TYPE_ENUM.POINTS.value && menu.visibleFlag && !menu.disabledFlag,
      )

      // 获取用户未读消息
      this.queryUnreadMessageCount()
      // 获取待办工作数
      this.queryToBeDoneList()
    },

    setToken(token: string): void {
      this.token = token
    },

    setTagNav(route: RouteLocationNormalized, from: RouteLocationNormalized): void {
      if (isNull(this.tagNav)) {
        const localTagNav = localRead(localKey.USER_TAG_NAV) || ''
        this.tagNav = localTagNav ? JSON.parse(localTagNav) : []
      }

      const name = route.name as string
      if (!name || name === HOME_PAGE_NAME || name === '403' || name === '404') {
        return
      }

      const tagNavList = this.tagNav || []
      const findTag = tagNavList.find(e => e.menuName === name)

      if (findTag) {
        findTag.fromMenuName = from.name as string
        findTag.fromMenuQuery = from.query
      }
      else {
        tagNavList.push({
          menuName: name,
          menuTitle: route.meta.title as string,
          menuQuery: route.query,
          fromMenuName: from.name as string,
          fromMenuQuery: from.query,
        })
      }

      localSave(localKey.USER_TAG_NAV, JSON.stringify(this.tagNav))
    },

    closeTagNav(menuName?: string, closeAll?: boolean): void {
      if (isEmpty(this.getTagNav))
        return

      if (closeAll && !menuName) {
        this.tagNav = []
        this.clearKeepAliveIncludes()
      }
      else {
        const tagNavList = this.tagNav || []
        const findIndex = tagNavList.findIndex(e => e.menuName === menuName)

        if (closeAll) {
          if (findIndex === -1) {
            this.tagNav = []
            this.clearKeepAliveIncludes()
          }
          else {
            const tagNavElement = tagNavList[findIndex]
            this.tagNav = [tagNavElement]
            this.clearKeepAliveIncludes(tagNavElement.menuName)
          }
        }
        else {
          tagNavList.splice(findIndex, 1)
          this.deleteKeepAliveIncludes(menuName)
        }
      }

      localSave(localKey.USER_TAG_NAV, JSON.stringify(this.tagNav))
    },

    closePage(route: RouteLocationNormalized, router: Router, path?: string): void {
      if (!this.getTagNav || isEmpty(this.getTagNav))
        return

      if (path) {
        router.push({ path })
      }
      else {
        const index = this.getTagNav.findIndex(e => e.menuName === route.name)
        if (index === -1) {
          router.push({ name: HOME_PAGE_NAME })
        }
        else {
          const tagNav = this.getTagNav[index]
          if (tagNav.fromMenuName && this.getTagNav.some(e => e.menuName === tagNav.fromMenuName)) {
            router.push({ name: tagNav.fromMenuName, query: tagNav.fromMenuQuery })
          }
          else {
            const leftTagNav = this.getTagNav[index - 1]
            router.push({ name: leftTagNav.menuName, query: leftTagNav.menuQuery })
          }
        }
      }

      this.closeTagNav(route.name as string, false)
    },

    pushKeepAliveIncludes(val: string): void {
      if (!val)
        return

      if (!this.keepAliveIncludes) {
        this.keepAliveIncludes = []
      }

      if (this.keepAliveIncludes.length < 30 && !this.keepAliveIncludes.includes(val)) {
        this.keepAliveIncludes.push(val)
      }
    },

    deleteKeepAliveIncludes(val?: string): void {
      if (!this.keepAliveIncludes || !val)
        return

      const index = this.keepAliveIncludes.indexOf(val)
      if (index !== -1) {
        this.keepAliveIncludes.splice(index, 1)
      }
    },

    clearKeepAliveIncludes(val?: string): void {
      if (!val || !this.keepAliveIncludes.includes(val)) {
        this.keepAliveIncludes = []
        return
      }
      this.keepAliveIncludes = [val]
    },
  },
})

// ===================== 工具函数 =====================

/**
 * 构建菜单树
 * @param menuList 扁平化的菜单列表
 * @returns 树形结构的菜单
 */
function buildMenuTree(menuList: MenuItem[]): MenuItem[] {
  // 1. 过滤出有效的目录和菜单（不包含功能点）
  const validMenuList = menuList.filter(
    menu => menu.menuType !== MENU_TYPE_ENUM.POINTS.value && menu.visibleFlag && !menu.disabledFlag,
  )

  // 2. 创建菜单映射表，便于快速查找
  const menuMap = new Map<number, MenuItem>()
  validMenuList.forEach((menu) => {
    menuMap.set(menu.menuId, { ...menu, children: [] })
  })

  // 3. 构建树形结构
  const menuTree: MenuItem[] = []

  validMenuList.forEach((menu) => {
    const menuItem = menuMap.get(menu.menuId)!

    if (menu.parentId === 0) {
      // 顶级菜单
      menuTree.push(menuItem)
    }
    else {
      // 子菜单
      const parentMenu = menuMap.get(menu.parentId)
      if (parentMenu) {
        if (!parentMenu.children) {
          parentMenu.children = []
        }
        parentMenu.children.push(menuItem)
      }
    }
  })

  return menuTree
}

/**
 * 构建菜单父级ID映射表
 * @param menuTree 菜单树
 * @returns 菜单ID到其所有父级菜单的映射
 */
function buildMenuParentIdListMap(menuTree: MenuItem[]): Map<string, ParentMenuItem[]> {
  const menuParentIdListMap = new Map<string, ParentMenuItem[]>()

  // 递归遍历菜单树
  function traverse(menuList: MenuItem[], parentPath: ParentMenuItem[]): void {
    menuList.forEach((menu) => {
      const currentPath = [...parentPath]
      const menuIdStr = menu.menuId.toString()

      // 为当前菜单设置父级路径
      menuParentIdListMap.set(menuIdStr, currentPath)

      // 如果有子菜单，继续递归
      if (menu.children && menu.children.length > 0) {
        // 将当前菜单加入到路径中
        currentPath.push({
          name: menuIdStr,
          title: menu.menuName || menu.menuTitle || '',
        })
        traverse(menu.children, currentPath)
      }
    })
  }

  traverse(menuTree, [])
  return menuParentIdListMap
}
