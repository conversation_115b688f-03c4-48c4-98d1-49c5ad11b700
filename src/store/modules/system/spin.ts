/*
 * loading
 */
import { defineStore } from 'pinia'

export const useSpinStore = defineStore('spinStore', {
  state: () => ({
    loading: false,
  }),

  actions: {
    hide() {
      this.loading = false
      const spins = document.querySelector('.ant-spin-nested-loading')
      spins.style.zIndex = 999
    },
    show() {
      this.loading = true
      const spins = document.querySelector('.ant-spin-nested-loading')
      spins.style.zIndex = 1001
    },
  },
})
