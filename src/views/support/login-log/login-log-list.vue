<!--
  * 登录、登出 日志
-->
<script setup lang="ts">
import { loginLogApi } from '@/api/support/login-log-api'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { LOGIN_RESULT_ENUM } from '@/constants/support/login-log-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { defaultTimeRanges } from '@/lib/default-time-ranges'
import { smartSentry } from '@/lib/smart-sentry'
import { calcTableHeight } from '@/lib/table-auto-height'
import uaparser from 'ua-parser-js'
import { onMounted, onUnmounted, reactive, ref } from 'vue'

const columns = ref([
  {
    title: '用户ID',
    dataIndex: 'userId',
    width: 70,
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    ellipsis: true,
  },
  {
    title: '类型',
    dataIndex: 'userType',
    width: 50,
    ellipsis: true,
  },
  {
    title: 'IP',
    dataIndex: 'loginIp',
    ellipsis: true,
  },
  {
    title: 'IP地区',
    dataIndex: 'loginIpRegion',
    ellipsis: true,
  },
  {
    title: '设备信息',
    dataIndex: 'userAgent',
    ellipsis: true,
  },
  {
    title: '结果',
    dataIndex: 'loginResult',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '时间',
    dataIndex: 'createTime',
    width: 150,
  },
])

const queryFormState = {
  userName: '',
  ip: '',
  startDate: undefined,
  endDate: undefined,
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
const createDateRange = ref([])
const defaultChooseTimeRange = defaultTimeRanges
// 时间变动
function changeCreateDate(dates, dateStrings) {
  queryForm.startDate = dateStrings[0]
  queryForm.endDate = dateStrings[1]
}

const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  createDateRange.value = []
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await loginLogApi.queryList(queryForm)

    for (const e of responseModel.data.list) {
      if (!e.userAgent) {
        continue
      }
      const ua = uaparser(e.userAgent)
      e.browser = ua.browser.name
      e.os = ua.os.name
      e.device = ua.device.vendor ? ua.device.vendor + ua.device.model : ''
    }

    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ----------------- 表格自适应高度 --------------------
const scrollY = ref(100)
const tableOperatorRef = ref()
const queryFormRef = ref()

function autoCalcTableHeight() {
  calcTableHeight(scrollY, [tableOperatorRef, queryFormRef], 10)
}

window.addEventListener('resize', autoCalcTableHeight)

onMounted(() => {
  ajaxQuery()
  autoCalcTableHeight()
})

onUnmounted(() => {
  window.removeEventListener('resize', autoCalcTableHeight)
})
</script>

<template>
  <a-form ref="queryFormRef" v-privilege="'support:loginLog:query'" class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="用户名称" class="smart-query-form-item">
        <a-input v-model:value="queryForm.userName" style="width: 300px" placeholder="用户名称" />
      </a-form-item>

      <a-form-item label="用户IP" class="smart-query-form-item">
        <a-input v-model:value="queryForm.ip" style="width: 120px" placeholder="IP" />
      </a-form-item>

      <a-form-item label="时间" class="smart-query-form-item">
        <a-range-picker v-model:value="createDateRange" :presets="defaultChooseTimeRange" style="width: 240px" @change="changeCreateDate" />
      </a-form-item>

      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row ref="tableOperatorRef" justify="end">
      <TableOperator v-model="columns" class="smart-margin-bottom5" :table-id="TABLE_ID_CONST.SUPPORT.LOGIN_LOG" :refresh="ajaxQuery" />
    </a-row>
    <a-table
      size="small"
      :data-source="tableData"
      :columns="columns"
      bordered
      row-key="loginLogId"
      :pagination="false"
      :loading="tableLoading"
      :scroll="{ y: scrollY }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'loginResult'">
          <template v-if="text === LOGIN_RESULT_ENUM.LOGIN_SUCCESS.value">
            <a-tag color="success">
              登录成功
            </a-tag>
          </template>
          <template v-if="text === LOGIN_RESULT_ENUM.LOGIN_FAIL.value">
            <a-tag color="error">
              登录失败
            </a-tag>
          </template>
          <template v-if="text === LOGIN_RESULT_ENUM.LOGIN_OUT.value">
            <a-tag color="processing">
              退出登录
            </a-tag>
          </template>
        </template>

        <template v-if="column.dataIndex === 'userAgent'">
          <div>{{ record.browser }} / {{ record.os }} / {{ record.device }}</div>
        </template>

        <template v-if="column.dataIndex === 'userType'">
          <span>{{ $smartEnumPlugin.getDescByValue('USER_TYPE_ENUM', text) }}</span>
        </template>
      </template>
    </a-table>

    <div class="table-pagination">
      <a-pagination
        v-model:current="queryForm.pageNum"
        v-model:page-size="queryForm.pageSize"
        show-size-changer
        show-quick-jumper
        show-less-items
        :page-size-options="PAGE_SIZE_OPTIONS"
        :default-page-size="queryForm.pageSize"
        :total="total"
        :show-total="(total) => `共${total}条`"
        @change="ajaxQuery"
        @show-size-change="ajaxQuery"
      />
    </div>
  </a-card>
</template>
