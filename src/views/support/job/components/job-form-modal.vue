<!--
  * JOB 表单
-->
<script setup lang="ts">
import { jobApi } from '@/api/support/job-api'
import { SmartLoading } from '@/components/base-old/smart-loading/index.js'
import { TRIGGER_TYPE_ENUM } from '@/constants/support/job-const.js'
import { smartSentry } from '@/lib/smart-sentry'
import { message } from 'ant-design-vue'
import { reactive, ref } from 'vue'

// emit
const emit = defineEmits(['reloadList'])

const isAdd = ref(false)
const updateModalShow = ref(false)
const updateFormRef = ref()

const updateFormDefault = {
  jobId: null,
  jobName: '',
  jobClass: '',
  triggerType: TRIGGER_TYPE_ENUM.CRON.value,
  triggerValue: null,
  cron: '',
  fixedDelay: null,
  param: '',
  enabledFlag: false,
  remark: '',
  sort: null,
}
const updateForm = reactive({ ...updateFormDefault })
const updateRules = {
  jobName: [{ required: true, message: '请输入任务名称' }],
  jobClass: [{ required: true, message: '请输入执行类' }],
  triggerType: [{ required: true, message: '请选择触发类型' }],
  sort: [{ required: true, message: '请输入排序' }],
}

// 打开编辑弹框
function openUpdateModal(record) {
  isAdd.value = record == null
  // 更新
  if (!isAdd.value) {
    Object.assign(updateForm, record)
    if (TRIGGER_TYPE_ENUM.CRON.value === record.triggerType) {
      updateForm.cron = record.triggerValue
    }
    if (TRIGGER_TYPE_ENUM.FIXED_DELAY.value === record.triggerType) {
      updateForm.fixedDelay = record.triggerValue
    }
  }
  updateModalShow.value = true
}

// 关闭编辑弹框
function closeUpdateModal() {
  Object.assign(updateForm, updateFormDefault)
  updateModalShow.value = false
}

// 确认更新
async function confirmUpdateJob() {
  updateFormRef.value
    .validate()
    .then(async () => {
      if (TRIGGER_TYPE_ENUM.CRON.value === updateForm.triggerType) {
        updateForm.triggerValue = updateForm.cron
      }
      if (TRIGGER_TYPE_ENUM.FIXED_DELAY.value === updateForm.triggerType) {
        updateForm.triggerValue = updateForm.fixedDelay
      }

      if (!updateForm.triggerValue) {
        message.error('请填写 触发时间')
        return
      }

      try {
        SmartLoading.show()
        if (isAdd.value) {
          await jobApi.addJob(updateForm)
          message.success('添加成功')
        }
        else {
          await jobApi.updateJob(updateForm)
          message.success('更新成功')
        }
        closeUpdateModal()
        emit('reloadList')
      }
      catch (error) {
        smartSentry.captureError(error)
      }
      finally {
        SmartLoading.hide()
      }
    })
    .catch((error) => {
      console.log('error', error)
      message.error('参数验证错误，请检查表单数据')
    })
}

// ------------------------------------ 执行任务 -------------------------------------
const executeModalShow = ref(false)
const executeFormDefault = {
  jobId: null,
  jobName: '',
  jobClass: '',
  param: null,
}
const executeForm = reactive({ ...executeFormDefault })

// 打开执行弹框
function openExecuteModal(record) {
  Object.assign(executeForm, record)
  executeModalShow.value = true
}

// 关闭执行弹框
function closeExecuteModal() {
  Object.assign(executeForm, executeFormDefault)
  executeModalShow.value = false
}

// 确认执行
async function confirmExecuteJob() {
  try {
    const executeParam = {
      jobId: executeForm.jobId,
      param: executeForm.param,
    }
    await jobApi.executeJob(executeParam)
    // loading 延迟后再提示刷新
    SmartLoading.show()
    await new Promise(resolve => setTimeout(resolve, 2000))
    message.success('执行成功')
    closeExecuteModal()
    emit('reloadList')
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    SmartLoading.hide()
  }
}

defineExpose({
  openUpdateModal,
  openExecuteModal,
})
</script>

<template>
  <div>
    <!-- 编辑 -->
    <a-modal :open="updateModalShow" :width="650" :title="isAdd ? '添加' : '编辑'" ok-text="确认" cancel-text="取消" @cancel="closeUpdateModal" @ok="confirmUpdateJob">
      <a-form ref="updateFormRef" :model="updateForm" :rules="updateRules" :label-col="{ span: 4 }">
        <a-form-item label="任务名称" name="jobName">
          <a-input v-model:value="updateForm.jobName" placeholder="请输入任务名称" :maxlength="100" :show-count="true" />
        </a-form-item>
        <a-form-item label="任务描述" name="remark">
          <a-textarea
            v-model:value="updateForm.remark"
            :auto-size="{ minRows: 2, maxRows: 4 }"
            placeholder="(可选)请输入任务备注描述"
            :maxlength="250"
            :show-count="true"
          />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="updateForm.sort"
            :min="-99999999"
            :max="99999999"
            :precision="0"
            style="width: 100%"
            placeholder="值越小越靠前"
          />
        </a-form-item>
        <a-form-item label="执行类" name="jobClass">
          <a-textarea
            v-model:value="updateForm.jobClass"
            :auto-size="{ minRows: 2, maxRows: 4 }"
            placeholder="示例：sample.job.support.module.base.com.anxys.SmartJobSample1"
            :maxlength="200"
            :show-count="true"
          />
        </a-form-item>
        <a-form-item label="任务参数" name="param">
          <a-textarea
            v-model:value="updateForm.param"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="(可选)请输入任务执行参数"
            :maxlength="1000"
            :show-count="true"
          />
        </a-form-item>
        <a-form-item label="触发类型" name="triggerType">
          <a-radio-group v-model:value="updateForm.triggerType">
            <a-radio-button :value="TRIGGER_TYPE_ENUM.CRON.value">
              CRON表达式
            </a-radio-button>
            <a-radio-button :value="TRIGGER_TYPE_ENUM.FIXED_DELAY.value">
              固定间隔
            </a-radio-button>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="触发时间" name="triggerTime">
          <a-input
            v-if="updateForm.triggerType === TRIGGER_TYPE_ENUM.CRON.value"
            v-model:value="updateForm.cron"
            placeholder="示例：10 15 0/1 * * *"
            :maxlength="100"
            :show-count="true"
          />
          <a-input-number
            v-else-if="updateForm.triggerType === TRIGGER_TYPE_ENUM.FIXED_DELAY.value"
            v-model:value="updateForm.fixedDelay"
            :min="1"
            :max="100000000"
            :precision="0"
          >
            <template #addonBefore>
              每隔
            </template>
            <template #addonAfter>
              秒
            </template>
          </a-input-number>
        </a-form-item>
        <a-form-item label="是否开启" name="enabledFlag">
          <a-switch v-model:checked="updateForm.enabledFlag" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 立即执行 -->
    <a-modal
      :open="executeModalShow"
      :width="650"
      title="执行任务"
      ok-text="执行"
      cancel-text="取消"
      @cancel="closeExecuteModal"
      @ok="confirmExecuteJob"
    >
      <br>
      <a-alert type="info" show-icon style="margin-left: 25px">
        <template #message>
          点击【执行】后会按照【任务参数】，无论任务是否开启，都会立即执行。
        </template>
      </a-alert>
      <br>
      <a-form :label-col="{ span: 4 }">
        <a-form-item label="任务名称" name="jobName">
          <a-input v-model:value="executeForm.jobName" :disabled="true" />
        </a-form-item>
        <a-form-item label="任务类名" name="jobClass">
          <a-textarea v-model:value="executeForm.jobClass" :auto-size="{ minRows: 2, maxRows: 4 }" :disabled="true" />
        </a-form-item>
        <a-form-item label="任务参数" name="param">
          <a-textarea
            v-model:value="executeForm.param"
            :auto-size="{ minRows: 3, maxRows: 6 }"
            placeholder="(可选)请输入任务执行参数"
            :maxlength="1000"
            :show-count="true"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
