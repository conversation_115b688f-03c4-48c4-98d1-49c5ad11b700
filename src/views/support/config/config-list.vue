<!--
  * 系统设置 列表
-->
<script setup lang="ts">
import { configApi } from '@/api/support/config-api'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { smartSentry } from '@/lib/smart-sentry'
import { onMounted, reactive, ref } from 'vue'
import ConfigFormModal from './config-form-modal.vue'

const columns = ref([
  {
    title: 'id',
    width: 50,
    dataIndex: 'configId',
  },
  {
    title: '参数key',
    dataIndex: 'configKey',
    ellipsis: true,
  },
  {
    title: '参数名称',
    dataIndex: 'configName',
    ellipsis: true,
  },
  {
    title: '参数值',
    dataIndex: 'configValue',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '修改时间',
    dataIndex: 'updateTime',
    width: 150,
  },

  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 60,
  },
])

// ---------------- 查询数据 -----------------------

const queryFormState = {
  configKey: '',
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })

const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await configApi.queryList(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ------------------------- 表单操作 弹窗 ------------------------------

const configFormModal = ref()
function toEditOrAdd(rowData) {
  configFormModal.value.showModal(rowData)
}

onMounted(ajaxQuery)
</script>

<template>
  <div>
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="参数Key" class="smart-query-form-item">
          <a-input v-model:value="queryForm.configKey" style="width: 300px" placeholder="请输入key" />
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button-group>
            <a-button v-privilege="'support:config:query'" type="primary" @click="onSearch">
              <template #icon>
                <ReloadOutlined />
              </template>
              查询
            </a-button>
            <a-button v-privilege="'support:config:query'" @click="resetQuery">
              <template #icon>
                <SearchOutlined />
              </template>
              重置
            </a-button>
          </a-button-group>
          <a-button v-privilege="'support:config:add'" type="primary" class="smart-margin-left20" @click="toEditOrAdd()">
            <template #icon>
              <PlusOutlined />
            </template>
            新建
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <a-card size="small" :bordered="false" :hoverable="true">
      <a-row justify="end">
        <TableOperator v-model="columns" class="smart-margin-bottom5" :table-id="TABLE_ID_CONST.SUPPORT.CONFIG" :refresh="ajaxQuery" />
      </a-row>

      <a-table size="small" :loading="tableLoading" bordered :data-source="tableData" :columns="columns" row-key="configId" :pagination="false">
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button v-privilege="'support:config:update'" type="link" @click="toEditOrAdd(record)">
                编辑
              </a-button>
            </div>
          </template>
        </template>
      </a-table>

      <div class="table-pagination">
        <a-pagination
          v-model:current="queryForm.pageNum"
          v-model:page-size="queryForm.pageSize"
          show-size-changer
          show-quick-jumper
          show-less-items
          :page-size-options="PAGE_SIZE_OPTIONS"
          :default-page-size="queryForm.pageSize"
          :total="total"
          :show-total="(total) => `共${total}条`"
          @change="ajaxQuery"
          @show-size-change="ajaxQuery"
        />
      </div>
    </a-card>
    <ConfigFormModal ref="configFormModal" @reload-list="resetQuery" />
  </div>
</template>
