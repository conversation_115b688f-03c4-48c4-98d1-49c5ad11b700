<!--
  * 帮助文档 管理
-->
<script setup lang="ts">
import { computed, ref } from 'vue'
import HelpDocCatalogTree from './components/help-doc-catalog-tree.vue'
import HelpDocList from './components/help-doc-list.vue'

const helpDocCatalogTreeRef = ref()

// 当前选中的目录id
const selectedHelpDocCatalogId = computed(() => {
  if (helpDocCatalogTreeRef.value) {
    const selectedKeys = helpDocCatalogTreeRef.value.selectedKeys
    return _isEmpty(selectedKeys) ? null : selectedKeys[0]
  }
  return null
})
</script>

<template>
  <div class="height100">
    <a-row :gutter="16" class="height100">
      <a-col :span="6">
        <HelpDocCatalogTree ref="helpDocCatalogTreeRef" />
      </a-col>

      <a-col :span="18" class="height100">
        <div class="help-doc-box height100">
          <HelpDocList :help-doc-catalog-id="selectedHelpDocCatalogId" />
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<style scoped lang="less">
  .height100 {
    height: 100%;
  }
  .help-doc-box {
    display: flex;
    flex-direction: column;

    .employee {
      flex-grow: 2;
      margin-top: 10px;
    }
  }
</style>
