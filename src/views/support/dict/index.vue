<!--
  * 数据 字典
-->
<script setup lang="ts">
import { dictApi } from '@/api/support/dict-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { smartSentry } from '@/lib/smart-sentry'
import { message, Modal } from 'ant-design-vue'
import { onMounted, reactive, ref } from 'vue'
import DictKeyOperateModal from './components/dict-key-operate-modal.vue'
import DictValueModal from './components/dict-value-modal.vue'

const columns = ref([
  {
    title: 'ID',
    width: 90,
    dataIndex: 'dictKeyId',
  },
  {
    title: '编码',
    dataIndex: 'keyCode',
  },
  {
    title: '名称',
    dataIndex: 'keyName',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 50,
  },
])

// ---------------- 查询数据 -----------------

const queryFormState = {
  searchWord: '',
  pageNum: 1,
  pageSize: 10,
}
const queryForm = reactive({ ...queryFormState })
const tableLoading = ref(false)
const selectedRowKeyList = ref([])
const tableData = ref([])
const total = ref(0)
const operateModal = ref()
const dictValueModal = ref()

// 显示操作记录弹窗
function showValueList(dictKeyId) {
  dictValueModal.value.showModal(dictKeyId)
}

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys
}

function resetQuery() {
  Object.assign(queryForm, queryFormState)
  ajaxQuery()
}
function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}
async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await dictApi.keyQuery(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

// ---------------- 刷新缓存 -----------------

async function cacheRefresh() {
  try {
    SmartLoading.show()
    await dictApi.cacheRefresh()
    message.success('缓存刷新成功')
    ajaxQuery()
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    SmartLoading.hide()
  }
}

// ---------------- 批量 删除 -----------------

function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选中Key吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      batchDelete()
    },
    cancelText: '取消',
    onCancel() {},
  })
}

async function batchDelete() {
  try {
    SmartLoading.show()
    await dictApi.keyDelete(selectedRowKeyList.value)
    message.success('删除成功')
    ajaxQuery()
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    SmartLoading.hide()
  }
}

// ---------------- 添加/更新 -----------------

function addOrUpdateKey(rowData) {
  operateModal.value.showModal(rowData)
}

onMounted(ajaxQuery)
</script>

<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字" class="smart-query-form-item">
        <a-input v-model:value="queryForm.searchWord" style="width: 300px" placeholder="关键字" />
      </a-form-item>

      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <ReloadOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <SearchOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button v-privilege="'support:dict:add'" type="primary" @click="addOrUpdateKey">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>

        <a-button v-privilege="'support:dict:batchDelete'" type="text" danger :disabled="selectedRowKeyList.length === 0" @click="confirmBatchDelete">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>

        <a-button v-privilege="'support:dict:refresh'" type="primary" @click="cacheRefresh">
          <template #icon>
            <cloud-sync-outlined />
          </template>
          缓存刷新
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" class="smart-margin-bottom5" :table-id="TABLE_ID_CONST.SUPPORT.DICT" :refresh="ajaxQuery" />
      </div>
    </a-row>

    <a-table
      size="small"
      :data-source="tableData"
      :columns="columns"
      :loading="tableLoading"
      row-key="dictKeyId"
      :pagination="false"
      bordered
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'keyCode'">
          <a @click="showValueList(record.dictKeyId)">{{ record.keyCode }}</a>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button v-privilege="'support:dict:edit'" type="link" @click="addOrUpdateKey(record)">
              编辑
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="table-pagination">
      <a-pagination
        v-model:current="queryForm.pageNum"
        v-model:page-size="queryForm.pageSize"
        show-size-changer
        show-quick-jumper
        show-less-items
        :page-size-options="PAGE_SIZE_OPTIONS"
        :default-page-size="queryForm.pageSize"
        :total="total"
        :show-total="(total) => `共${total}条`"
        @change="ajaxQuery"
        @show-size-change="ajaxQuery"
      />
    </div>

    <DictKeyOperateModal ref="operateModal" @reload-list="ajaxQuery" />
    <!-- 值列表 -->
    <DictValueModal ref="dictValueModal" />
  </a-card>
</template>
