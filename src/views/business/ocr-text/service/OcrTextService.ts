import { ocrTextApi } from '@/api/business/ocr-text/ocr-text-api'
import { message } from 'ant-design-vue'

/**
 * OCR文字识别服务
 * 提供OCR文字识别相关的业务逻辑和数据管理
 */
export class OcrTextService {
  /**
   * 上传单张图片并识别文字
   * @param file 图片文件
   */
  async recognizeImage(file: File): Promise<string> {
    try {
      const response = await ocrTextApi.uploadAndRecognize(file)
      if (response.success && response.data) {
        message.success('图片识别成功')
        return response.data.text
      }
      message.error(`识别失败: ${response.msg || '未知错误'}`)
      return ''
    }
    catch (error) {
      console.error('图片识别失败:', error)
      message.error(`识别失败: ${(error as Error).message || '未知错误'}`)
      return ''
    }
  }

  /**
   * 批量上传图片并识别文字
   * @param files 图片文件数组
   */
  async batchRecognizeImages(files: File[]): Promise<{ results: Array<{ text: string, fileName: string }>, success: number, failed: number }> {
    try {
      const response = await ocrTextApi.batchUploadAndRecognize(files)
      if (response.success && response.data) {
        const result = {
          results: response.data.results.map(item => ({
            text: item.text,
            fileName: item.fileName,
          })),
          success: response.data.totalSuccess,
          failed: response.data.totalFailed,
        }

        message.success(`成功识别${response.data.totalSuccess}张图片`)
        return result
      }

      message.error(`批量识别失败: ${response.msg || '未知错误'}`)
      return { results: [], success: 0, failed: files.length }
    }
    catch (error) {
      console.error('批量图片识别失败:', error)
      message.error(`批量识别失败: ${(error as Error).message || '未知错误'}`)
      return { results: [], success: 0, failed: files.length }
    }
  }
}

export const ocrTextService = new OcrTextService()
