<!--
  * OCR图片上传组件
-->
<script setup lang="ts">
import type { UploadFile } from 'ant-design-vue'
import { useUserStore } from '@/store/modules/system/user'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref } from 'vue'

interface CustomFileItem extends UploadFile {
  fileUrl?: string
  fileName?: string
}

interface UploadOptions {
  file: File
  onSuccess?: () => void
  onError?: (error: Error) => void
}

const props = defineProps({
  maxCount: {
    type: Number,
    default: 10, // 默认允许上传10张图片
  },
  accept: {
    type: String,
    default: '.jpg,.jpeg,.png,.gif',
  },
})

const emit = defineEmits(['change'])

// 图片类型的后缀名
const imgFileType = ['jpg', 'jpeg', 'png', 'gif']

// 预览相关
const previewVisible = ref(false)
const previewImage = ref('')
const fileList = ref<CustomFileItem[]>([])

// 检查文件类型是否为图片
function isImage(file: File): boolean {
  const suffix = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
  return imgFileType.includes(suffix)
}

// 上传前验证
function beforeUpload(file: File) {
  // 检查文件类型
  if (!isImage(file)) {
    message.error('只能上传图片格式文件！')
    return false
  }

  // 检查文件大小
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    message.error('图片大小不能超过5MB！')
    return false
  }

  // 检查数量限制
  if (fileList.value.length >= props.maxCount) {
    message.error(`最多只能上传${props.maxCount}张图片！`)
    return false
  }

  return true
}

// 图片本地预览
function handleLocalUpload(options: UploadOptions) {
  const file = options.file as File

  if (!file) {
    message.error('无效的文件对象')
    return
  }

  // 创建本地预览URL
  const localUrl = URL.createObjectURL(file)

  // 添加到文件列表
  const uploadFile: CustomFileItem = {
    uid: `local_${Date.now()}_${fileList.value.length}`,
    name: file.name,
    status: 'done',
    url: localUrl,
    size: file.size,
    type: file.type,
    originFileObj: file,
  }

  fileList.value.push(uploadFile)

  // 触发变化事件
  emit('change', fileList.value)

  // 通知成功
  if (options.onSuccess) {
    options.onSuccess()
  }
}

// 预览图片
function handlePreview(file: CustomFileItem) {
  // 确保文件有URL
  if (file && (file.url || file.fileUrl)) {
    previewImage.value = file.url || file.fileUrl || ''
    previewVisible.value = true
  }
  else {
    message.warning('无法预览此图片')
  }
}

// 移除图片
function handleRemove(file: CustomFileItem) {
  const index = fileList.value.findIndex(item => item.uid === file.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
    emit('change', fileList.value)
  }
}

// 取消预览
function handleCancel() {
  previewVisible.value = false
}

// 获取文件列表
function getFileList(): File[] {
  return fileList.value
    .filter(file => file.originFileObj && file.originFileObj instanceof File)
    .map(file => file.originFileObj as File)
}

// 清空文件列表
function clearFiles() {
  fileList.value = []
  emit('change', fileList.value)
}

// 对外暴露方法
defineExpose({
  fileList,
  getFileList,
  clearFiles,
})
</script>

<template>
  <div class="ocr-image-upload">
    <a-upload
      list-type="picture-card"
      :accept="accept"
      :before-upload="beforeUpload"
      :custom-request="handleLocalUpload"
      :file-list="fileList"
      :multiple="true"
      :headers="{ 'x-access-token': useUserStore().getToken }"
      @preview="handlePreview"
      @remove="handleRemove"
    >
      <div v-if="fileList.length < maxCount">
        <PlusOutlined />
        <div class="ant-upload-text">
          上传图片
        </div>
      </div>
    </a-upload>

    <a-modal :open="previewVisible" :footer="null" @cancel="handleCancel">
      <img alt="预览图片" style="width: 100%" :src="previewImage">
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.ocr-image-upload {
  :deep(.ant-upload-list-picture-card-container) {
    width: 104px;
    height: 104px;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  :deep(.ant-upload.ant-upload-select-picture-card) {
    width: 104px;
    height: 104px;
    margin-right: 8px;
    margin-bottom: 8px;
  }
}
</style>
