<!--
  * OCR图片转文字功能
-->
<script lang="ts" setup>
import { PaperClipOutlined, SyncOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import OcrImageUpload from './components/ax-ocr-image-upload/index.vue'
import { ocrTextService } from './service/OcrTextService'

// 上传界面
const uploadVisible = ref(false)
const uploadRef = ref()
const isProcessing = ref(false)
const resultText = ref<string>('')
const batchResults = ref<Array<{ text: string, fileName: string }>>([])
const showBatchResults = ref(false)

// 打开上传弹窗
function openUploadModal() {
  uploadVisible.value = true
}

// 处理文件变更
function handleFileChange() {
  // 文件变更事件处理
}

// 提交图片进行OCR识别
async function submitForOcr() {
  if (!uploadRef.value) {
    message.error('请先选择图片')
    return
  }

  const files = uploadRef.value.getFileList()

  if (!files || files.length === 0) {
    message.error('请先选择图片')
    return
  }

  isProcessing.value = true
  resultText.value = ''
  showBatchResults.value = false
  batchResults.value = []

  try {
    // 单图片处理
    if (files.length === 1) {
      message.loading({ content: '正在识别图片...', key: 'ocrProcess', duration: 0 })
      const text = await ocrTextService.recognizeImage(files[0])

      if (text) {
        resultText.value = text
        message.success({ content: '识别成功', key: 'ocrProcess' })
      }
      else {
        message.error({ content: '识别失败', key: 'ocrProcess' })
      }
    }
    // 批量处理
    else {
      message.loading({ content: `正在批量识别${files.length}张图片...`, key: 'ocrProcess', duration: 0 })
      const result = await ocrTextService.batchRecognizeImages(files)

      if (result.results.length > 0) {
        batchResults.value = result.results
        showBatchResults.value = true
        message.success({
          content: `成功识别${result.success}张图片，失败${result.failed}张`,
          key: 'ocrProcess',
        })
      }
      else {
        message.error({ content: '批量识别失败', key: 'ocrProcess' })
      }
    }
  }
  catch (error) {
    console.error('OCR处理错误:', error)
    message.error({ content: '识别过程中发生错误', key: 'ocrProcess' })
  }
  finally {
    isProcessing.value = false
  }
}

// 下载文本结果
function downloadText(text: string, fileName: string = 'ocr_result') {
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)

  const link = document.createElement('a')
  link.href = url
  link.download = `${fileName}.txt`
  link.click()

  URL.revokeObjectURL(url)
}

// 关闭弹窗
function closeUploadModal() {
  uploadVisible.value = false
  // 清空结果
  resultText.value = ''
  showBatchResults.value = false
  batchResults.value = []
  // 如果存在上传组件，清空已选择的文件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}
</script>

<template>
  <div class="ocr-text-container">
    <a-card
      title="图片转文字 OCR"
      size="small"
      :bordered="false"
      :hoverable="true"
    >
      <template #extra>
        <a-button type="primary" @click="openUploadModal">
          <template #icon>
            <UploadOutlined />
          </template>
          上传图片
        </a-button>
      </template>

      <div class="ocr-description">
        <p>
          通过OCR识别技术将图片转换为文字，支持批量处理图片。<br>
          支持格式：JPG、JPEG、PNG、GIF 等常见图片格式。<br>
          图片大小限制：单张图片不超过5MB。
        </p>
      </div>

      <div class="ocr-steps">
        <a-steps size="small">
          <a-step title="上传图片" description="选择本地图片上传" />
          <a-step title="提交处理" description="将图片发送到OCR服务" />
          <a-step title="查看结果" description="显示识别出的文字内容" />
        </a-steps>
      </div>
    </a-card>

    <!-- 上传弹窗 -->
    <a-modal
      v-model:open="uploadVisible"
      title="图片上传与OCR识别"
      :mask-closable="false"
      :destroy-on-close="true"
      width="800px"
      @cancel="closeUploadModal"
    >
      <div class="upload-container">
        <div class="upload-panel">
          <h3>1. 选择图片</h3>
          <OcrImageUpload
            ref="uploadRef"
            :max-count="10"
            @change="handleFileChange"
          />
          <div class="upload-tips">
            <p>
              <PaperClipOutlined /> 单次最多上传10张图片，单张图片大小不超过5MB<br>
              <PaperClipOutlined /> 支持JPG、JPEG、PNG、GIF等常见图片格式
            </p>
          </div>
        </div>

        <a-divider />

        <div class="action-panel">
          <a-button
            type="primary"
            :loading="isProcessing"
            @click="submitForOcr"
          >
            <template #icon>
              <SyncOutlined :spin="isProcessing" />
            </template>
            {{ isProcessing ? '处理中...' : '提交识别' }}
          </a-button>
        </div>

        <a-divider />

        <!-- 识别结果展示 -->
        <div v-if="resultText || showBatchResults" class="result-panel">
          <h3>识别结果</h3>

          <!-- 单个图片结果 -->
          <div v-if="resultText" class="single-result">
            <a-textarea
              v-model:value="resultText"
              :rows="10"
              readonly
            />
            <div class="result-actions">
              <a-button type="primary" @click="downloadText(resultText)">
                下载文本
              </a-button>
            </div>
          </div>

          <!-- 批量图片结果 -->
          <div v-if="showBatchResults" class="batch-results">
            <a-collapse>
              <a-collapse-panel
                v-for="(result, index) in batchResults"
                :key="index"
                :header="`文件 ${index + 1}: ${result.fileName}`"
              >
                <a-textarea
                  v-model:value="result.text"
                  :rows="5"
                  readonly
                />
                <div class="result-actions">
                  <a-button
                    type="primary"
                    size="small"
                    @click="downloadText(result.text, result.fileName.split('.')[0])"
                  >
                    下载文本
                  </a-button>
                </div>
              </a-collapse-panel>
            </a-collapse>

            <div class="batch-actions">
              <a-button
                type="primary"
                @click="downloadText(batchResults.map(r => `=== ${r.fileName} ===\n${r.text}`).join('\n\n'), 'ocr_batch_results')"
              >
                下载全部结果
              </a-button>
            </div>
          </div>
        </div>

        <!-- 没有结果时 -->
        <div v-else-if="!isProcessing" class="empty-result">
          <a-empty description="请选择图片并提交进行OCR识别" />
        </div>
      </div>

      <template #footer>
        <a-button @click="closeUploadModal">
          关闭
        </a-button>
      </template>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.ocr-text-container {
  .ocr-description {
    margin-bottom: 20px;
  }

  .ocr-steps {
    margin-top: 30px;
  }

  .upload-container {
    .upload-panel {
      margin-bottom: 20px;
    }

    .upload-tips {
      margin-top: 10px;
      color: #999;
      font-size: 12px;
    }

    .action-panel {
      display: flex;
      justify-content: center;
      margin: 20px 0;
    }

    .result-panel {
      margin-top: 20px;

      .single-result, .batch-results {
        margin-top: 10px;
      }

      .result-actions {
        margin-top: 10px;
        display: flex;
        justify-content: flex-end;
      }

      .batch-actions {
        margin-top: 20px;
        display: flex;
        justify-content: center;
      }
    }

    .empty-result {
      margin: 30px 0;
    }
  }
}
</style>
