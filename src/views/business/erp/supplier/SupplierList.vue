<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import SupplierDetail from './components/SupplierDetail.vue'
import SupplierForm from './components/SupplierForm.vue'
import { supplierService } from './service/SupplierService'

// 提供 service
supplierService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'supplier:query'" layout="vertical">
      <FormGrid>
        <FormItem label="供应商编码">
          <InputText v-model:value="supplierService.queryParam.supplierCode" placeholder="请输入供应商编码" />
        </FormItem>
        <FormItem label="供应商名称">
          <InputText v-model:value="supplierService.queryParam.supplierName" placeholder="请输入供应商名称" />
        </FormItem>
        <FormItem label="供应商类型">
          <DictSelect v-model:value="supplierService.queryParam.supplierType" key-code="supplier_type" placeholder="请选择供应商类型" />
        </FormItem>
        <FormItem label="状态">
          <DictSelect v-model:value="supplierService.queryParam.status" key-code="supplier_status" placeholder="请选择状态" />
        </FormItem>
        <FormItem label="联系人">
          <InputText v-model:value="supplierService.queryParam.contactPerson" placeholder="请输入联系人" />
        </FormItem>
        <FormItem label="联系电话">
          <InputText v-model:value="supplierService.queryParam.contactPhone" placeholder="请输入联系电话" />
        </FormItem>
        <FormItem label="审批状态">
          <DictSelect v-model:value="supplierService.queryParam.approvalStatus" key-code="approval_status" placeholder="请选择审批状态" />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker v-model:from="supplierService.queryParam.createTimeFrom" v-model:to="supplierService.queryParam.createTimeTo" />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="supplier:add" batch-delete-config="supplier:delete" import-config="supplier:import" export-config="supplier:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="supplierService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="supplierService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'supplier:view'" label="查看" icon-type="EyeOutlined"
              @click="supplierService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'supplier:edit'" label="编辑" icon-type="EditOutlined"
              @click="supplierService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'supplier:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="supplierService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 供应商表单 -->
  <SupplierForm />
  <!-- 供应商详情 -->
  <SupplierDetail />
</template>
