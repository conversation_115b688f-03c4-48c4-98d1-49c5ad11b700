import type { Rule } from 'ant-design-vue/es/form'

export const rules: Record<string, Rule[]> = {
  supplierCode: [
    { required: true, message: '请输入供应商编码', trigger: 'blur' },
    { max: 50, message: '供应商编码长度不能超过50个字符', trigger: 'blur' },
  ],
  supplierName: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { max: 100, message: '供应商名称长度不能超过100个字符', trigger: 'blur' },
  ],
  supplierType: [
    { required: true, message: '请选择供应商类型', trigger: 'change' },
  ],
  contactPerson: [
    { max: 50, message: '联系人姓名长度不能超过50个字符', trigger: 'blur' },
  ],
  contactPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  contactEmail: [
    { pattern: /\S[^\s@]*@\S+\.\S+/, message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  businessLicense: [
    { max: 50, message: '营业执照号长度不能超过50个字符', trigger: 'blur' },
  ],
  taxId: [
    { max: 50, message: '税务登记号长度不能超过50个字符', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
}
