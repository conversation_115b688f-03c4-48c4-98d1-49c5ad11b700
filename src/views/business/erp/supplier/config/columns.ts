import type { SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取供应商类型对应的颜色
export function getSupplierTypeColor(type?: string): DictColor {
  if (!type)
    return 'default'
  // 根据类型返回不同颜色
  switch (type) {
    case 'material':
      return 'blue'
    case 'equipment':
      return 'green'
    case 'service':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取状态对应的颜色
export function getStatusColor(status?: string): DictColor {
  switch (status) {
    case 'active':
      return 'green'
    case 'inactive':
      return 'orange'
    case 'blacklisted':
      return 'red'
    default:
      return 'default'
  }
}

// 获取审批状态对应的颜色
export function getApprovalStatusColor(status?: string): DictColor {
  switch (status) {
    case 'approved':
      return 'green'
    case 'pending':
      return 'orange'
    case 'rejected':
      return 'red'
    default:
      return 'default'
  }
}

// 列定义
export const supplierColumns = [
  {
    title: '供应商编码',
    dataIndex: 'supplierCode',
    width: 120,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '供应商类型',
    dataIndex: 'supplierType',
    width: 120,
    customRender: ({ record }: { record: SupplierResult }) => {
      return h(DictTag, { color: getSupplierTypeColor(record.supplierType), keyCode: 'supplier_type', valueCode: record.supplierType })
    },
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 130,
  },
  {
    title: '联系邮箱',
    dataIndex: 'contactEmail',
    width: 160,
    ellipsis: true,
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '信用额度',
    dataIndex: 'creditLimit',
    width: 100,
    customRender: ({ record }: { record: SupplierResult }) => {
      return record.creditLimit ? `¥${record.creditLimit.toLocaleString()}` : '-'
    },
  },
  {
    title: '是否战略供应商',
    dataIndex: 'isStrategic',
    width: 120,
    customRender: ({ record }: { record: SupplierResult }) => {
      return h(DictTag, { color: record.isStrategic ? 'green' : 'default' }, record.isStrategic ? '是' : '否')
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: SupplierResult }) => {
      return h(DictTag, { color: getStatusColor(record.status), keyCode: 'supplier_status', valueCode: record.status })
    },
  },
  {
    title: '审批状态',
    dataIndex: 'approvalStatus',
    width: 100,
    customRender: ({ record }: { record: SupplierResult }) => {
      return h(DictTag, { color: getApprovalStatusColor(record.approvalStatus), keyCode: 'approval_status', valueCode: record.approvalStatus })
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]
