import type { SupplierFormParam, SupplierPageParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { supplierColumns } from '../config/columns'

/**
 * 供应商服务
 * 提供供应商相关的业务逻辑和数据管理
 */
export class SupplierService extends BaseCrudService<SupplierResult, SupplierFormParam, SupplierPageParam> {
  constructor() {
    // 初始化服务
    super(
      '供应商', // 业务名称
      supplierColumns,
      {
        // 使用已有的API
        add: supplierApi.addSupplier,
        queryPage: supplierApi.supplierPage,
        getDetail: supplierApi.getSupplierDetail,
        update: supplierApi.updateSupplier,
        import: supplierApi.importSupplier,
        export: supplierApi.exportSupplier,
        delete: supplierApi.deleteSupplier,
        batchDelete: supplierApi.batchDeleteSupplier,
        downloadTemplate: supplierApi.exportSupplier,
      },
    )
  }
}

// 单例模式
export const supplierService = new SupplierService()
