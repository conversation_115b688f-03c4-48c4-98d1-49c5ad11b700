<script lang="ts" setup>
import type { SupplierService } from '../service/SupplierService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'

// 获取服务实例
const supplierService = inject<SupplierService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="supplierService.detailOpen" :title="supplierService.detailTitle" :footer="null" width="1200px" @cancel="supplierService.closeDetail()">
    <Spin :spinning="supplierService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="供应商编码">
          <Text :value="supplierService.detailData.supplierCode" />
        </DescriptionsItem>
        <DescriptionsItem label="供应商名称">
          <Text :value="supplierService.detailData.supplierName" />
        </DescriptionsItem>
        <DescriptionsItem label="供应商类型">
          <DictTag key-code="supplier_type" :value-code="supplierService.detailData.supplierType" />
        </DescriptionsItem>
        <DescriptionsItem label="营业执照号">
          <Text :value="supplierService.detailData.businessLicense" />
        </DescriptionsItem>
        <DescriptionsItem label="税务登记号">
          <Text :value="supplierService.detailData.taxId" />
        </DescriptionsItem>
        <DescriptionsItem label="状态">
          <DictTag key-code="supplier_status" :value-code="supplierService.detailData.status" />
        </DescriptionsItem>

        <!-- 联系信息 -->
        <DescriptionsItem label="主要联系人">
          <Text :value="supplierService.detailData.contactPerson" />
        </DescriptionsItem>
        <DescriptionsItem label="联系电话">
          <Text :value="supplierService.detailData.contactPhone" />
        </DescriptionsItem>
        <DescriptionsItem label="联系邮箱">
          <Text :value="supplierService.detailData.contactEmail" />
        </DescriptionsItem>
        <DescriptionsItem label="联系人职位">
          <Text :value="supplierService.detailData.contactPosition" />
        </DescriptionsItem>
        <DescriptionsItem label="备用联系人">
          <Text :value="supplierService.detailData.alternativeContact" />
        </DescriptionsItem>
        <DescriptionsItem label="备用联系电话">
          <Text :value="supplierService.detailData.alternativePhone" />
        </DescriptionsItem>

        <!-- 地址信息 -->
        <DescriptionsItem label="省份">
          <Text :value="supplierService.detailData.province" />
        </DescriptionsItem>
        <DescriptionsItem label="城市">
          <Text :value="supplierService.detailData.city" />
        </DescriptionsItem>
        <DescriptionsItem label="区县">
          <Text :value="supplierService.detailData.district" />
        </DescriptionsItem>
        <DescriptionsItem label="邮政编码">
          <Text :value="supplierService.detailData.postalCode" />
        </DescriptionsItem>
        <DescriptionsItem label="详细地址" :span="2">
          <Text :value="supplierService.detailData.address" />
        </DescriptionsItem>

        <!-- 银行信息 -->
        <DescriptionsItem label="开户银行">
          <Text :value="supplierService.detailData.bankName" />
        </DescriptionsItem>
        <DescriptionsItem label="银行账号">
          <Text :value="supplierService.detailData.bankAccount" />
        </DescriptionsItem>
        <DescriptionsItem label="开户名称">
          <Text :value="supplierService.detailData.bankAccountName" />
        </DescriptionsItem>
        <DescriptionsItem label="付款条件">
          <Text :value="supplierService.detailData.paymentTerms" />
        </DescriptionsItem>

        <!-- 商务信息 -->
        <DescriptionsItem label="信用额度">
          <Text :value="supplierService.detailData.creditLimit ? `¥${supplierService.detailData.creditLimit.toLocaleString()}` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="信用期(天)">
          <Text :value="supplierService.detailData.creditPeriod?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="折扣率">
          <Text :value="supplierService.detailData.discountRate ? `${supplierService.detailData.discountRate}%` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="税率">
          <Text :value="supplierService.detailData.taxRate ? `${supplierService.detailData.taxRate}%` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="单瓦加工费(元/m²)">
          <Text :value="supplierService.detailData.processingFeeSingle?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="双瓦加工费(元/m²)">
          <Text :value="supplierService.detailData.processingFeeDouble?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="最小订单量">
          <Text :value="supplierService.detailData.minOrderQuantity?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="交货提前期(天)">
          <Text :value="supplierService.detailData.leadTime?.toString()" />
        </DescriptionsItem>

        <!-- 日期信息 -->
        <DescriptionsItem label="合作开始日期">
          <Text :value="formatDateTime(supplierService.detailData.cooperationStartDate)" />
        </DescriptionsItem>
        <DescriptionsItem label="合同到期日期">
          <Text :value="formatDateTime(supplierService.detailData.contractExpiryDate)" />
        </DescriptionsItem>
        <DescriptionsItem label="最近订单日期">
          <Text :value="formatDateTime(supplierService.detailData.lastOrderDate)" />
        </DescriptionsItem>
        <DescriptionsItem label="累计订单金额">
          <Text :value="supplierService.detailData.totalOrderAmount ? `¥${supplierService.detailData.totalOrderAmount.toLocaleString()}` : '-'" />
        </DescriptionsItem>

        <!-- 其他信息 -->
        <DescriptionsItem label="是否战略供应商">
          <DictTag :color="supplierService.detailData.isStrategic ? 'green' : 'default'" :value-code="supplierService.detailData.isStrategic ? '是' : '否'" />
        </DescriptionsItem>
        <DescriptionsItem label="审批状态">
          <DictTag key-code="approval_status" :value-code="supplierService.detailData.approvalStatus" />
        </DescriptionsItem>
        <DescriptionsItem label="审批人ID">
          <Text :value="supplierService.detailData.approvalUserId?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="审批日期">
          <Text :value="formatDateTime(supplierService.detailData.approvalDate)" />
        </DescriptionsItem>
        <DescriptionsItem label="审批备注" :span="2">
          <Text :value="supplierService.detailData.approvalNotes" />
        </DescriptionsItem>
        <DescriptionsItem label="备注" :span="2">
          <Text :value="supplierService.detailData.remark" />
        </DescriptionsItem>

        <!-- 系统信息 -->
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(supplierService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(supplierService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
