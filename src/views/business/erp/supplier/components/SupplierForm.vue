<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { SupplierService } from '../service/SupplierService'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { DatePicker } from '@/components/base/Picker'
import { Radio, RadioGroup } from '@/components/base/Radio'
import { Spin } from '@/components/base/Spin'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { Input } from 'ant-design-vue'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from '../config/rule'

const { TextArea } = Input

// 获取服务实例
const supplierService = inject<SupplierService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await supplierService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="supplierService.formOpen"
    :title="supplierService.formTitle"
    :confirm-loading="supplierService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
    @ok="handleSubmit"
    @cancel="supplierService.closeForm()"
  >
    <Spin :spinning="supplierService.formLoading">
      <Form ref="formRef" :model="supplierService.formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="供应商编码" name="supplierCode">
              <InputText v-model:value="supplierService.formData.supplierCode" placeholder="请输入供应商编码" :disabled="supplierService.formType === 'edit'" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="供应商名称" name="supplierName">
              <InputText v-model:value="supplierService.formData.supplierName" placeholder="请输入供应商名称" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="供应商类型" name="supplierType">
              <DictSelect
                v-model:value="supplierService.formData.supplierType" key-code="supplier_type" placeholder="请选择供应商类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="营业执照号" name="businessLicense">
              <InputText v-model:value="supplierService.formData.businessLicense" placeholder="请输入营业执照号" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="税务登记号" name="taxId">
              <InputText v-model:value="supplierService.formData.taxId" placeholder="请输入税务登记号" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="状态" name="status">
              <DictSelect
                v-model:value="supplierService.formData.status" key-code="supplier_status" placeholder="请选择状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="主要联系人" name="contactPerson">
              <InputText v-model:value="supplierService.formData.contactPerson" placeholder="请输入主要联系人" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="supplierService.formData.contactPhone" placeholder="请输入联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="supplierService.formData.contactEmail" placeholder="请输入联系邮箱" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系人职位" name="contactPosition">
              <InputText v-model:value="supplierService.formData.contactPosition" placeholder="请输入联系人职位" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="备用联系人" name="alternativeContact">
              <InputText v-model:value="supplierService.formData.alternativeContact" placeholder="请输入备用联系人" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="备用联系电话" name="alternativePhone">
              <InputText v-model:value="supplierService.formData.alternativePhone" placeholder="请输入备用联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="省份" name="province">
              <InputText v-model:value="supplierService.formData.province" placeholder="请输入省份" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="城市" name="city">
              <InputText v-model:value="supplierService.formData.city" placeholder="请输入城市" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="区县" name="district">
              <InputText v-model:value="supplierService.formData.district" placeholder="请输入区县" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="18">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="supplierService.formData.address" placeholder="请输入详细地址" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="邮政编码" name="postalCode">
              <InputText v-model:value="supplierService.formData.postalCode" placeholder="请输入邮政编码" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 银行信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="开户银行" name="bankName">
              <InputText v-model:value="supplierService.formData.bankName" placeholder="请输入开户银行" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="银行账号" name="bankAccount">
              <InputText v-model:value="supplierService.formData.bankAccount" placeholder="请输入银行账号" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="开户名称" name="bankAccountName">
              <InputText v-model:value="supplierService.formData.bankAccountName" placeholder="请输入开户名称" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="付款条件" name="paymentTerms">
              <InputText v-model:value="supplierService.formData.paymentTerms" placeholder="请输入付款条件" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 商务信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="信用额度(元)" name="creditLimit">
              <InputNumber v-model:value="supplierService.formData.creditLimit" placeholder="请输入信用额度" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="信用期(天)" name="creditPeriod">
              <InputNumber v-model:value="supplierService.formData.creditPeriod" placeholder="请输入信用期" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="折扣率(%)" name="discountRate">
              <InputNumber v-model:value="supplierService.formData.discountRate" placeholder="请输入折扣率" :max="100" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="税率(%)" name="taxRate">
              <InputNumber v-model:value="supplierService.formData.taxRate" placeholder="请输入税率" :max="100" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="单瓦加工费(元/m²)" name="processingFeeSingle">
              <InputNumber v-model:value="supplierService.formData.processingFeeSingle" placeholder="请输入单瓦加工费" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="双瓦加工费(元/m²)" name="processingFeeDouble">
              <InputNumber v-model:value="supplierService.formData.processingFeeDouble" placeholder="请输入双瓦加工费" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最小订单量" name="minOrderQuantity">
              <InputNumber v-model:value="supplierService.formData.minOrderQuantity" placeholder="请输入最小订单量" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="交货提前期(天)" name="leadTime">
              <InputNumber v-model:value="supplierService.formData.leadTime" placeholder="请输入交货提前期" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 日期信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="合作开始日期" name="cooperationStartDate">
              <DatePicker v-model:value="supplierService.formData.cooperationStartDate" placeholder="请选择合作开始日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="合同到期日期" name="contractExpiryDate">
              <DatePicker v-model:value="supplierService.formData.contractExpiryDate" placeholder="请选择合同到期日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 其他信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="是否战略供应商" name="isStrategic">
              <RadioGroup v-model:value="supplierService.formData.isStrategic">
                <Radio :value="true" label="是" class="radio-style" />
                <Radio :value="false" label="否" class="radio-style" />
              </RadioGroup>
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="审批状态" name="approvalStatus">
              <DictSelect
                v-model:value="supplierService.formData.approvalStatus" key-code="approval_status" placeholder="请选择审批状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="备注" name="remark">
              <TextArea v-model:value="supplierService.formData.remark" placeholder="请输入备注" :rows="3" />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
