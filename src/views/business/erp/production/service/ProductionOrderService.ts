import type { ProductionOrderForm, ProductionOrderQueryParam, ProductionOrderResult } from '../types/production-types'
import { reactive, ref } from 'vue'

/**
 * 生产工单服务类
 * 使用模拟数据进行演示
 */
export class ProductionOrderService {
  // 响应式数据
  public dataSource = ref<ProductionOrderResult[]>([])
  public loading = ref(false)
  public total = ref(0)
  public current = ref(1)
  public pageSize = ref(10)

  // 查询参数
  public queryParam = reactive<ProductionOrderQueryParam>({
    orderNo: '',
    productName: '',
    status: '',
    priority: '',
    createTimeFrom: '',
    createTimeTo: '',
    startDateFrom: '',
    startDateTo: '',
  })

  // 表单相关
  public formVisible = ref(false)
  public formData = reactive<ProductionOrderForm>({})
  public formMode = ref<'add' | 'edit'>('add')

  // 详情相关
  public detailVisible = ref(false)
  public detailData = reactive<ProductionOrderResult>({})

  // 模拟数据
  private mockData: ProductionOrderResult[] = [
    {
      id: 1,
      orderNo: 'PO202411060001',
      planId: 1,
      planName: '11月生产计划',
      productId: 101,
      productName: '纸箱A型',
      productCode: 'BOX-A-001',
      quantity: 1000,
      completedQuantity: 650,
      startDate: '2024-11-01',
      endDate: '2024-11-15',
      priority: 'high',
      priorityText: '高',
      status: 'processing',
      statusText: '进行中',
      progressPercent: 65,
      remark: '客户急单，优先生产',
      createTime: '2024-11-01 08:00:00',
      updateTime: '2024-11-06 15:30:00',
    },
    {
      id: 2,
      orderNo: 'PO202411060002',
      planId: 1,
      planName: '11月生产计划',
      productId: 102,
      productName: '纸箱B型',
      productCode: 'BOX-B-002',
      quantity: 800,
      completedQuantity: 800,
      startDate: '2024-11-02',
      endDate: '2024-11-10',
      priority: 'normal',
      priorityText: '普通',
      status: 'completed',
      statusText: '已完成',
      progressPercent: 100,
      remark: '按时完成',
      createTime: '2024-11-02 09:00:00',
      updateTime: '2024-11-10 17:00:00',
    },
    {
      id: 3,
      orderNo: 'PO202411060003',
      planId: 2,
      planName: '12月生产计划',
      productId: 103,
      productName: '纸箱C型',
      productCode: 'BOX-C-003',
      quantity: 1200,
      completedQuantity: 0,
      startDate: '2024-11-20',
      endDate: '2024-12-05',
      priority: 'urgent',
      priorityText: '紧急',
      status: 'pending',
      statusText: '待开始',
      progressPercent: 0,
      remark: '新产品试产',
      createTime: '2024-11-05 10:00:00',
      updateTime: '2024-11-05 10:00:00',
    },
    {
      id: 4,
      orderNo: 'PO202411060004',
      planId: 1,
      planName: '11月生产计划',
      productId: 104,
      productName: '纸箱D型',
      productCode: 'BOX-D-004',
      quantity: 600,
      completedQuantity: 200,
      startDate: '2024-11-03',
      endDate: '2024-11-18',
      priority: 'low',
      priorityText: '低',
      status: 'processing',
      statusText: '进行中',
      progressPercent: 33,
      remark: '常规订单',
      createTime: '2024-11-03 14:00:00',
      updateTime: '2024-11-06 16:00:00',
    },
    {
      id: 5,
      orderNo: 'PO202411060005',
      planId: 2,
      planName: '12月生产计划',
      productId: 105,
      productName: '纸箱E型',
      productCode: 'BOX-E-005',
      quantity: 900,
      completedQuantity: 450,
      startDate: '2024-11-10',
      endDate: '2024-11-25',
      priority: 'normal',
      priorityText: '普通',
      status: 'processing',
      statusText: '进行中',
      progressPercent: 50,
      remark: '批量生产',
      createTime: '2024-11-04 11:00:00',
      updateTime: '2024-11-06 14:20:00',
    },
  ]

  constructor() {
    this.loadData()
  }

  /**
   * 加载数据
   */
  private loadData() {
    this.dataSource.value = [...this.mockData]
    this.total.value = this.mockData.length
  }

  /**
   * 查询数据
   */
  public async queryPage() {
    this.loading.value = true

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    let filteredData = [...this.mockData]

    // 模拟筛选逻辑
    if (this.queryParam.orderNo) {
      filteredData = filteredData.filter(item =>
        item.orderNo?.includes(this.queryParam.orderNo!),
      )
    }

    if (this.queryParam.productName) {
      filteredData = filteredData.filter(item =>
        item.productName?.includes(this.queryParam.productName!),
      )
    }

    if (this.queryParam.status) {
      filteredData = filteredData.filter(item =>
        item.status === this.queryParam.status,
      )
    }

    if (this.queryParam.priority) {
      filteredData = filteredData.filter(item =>
        item.priority === this.queryParam.priority,
      )
    }

    this.dataSource.value = filteredData
    this.total.value = filteredData.length
    this.loading.value = false
  }

  /**
   * 重置查询条件
   */
  public resetQuery() {
    Object.keys(this.queryParam).forEach((key) => {
      ;(this.queryParam as Record<string, any>)[key] = ''
    })
    this.queryPage()
  }

  /**
   * 打开新增表单
   */
  public openAddForm() {
    this.formMode.value = 'add'
    Object.keys(this.formData).forEach((key) => {
      ;(this.formData as Record<string, any>)[key] = ''
    })
    this.formVisible.value = true
  }

  /**
   * 打开编辑表单
   */
  public openEditForm(id: number) {
    this.formMode.value = 'edit'
    const item = this.mockData.find(d => d.id === id)
    if (item) {
      Object.assign(this.formData, item)
    }
    this.formVisible.value = true
  }

  /**
   * 打开详情
   */
  public openDetailView(id: number) {
    const item = this.mockData.find(d => d.id === id)
    if (item) {
      Object.assign(this.detailData, item)
    }
    this.detailVisible.value = true
  }

  /**
   * 保存表单
   */
  public async saveForm() {
    this.loading.value = true

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (this.formMode.value === 'add') {
      // 模拟新增
      const newId = Math.max(...this.mockData.map(d => d.id || 0)) + 1
      const newItem: ProductionOrderResult = {
        ...this.formData,
        id: newId,
        orderNo: `PO${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(newId).padStart(4, '0')}`,
        completedQuantity: 0,
        progressPercent: 0,
        statusText: this.getStatusText(this.formData.status || ''),
        priorityText: this.getPriorityText(this.formData.priority || ''),
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString(),
      }
      this.mockData.push(newItem)
    }
    else {
      // 模拟编辑
      const index = this.mockData.findIndex(d => d.id === this.formData.id)
      if (index !== -1) {
        this.mockData[index] = {
          ...this.mockData[index],
          ...this.formData,
          statusText: this.getStatusText(this.formData.status || ''),
          priorityText: this.getPriorityText(this.formData.priority || ''),
          updateTime: new Date().toLocaleString(),
        }
      }
    }

    this.loadData()
    this.formVisible.value = false
    this.loading.value = false
  }

  /**
   * 删除记录
   */
  public async deleteEntity(id: number) {
    this.loading.value = true

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = this.mockData.findIndex(d => d.id === id)
    if (index !== -1) {
      this.mockData.splice(index, 1)
    }

    this.loadData()
    this.loading.value = false
  }

  /**
   * 获取状态文本
   */
  private getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      pending: '待开始',
      processing: '进行中',
      completed: '已完成',
      cancelled: '已取消',
    }
    return statusMap[status] || status
  }

  /**
   * 获取优先级文本
   */
  private getPriorityText(priority: string): string {
    const priorityMap: Record<string, string> = {
      low: '低',
      normal: '普通',
      high: '高',
      urgent: '紧急',
    }
    return priorityMap[priority] || priority
  }

  /**
   * 提供服务实例
   */
  public provide() {
    // 初始化时加载数据
    this.queryPage()
  }
}

// 单例模式
export const productionOrderService = new ProductionOrderService()
