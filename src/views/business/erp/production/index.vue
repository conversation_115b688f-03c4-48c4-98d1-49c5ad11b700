<!-- 在其他页面中使用流程设计器 -->
<script setup lang="ts">
import { ref } from 'vue'
import ProcessDesigner from './process-designer/ProcessDesigner.vue'

const showDesigner = ref(false)
const currentRouteId = ref<number>()

// 打开设计器
function openDesigner(routeId?: number) {
  currentRouteId.value = routeId
  showDesigner.value = true
}

// 关闭设计器
function closeDesigner() {
  showDesigner.value = false
  currentRouteId.value = undefined
}
</script>

<template>
  <div>
    <!-- 触发按钮 -->
    <button
      class="bg-blue-500 text-white px-4 py-2 rounded-lg"
      @click="openDesigner()"
    >
      新建流程
    </button>

    <button
      class="bg-green-500 text-white px-4 py-2 rounded-lg ml-2"
      @click="openDesigner(123)"
    >
      编辑流程
    </button>

    <!-- 设计器弹窗 -->
    <a-modal
      v-model:open="showDesigner"
      title="流程设计器"
      width="90vw"
      :style="{ top: '20px' }"
      :footer="null"
      :destroy-on-close="true"
    >
      <div class="h-[80vh]">
        <ProcessDesigner :route-id="currentRouteId" />
      </div>
    </a-modal>
  </div>
</template>
