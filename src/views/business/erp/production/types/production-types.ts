/**
 * 生产工单模型
 */
export interface ProductionOrderModel {
  id?: number
  orderNo?: string // 工单编号
  planId?: number // 生产计划ID
  planName?: string // 生产计划名称
  productId?: number // 产品ID
  productName?: string // 产品名称
  productCode?: string // 产品编码
  quantity?: number // 生产数量
  completedQuantity?: number // 已完成数量
  startDate?: string // 开始日期
  endDate?: string // 结束日期
  priority?: string // 优先级
  status?: string // 状态
  remark?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  createdBy?: number // 创建人
  updatedBy?: number // 更新人
}

/**
 * 生产工单查询参数
 */
export interface ProductionOrderQueryParam extends ProductionOrderModel {
  createTimeFrom?: string // 创建时间始
  createTimeTo?: string // 创建时间至
  startDateFrom?: string // 开始日期始
  startDateTo?: string // 开始日期至
}

/**
 * 生产工单表单类型
 */
export interface ProductionOrderForm extends ProductionOrderModel {
}

/**
 * 生产工单查询结果类型
 */
export interface ProductionOrderResult extends ProductionOrderModel {
  progressPercent?: number // 进度百分比
  statusText?: string // 状态文本
  priorityText?: string // 优先级文本
}

/**
 * 生产任务模型
 */
export interface ProductionTaskModel {
  id?: number
  taskNo?: string // 任务编号
  orderId?: number // 工单ID
  orderNo?: string // 工单编号
  stepId?: number // 工序ID
  stepName?: string // 工序名称
  workstationId?: number // 工作站ID
  workstationName?: string // 工作站名称
  quantity?: number // 任务数量
  completedQuantity?: number // 已完成数量
  qualifiedQuantity?: number // 合格数量
  defectiveQuantity?: number // 不良数量
  planStartTime?: string // 计划开始时间
  planEndTime?: string // 计划结束时间
  actualStartTime?: string // 实际开始时间
  actualEndTime?: string // 实际结束时间
  status?: string // 状态
  operatorId?: number // 操作员ID
  operatorName?: string // 操作员姓名
  remark?: string // 备注
}

/**
 * 工作站模型
 */
export interface WorkstationModel {
  id?: number
  stationCode?: string // 工作站编码
  stationName?: string // 工作站名称
  stationType?: string // 工作站类型
  status?: string // 状态
  capacity?: number // 产能
  equipmentInfo?: string // 设备信息
  remark?: string // 备注
}
