<script setup lang="ts">
import type { ProductionOrderResult } from '../types/production-types'
import { ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  detailData: ProductionOrderResult
}

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(props.visible)

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal
  },
)

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 任务表格列定义
const taskColumns = [
  {
    title: '任务编号',
    dataIndex: 'taskNo',
    key: 'taskNo',
    width: 120,
  },
  {
    title: '工序名称',
    dataIndex: 'stepName',
    key: 'stepName',
    width: 100,
  },
  {
    title: '工作站',
    dataIndex: 'workstationName',
    key: 'workstationName',
    width: 100,
  },
  {
    title: '任务数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 80,
  },
  {
    title: '已完成',
    dataIndex: 'completedQuantity',
    key: 'completedQuantity',
    width: 80,
  },
  {
    title: '进度',
    key: 'progress',
    width: 120,
    slots: { customRender: 'progress' },
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' },
  },
  {
    title: '操作员',
    dataIndex: 'operatorName',
    key: 'operatorName',
    width: 80,
  },
]

// 模拟任务数据
const mockTasks = [
  {
    id: 1,
    taskNo: 'T001',
    stepName: '裁切',
    workstationName: '裁切工作站A',
    quantity: 1000,
    completedQuantity: 1000,
    status: 'completed',
    operatorName: '张三',
  },
  {
    id: 2,
    taskNo: 'T002',
    stepName: '印刷',
    workstationName: '印刷工作站B',
    quantity: 1000,
    completedQuantity: 650,
    status: 'processing',
    operatorName: '李四',
  },
  {
    id: 3,
    taskNo: 'T003',
    stepName: '模切',
    workstationName: '模切工作站C',
    quantity: 1000,
    completedQuantity: 0,
    status: 'pending',
    operatorName: '',
  },
  {
    id: 4,
    taskNo: 'T004',
    stepName: '糊盒',
    workstationName: '糊盒工作站D',
    quantity: 1000,
    completedQuantity: 0,
    status: 'pending',
    operatorName: '',
  },
]

// 状态颜色映射
function getStatusColor(status?: string) {
  const colorMap: Record<string, string> = {
    pending: 'default',
    processing: 'processing',
    completed: 'success',
    cancelled: 'error',
  }
  return colorMap[status || ''] || 'default'
}

// 优先级颜色映射
function getPriorityColor(priority?: string) {
  const colorMap: Record<string, string> = {
    low: 'default',
    normal: 'blue',
    high: 'orange',
    urgent: 'red',
  }
  return colorMap[priority || ''] || 'default'
}

// 任务状态颜色映射
function getTaskStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    pending: 'default',
    processing: 'processing',
    completed: 'success',
    paused: 'warning',
  }
  return colorMap[status] || 'default'
}

// 任务状态文本映射
function getTaskStatusText(status: string) {
  const textMap: Record<string, string> = {
    pending: '待开始',
    processing: '进行中',
    completed: '已完成',
    paused: '暂停',
  }
  return textMap[status] || status
}
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="生产工单详情"
    :width="900"
    :footer="null"
  >
    <div class="production-order-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" :bordered="false" class="detail-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="工单编号">
            <a-tag color="blue">
              {{ detailData.orderNo }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="生产计划">
            {{ detailData.planName }}
          </a-descriptions-item>
          <a-descriptions-item label="产品名称">
            <div class="product-info">
              <div class="product-name">
                {{ detailData.productName }}
              </div>
              <div class="product-code">
                {{ detailData.productCode }}
              </div>
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="生产数量">
            <a-statistic
              :value="detailData.quantity"
              suffix="件"
              :value-style="{ fontSize: '16px' }"
            />
          </a-descriptions-item>
          <a-descriptions-item label="已完成数量">
            <a-statistic
              :value="detailData.completedQuantity"
              suffix="件"
              :value-style="{ fontSize: '16px', color: '#52c41a' }"
            />
          </a-descriptions-item>
          <a-descriptions-item label="完成进度">
            <div class="progress-info">
              <a-progress
                :percent="detailData.progressPercent"
                :status="detailData.status === 'completed' ? 'success' : 'active'"
              />
              <div class="progress-text">
                {{ detailData.progressPercent }}%
              </div>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 计划信息 -->
      <a-card title="计划信息" :bordered="false" class="detail-card">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="开始日期">
            <a-tag color="green">
              {{ detailData.startDate }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="结束日期">
            <a-tag color="orange">
              {{ detailData.endDate }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="getPriorityColor(detailData.priority)">
              {{ detailData.priorityText }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(detailData.status)">
              {{ detailData.statusText }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">
            {{ detailData.createTime }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间" :span="2">
            {{ detailData.updateTime }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 备注信息 -->
      <a-card title="备注信息" :bordered="false" class="detail-card">
        <div class="remark-content">
          {{ detailData.remark || '暂无备注' }}
        </div>
      </a-card>

      <!-- 生产任务列表 -->
      <a-card title="生产任务" :bordered="false" class="detail-card">
        <a-table
          :columns="taskColumns"
          :data-source="mockTasks"
          :pagination="false"
          size="small"
          row-key="id"
        >
          <template #status="{ record }">
            <a-tag :color="getTaskStatusColor(record.status)">
              {{ getTaskStatusText(record.status) }}
            </a-tag>
          </template>
          <template #progress="{ record }">
            <a-progress
              :percent="Math.round((record.completedQuantity / record.quantity) * 100)"
              size="small"
            />
          </template>
        </a-table>
      </a-card>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.production-order-detail {
  .detail-card {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .product-info {
    .product-name {
      font-weight: 500;
      color: #1890ff;
    }

    .product-code {
      font-size: 12px;
      color: #999;
      margin-top: 2px;
    }
  }

  .progress-info {
    .progress-text {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }

  .remark-content {
    padding: 12px;
    background-color: #fafafa;
    border-radius: 6px;
    min-height: 60px;
    color: #666;
    line-height: 1.6;
  }
}
</style>
