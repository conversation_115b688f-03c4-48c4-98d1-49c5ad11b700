<script setup lang="ts">
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onMounted } from 'vue'
import { productionOrderService } from '../service/ProductionOrderService'
import ProductionOrderDetail from './ProductionOrderDetail.vue'
import ProductionOrderForm from './ProductionOrderForm.vue'

// 使用服务
const service = productionOrderService

// 表格列定义
const columns = [
  {
    title: '工单编号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 150,
    slots: { customRender: 'orderNo' },
  },
  {
    title: '生产计划',
    dataIndex: 'planName',
    key: 'planName',
    width: 120,
  },
  {
    title: '产品信息',
    key: 'product',
    width: 180,
    slots: { customRender: 'product' },
  },
  {
    title: '生产进度',
    key: 'progress',
    width: 150,
    slots: { customRender: 'progress' },
  },
  {
    title: '开始日期',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 100,
  },
  {
    title: '结束日期',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 100,
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    slots: { customRender: 'status' },
  },
  {
    title: '优先级',
    key: 'priority',
    width: 80,
    slots: { customRender: 'priority' },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
]

// 事件处理
function handleSearch() {
  service.current.value = 1
  service.queryPage()
}

function handleReset() {
  service.resetQuery()
}

function handleRefresh() {
  service.queryPage()
}

function handleAdd() {
  service.openAddForm()
}

function handleEdit(id: number) {
  service.openEditForm(id)
}

function handleDetail(id: number) {
  service.openDetailView(id)
}

async function handleDelete(id: number) {
  try {
    await service.deleteEntity(id)
    message.success('删除成功')
  }
  catch (error) {
    message.error('删除失败')
  }
}

async function handleSave() {
  try {
    await service.saveForm()
    message.success('保存成功')
  }
  catch (error) {
    message.error('保存失败')
  }
}

function handleTableChange(pagination: any) {
  service.current.value = pagination.current
  service.pageSize.value = pagination.pageSize
  service.queryPage()
}

// 状态颜色映射
function getStatusColor(status: string) {
  const colorMap: Record<string, string> = {
    pending: 'default',
    processing: 'processing',
    completed: 'success',
    cancelled: 'error',
  }
  return colorMap[status] || 'default'
}

// 优先级颜色映射
function getPriorityColor(priority: string) {
  const colorMap: Record<string, string> = {
    low: 'default',
    normal: 'blue',
    high: 'orange',
    urgent: 'red',
  }
  return colorMap[priority] || 'default'
}

// 组件挂载时初始化
onMounted(() => {
  service.provide()
})
</script>

<template>
  <div class="production-order-list">
    <!-- 查询区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="service.queryParam">
        <a-form-item label="工单编号">
          <a-input
            v-model:value="service.queryParam.orderNo"
            placeholder="请输入工单编号"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="产品名称">
          <a-input
            v-model:value="service.queryParam.productName"
            placeholder="请输入产品名称"
            allow-clear
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="状态">
          <a-select
            v-model:value="service.queryParam.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="pending">
              待开始
            </a-select-option>
            <a-select-option value="processing">
              进行中
            </a-select-option>
            <a-select-option value="completed">
              已完成
            </a-select-option>
            <a-select-option value="cancelled">
              已取消
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级">
          <a-select
            v-model:value="service.queryParam.priority"
            placeholder="请选择优先级"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="low">
              低
            </a-select-option>
            <a-select-option value="normal">
              普通
            </a-select-option>
            <a-select-option value="high">
              高
            </a-select-option>
            <a-select-option value="urgent">
              紧急
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
            <a-button @click="handleReset">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮区域 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <a-space>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <PlusOutlined />
            </template>
            新增工单
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon>
              <ReloadOutlined />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="service.dataSource.value"
        :loading="service.loading.value"
        :pagination="{
          current: service.current.value,
          pageSize: service.pageSize.value,
          total: service.total.value,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
        }"
        row-key="id"
        size="middle"
        @change="handleTableChange"
      >
        <!-- 工单编号 -->
        <template #orderNo="{ record }">
          <a-button type="link" @click="handleDetail(record.id)">
            {{ record.orderNo }}
          </a-button>
        </template>

        <!-- 产品信息 -->
        <template #product="{ record }">
          <div>
            <div class="product-name">
              {{ record.productName }}
            </div>
            <div class="product-code">
              {{ record.productCode }}
            </div>
          </div>
        </template>

        <!-- 生产进度 -->
        <template #progress="{ record }">
          <div>
            <a-progress
              :percent="record.progressPercent"
              :status="record.status === 'completed' ? 'success' : 'active'"
              size="small"
            />
            <div class="progress-text">
              {{ record.completedQuantity }}/{{ record.quantity }}
            </div>
          </div>
        </template>

        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag
            :color="getStatusColor(record.status)"
          >
            {{ record.statusText }}
          </a-tag>
        </template>

        <!-- 优先级 -->
        <template #priority="{ record }">
          <a-tag
            :color="getPriorityColor(record.priority)"
          >
            {{ record.priorityText }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleDetail(record.id)">
              详情
            </a-button>
            <a-button type="link" size="small" @click="handleEdit(record.id)">
              编辑
            </a-button>
            <a-popconfirm
              title="确定要删除这条记录吗？"
              @confirm="handleDelete(record.id)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 表单弹窗 -->
    <ProductionOrderForm
      v-model:visible="service.formVisible.value"
      :form-data="service.formData"
      :form-mode="service.formMode.value"
      @save="handleSave"
    />

    <!-- 详情弹窗 -->
    <ProductionOrderDetail
      v-model:visible="service.detailVisible.value"
      :detail-data="service.detailData"
    />
  </div>
</template>

<style scoped lang="less">
.production-order-list {
  .search-card {
    margin-bottom: 16px;
  }

  .table-card {
    .product-name {
      font-weight: 500;
      color: #1890ff;
    }

    .product-code {
      font-size: 12px;
      color: #999;
    }

    .progress-text {
      font-size: 12px;
      color: #666;
      text-align: center;
      margin-top: 4px;
    }
  }
}
</style>
