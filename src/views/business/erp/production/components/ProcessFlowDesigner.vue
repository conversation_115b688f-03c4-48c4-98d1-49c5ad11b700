<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { VueFlow, useVueFlow, Panel } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { Controls } from '@vue-flow/controls'
import { MiniMap } from '@vue-flow/minimap'
import {
  SaveOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  DeleteOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 导入Vue Flow样式
import '@vue-flow/core/dist/style.css'
import '@vue-flow/core/dist/theme-default.css'
import '@vue-flow/controls/dist/style.css'
import '@vue-flow/minimap/dist/style.css'

// 节点类型定义
interface ProcessNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: {
    label: string
    description?: string
    duration?: number
    resources?: string[]
  }
}

// 连线定义
interface ProcessEdge {
  id: string
  source: string
  target: string
  type?: string
}

// 响应式数据
const nodes = ref<ProcessNode[]>([
  {
    id: '1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { label: '开始', description: '生产流程开始' }
  },
  {
    id: '2',
    type: 'default',
    position: { x: 300, y: 100 },
    data: { label: '原料准备', description: '准备生产所需原料', duration: 30 }
  },
  {
    id: '3',
    type: 'default',
    position: { x: 500, y: 100 },
    data: { label: '生产加工', description: '进行产品加工', duration: 120 }
  },
  {
    id: '4',
    type: 'default',
    position: { x: 700, y: 100 },
    data: { label: '质量检验', description: '产品质量检验', duration: 20 }
  },
  {
    id: '5',
    type: 'output',
    position: { x: 900, y: 100 },
    data: { label: '完成', description: '生产流程完成' }
  }
])

const edges = ref<ProcessEdge[]>([
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e2-3', source: '2', target: '3' },
  { id: 'e3-4', source: '3', target: '4' },
  { id: 'e4-5', source: '4', target: '5' }
])

// 侧边栏节点模板
const nodeTemplates = ref([
  {
    type: 'process',
    label: '生产工序',
    icon: '🏭',
    color: 'bg-blue-100 border-blue-300 text-blue-800'
  },
  {
    type: 'quality',
    label: '质量检验',
    icon: '✅',
    color: 'bg-green-100 border-green-300 text-green-800'
  },
  {
    type: 'material',
    label: '物料准备',
    icon: '📦',
    color: 'bg-orange-100 border-orange-300 text-orange-800'
  },
  {
    type: 'decision',
    label: '决策节点',
    icon: '❓',
    color: 'bg-yellow-100 border-yellow-300 text-yellow-800'
  }
])

// 选中的节点
const selectedNode = ref<ProcessNode | null>(null)
const showNodePanel = ref(false)

// Vue Flow实例
const { onNodeClick, onPaneClick, addNodes, addEdges } = useVueFlow()

// 事件处理
function handleNodeClick(event: any) {
  selectedNode.value = event.node
  showNodePanel.value = true
}

function handlePaneClick() {
  selectedNode.value = null
  showNodePanel.value = false
}

function handleSaveFlow() {
  const flowData = {
    nodes: nodes.value,
    edges: edges.value,
    timestamp: new Date().toISOString()
  }
  console.log('保存流程数据:', flowData)
  // 这里可以调用API保存到后端
}

function handleRunFlow() {
  console.log('执行流程')
  // 这里可以实现流程执行逻辑
}

function handleAddNode(template: any) {
  const newNode: ProcessNode = {
    id: `node-${Date.now()}`,
    type: 'default',
    position: { x: Math.random() * 400 + 200, y: Math.random() * 300 + 150 },
    data: {
      label: template.label,
      description: `新的${template.label}节点`
    }
  }
  addNodes([newNode])
}

function handleDeleteNode() {
  if (selectedNode.value) {
    nodes.value = nodes.value.filter(node => node.id !== selectedNode.value?.id)
    edges.value = edges.value.filter(edge => 
      edge.source !== selectedNode.value?.id && edge.target !== selectedNode.value?.id
    )
    selectedNode.value = null
    showNodePanel.value = false
  }
}

// 拖拽处理
function onDragStart(event: DragEvent, template: any) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(template))
    event.dataTransfer.effectAllowed = 'move'
  }
}

function onDrop(event: DragEvent) {
  event.preventDefault()
  if (event.dataTransfer) {
    const template = JSON.parse(event.dataTransfer.getData('application/vueflow'))
    const rect = (event.target as Element).getBoundingClientRect()
    const newNode: ProcessNode = {
      id: `node-${Date.now()}`,
      type: 'default',
      position: { 
        x: event.clientX - rect.left, 
        y: event.clientY - rect.top 
      },
      data: {
        label: template.label,
        description: `新的${template.label}节点`
      }
    }
    addNodes([newNode])
  }
}

// 组件挂载
onMounted(() => {
  onNodeClick(handleNodeClick)
  onPaneClick(handlePaneClick)
})
</script>

<template>
  <div class="process-flow-designer h-screen bg-gray-50 flex">
    <!-- 侧边栏 -->
    <div class="w-64 bg-white border-r border-gray-200 flex flex-col">
      <!-- 工具栏 -->
      <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">流程设计器</h3>
        <div class="flex flex-col space-y-2">
          <button 
            @click="handleSaveFlow"
            class="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <SaveOutlined class="mr-2" />
            保存流程
          </button>
          <button 
            @click="handleRunFlow"
            class="flex items-center justify-center px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            <PlayCircleOutlined class="mr-2" />
            执行流程
          </button>
        </div>
      </div>

      <!-- 节点模板 -->
      <div class="flex-1 p-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">节点模板</h4>
        <div class="space-y-2">
          <div
            v-for="template in nodeTemplates"
            :key="template.type"
            :class="[
              'border-2 border-dashed rounded-lg p-3 cursor-move hover:shadow-md transition-all',
              template.color
            ]"
            draggable="true"
            @dragstart="onDragStart($event, template)"
            @click="handleAddNode(template)"
          >
            <div class="flex items-center">
              <span class="text-lg mr-2">{{ template.icon }}</span>
              <span class="font-medium">{{ template.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主画布区域 -->
    <div class="flex-1 relative">
      <VueFlow
        :nodes="nodes"
        :edges="edges"
        class="bg-gray-50"
        @drop="onDrop"
        @dragover.prevent
      >
        <Background />
        <Controls />
        <MiniMap />
        
        <!-- 顶部工具栏 -->
        <Panel position="top-right" class="flex space-x-2">
          <button
            v-if="selectedNode"
            @click="handleDeleteNode"
            class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            <DeleteOutlined />
          </button>
        </Panel>
      </VueFlow>
    </div>

    <!-- 节点属性面板 -->
    <div
      v-if="showNodePanel && selectedNode"
      class="w-80 bg-white border-l border-gray-200 p-4"
    >
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">节点属性</h3>
        <button
          @click="showNodePanel = false"
          class="text-gray-400 hover:text-gray-600"
        >
          ✕
        </button>
      </div>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">节点名称</label>
          <input
            v-model="selectedNode.data.label"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">描述</label>
          <textarea
            v-model="selectedNode.data.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">预计时长(分钟)</label>
          <input
            v-model.number="selectedNode.data.duration"
            type="number"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
        </div>
      </div>
    </div>
  </div>
</template> 