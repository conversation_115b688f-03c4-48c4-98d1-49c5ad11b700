<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue'
import type { ProductionOrderForm } from '../types/production-types'
import { ref, watch } from 'vue'

// Props
interface Props {
  visible: boolean
  formData: ProductionOrderForm
  formMode: 'add' | 'edit'
}

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'save'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(props.visible)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单验证规则
const rules = {
  planName: [
    { required: true, message: '请选择生产计划', trigger: 'change' },
  ],
  productName: [
    { required: true, message: '请选择产品', trigger: 'change' },
  ],
  quantity: [
    { required: true, message: '请输入生产数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '生产数量必须大于0', trigger: 'blur' },
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' },
  ],
  endDate: [
    { required: true, message: '请选择结束日期', trigger: 'change' },
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' },
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' },
  ],
}

// 产品编码映射
const productCodeMap: Record<string, string> = {
  纸箱A型: 'BOX-A-001',
  纸箱B型: 'BOX-B-002',
  纸箱C型: 'BOX-C-003',
  纸箱D型: 'BOX-D-004',
  纸箱E型: 'BOX-E-005',
}

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal
  },
)

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 产品变化处理
function handleProductChange(productName: string) {
  if (productName && productCodeMap[productName]) {
    props.formData.productCode = productCodeMap[productName]
  }
  else {
    props.formData.productCode = ''
  }
}

// 保存处理
async function handleSave() {
  try {
    await formRef.value?.validate()
    loading.value = true
    emit('save')
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
  finally {
    loading.value = false
  }
}

// 取消处理
function handleCancel() {
  visible.value = false
  formRef.value?.resetFields()
}
</script>

<template>
  <a-modal
    v-model:open="visible"
    :title="formMode === 'add' ? '新增生产工单' : '编辑生产工单'"
    :width="800"
    :confirm-loading="loading"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="生产计划" name="planName">
            <a-select
              v-model:value="formData.planName"
              placeholder="请选择生产计划"
              allow-clear
            >
              <a-select-option value="11月生产计划">
                11月生产计划
              </a-select-option>
              <a-select-option value="12月生产计划">
                12月生产计划
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="产品名称" name="productName">
            <a-select
              v-model:value="formData.productName"
              placeholder="请选择产品"
              allow-clear
              @change="handleProductChange"
            >
              <a-select-option value="纸箱A型">
                纸箱A型
              </a-select-option>
              <a-select-option value="纸箱B型">
                纸箱B型
              </a-select-option>
              <a-select-option value="纸箱C型">
                纸箱C型
              </a-select-option>
              <a-select-option value="纸箱D型">
                纸箱D型
              </a-select-option>
              <a-select-option value="纸箱E型">
                纸箱E型
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="产品编码" name="productCode">
            <a-input
              v-model:value="formData.productCode"
              placeholder="自动生成"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="生产数量" name="quantity">
            <a-input-number
              v-model:value="formData.quantity"
              placeholder="请输入生产数量"
              :min="1"
              :max="99999"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="开始日期" name="startDate">
            <a-date-picker
              v-model:value="formData.startDate"
              placeholder="请选择开始日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="结束日期" name="endDate">
            <a-date-picker
              v-model:value="formData.endDate"
              placeholder="请选择结束日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="优先级" name="priority">
            <a-select
              v-model:value="formData.priority"
              placeholder="请选择优先级"
              allow-clear
            >
              <a-select-option value="low">
                低
              </a-select-option>
              <a-select-option value="normal">
                普通
              </a-select-option>
              <a-select-option value="high">
                高
              </a-select-option>
              <a-select-option value="urgent">
                紧急
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-select
              v-model:value="formData.status"
              placeholder="请选择状态"
              allow-clear
            >
              <a-select-option value="pending">
                待开始
              </a-select-option>
              <a-select-option value="processing">
                进行中
              </a-select-option>
              <a-select-option value="completed">
                已完成
              </a-select-option>
              <a-select-option value="cancelled">
                已取消
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="24">
          <a-form-item label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
            <a-textarea
              v-model:value="formData.remark"
              placeholder="请输入备注信息"
              :rows="3"
              :max-length="500"
              show-count
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<style scoped lang="less">
// 可以添加自定义样式
</style>
