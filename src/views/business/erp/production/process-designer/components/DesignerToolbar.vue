<!-- production/process-designer/components/DesignerToolbar.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { ProcessDesignerService } from '../service/ProcessDesignerService'

// 注入服务
const service = ProcessDesignerService.inject()

// 工具栏操作
const toolbarActions = computed(() => service.getToolbarActions())

// 路线信息表单
const routeForm = computed(() => service.flowData.routeInfo)

// 更新路线信息
function updateRouteInfo(key: string, value: any) {
  ;(service.flowData.routeInfo as any)[key] = value
}
</script>

<template>
  <div class="bg-white border-b border-gray-200 p-4">
    <div class="flex items-center justify-between">
      <!-- 左侧：路线信息 -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">工艺编码:</label>
          <a-input
            :value="routeForm.routeCode"
            placeholder="请输入工艺编码"
            class="w-32"
            @change="updateRouteInfo('routeCode', $event.target.value)"
          />
        </div>

        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">工艺名称:</label>
          <a-input
            :value="routeForm.routeName"
            placeholder="请输入工艺名称"
            class="w-48"
            @change="updateRouteInfo('routeName', $event.target.value)"
          />
        </div>

        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700">版本:</label>
          <a-input
            :value="routeForm.version"
            placeholder="版本号"
            class="w-20"
            @change="updateRouteInfo('version', $event.target.value)"
          />
        </div>
      </div>

      <!-- 右侧：操作按钮 -->
      <div class="flex items-center space-x-2">
        <a-button-group>
          <a-button
            v-for="action in toolbarActions"
            :key="action.key"
            :type="action.key === 'save' ? 'primary' : 'default'"
            :disabled="action.disabled"
            @click="action.action"
          >
            <template #icon>
              <component :is="action.icon" />
            </template>
            {{ action.label }}
          </a-button>
        </a-button-group>

        <!-- 加载状态 -->
        <div v-if="service.isLoading.value" class="flex items-center text-blue-500">
          <loading-outlined class="animate-spin mr-2" />
          <span class="text-sm">处理中...</span>
        </div>
      </div>
    </div>
  </div>
</template>
