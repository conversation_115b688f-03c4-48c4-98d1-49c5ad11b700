<!-- production/process-designer/components/PropertyPanel.vue -->
<script setup lang="ts">
import { computed } from 'vue'
import { nodePropertyConfigs } from '../config/node-config'
import { ProcessDesignerService } from '../service/ProcessDesignerService'
import { NodeType } from '../types/process-designer-types'

// 注入服务
const service = ProcessDesignerService.inject()

// 当前选中的配置
const currentConfig = computed(() => {
  if (service.selectedNode.value) {
    return nodePropertyConfigs[service.selectedNode.value.data.type] || []
  }
  return []
})

// 当前表单数据
const formData = computed(() => {
  return service.selectedNode.value?.data || {}
})

// 更新属性
function updateProperty(key: string, value: any) {
  if (service.selectedNode.value) {
    service.updateNodeProperty(service.selectedNode.value.id, key, value)
  }
}

// 删除当前节点
function deleteCurrentNode() {
  if (service.selectedNode.value) {
    service.deleteNode(service.selectedNode.value.id)
  }
}

// 关闭面板
function closePanel() {
  service.selectedNode.value = null
  service.selectedEdge.value = null
  service.showPropertyPanel.value = false
}
</script>

<template>
  <div
    v-if="service.showPropertyPanel.value"
    class="w-80 bg-white border-l border-gray-200 flex flex-col h-full"
  >
    <!-- 面板标题 -->
    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
      <div>
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
          <setting-outlined class="mr-2 text-blue-500" />
          属性设置
        </h3>
        <p v-if="service.selectedNode.value" class="text-sm text-gray-500 mt-1">
          {{ service.selectedNode.value.data.label }}
        </p>
      </div>
      <button
        class="text-gray-400 hover:text-gray-600 p-1"
        @click="closePanel"
      >
        <close-outlined />
      </button>
    </div>

    <!-- 属性表单 -->
    <div class="flex-1 p-4 overflow-y-auto">
      <a-form v-if="service.selectedNode.value" layout="vertical">
        <a-form-item
          v-for="config in currentConfig"
          :key="config.key"
          :label="config.label"
          :required="config.required"
        >
          <!-- 文本输入 -->
          <a-input
            v-if="config.type === 'input'"
            :value="formData[config.key]"
            :placeholder="config.placeholder"
            @change="updateProperty(config.key, $event.target.value)"
          />

          <!-- 数字输入 -->
          <a-input-number
            v-else-if="config.type === 'number'"
            :value="formData[config.key]"
            :placeholder="config.placeholder"
            :min="0"
            class="w-full"
            @change="updateProperty(config.key, $event)"
          />

          <!-- 文本域 -->
          <a-textarea
            v-else-if="config.type === 'textarea'"
            :value="formData[config.key]"
            :placeholder="config.placeholder"
            :rows="3"
            @change="updateProperty(config.key, $event.target.value)"
          />

          <!-- 选择器 -->
          <a-select
            v-else-if="config.type === 'select'"
            :value="formData[config.key]"
            :placeholder="config.placeholder"
            class="w-full"
            @change="updateProperty(config.key, $event)"
          >
            <a-select-option
              v-for="option in config.options"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </a-form>

      <!-- 边属性编辑 -->
      <a-form v-else-if="service.selectedEdge.value" layout="vertical">
        <a-form-item label="连线标签">
          <a-input
            :value="service.selectedEdge.value.data?.label"
            placeholder="请输入连线标签"
            @change="service.selectedEdge.value!.data!.label = $event.target.value"
          />
        </a-form-item>

        <a-form-item label="条件">
          <a-input
            :value="service.selectedEdge.value.data?.condition"
            placeholder="请输入条件"
            @change="service.selectedEdge.value!.data!.condition = $event.target.value"
          />
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea
            :value="service.selectedEdge.value.data?.remark"
            placeholder="请输入备注"
            :rows="3"
            @change="service.selectedEdge.value!.data!.remark = $event.target.value"
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮 -->
    <div class="p-4 border-t border-gray-100 space-y-2">
      <button
        v-if="service.selectedNode.value"
        class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center justify-center"
        @click="deleteCurrentNode"
      >
        <delete-outlined class="mr-2" />
        删除节点
      </button>

      <button
        v-if="service.selectedEdge.value"
        class="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center justify-center"
        @click="service.deleteEdge(service.selectedEdge.value.id)"
      >
        <delete-outlined class="mr-2" />
        删除连线
      </button>
    </div>
  </div>
</template>
