# 流程设计器 (Process Designer)

基于 Vue Flow 最新版本构建的生产工艺流程设计器 🎯

## 功能特性

### ✅ 已完成功能

1. **核心架构**
   - 基于 Vue Flow 最新版本 (TypeScript 支持)
   - 响应式数据管理 (Vue 3 Composition API)
   - 依赖注入服务架构
   - 完整的类型定义

2. **节点管理**
   - 支持 4 种节点类型：开始、工序、检验、结束
   - 动态添加/删除节点
   - 节点选择和属性编辑
   - 自动布局功能

3. **连线功能**
   - 节点间连接
   - 连线样式自定义
   - 连接验证

4. **画布操作**
   - 缩放和平移
   - 背景网格
   - 小地图导航
   - 控制面板

5. **数据持久化**
   - 流程数据保存/加载
   - 工艺路线信息管理
   - 模拟 API 接口

### 🚧 技术改进

1. **Vue Flow 集成**
   - 修复了 Edge 类型定义问题
   - 正确使用模板插槽渲染自定义节点
   - 优化事件处理机制
   - 添加完整的 CSS 样式导入

2. **TypeScript 优化**
   - 更新接口定义以符合 Vue Flow 要求
   - 修复类型兼容性问题
   - 添加连接参数类型定义

3. **组件架构**
   - 创建简化版画布组件 (ProcessCanvasSimple)
   - 修复图标组件导入问题
   - 优化服务类依赖注入

## 文件结构

```
process-designer/
├── ProcessDesigner.vue          # 主设计器组件
├── ProcessDesignerTest.vue      # 测试页面
├── README.md                    # 说明文档
├── components/
│   ├── ProcessCanvas.vue        # 完整画布组件 (带自定义节点)
│   ├── ProcessCanvasSimple.vue  # 简化画布组件 (默认节点)
│   ├── NodePanel.vue           # 节点面板
│   ├── DesignerToolbar.vue     # 工具栏
│   ├── PropertyPanel.vue       # 属性面板
│   └── nodes/
│       └── ProcessNode.vue     # 自定义节点组件
├── service/
│   └── ProcessDesignerService.ts # 业务服务类
├── types/
│   └── process-designer-types.ts # 类型定义
├── config/
│   └── node-config.ts          # 节点配置
└── styles/                     # 样式文件
```

## 使用方法

### 1. 基础使用

```vue
<script setup lang="ts">
import { ProcessDesignerService } from './service/ProcessDesignerService'
import ProcessCanvasSimple from './components/ProcessCanvasSimple.vue'

// 创建服务实例
const service = new ProcessDesignerService()
service.provide()
</script>

<template>
  <ProcessCanvasSimple />
</template>
```

### 2. 测试页面

访问 `ProcessDesignerTest.vue` 查看完整的功能演示：

- 左侧面板显示服务状态和操作按钮
- 右侧画布支持节点添加、连接、选择
- 实时显示节点和连线数量
- 支持自动布局和清空画布

### 3. API 使用

```typescript
// 添加节点
service.addNode('process', { x: 100, y: 100 })

// 删除节点
service.deleteNode('nodeId')

// 自动布局
service.autoLayout()

// 保存流程
await service.saveFlow()

// 加载流程
await service.loadFlow(routeId)
```

## 技术栈

- **Vue 3** - 响应式框架
- **TypeScript** - 类型安全
- **Vue Flow** - 流程图核心库
- **Ant Design Vue** - UI 组件库
- **TailwindCSS** - 样式框架

## 下一步计划

1. **自定义节点组件**
   - 修复 ProcessNode.vue 导入问题
   - 完善节点样式和交互
   - 添加节点工具栏

2. **属性面板**
   - 完善节点属性编辑
   - 添加连线属性配置
   - 表单验证

3. **数据持久化**
   - 集成真实 API 接口
   - 添加版本控制
   - 导入/导出功能

4. **用户体验**
   - 添加快捷键支持
   - 优化拖拽体验
   - 添加撤销/重做功能

## 问题解决记录

### 1. Vue Flow 版本兼容性
- **问题**: Edge 类型定义不匹配
- **解决**: 更新类型定义，使用正确的泛型参数

### 2. 自定义节点渲染
- **问题**: nodeTypes 属性使用方式过时
- **解决**: 改用模板插槽方式渲染自定义节点

### 3. 事件处理
- **问题**: 拖拽事件处理不正确
- **解决**: 分离拖拽事件处理函数，正确绑定到容器元素

### 4. 图标组件导入
- **问题**: 字符串形式的图标无法渲染
- **解决**: 直接导入 Vue 组件形式的图标

## 贡献指南

1. 遵循项目的 TypeScript 规范
2. 使用 TailwindCSS 进行样式编写
3. 保持组件的响应式设计
4. 添加适当的中文注释
5. 测试新功能的兼容性

---

*最后更新: 2024年12月* 