/* production/process-designer/styles/designer.css */

/* Vue Flow 自定义样式 */
.vue-flow__node {
  @apply cursor-pointer;
}

.vue-flow__node.selected {
  @apply ring-2 ring-blue-500 ring-opacity-50;
}

.vue-flow__edge {
  @apply cursor-pointer;
}

.vue-flow__edge.selected path {
  @apply stroke-blue-500 stroke-2;
}

/* 连接点样式 */
.vue-flow__handle {
  @apply w-3 h-3 border-2 border-white bg-gray-400 hover:bg-blue-500 transition-colors;
}

.vue-flow__handle.connectable {
  @apply cursor-crosshair;
}

.vue-flow__handle.connecting {
  @apply bg-blue-500;
}

/* 控制面板样式 */
.vue-flow__controls {
  @apply shadow-lg;
}

.vue-flow__controls-button {
  @apply bg-white hover:bg-gray-50 border border-gray-200 text-gray-600 hover:text-gray-900;
}

/* 小地图样式 */
.vue-flow__minimap {
  @apply shadow-lg;
}

/* 背景样式 */
.vue-flow__background {
  @apply bg-gray-50;
}

/* 拖拽时的样式 */
.vue-flow__node.dragging {
  @apply opacity-75 transform scale-105;
}

/* 动画效果 */
.vue-flow__edge-path {
  @apply transition-all duration-200;
}

.vue-flow__edge.animated path {
  stroke-dasharray: 5;
  animation: dashdraw 0.5s linear infinite;
}

@keyframes dashdraw {
  to {
    stroke-dashoffset: -10;
  }
}

/* 节点悬停效果 */
.vue-flow__node:hover {
  @apply transform scale-105 transition-transform duration-200;
}

/* 选中状态的特殊样式 */
.vue-flow__node.selected {
  @apply shadow-lg shadow-blue-200;
}