import type {
  NodeTemplate,
  ProcessFlowData,
  ProcessFlowEdge,
  ProcessFlowNode,
  ProcessNodeData,
  ToolbarAction,
} from '../types/process-designer-types'
import { message } from 'ant-design-vue'
// production/process-designer/service/ProcessDesignerService.ts
import { inject, nextTick, provide, reactive, ref } from 'vue'
import { nodeTemplates } from '../config/node-config'
import { NodeStatus, NodeType } from '../types/process-designer-types'

/**
 * 流程设计器业务服务
 * 提供流程设计器相关的业务逻辑和数据管理
 */
export class ProcessDesignerService {
  // 流程数据
  public flowData = reactive<ProcessFlowData>({
    nodes: [],
    edges: [],
    routeInfo: {
      routeCode: '',
      routeName: '',
      productId: 0,
      productName: '',
      version: '1.0',
      isActive: true,
      remark: '',
    },
  })

  // 选中的节点/边
  public selectedNode = ref<ProcessFlowNode | null>(null)
  public selectedEdge = ref<ProcessFlowEdge | null>(null)

  // UI状态
  public isLoading = ref(false)
  public showPropertyPanel = ref(false)
  public showNodePanel = ref(true)

  // 画布实例
  public vueFlowInstance = ref<any>(null)

  // 节点序号计数器
  private nodeCounter = 0

  constructor() {
    this.initializeFlow()
  }

  /**
   * 依赖注入 - provide
   */
  provide() {
    provide('processDesignerService', this)
  }

  /**
   * 依赖注入 - inject
   */
  static inject(): ProcessDesignerService {
    const service = inject<ProcessDesignerService>('processDesignerService')
    if (!service) {
      throw new Error('ProcessDesignerService not provided')
    }
    return service
  }

  /**
   * 初始化流程
   */
  private initializeFlow() {
    // 添加默认开始节点
    this.addNode(NodeType.START, { x: 100, y: 100 })
  }

  /**
   * 添加节点
   */
  addNode(type: NodeType, position: { x: number, y: number }) {
    this.nodeCounter++
    const nodeId = `${type}_${this.nodeCounter}`

    const template = nodeTemplates.find(t => t.type === type)
    if (!template)
      return

    const nodeData: ProcessNodeData = {
      id: nodeId,
      label: template.label,
      type,
      status: NodeStatus.ACTIVE,
      stepCode: type === NodeType.PROCESS || type === NodeType.DECISION ? `STEP_${this.nodeCounter}` : undefined,
      stepName: template.label,
      sequence: this.nodeCounter,
    }

    const newNode: ProcessFlowNode = {
      id: nodeId,
      type,
      position,
      data: nodeData,
      style: {
        width: 160,
        height: 80,
      },
    }

    this.flowData.nodes.push(newNode)
    message.success(`已添加${template.label}节点`)
  }

  /**
   * 删除节点
   */
  deleteNode(nodeId: string) {
    // 删除节点
    const nodeIndex = this.flowData.nodes.findIndex(n => n.id === nodeId)
    if (nodeIndex > -1) {
      this.flowData.nodes.splice(nodeIndex, 1)
    }

    // 删除相关连线
    this.flowData.edges = this.flowData.edges.filter(
      e => e.source !== nodeId && e.target !== nodeId,
    )

    // 清除选中状态
    if (this.selectedNode.value?.id === nodeId) {
      this.selectedNode.value = null
      this.showPropertyPanel.value = false
    }

    message.success('节点已删除')
  }

  /**
   * 选中节点
   */
  selectNode(node: ProcessFlowNode) {
    this.selectedNode.value = node
    this.selectedEdge.value = null
    this.showPropertyPanel.value = true
  }

  /**
   * 选中边
   */
  selectEdge(edge: ProcessFlowEdge) {
    this.selectedEdge.value = edge
    this.selectedNode.value = null
    this.showPropertyPanel.value = true
  }

  /**
   * 更新节点属性
   */
  updateNodeProperty(nodeId: string, key: string, value: any) {
    const node = this.flowData.nodes.find(n => n.id === nodeId)
    if (node) {
      ;(node.data as any)[key] = value
    }
  }

  /**
   * 连接节点
   */
  onConnect(connection: any) {
    const edgeId = `${connection.source}_to_${connection.target}`

    const newEdge: ProcessFlowEdge = {
      id: edgeId,
      source: connection.source,
      target: connection.target,
      type: 'smoothstep',
      animated: true,
      style: {
        stroke: '#3B82F6',
        strokeWidth: 2,
      },
      data: {
        id: edgeId,
        label: '',
        condition: '',
        remark: '',
      },
    }

    this.flowData.edges.push(newEdge)
  }

  /**
   * 删除连线
   */
  deleteEdge(edgeId: string) {
    const edgeIndex = this.flowData.edges.findIndex(e => e.id === edgeId)
    if (edgeIndex > -1) {
      this.flowData.edges.splice(edgeIndex, 1)
    }

    // 清除选中状态
    if (this.selectedEdge.value?.id === edgeId) {
      this.selectedEdge.value = null
      this.showPropertyPanel.value = false
    }

    message.success('连线已删除')
  }

  /**
   * 自动布局
   */
  autoLayout() {
    // 简单的自动布局算法
    const nodes = this.flowData.nodes
    const startY = 100
    const stepX = 200
    const stepY = 150

    let currentX = 100
    let currentY = startY

    // 按序号排序
    const sortedNodes = [...nodes].sort((a, b) =>
      (a.data.sequence || 0) - (b.data.sequence || 0),
    )

    sortedNodes.forEach((node, index) => {
      node.position = {
        x: currentX,
        y: currentY,
      }

      // 每3个节点换行
      if ((index + 1) % 3 === 0) {
        currentX = 100
        currentY += stepY
      }
      else {
        currentX += stepX
      }
    })

    message.success('自动布局完成')
  }

  /**
   * 保存流程
   */
  async saveFlow() {
    if (!this.validateFlow()) {
      return false
    }

    this.isLoading.value = true

    try {
      // 构建保存数据
      const saveData = {
        routeInfo: this.flowData.routeInfo,
        steps: this.flowData.nodes
          .filter(node => node.data.type !== NodeType.START && node.data.type !== NodeType.END)
          .map((node, index) => ({
            stepCode: node.data.stepCode,
            stepName: node.data.stepName,
            sequence: index + 1,
            workstationName: node.data.workstationName,
            standardTime: node.data.standardTime,
            setupTime: node.data.setupTime,
            operationDesc: node.data.operationDesc,
            qualityStandards: node.data.qualityStandards,
            remark: node.data.remark,
          })),
        connections: this.flowData.edges.map(edge => ({
          sourceStep: edge.source,
          targetStep: edge.target,
          condition: edge.data?.condition,
          remark: edge.data?.remark,
        })),
      }

      // 这里应该调用真实API保存
      console.log('保存流程数据:', saveData)

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      message.success('流程保存成功')
      return true
    }
    catch (error) {
      message.error('流程保存失败')
      return false
    }
    finally {
      this.isLoading.value = false
    }
  }

  /**
   * 加载流程
   */
  async loadFlow(routeId: number) {
    this.isLoading.value = true

    try {
      // 这里应该调用真实API加载
      const mockData = this.getMockFlowData(routeId)

      this.flowData.routeInfo = mockData.routeInfo
      this.flowData.nodes = mockData.nodes
      this.flowData.edges = mockData.edges

      message.success('流程加载成功')
    }
    catch (error) {
      message.error('流程加载失败')
    }
    finally {
      this.isLoading.value = false
    }
  }

  /**
   * 验证流程
   */
  private validateFlow(): boolean {
    if (!this.flowData.routeInfo.routeCode) {
      message.error('请输入工艺路线编码')
      return false
    }

    if (!this.flowData.routeInfo.routeName) {
      message.error('请输入工艺路线名称')
      return false
    }

    if (this.flowData.nodes.length < 2) {
      message.error('流程至少需要包含开始和结束节点')
      return false
    }

    const hasStart = this.flowData.nodes.some(n => n.data.type === NodeType.START)
    const hasEnd = this.flowData.nodes.some(n => n.data.type === NodeType.END)

    if (!hasStart) {
      message.error('流程必须包含开始节点')
      return false
    }

    if (!hasEnd) {
      message.error('流程必须包含结束节点')
      return false
    }

    return true
  }

  /**
   * 清空画布
   */
  clearCanvas() {
    this.flowData.nodes = []
    this.flowData.edges = []
    this.selectedNode.value = null
    this.selectedEdge.value = null
    this.showPropertyPanel.value = false
    this.nodeCounter = 0

    // 重新添加开始节点
    this.addNode(NodeType.START, { x: 100, y: 100 })

    message.success('画布已清空')
  }

  /**
   * 获取工具栏操作
   */
  getToolbarActions(): ToolbarAction[] {
    return [
      {
        key: 'save',
        label: '保存',
        icon: 'SaveOutlined',
        action: () => this.saveFlow(),
      },
      {
        key: 'autoLayout',
        label: '自动布局',
        icon: 'PartitionOutlined',
        action: () => this.autoLayout(),
      },
      {
        key: 'clear',
        label: '清空',
        icon: 'ClearOutlined',
        action: () => this.clearCanvas(),
      },
      {
        key: 'toggleNodePanel',
        label: this.showNodePanel.value ? '隐藏节点面板' : '显示节点面板',
        icon: 'AppstoreOutlined',
        action: () => { this.showNodePanel.value = !this.showNodePanel.value },
      },
    ]
  }

  /**
   * 模拟数据
   */
  private getMockFlowData(routeId: number): ProcessFlowData {
    return {
      routeInfo: {
        id: routeId,
        routeCode: 'RT001',
        routeName: '智能手机外壳生产工艺',
        productId: 1001,
        productName: '智能手机外壳',
        version: '1.0',
        isActive: true,
        remark: '标准生产工艺路线',
      },
      nodes: [
        {
          id: 'start_1',
          type: NodeType.START,
          position: { x: 100, y: 100 },
          data: {
            id: 'start_1',
            label: '开始',
            type: NodeType.START,
            status: NodeStatus.ACTIVE,
            sequence: 1,
          },
        },
        {
          id: 'process_1',
          type: NodeType.PROCESS,
          position: { x: 300, y: 100 },
          data: {
            id: 'process_1',
            label: '原料准备',
            type: NodeType.PROCESS,
            status: NodeStatus.ACTIVE,
            stepCode: 'STEP_001',
            stepName: '原料准备',
            workstationName: '原料仓库',
            standardTime: 30,
            setupTime: 10,
            operationDesc: '准备生产所需原料',
            sequence: 2,
          },
        },
        {
          id: 'process_2',
          type: NodeType.PROCESS,
          position: { x: 500, y: 100 },
          data: {
            id: 'process_2',
            label: '注塑成型',
            type: NodeType.PROCESS,
            status: NodeStatus.ACTIVE,
            stepCode: 'STEP_002',
            stepName: '注塑成型',
            workstationName: '注塑机01',
            standardTime: 120,
            setupTime: 20,
            operationDesc: '使用注塑机进行外壳成型',
            sequence: 3,
          },
        },
        {
          id: 'decision_1',
          type: NodeType.DECISION,
          position: { x: 700, y: 100 },
          data: {
            id: 'decision_1',
            label: '质量检验',
            type: NodeType.DECISION,
            status: NodeStatus.ACTIVE,
            stepCode: 'QC_001',
            stepName: '外观检验',
            operationDesc: '检查外壳外观质量',
            sequence: 4,
          },
        },
        {
          id: 'end_1',
          type: NodeType.END,
          position: { x: 900, y: 100 },
          data: {
            id: 'end_1',
            label: '结束',
            type: NodeType.END,
            status: NodeStatus.ACTIVE,
            sequence: 5,
          },
        },
      ],
      edges: [
        {
          id: 'start_1_to_process_1',
          source: 'start_1',
          target: 'process_1',
          type: 'smoothstep',
          animated: true,
          data: { id: 'start_1_to_process_1' },
        },
        {
          id: 'process_1_to_process_2',
          source: 'process_1',
          target: 'process_2',
          type: 'smoothstep',
          animated: true,
          data: { id: 'process_1_to_process_2' },
        },
        {
          id: 'process_2_to_decision_1',
          source: 'process_2',
          target: 'decision_1',
          type: 'smoothstep',
          animated: true,
          data: { id: 'process_2_to_decision_1' },
        },
        {
          id: 'decision_1_to_end_1',
          source: 'decision_1',
          target: 'end_1',
          type: 'smoothstep',
          animated: true,
          data: { id: 'decision_1_to_end_1', condition: '合格' },
        },
      ],
    }
  }
}

// 单例模式
export const processDesignerService = new ProcessDesignerService()
