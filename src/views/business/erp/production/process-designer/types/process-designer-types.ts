// production/process-designer/types/process-designer-types.ts
import type { Edge, Node, Position } from '@vue-flow/core'

// 节点类型枚举
export enum NodeType {
  START = 'start',
  PROCESS = 'process',
  DECISION = 'decision',
  END = 'end',
}

// 节点状态
export enum NodeStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
}

// 自定义节点数据
export interface ProcessNodeData {
  id: string
  label: string
  type: NodeType
  status: NodeStatus
  stepCode?: string
  stepName?: string
  workstationId?: number
  workstationName?: string
  standardTime?: number
  setupTime?: number
  operationDesc?: string
  qualityStandards?: Record<string, any>
  sequence?: number
  remark?: string
}

// 自定义边数据
export interface ProcessEdgeData {
  id: string
  label?: string
  condition?: string
  remark?: string
}

// 流程节点 - 符合 Vue Flow 类型要求
export type ProcessFlowNode = Node<ProcessNodeData> & {
  type: NodeType
}

// 流程边 - 符合 Vue Flow 类型要求
export type ProcessFlowEdge = Edge<ProcessEdgeData>

// 流程图数据
export interface ProcessFlowData {
  nodes: ProcessFlowNode[]
  edges: ProcessFlowEdge[]
  routeInfo: {
    id?: number
    routeCode: string
    routeName: string
    productId: number
    productName?: string
    version: string
    isActive: boolean
    remark?: string
  }
}

// 节点模板
export interface NodeTemplate {
  type: NodeType
  label: string
  icon: any // Vue 组件类型
  color: string
  description: string
}

// 工具栏操作
export interface ToolbarAction {
  key: string
  label: string
  icon: string
  action: () => void
  disabled?: boolean
}

// 属性面板配置
export interface PropertyConfig {
  key: string
  label: string
  type: 'input' | 'textarea' | 'select' | 'number' | 'switch'
  options?: Array<{ label: string, value: any }>
  required?: boolean
  placeholder?: string
}

// 连接参数类型
export interface ConnectionParams {
  source: string
  target: string
  sourceHandle?: string | null
  targetHandle?: string | null
}
