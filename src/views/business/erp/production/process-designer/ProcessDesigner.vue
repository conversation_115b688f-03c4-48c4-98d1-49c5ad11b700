<!-- production/process-designer/ProcessDesigner.vue -->
<script setup lang="ts">
import { onMounted } from 'vue'
import DesignerToolbar from './components/DesignerToolbar.vue'
import NodePanel from './components/NodePanel.vue'
import ProcessCanvas from './components/ProcessCanvas.vue'
import PropertyPanel from './components/PropertyPanel.vue'
import { ProcessDesignerService } from './service/ProcessDesignerService'

// 接收路由参数
interface Props {
  routeId?: number
}

const props = defineProps<Props>()

// 创建服务实例并提供依赖注入
const service = new ProcessDesignerService()
service.provide()

// 组件挂载后加载数据
onMounted(async () => {
  if (props.routeId) {
    await service.loadFlow(props.routeId)
  }
})
</script>

<template>
  <div class="h-screen bg-gray-50 flex flex-col">
    <!-- 工具栏 -->
    <DesignerToolbar />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 节点面板 -->
      <NodePanel v-show="service.showNodePanel.value" />

      <!-- 画布区域 -->
      <ProcessCanvas />

      <!-- 属性面板 -->
      <PropertyPanel />
    </div>
  </div>
</template>
