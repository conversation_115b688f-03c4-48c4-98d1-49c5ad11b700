<!-- 流程设计器测试页面 -->
<script setup lang="ts">
import { onMounted } from 'vue'
import { ProcessDesignerService } from './service/ProcessDesignerService'
import ProcessCanvasSimple from './components/ProcessCanvasSimple.vue'

// 创建服务实例
const service = new ProcessDesignerService()
service.provide()

// 组件挂载后初始化
onMounted(() => {
  console.log('流程设计器测试页面已加载')
  console.log('服务实例:', service)
  console.log('流程数据:', service.flowData)
})
</script>

<template>
  <div class="h-screen bg-gray-50 flex flex-col">
    <!-- 标题栏 -->
    <div class="bg-white border-b border-gray-200 p-4">
      <h1 class="text-xl font-bold text-gray-900">
        流程设计器测试页面 🎯
      </h1>
      <p class="text-sm text-gray-600 mt-1">
        基于 Vue Flow 最新版本的流程设计器
      </p>
    </div>

    <!-- 主要内容 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧信息面板 -->
      <div class="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
        <div class="space-y-4">
          <!-- 服务状态 -->
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h3 class="font-medium text-blue-900 mb-2">服务状态</h3>
            <div class="text-sm space-y-1">
              <div class="flex justify-between">
                <span class="text-blue-700">加载状态:</span>
                <span :class="service.isLoading.value ? 'text-orange-600' : 'text-green-600'">
                  {{ service.isLoading.value ? '加载中' : '就绪' }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-blue-700">节点数量:</span>
                <span class="text-blue-900 font-medium">{{ service.flowData.nodes.length }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-blue-700">连线数量:</span>
                <span class="text-blue-900 font-medium">{{ service.flowData.edges.length }}</span>
              </div>
            </div>
          </div>

          <!-- 路线信息 -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-3">
            <h3 class="font-medium text-green-900 mb-2">路线信息</h3>
            <div class="text-sm space-y-1">
              <div>
                <span class="text-green-700">编码:</span>
                <span class="text-green-900 ml-1">{{ service.flowData.routeInfo.routeCode || '未设置' }}</span>
              </div>
              <div>
                <span class="text-green-700">名称:</span>
                <span class="text-green-900 ml-1">{{ service.flowData.routeInfo.routeName || '未设置' }}</span>
              </div>
              <div>
                <span class="text-green-700">版本:</span>
                <span class="text-green-900 ml-1">{{ service.flowData.routeInfo.version }}</span>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="space-y-2">
            <button
              class="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              @click="service.addNode('start', { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 })"
            >
              添加开始节点
            </button>
            <button
              class="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              @click="service.addNode('process', { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 })"
            >
              添加工序节点
            </button>
            <button
              class="w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
              @click="service.addNode('decision', { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 })"
            >
              添加检验节点
            </button>
            <button
              class="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              @click="service.addNode('end', { x: Math.random() * 400 + 100, y: Math.random() * 300 + 100 })"
            >
              添加结束节点
            </button>
            <hr class="border-gray-200">
            <button
              class="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              @click="service.autoLayout()"
            >
              自动布局
            </button>
            <button
              class="w-full px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
              @click="service.clearCanvas()"
            >
              清空画布
            </button>
          </div>

          <!-- 选中节点信息 -->
          <div v-if="service.selectedNode.value" class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <h3 class="font-medium text-yellow-900 mb-2">选中节点</h3>
            <div class="text-sm space-y-1">
              <div>
                <span class="text-yellow-700">ID:</span>
                <span class="text-yellow-900 ml-1">{{ service.selectedNode.value.id }}</span>
              </div>
              <div>
                <span class="text-yellow-700">类型:</span>
                <span class="text-yellow-900 ml-1">{{ service.selectedNode.value.type }}</span>
              </div>
              <div>
                <span class="text-yellow-700">标签:</span>
                <span class="text-yellow-900 ml-1">{{ service.selectedNode.value.data.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧画布区域 -->
      <ProcessCanvasSimple />
    </div>
  </div>
</template> 