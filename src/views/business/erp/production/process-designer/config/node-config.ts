// production/process-designer/config/node-config.ts
import type { NodeTemplate, PropertyConfig } from '../types/process-designer-types'
import { NodeType } from '../types/process-designer-types'
import {
  PlayCircleOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  StopOutlined,
} from '@ant-design/icons-vue'

// 节点模板配置
export const nodeTemplates: NodeTemplate[] = [
  {
    type: NodeType.START,
    label: '开始',
    icon: PlayCircleOutlined,
    color: 'bg-green-500',
    description: '流程开始节点'
  },
  {
    type: NodeType.PROCESS,
    label: '生产工序',
    icon: SettingOutlined,
    color: 'bg-blue-500',
    description: '生产加工工序'
  },
  {
    type: NodeType.DECISION,
    label: '质量检验',
    icon: CheckCircleOutlined,
    color: 'bg-orange-500',
    description: '质量检验决策点'
  },
  {
    type: NodeType.END,
    label: '结束',
    icon: StopOutlined,
    color: 'bg-red-500',
    description: '流程结束节点'
  }
]

// 属性配置
export const nodePropertyConfigs: Record<NodeType, PropertyConfig[]> = {
  [NodeType.START]: [
    { key: 'label', label: '节点名称', type: 'input', required: true, placeholder: '请输入节点名称' },
    { key: 'remark', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
  ],
  [NodeType.PROCESS]: [
    { key: 'stepCode', label: '工序编码', type: 'input', required: true, placeholder: '请输入工序编码' },
    { key: 'stepName', label: '工序名称', type: 'input', required: true, placeholder: '请输入工序名称' },
    { key: 'workstationName', label: '工作站', type: 'input', placeholder: '请输入工作站名称' },
    { key: 'standardTime', label: '标准工时(分钟)', type: 'number', placeholder: '请输入标准工时' },
    { key: 'setupTime', label: '准备工时(分钟)', type: 'number', placeholder: '请输入准备工时' },
    { key: 'operationDesc', label: '操作说明', type: 'textarea', placeholder: '请输入操作说明' },
    { key: 'remark', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
  ],
  [NodeType.DECISION]: [
    { key: 'stepCode', label: '检验编码', type: 'input', required: true, placeholder: '请输入检验编码' },
    { key: 'stepName', label: '检验名称', type: 'input', required: true, placeholder: '请输入检验名称' },
    { key: 'operationDesc', label: '检验标准', type: 'textarea', placeholder: '请输入检验标准' },
    { key: 'remark', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
  ],
  [NodeType.END]: [
    { key: 'label', label: '节点名称', type: 'input', required: true, placeholder: '请输入节点名称' },
    { key: 'remark', label: '备注', type: 'textarea', placeholder: '请输入备注信息' }
  ]
}

// 默认节点样式
export const defaultNodeStyles = {
  [NodeType.START]: 'bg-gradient-to-r from-green-400 to-green-600 text-white',
  [NodeType.PROCESS]: 'bg-gradient-to-r from-blue-400 to-blue-600 text-white',
  [NodeType.DECISION]: 'bg-gradient-to-r from-orange-400 to-orange-600 text-white',
  [NodeType.END]: 'bg-gradient-to-r from-red-400 to-red-600 text-white'
}