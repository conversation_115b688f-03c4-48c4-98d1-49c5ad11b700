<script lang="ts" setup>
import type { StockTransactionResult } from '@/api/business/stock-transaction/model/stock-transaction-types'
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import StockTransactionDetail from './components/StockTransactionDetail.vue'
import StockTransactionForm from './components/StockTransactionForm.vue'
import { stockTransactionService } from './service/StockTransactionService'

// 提供 service
stockTransactionService.provide()

// 审核处理逻辑
function handleAudit(record: StockTransactionResult) {
  // 这里可以根据需要添加审核弹窗或直接调用审核API
  // 示例：直接审核通过
  stockTransactionService.auditStockTransaction(record.id!, 'APPROVED', '系统自动审核')
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'stock-transaction:query'" layout="vertical">
      <FormGrid>
        <FormItem label="流水单号">
          <InputText v-model:value="stockTransactionService.queryParam.transactionNo" placeholder="请输入流水单号" />
        </FormItem>
        <FormItem label="流水类型">
          <DictSelect
            v-model:value="stockTransactionService.queryParam.transactionType"
            key-code="stock_transaction_type"
            placeholder="请选择流水类型"
          />
        </FormItem>
        <FormItem label="关联单据类型">
          <DictSelect
            v-model:value="stockTransactionService.queryParam.relatedType"
            key-code="related_doc_type"
            placeholder="请选择关联单据类型"
          />
        </FormItem>
        <FormItem label="关联单据号">
          <InputText v-model:value="stockTransactionService.queryParam.relatedNo" placeholder="请输入关联单据号" />
        </FormItem>
        <FormItem label="操作人">
          <InputText v-model:value="stockTransactionService.queryParam.operatorName" placeholder="请输入操作人姓名" />
        </FormItem>
        <FormItem label="审核状态">
          <DictSelect
            v-model:value="stockTransactionService.queryParam.auditStatus"
            key-code="audit_status"
            placeholder="请选择审核状态"
          />
        </FormItem>
        <FormItem label="处理状态">
          <DictSelect
            v-model:value="stockTransactionService.queryParam.processStatus"
            key-code="process_status"
            placeholder="请选择处理状态"
          />
        </FormItem>
        <FormItem label="流水日期">
          <DateRangePicker
            v-model:from="stockTransactionService.queryParam.transactionDateFrom"
            v-model:to="stockTransactionService.queryParam.transactionDateTo"
          />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker
            v-model:from="stockTransactionService.queryParam.createTimeFrom"
            v-model:to="stockTransactionService.queryParam.createTimeTo"
          />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="stock-transaction:add"
          batch-delete-config="stock-transaction:delete"
          import-config="stock-transaction:import"
          export-config="stock-transaction:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockTransactionService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockTransactionService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>

    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stock-transaction:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="stockTransactionService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="stockTransactionService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:audit'"
              label="审核"
              icon-type="CheckOutlined"
              color="blue"
              @click="handleAudit(record)"
            />
            <IconAnchor
              v-privilege="'stock-transaction:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="stockTransactionService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>

  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 库存流水单表单 -->
  <StockTransactionForm />
  <!-- 库存流水单详情 -->
  <StockTransactionDetail />
</template>
