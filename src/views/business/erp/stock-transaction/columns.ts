import type { StockTransactionResult } from '@/api/business/stock-transaction/model/stock-transaction-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag, DictValue } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取流水类型对应的颜色
export function getTransactionTypeColor(type?: string): DictColor {
  if (!type)
    return 'default'
  // 根据类型返回不同颜色
  switch (type) {
    case 'IN_PURCHASE':
    case 'IN_RETURN':
    case 'IN_TRANSFER':
    case 'IN_ADJUST':
    case 'IN_PRODUCTION':
      return 'green' // 入库类型用绿色
    case 'OUT_SALES':
    case 'OUT_RETURN':
    case 'OUT_TRANSFER':
    case 'OUT_ADJUST':
    case 'OUT_PRODUCTION':
      return 'red' // 出库类型用红色
    default:
      return 'default'
  }
}

// 获取审核状态对应的颜色
export function getAuditStatusColor(status?: string): DictColor {
  switch (status) {
    case 'APPROVED':
      return 'green'
    case 'REJECTED':
      return 'red'
    case 'CANCELLED':
      return 'gray'
    case 'PENDING':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取处理状态对应的颜色
export function getProcessStatusColor(status?: string): DictColor {
  switch (status) {
    case 'COMPLETED':
      return 'green'
    case 'CANCELLED':
      return 'red'
    case 'IN_PROGRESS':
      return 'blue'
    case 'PENDING':
      return 'orange'
    default:
      return 'default'
  }
}

// 列定义
export const stockTransactionColumns = [
  {
    title: '流水单号',
    dataIndex: 'transactionNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: '流水类型',
    dataIndex: 'transactionType',
    width: 120,
    customRender: ({ record }: { record: StockTransactionResult }) => {
      return h(DictTag, {
        color: getTransactionTypeColor(record.transactionType),
        keyCode: 'stock_transaction_type',
        valueCode: record.transactionType,
      })
    },
  },
  {
    title: '流水日期',
    dataIndex: 'transactionDate',
    width: 120,
    sorter: true,
  },
  {
    title: '关联单据类型',
    dataIndex: 'relatedType',
    width: 120,
    customRender: ({ record }: { record: StockTransactionResult }) => {
      return record.relatedType ? h(DictValue, { keyCode: 'related_doc_type', valueCode: record.relatedType }) : '-'
    },
  },
  {
    title: '关联单据号',
    dataIndex: 'relatedNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: '操作人',
    dataIndex: 'operatorName',
    width: 100,
  },
  {
    title: '总数量',
    dataIndex: 'totalQuantity',
    width: 100,
    align: 'right',
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: StockTransactionResult }) => {
      return record.totalAmount ? `¥${record.totalAmount.toFixed(2)}` : '-'
    },
  },
  {
    title: '明细条数',
    dataIndex: 'itemCount',
    width: 80,
    align: 'center',
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    width: 100,
    customRender: ({ record }: { record: StockTransactionResult }) => {
      return h(DictTag, {
        color: getAuditStatusColor(record.auditStatus),
        keyCode: 'audit_status',
        valueCode: record.auditStatus,
      })
    },
  },
  {
    title: '处理状态',
    dataIndex: 'processStatus',
    width: 100,
    customRender: ({ record }: { record: StockTransactionResult }) => {
      return h(DictTag, {
        color: getProcessStatusColor(record.processStatus),
        keyCode: 'process_status',
        valueCode: record.processStatus,
      })
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
  },
]
