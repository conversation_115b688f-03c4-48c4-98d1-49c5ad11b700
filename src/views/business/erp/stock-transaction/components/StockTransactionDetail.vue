<script lang="ts" setup>
import type { StockTransactionService } from '../service/StockTransactionService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { getAuditStatusColor, getProcessStatusColor, getTransactionTypeColor } from '../columns'

// 获取服务实例
const stockTransactionService = inject<StockTransactionService>(CRUD_KEY)!
</script>

<template>
  <Modal
    :open="stockTransactionService.detailOpen"
    :title="stockTransactionService.detailTitle"
    :footer="null"
    width="1000px"
    @cancel="stockTransactionService.closeDetail()"
  >
    <Spin :spinning="stockTransactionService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="流水单号">
          <Text :value="stockTransactionService.detailData.transactionNo" />
        </DescriptionsItem>
        <DescriptionsItem label="流水类型">
          <DictTag
            :color="getTransactionTypeColor(stockTransactionService.detailData.transactionType)"
            key-code="stock_transaction_type"
            :value-code="stockTransactionService.detailData.transactionType"
          />
        </DescriptionsItem>

        <DescriptionsItem label="流水日期">
          <Text :value="stockTransactionService.detailData.transactionDate" />
        </DescriptionsItem>
        <DescriptionsItem label="操作人">
          <Text :value="stockTransactionService.detailData.operatorName" />
        </DescriptionsItem>

        <!-- 关联信息 -->
        <DescriptionsItem label="关联单据类型">
          <DictTag
            v-if="stockTransactionService.detailData.relatedType"
            key-code="related_doc_type"
            :value-code="stockTransactionService.detailData.relatedType"
          />
          <Text v-else value="--" />
        </DescriptionsItem>
        <DescriptionsItem label="关联单据号">
          <Text :value="stockTransactionService.detailData.relatedNo" />
        </DescriptionsItem>

        <!-- 数量信息 -->
        <DescriptionsItem label="总数量">
          <Text :value="stockTransactionService.detailData.totalQuantity" />
        </DescriptionsItem>
        <DescriptionsItem label="总金额">
          <Text :value="stockTransactionService.detailData.totalAmount ? `¥${stockTransactionService.detailData.totalAmount.toFixed(2)}` : '--'" />
        </DescriptionsItem>

        <DescriptionsItem label="明细条数">
          <Text :value="stockTransactionService.detailData.itemCount" />
        </DescriptionsItem>
        <DescriptionsItem label="计划处理数量">
          <Text :value="stockTransactionService.detailData.plannedQuantity" />
        </DescriptionsItem>

        <DescriptionsItem label="已处理数量">
          <Text :value="stockTransactionService.detailData.processedQuantity" />
        </DescriptionsItem>
        <DescriptionsItem label="剩余未处理数量">
          <Text :value="stockTransactionService.detailData.remainingQuantity" />
        </DescriptionsItem>

        <!-- 状态信息 -->
        <DescriptionsItem label="审核状态">
          <DictTag
            :color="getAuditStatusColor(stockTransactionService.detailData.auditStatus)"
            key-code="audit_status"
            :value-code="stockTransactionService.detailData.auditStatus"
          />
        </DescriptionsItem>
        <DescriptionsItem label="处理状态">
          <DictTag
            v-if="stockTransactionService.detailData.processStatus"
            :color="getProcessStatusColor(stockTransactionService.detailData.processStatus)"
            key-code="process_status"
            :value-code="stockTransactionService.detailData.processStatus"
          />
          <Text v-else value="--" />
        </DescriptionsItem>

        <!-- 时间信息 -->
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(stockTransactionService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(stockTransactionService.detailData.updateTime)" />
        </DescriptionsItem>

        <!-- 备注信息 -->
        <DescriptionsItem label="流水备注" :span="2">
          <Text :value="stockTransactionService.detailData.remark" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
