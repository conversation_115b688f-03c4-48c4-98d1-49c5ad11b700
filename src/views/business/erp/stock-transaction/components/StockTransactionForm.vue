<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { StockTransactionService } from '../service/StockTransactionService'
import { CustomTextArea as TextArea } from '@/components/base/CustomText'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { DatePicker } from '@/components/base/Picker'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
import { rules } from './rule'

// 获取服务实例
const stockTransactionService = inject<StockTransactionService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await stockTransactionService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="stockTransactionService.formOpen"
    :title="stockTransactionService.formTitle"
    :confirm-loading="stockTransactionService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="stockTransactionService.closeForm()"
  >
    <Spin :spinning="stockTransactionService.formLoading">
      <Form
        ref="formRef"
        layout="vertical"
        :model="stockTransactionService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="流水单号" name="transactionNo">
              <InputText
                v-model:value="stockTransactionService.formData.transactionNo"
                placeholder="请输入流水单号（不填则自动生成）"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="流水类型" name="transactionType">
              <DictSelect
                v-model:value="stockTransactionService.formData.transactionType"
                key-code="stock_transaction_type"
                placeholder="请选择流水类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="流水日期" name="transactionDate">
              <DatePicker
                v-model:value="stockTransactionService.formData.transactionDate"
                placeholder="请选择流水日期"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="关联单据类型" name="relatedType">
              <DictSelect
                v-model:value="stockTransactionService.formData.relatedType"
                key-code="related_doc_type"
                placeholder="请选择关联单据类型"
                width="100%"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="关联单据号" name="relatedNo">
              <InputText
                v-model:value="stockTransactionService.formData.relatedNo"
                placeholder="请输入关联单据号"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="操作人姓名" name="operatorName">
              <InputText
                v-model:value="stockTransactionService.formData.operatorName"
                placeholder="请输入操作人姓名"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 数量信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="计划处理数量" name="plannedQuantity">
              <InputNumber
                v-model:value="stockTransactionService.formData.plannedQuantity"
                placeholder="请输入计划处理数量"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="已处理数量" name="processedQuantity">
              <InputNumber
                v-model:value="stockTransactionService.formData.processedQuantity"
                placeholder="请输入已处理数量"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="剩余未处理数量" name="remainingQuantity">
              <InputNumber
                v-model:value="stockTransactionService.formData.remainingQuantity"
                placeholder="请输入剩余未处理数量"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 状态信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="审核状态" name="auditStatus">
              <DictSelect
                v-model:value="stockTransactionService.formData.auditStatus"
                key-code="audit_status"
                placeholder="请选择审核状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="处理状态" name="processStatus">
              <DictSelect
                v-model:value="stockTransactionService.formData.processStatus"
                key-code="process_status"
                placeholder="请选择处理状态"
                width="100%"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 备注信息 -->
        <FormRow>
          <FormColumn :span="24">
            <FormItem label="流水备注" name="remark">
              <TextArea
                v-model:value="stockTransactionService.formData.remark"
                placeholder="请输入流水备注"
                :rows="3"
                :max-length="500"
                show-count
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
