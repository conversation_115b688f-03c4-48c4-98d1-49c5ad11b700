export const rules = {
  // 流水单号
  transactionNo: [
    { max: 50, message: '流水单号最多50个字符' },
  ],

  // 流水类型 - 必填
  transactionType: [
    { required: true, message: '请选择流水类型' },
  ],

  // 流水日期 - 必填
  transactionDate: [
    { required: true, message: '请选择流水日期' },
  ],

  // 关联单据类型
  relatedType: [
    { max: 20, message: '关联单据类型最多20个字符' },
  ],

  // 关联单据号
  relatedNo: [
    { max: 50, message: '关联单据号最多50个字符' },
  ],

  // 操作人ID
  operatorId: [
    { type: 'number', message: '操作人ID必须为数字' },
  ],

  // 操作人姓名
  operatorName: [
    { max: 50, message: '操作人姓名最多50个字符' },
  ],

  // 总数量
  totalQuantity: [
    { type: 'number', min: 0, message: '总数量不能小于0' },
  ],

  // 总金额
  totalAmount: [
    { type: 'number', min: 0, message: '总金额不能小于0' },
  ],

  // 明细条数
  itemCount: [
    { type: 'number', min: 0, message: '明细条数不能小于0' },
  ],

  // 审核状态
  auditStatus: [
    { required: true, message: '请选择审核状态' },
  ],

  // 计划处理数量
  plannedQuantity: [
    { type: 'number', min: 0, message: '计划处理数量不能小于0' },
  ],

  // 已处理数量
  processedQuantity: [
    { type: 'number', min: 0, message: '已处理数量不能小于0' },
  ],

  // 剩余未处理数量
  remainingQuantity: [
    { type: 'number', min: 0, message: '剩余未处理数量不能小于0' },
  ],

  // 处理状态
  processStatus: [
    { max: 20, message: '处理状态最多20个字符' },
  ],

  // 流水备注
  remark: [
    { max: 500, message: '流水备注最多500个字符' },
  ],
}
