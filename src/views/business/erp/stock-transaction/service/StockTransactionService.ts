import type { StockTransactionForm, StockTransactionPageParam, StockTransactionResult } from '@/api/business/stock-transaction/model/stock-transaction-types'
import { stockTransactionApi } from '@/api/business/stock-transaction/stock-transaction-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { stockTransactionColumns } from '../columns'

/**
 * 库存流水单服务
 * 提供库存流水单相关的业务逻辑和数据管理
 */
export class StockTransactionService extends BaseCrudService<StockTransactionResult, StockTransactionForm, StockTransactionPageParam> {
  constructor() {
    // 初始化服务
    super(
      '库存流水单', // 业务名称
      stockTransactionColumns,
      {
        // 使用已有的API
        add: stockTransactionApi.addStockTransaction,
        queryPage: stockTransactionApi.stockTransactionPage,
        getDetail: stockTransactionApi.stockTransactionDetail,
        update: stockTransactionApi.updateStockTransaction,
        import: stockTransactionApi.importStockTransaction,
        export: stockTransactionApi.exportStockTransaction,
        delete: stockTransactionApi.deleteStockTransaction,
        batchDelete: stockTransactionApi.batchDeleteStockTransaction,
        downloadTemplate: stockTransactionApi.exportStockTransaction,
      },
    )
  }

  // 审核库存流水单
  auditStockTransaction = async (id: number, auditStatus: string, auditRemark?: string) => {
    try {
      await stockTransactionApi.auditStockTransaction({
        id,
        auditStatus,
        auditRemark,
      })
      await this.queryPage()
    }
    catch (error) {
      console.error('审核失败:', error)
    }
  }
}

// 单例模式
export const stockTransactionService = new StockTransactionService()
