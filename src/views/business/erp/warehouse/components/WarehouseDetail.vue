<script lang="ts" setup>
import type { WarehouseService } from '../service/warehouseService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { formatBoolean } from '../config/columns'

// 获取服务实例
const warehouseService = inject<WarehouseService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="warehouseService.detailOpen" :title="warehouseService.detailTitle" :footer="null" @cancel="warehouseService.closeDetail()">
    <Spin :spinning="warehouseService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <DescriptionsItem label="仓库编码">
          <Text :value="warehouseService.detailData.warehouseCode" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库名称">
          <Text :value="warehouseService.detailData.warehouseName" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库类型">
          <DictTag key-code="warehouse_type" :value-code="warehouseService.detailData.warehouseType" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库状态">
          <DictTag key-code="warehouse_status" :value-code="warehouseService.detailData.status" />
        </DescriptionsItem>
        <DescriptionsItem label="省份">
          <Text :value="warehouseService.detailData.province" />
        </DescriptionsItem>
        <DescriptionsItem label="城市">
          <Text :value="warehouseService.detailData.city" />
        </DescriptionsItem>
        <DescriptionsItem label="区县">
          <Text :value="warehouseService.detailData.district" />
        </DescriptionsItem>
        <DescriptionsItem label="详细地址">
          <Text :value="warehouseService.detailData.address" />
        </DescriptionsItem>
        <DescriptionsItem label="邮政编码">
          <Text :value="warehouseService.detailData.postalCode" />
        </DescriptionsItem>
        <DescriptionsItem label="联系人">
          <Text :value="warehouseService.detailData.contactPerson" />
        </DescriptionsItem>
        <DescriptionsItem label="联系电话">
          <Text :value="warehouseService.detailData.contactPhone" />
        </DescriptionsItem>
        <DescriptionsItem label="联系手机">
          <Text :value="warehouseService.detailData.contactMobile" />
        </DescriptionsItem>
        <DescriptionsItem label="联系邮箱">
          <Text :value="warehouseService.detailData.contactEmail" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库面积(㎡)">
          <Text :value="warehouseService.detailData.area" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库容量">
          <Text :value="warehouseService.detailData.capacity" />
        </DescriptionsItem>
        <DescriptionsItem label="温度控制">
          <Text :value="formatBoolean(warehouseService.detailData.temperatureControlled)" />
        </DescriptionsItem>
        <DescriptionsItem label="湿度控制">
          <Text :value="formatBoolean(warehouseService.detailData.humidityControlled)" />
        </DescriptionsItem>
        <DescriptionsItem v-if="warehouseService.detailData.temperatureControlled" label="温度范围(℃)">
          <Text :value="`${warehouseService.detailData.minTemperature || '-'} ~ ${warehouseService.detailData.maxTemperature || '-'}`" />
        </DescriptionsItem>
        <DescriptionsItem v-if="warehouseService.detailData.humidityControlled" label="湿度范围(%)">
          <Text :value="`${warehouseService.detailData.minHumidity || '-'} ~ ${warehouseService.detailData.maxHumidity || '-'}`" />
        </DescriptionsItem>
        <DescriptionsItem label="管理员ID">
          <Text :value="warehouseService.detailData.managerId" />
        </DescriptionsItem>
        <DescriptionsItem label="管理员姓名">
          <Text :value="warehouseService.detailData.managerName" />
        </DescriptionsItem>
        <DescriptionsItem label="上级仓库ID">
          <Text :value="warehouseService.detailData.parentWarehouseId" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库层级">
          <Text :value="warehouseService.detailData.warehouseLevel" />
        </DescriptionsItem>
        <DescriptionsItem label="默认仓库">
          <Text :value="formatBoolean(warehouseService.detailData.isDefault)" />
        </DescriptionsItem>
        <DescriptionsItem label="排序">
          <Text :value="warehouseService.detailData.sortOrder" />
        </DescriptionsItem>
        <DescriptionsItem label="备注" :span="2">
          <Text :value="warehouseService.detailData.remark" />
        </DescriptionsItem>
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(warehouseService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(warehouseService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
