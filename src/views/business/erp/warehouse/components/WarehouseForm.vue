<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { WarehouseService } from '../service/warehouseService'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { Input, InputNumber, Switch } from 'ant-design-vue'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from '../config/rule'

const { TextArea } = Input

// 获取服务实例
const warehouseService = inject<WarehouseService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await warehouseService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="warehouseService.formOpen"
    :title="warehouseService.formTitle"
    :confirm-loading="warehouseService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="warehouseService.closeForm()"
  >
    <Spin :spinning="warehouseService.formLoading">
      <Form ref="formRef" :model="warehouseService.formData" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库编码" name="warehouseCode">
              <InputText
                v-model:value="warehouseService.formData.warehouseCode"
                placeholder="请输入仓库编码"
                :disabled="warehouseService.formType === 'edit'"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库名称" name="warehouseName">
              <InputText v-model:value="warehouseService.formData.warehouseName" placeholder="请输入仓库名称" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库类型" name="warehouseType">
              <DictSelect
                v-model:value="warehouseService.formData.warehouseType"
                key-code="warehouse_type"
                placeholder="请选择仓库类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库状态" name="status">
              <DictSelect
                v-model:value="warehouseService.formData.status"
                key-code="warehouse_status"
                placeholder="请选择仓库状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="省份" name="province">
              <InputText v-model:value="warehouseService.formData.province" placeholder="请输入省份" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="城市" name="city">
              <InputText v-model:value="warehouseService.formData.city" placeholder="请输入城市" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="区县" name="district">
              <InputText v-model:value="warehouseService.formData.district" placeholder="请输入区县" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="warehouseService.formData.address" placeholder="请输入详细地址" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="邮政编码" name="postalCode">
              <InputText v-model:value="warehouseService.formData.postalCode" placeholder="请输入邮政编码" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人" name="contactPerson">
              <InputText v-model:value="warehouseService.formData.contactPerson" placeholder="请输入联系人" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="warehouseService.formData.contactPhone" placeholder="请输入联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model:value="warehouseService.formData.contactMobile" placeholder="请输入联系手机" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="warehouseService.formData.contactEmail" placeholder="请输入联系邮箱" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 仓库属性 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库面积(㎡)" name="area">
              <InputNumber
                v-model:value="warehouseService.formData.area"
                placeholder="请输入仓库面积"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库容量" name="capacity">
              <InputNumber
                v-model:value="warehouseService.formData.capacity"
                placeholder="请输入仓库容量"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 温湿度控制 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="温度控制" name="temperatureControlled">
              <Switch v-model:checked="warehouseService.formData.temperatureControlled" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="湿度控制" name="humidityControlled">
              <Switch v-model:checked="warehouseService.formData.humidityControlled" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow v-if="warehouseService.formData.temperatureControlled" :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最低温度(℃)" name="minTemperature">
              <InputNumber
                v-model:value="warehouseService.formData.minTemperature"
                placeholder="请输入最低温度"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="最高温度(℃)" name="maxTemperature">
              <InputNumber
                v-model:value="warehouseService.formData.maxTemperature"
                placeholder="请输入最高温度"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow v-if="warehouseService.formData.humidityControlled" :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最低湿度(%)" name="minHumidity">
              <InputNumber
                v-model:value="warehouseService.formData.minHumidity"
                placeholder="请输入最低湿度"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="最高湿度(%)" name="maxHumidity">
              <InputNumber
                v-model:value="warehouseService.formData.maxHumidity"
                placeholder="请输入最高湿度"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 管理信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库管理员ID" name="managerId">
              <InputNumber
                v-model:value="warehouseService.formData.managerId"
                placeholder="请输入管理员ID"
                :min="1"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="管理员姓名" name="managerName">
              <InputText v-model:value="warehouseService.formData.managerName" placeholder="请输入管理员姓名" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="上级仓库ID" name="parentWarehouseId">
              <InputNumber
                v-model:value="warehouseService.formData.parentWarehouseId"
                placeholder="请输入上级仓库ID"
                :min="1"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库层级" name="warehouseLevel">
              <InputNumber
                v-model:value="warehouseService.formData.warehouseLevel"
                placeholder="请输入仓库层级"
                :min="1"
                :max="10"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="默认仓库" name="isDefault">
              <Switch v-model:checked="warehouseService.formData.isDefault" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="排序" name="sortOrder">
              <InputNumber
                v-model:value="warehouseService.formData.sortOrder"
                placeholder="请输入排序"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow>
          <FormColumn :span="24">
            <FormItem label="备注" name="remark" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
              <TextArea
                v-model:value="warehouseService.formData.remark"
                placeholder="请输入备注"
                :rows="4"
                :max-length="500"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
