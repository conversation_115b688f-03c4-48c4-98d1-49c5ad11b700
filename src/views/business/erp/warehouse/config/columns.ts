import type { WarehouseResult } from '@/api/business/warehouse/model/warehouse-form-model'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取仓库类型对应的颜色
export function getWarehouseTypeColor(type?: string): DictColor {
  if (!type)
    return 'default'
  switch (type) {
    case 'RAW_MATERIAL':
      return 'blue'
    case 'FINISHED_GOODS':
      return 'green'
    case 'SEMI_FINISHED':
      return 'orange'
    case 'SPARE_PARTS':
      return 'purple'
    case 'RETURN_GOODS':
      return 'red'
    case 'TRANSIT':
      return 'blue'
    default:
      return 'default'
  }
}

// 获取仓库状态对应的颜色
export function getWarehouseStatusColor(status?: string): DictColor {
  if (!status)
    return 'default'
  switch (status) {
    case 'ACTIVE':
      return 'green'
    case 'INACTIVE':
      return 'orange'
    case 'MAINTENANCE':
      return 'blue'
    case 'CLOSED':
      return 'red'
    default:
      return 'default'
  }
}

// 格式化布尔值显示
export function formatBoolean(value?: number): string {
  return value === 1 ? '是' : '否'
}

// 列定义
export const warehouseColumns = [
  {
    title: '仓库编码',
    dataIndex: 'warehouseCode',
    width: 120,
    fixed: 'left',
  },
  {
    title: '仓库名称',
    dataIndex: 'warehouseName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '仓库类型',
    dataIndex: 'warehouseType',
    width: 120,
    customRender: ({ record }: { record: WarehouseResult }) => {
      return h(DictTag, {
        color: getWarehouseTypeColor(record.warehouseType),
        keyCode: 'warehouse_type',
        valueCode: record.warehouseType,
      })
    },
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    width: 200,
    ellipsis: true,
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 120,
  },
  {
    title: '面积(㎡)',
    dataIndex: 'area',
    width: 100,
    align: 'right',
  },
  {
    title: '容量',
    dataIndex: 'capacity',
    width: 100,
    align: 'right',
  },
  {
    title: '温控',
    dataIndex: 'temperatureControlled',
    width: 80,
    customRender: ({ record }: { record: WarehouseResult }) => {
      return formatBoolean(record.temperatureControlled)
    },
  },
  {
    title: '湿控',
    dataIndex: 'humidityControlled',
    width: 80,
    customRender: ({ record }: { record: WarehouseResult }) => {
      return formatBoolean(record.humidityControlled)
    },
  },
  {
    title: '管理员',
    dataIndex: 'managerName',
    width: 100,
  },
  {
    title: '仓库层级',
    dataIndex: 'warehouseLevel',
    width: 100,
    align: 'center',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: WarehouseResult }) => {
      return h(DictTag, {
        color: getWarehouseStatusColor(record.status),
        keyCode: 'warehouse_status',
        valueCode: record.status,
      })
    },
  },
  {
    title: '默认仓库',
    dataIndex: 'isDefault',
    width: 100,
    customRender: ({ record }: { record: WarehouseResult }) => {
      return formatBoolean(record.isDefault)
    },
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    width: 80,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]
