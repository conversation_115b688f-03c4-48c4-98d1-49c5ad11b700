<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import AxAreaTree from '@/components/business/AxTree/AxAreaTree/AxAreaTree.vue'
import { DictSelect } from '@/components/utils/Dict'
import WarehouseDetail from './components/WarehouseDetail.vue'
import WarehouseForm from './components/WarehouseForm.vue'
import { axAreaTreeServiceEx } from './service/axAreaTreeServiceEx'
import { warehouseService } from './service/warehouseService'

// 提供 service
warehouseService.provide()
axAreaTreeServiceEx.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'warehouse:query'" layout="vertical">
      <FormGrid>
        <FormItem label="仓库编码">
          <InputText v-model:value="warehouseService.queryParam.warehouseCode" placeholder="请输入仓库编码" />
        </FormItem>
        <FormItem label="仓库名称">
          <InputText v-model:value="warehouseService.queryParam.warehouseName" placeholder="请输入仓库名称" />
        </FormItem>
        <FormItem label="仓库类型">
          <DictSelect
            v-model:value="warehouseService.queryParam.warehouseType"
            key-code="warehouse_type"
            placeholder="请选择仓库类型"
          />
        </FormItem>
        <FormItem label="地区">
          <AxAreaTree />
        </FormItem>
        <FormItem label="城市">
          <InputText v-model:value="warehouseService.queryParam.city" placeholder="请输入城市" />
        </FormItem>
        <FormItem label="仓库状态">
          <DictSelect
            v-model:value="warehouseService.queryParam.status"
            key-code="warehouse_status"
            placeholder="请选择仓库状态"
          />
        </FormItem>
        <FormItem label="仓库层级">
          <InputNumber v-model:value="warehouseService.queryParam.warehouseLevel" placeholder="请输入仓库层级" />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker
            v-model:from="warehouseService.queryParam.createTimeFrom"
            v-model:to="warehouseService.queryParam.createTimeTo"
          />
        </formitem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card>
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="warehouse:add"
          batch-delete-config="warehouse:delete"
          import-config="warehouse:import"
          export-config="warehouse:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="warehouseService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="warehouseService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'warehouse:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="warehouseService.openDetailView(record)"
            />
            <IconAnchor
              v-privilege="'warehouse:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="warehouseService.openEditForm(record)"
            />
            <IconAnchor
              v-privilege="'warehouse:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="warehouseService.deleteEntity(record)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 仓库表单 -->
  <WarehouseForm />
  <!-- 仓库详情 -->
  <WarehouseDetail />
</template>
