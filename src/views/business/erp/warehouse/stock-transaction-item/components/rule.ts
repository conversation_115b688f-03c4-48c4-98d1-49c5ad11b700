export const rules = {
  transactionNo: [
    { required: true, message: '请输入流水单号' },
    { max: 50, message: '流水单号最多50个字符' },
  ],
  warehouseId: [
    { required: true, message: '请选择仓库' },
  ],
  productId: [
    { required: true, message: '请选择商品' },
  ],
  skuId: [
    { required: true, message: '请选择SKU' },
  ],
  quantity: [
    { required: true, message: '请输入变动数量' },
    { type: 'number', message: '变动数量必须为数字' },
  ],
  unitCost: [
    { type: 'number', min: 0, message: '单位成本不能小于0' },
  ],
  totalAmount: [
    { type: 'number', min: 0, message: '总金额不能小于0' },
  ],
  beforeStock: [
    { type: 'number', min: 0, message: '变动前库存不能小于0' },
  ],
  afterStock: [
    { type: 'number', min: 0, message: '变动后库存不能小于0' },
  ],
  unit: [
    { max: 20, message: '单位最多20个字符' },
  ],
  specSummary: [
    { max: 200, message: '规格摘要最多200个字符' },
  ],
  remark: [
    { max: 500, message: '备注最多500个字符' },
  ],
}
