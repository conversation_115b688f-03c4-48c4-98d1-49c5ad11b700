<script lang="ts" setup>
import type { StockTransactionItemService } from '../service/StockTransactionItemService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { formatQuantity, getQuantityColor } from '../columns'

// 获取服务实例
const stockTransactionItemService = inject<StockTransactionItemService>(CRUD_KEY)!
</script>

<template>
  <Modal
    :open="stockTransactionItemService.detailOpen"
    :title="stockTransactionItemService.detailTitle"
    :footer="null"
    width="1000px"
    @cancel="stockTransactionItemService.closeDetail()"
  >
    <Spin :spinning="stockTransactionItemService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="流水单号">
          <Text :value="stockTransactionItemService.detailData.transactionNo" />
        </DescriptionsItem>
        <DescriptionsItem label="流水主表ID">
          <Text :value="stockTransactionItemService.detailData.transactionId" />
        </DescriptionsItem>

        <!-- 仓库信息 -->
        <DescriptionsItem label="仓库编码">
          <Text :value="stockTransactionItemService.detailData.warehouseCode" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库名称">
          <Text :value="stockTransactionItemService.detailData.warehouseName" />
        </DescriptionsItem>

        <!-- 商品信息 -->
        <DescriptionsItem label="商品编码">
          <Text :value="stockTransactionItemService.detailData.productCode" />
        </DescriptionsItem>
        <DescriptionsItem label="商品名称">
          <Text :value="stockTransactionItemService.detailData.productName" />
        </DescriptionsItem>

        <!-- SKU信息 -->
        <DescriptionsItem label="SKU编码">
          <Text :value="stockTransactionItemService.detailData.skuCode" />
        </DescriptionsItem>
        <DescriptionsItem label="规格摘要">
          <Text :value="stockTransactionItemService.detailData.specSummary" />
        </DescriptionsItem>

        <!-- 数量和单位 -->
        <DescriptionsItem label="单位">
          <Text :value="stockTransactionItemService.detailData.unit" />
        </DescriptionsItem>
        <DescriptionsItem label="变动数量">
          <span
            :style="{
              color: getQuantityColor(stockTransactionItemService.detailData.quantity) === 'green' ? '#52c41a' : '#ff4d4f',
              fontWeight: 'bold',
            }"
          >
            {{ formatQuantity(stockTransactionItemService.detailData.quantity) }}
          </span>
        </DescriptionsItem>

        <!-- 成本和金额 -->
        <DescriptionsItem label="单位成本">
          <Text :value="stockTransactionItemService.detailData.unitCost ? `¥${stockTransactionItemService.detailData.unitCost.toFixed(2)}` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="总金额">
          <Text :value="stockTransactionItemService.detailData.totalAmount ? `¥${stockTransactionItemService.detailData.totalAmount.toFixed(2)}` : '-'" />
        </DescriptionsItem>

        <!-- 库存信息 -->
        <DescriptionsItem label="变动前库存">
          <Text :value="stockTransactionItemService.detailData.beforeStock" />
        </DescriptionsItem>
        <DescriptionsItem label="变动后库存">
          <Text :value="stockTransactionItemService.detailData.afterStock" />
        </DescriptionsItem>

        <!-- 备注 -->
        <DescriptionsItem label="备注" :span="2">
          <Text :value="stockTransactionItemService.detailData.remark || '-'" />
        </DescriptionsItem>

        <!-- 时间信息 -->
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(stockTransactionItemService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(stockTransactionItemService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
