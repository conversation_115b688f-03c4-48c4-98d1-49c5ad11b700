<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { StockTransactionItemService } from '../service/StockTransactionItemService'
import { CustomDivider } from '@/components/base/CustomDivider'
import { CustomTextArea as TextArea } from '@/components/base/CustomText'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
import { rules } from './rule'

// 获取服务实例
const stockTransactionItemService = inject<StockTransactionItemService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await stockTransactionItemService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="stockTransactionItemService.formOpen"
    :title="stockTransactionItemService.formTitle"
    :confirm-loading="stockTransactionItemService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="stockTransactionItemService.closeForm()"
  >
    <Spin :spinning="stockTransactionItemService.formLoading">
      <Form
        ref="formRef"
        :model="stockTransactionItemService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <CustomDivider label="基本信息" />
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="流水单号" name="transactionNo">
              <InputText
                v-model:value="stockTransactionItemService.formData.transactionNo"
                placeholder="请输入流水单号"
                :disabled="stockTransactionItemService.formType === 'edit'"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库编码" name="warehouseCode">
              <InputText
                v-model:value="stockTransactionItemService.formData.warehouseCode"
                placeholder="请输入仓库编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库名称" name="warehouseName">
              <InputText
                v-model:value="stockTransactionItemService.formData.warehouseName"
                placeholder="请输入仓库名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="商品编码" name="productCode">
              <InputText
                v-model:value="stockTransactionItemService.formData.productCode"
                placeholder="请输入商品编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="商品名称" name="productName">
              <InputText
                v-model:value="stockTransactionItemService.formData.productName"
                placeholder="请输入商品名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="SKU编码" name="skuCode">
              <InputText
                v-model:value="stockTransactionItemService.formData.skuCode"
                placeholder="请输入SKU编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="规格摘要" name="specSummary">
              <InputText
                v-model:value="stockTransactionItemService.formData.specSummary"
                placeholder="请输入规格摘要"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="单位" name="unit">
              <InputText
                v-model:value="stockTransactionItemService.formData.unit"
                placeholder="请输入单位"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 数量和金额信息 -->
        <CustomDivider label="数量和金额信息" />
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="变动数量" name="quantity">
              <InputNumber
                v-model:value="stockTransactionItemService.formData.quantity"
                placeholder="请输入变动数量"
                :precision="2"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="单位成本" name="unitCost">
              <InputNumber
                v-model:value="stockTransactionItemService.formData.unitCost"
                placeholder="请输入单位成本"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="总金额" name="totalAmount">
              <InputNumber
                v-model:value="stockTransactionItemService.formData.totalAmount"
                placeholder="请输入总金额"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 库存信息 -->
        <CustomDivider label="库存信息" />
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="变动前库存" name="beforeStock">
              <InputNumber
                v-model:value="stockTransactionItemService.formData.beforeStock"
                placeholder="请输入变动前库存"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="变动后库存" name="afterStock">
              <InputNumber
                v-model:value="stockTransactionItemService.formData.afterStock"
                placeholder="请输入变动后库存"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 备注信息 -->
        <CustomDivider label="备注信息" />
        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="备注" name="remark">
              <TextArea
                v-model:value="stockTransactionItemService.formData.remark"
                placeholder="请输入备注"
                :rows="3"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
