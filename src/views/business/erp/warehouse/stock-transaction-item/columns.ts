import type { StockTransactionItemResult } from '@/api/business/stock-transaction-item/model/stock-transaction-item-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取数量变动类型的颜色
export function getQuantityColor(quantity?: number): DictColor {
  if (!quantity)
    return 'default'
  return quantity > 0 ? 'green' : 'red'
}

// 格式化数量显示
export function formatQuantity(quantity?: number): string {
  if (!quantity)
    return '0'
  return quantity > 0 ? `+${quantity}` : `${quantity}`
}

// 列定义
export const stockTransactionItemColumns = [
  {
    title: '流水单号',
    dataIndex: 'transactionNo',
    width: 160,
    ellipsis: true,
  },
  {
    title: '仓库编码',
    dataIndex: 'warehouseCode',
    width: 120,
  },
  {
    title: '仓库名称',
    dataIndex: 'warehouseName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    width: 120,
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    ellipsis: true,
  },
  {
    title: 'SKU编码',
    dataIndex: 'skuCode',
    width: 120,
  },
  {
    title: '规格摘要',
    dataIndex: 'specSummary',
    width: 120,
    ellipsis: true,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 80,
  },
  {
    title: '变动数量',
    dataIndex: 'quantity',
    width: 100,
    customRender: ({ record }: { record: StockTransactionItemResult }) => {
      return h(DictTag, {
        color: getQuantityColor(record.quantity),
      }, {
        default: () => formatQuantity(record.quantity),
      })
    },
  },
  {
    title: '单位成本',
    dataIndex: 'unitCost',
    width: 100,
    customRender: ({ record }: { record: StockTransactionItemResult }) => {
      return record.unitCost ? `¥${record.unitCost.toFixed(2)}` : '-'
    },
  },
  {
    title: '总金额',
    dataIndex: 'totalAmount',
    width: 120,
    customRender: ({ record }: { record: StockTransactionItemResult }) => {
      return record.totalAmount ? `¥${record.totalAmount.toFixed(2)}` : '-'
    },
  },
  {
    title: '变动前库存',
    dataIndex: 'beforeStock',
    width: 100,
  },
  {
    title: '变动后库存',
    dataIndex: 'afterStock',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 150,
  },
]
