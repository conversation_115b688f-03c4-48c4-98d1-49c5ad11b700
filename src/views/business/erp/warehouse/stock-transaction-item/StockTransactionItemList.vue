<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import StockTransactionItemDetail from './components/StockTransactionItemDetail.vue'
import StockTransactionItemForm from './components/StockTransactionItemForm.vue'
import { stockTransactionItemService } from './service/StockTransactionItemService'

// 提供 service
stockTransactionItemService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'stockTransactionItem:query'">
      <FormGrid>
        <FormItem label="流水单号">
          <InputText v-model:value="stockTransactionItemService.queryParam.transactionNo" placeholder="请输入流水单号" />
        </FormItem>
        <FormItem label="仓库编码">
          <InputText v-model:value="stockTransactionItemService.queryParam.warehouseCode" placeholder="请输入仓库编码" />
        </FormItem>
        <FormItem label="仓库名称">
          <InputText v-model:value="stockTransactionItemService.queryParam.warehouseName" placeholder="请输入仓库名称" />
        </FormItem>
        <FormItem label="商品编码">
          <InputText v-model:value="stockTransactionItemService.queryParam.productCode" placeholder="请输入商品编码" />
        </FormItem>
        <FormItem label="商品名称">
          <InputText v-model:value="stockTransactionItemService.queryParam.productName" placeholder="请输入商品名称" />
        </FormItem>
        <FormItem label="SKU编码">
          <InputText v-model:value="stockTransactionItemService.queryParam.skuCode" placeholder="请输入SKU编码" />
        </FormItem>
        <FormItem label="变动数量">
          <InputNumber v-model:value="stockTransactionItemService.queryParam.quantity" placeholder="请输入变动数量" style="width: 100%" />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker
            v-model:from="stockTransactionItemService.queryParam.createTimeFrom"
            v-model:to="stockTransactionItemService.queryParam.createTimeTo"
          />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="stockTransactionItem:add"
          batch-delete-config="stockTransactionItem:delete"
          import-config="stockTransactionItem:import"
          export-config="stockTransactionItem:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockTransactionItemService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockTransactionItemService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>

    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stockTransactionItem:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="stockTransactionItemService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockTransactionItem:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="stockTransactionItemService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockTransactionItem:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="stockTransactionItemService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>

  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 库存流水明细表单 -->
  <StockTransactionItemForm />
  <!-- 库存流水明细详情 -->
  <StockTransactionItemDetail />
</template>
