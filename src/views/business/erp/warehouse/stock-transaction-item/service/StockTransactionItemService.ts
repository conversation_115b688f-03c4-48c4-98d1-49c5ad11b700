import type { StockTransactionItemForm, StockTransactionItemPageParam, StockTransactionItemResult } from '@/api/business/stock-transaction-item/model/stock-transaction-item-types'
import { stockTransactionItemApi } from '@/api/business/stock-transaction-item/stock-transaction-item-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { stockTransactionItemColumns } from '../columns'

/**
 * 库存流水明细服务
 * 提供库存流水明细相关的业务逻辑和数据管理
 */
export class StockTransactionItemService extends BaseCrudService<StockTransactionItemResult, StockTransactionItemForm, StockTransactionItemPageParam> {
  constructor() {
    // 初始化服务
    super(
      '库存流水明细', // 业务名称
      stockTransactionItemColumns,
      {
        // 使用已有的API
        add: stockTransactionItemApi.addStockTransactionItem,
        queryPage: stockTransactionItemApi.stockTransactionItemPage,
        getDetail: stockTransactionItemApi.stockTransactionItemDetail,
        update: stockTransactionItemApi.updateStockTransactionItem,
        import: stockTransactionItemApi.importStockTransactionItem,
        export: stockTransactionItemApi.exportStockTransactionItem,
        delete: stockTransactionItemApi.deleteStockTransactionItem,
        batchDelete: stockTransactionItemApi.batchDeleteStockTransactionItem,
        downloadTemplate: stockTransactionItemApi.exportStockTransactionItem,
      },
    )
  }
}

// 单例模式
export const stockTransactionItemService = new StockTransactionItemService()
