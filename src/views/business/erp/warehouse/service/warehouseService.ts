import type { WarehouseFormParam, WarehousePageParam, WarehouseResult } from '@/api/business/warehouse/model/warehouse-form-model'
import { warehouseApi } from '@/api/business/warehouse/warehouse-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { warehouseColumns } from '../config/columns'

/**
 * 仓库服务
 * 提供仓库相关的业务逻辑和数据管理
 */
export class WarehouseService extends BaseCrudService<WarehouseResult, WarehouseFormParam, WarehousePageParam> {
  constructor() {
    // 初始化服务
    super(
      '仓库', // 业务名称
      warehouseColumns,
      {
        // 使用已有的API
        add: warehouseApi.addWarehouse,
        queryPage: warehouseApi.warehousePage,
        getDetail: warehouseApi.warehouseDetail,
        update: warehouseApi.updateWarehouse,
        import: warehouseApi.importWarehouse,
        export: warehouseApi.exportWarehouse,
        delete: warehouseApi.deleteWarehouse,
        batchDelete: warehouseApi.batchDeleteWarehouse,
        downloadTemplate: warehouseApi.exportWarehouse,
      },
    )
  }
}

// 单例模式
export const warehouseService = new WarehouseService()
