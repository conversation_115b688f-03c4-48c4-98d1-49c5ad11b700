<script lang="ts" setup>
import type { CustomerService } from '../service/CustomerService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'

import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'

// 获取服务实例
const customerService = inject<CustomerService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="customerService.detailOpen" :title="customerService.detailTitle" :footer="null" @cancel="customerService.closeDetail()">
    <Spin :spinning="customerService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="客户编码">
          <Text :value="customerService.detailData.customerCode" />
        </DescriptionsItem>
        <DescriptionsItem label="客户名称">
          <Text :value="customerService.detailData.customerName" />
        </DescriptionsItem>
        <DescriptionsItem label="客户类型">
          <DictTag key-code="customer_type" :value-code="customerService.detailData.customerType" />
        </DescriptionsItem>
        <DescriptionsItem label="信用等级">
          <DictTag key-code="credit_level" :value-code="customerService.detailData.creditLevel" />
        </DescriptionsItem>

        <!-- 联系信息 -->
        <DescriptionsItem label="联系人姓名">
          <Text :value="customerService.detailData.contactName" />
        </DescriptionsItem>
        <DescriptionsItem label="联系人职位">
          <Text :value="customerService.detailData.contactPosition" />
        </DescriptionsItem>
        <DescriptionsItem label="联系人部门">
          <Text :value="customerService.detailData.contactDepartment" />
        </DescriptionsItem>
        <DescriptionsItem label="联系电话">
          <Text :value="customerService.detailData.contactPhone" />
        </DescriptionsItem>
        <DescriptionsItem label="联系手机">
          <Text :value="customerService.detailData.contactMobile" />
        </DescriptionsItem>
        <DescriptionsItem label="联系邮箱">
          <Text :value="customerService.detailData.contactEmail" />
        </DescriptionsItem>

        <!-- 地址信息 -->
        <DescriptionsItem label="省份">
          <Text :value="customerService.detailData.province" />
        </DescriptionsItem>
        <DescriptionsItem label="城市">
          <Text :value="customerService.detailData.city" />
        </DescriptionsItem>
        <DescriptionsItem label="区县">
          <Text :value="customerService.detailData.district" />
        </DescriptionsItem>
        <DescriptionsItem label="详细地址">
          <Text :value="customerService.detailData.address" />
        </DescriptionsItem>
        <DescriptionsItem label="收货地址">
          <Text :value="customerService.detailData.shippingAddress" />
        </DescriptionsItem>
        <DescriptionsItem label="发票地址">
          <Text :value="customerService.detailData.billingAddress" />
        </DescriptionsItem>

        <!-- 企业信息 -->
        <DescriptionsItem label="营业执照号">
          <Text :value="customerService.detailData.businessLicense" />
        </DescriptionsItem>
        <DescriptionsItem label="税务登记号">
          <Text :value="customerService.detailData.taxId" />
        </DescriptionsItem>
        <DescriptionsItem label="开户银行">
          <Text :value="customerService.detailData.bankName" />
        </DescriptionsItem>
        <DescriptionsItem label="银行账号">
          <Text :value="customerService.detailData.bankAccount" />
        </DescriptionsItem>

        <!-- 状态和时间 -->
        <DescriptionsItem label="状态">
          <DictTag key-code="customer_status" :value-code="customerService.detailData.status" />
        </DescriptionsItem>
        <DescriptionsItem label="备注">
          <Text :value="customerService.detailData.remark" />
        </DescriptionsItem>
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(customerService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(customerService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
