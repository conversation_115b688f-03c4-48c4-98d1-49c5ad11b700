<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { CustomerService } from '../service/CustomerService'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from '../config/rule'

// 获取服务实例
const customerService = inject<CustomerService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await customerService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="customerService.formOpen"
    :title="customerService.formTitle"
    :confirm-loading="customerService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="customerService.closeForm()"
  >
    <Spin :spinning="customerService.formLoading">
      <Form ref="formRef" layout="vertical" :model="customerService.formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="客户编码" name="customerCode">
              <InputText v-model:value="customerService.formData.customerCode" placeholder="请输入客户编码" :disabled="customerService.formType === 'edit'" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="客户名称" name="customerName">
              <InputText v-model:value="customerService.formData.customerName" placeholder="请输入客户名称" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="客户类型" name="customerType">
              <DictSelect
                v-model:value="customerService.formData.customerType" key-code="customer_type" placeholder="请选择客户类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="信用等级" name="creditLevel">
              <DictSelect v-model:value="customerService.formData.creditLevel" key-code="credit_level" placeholder="请选择信用等级" width="100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人姓名" name="contactName">
              <InputText v-model:value="customerService.formData.contactName" placeholder="请输入联系人姓名" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系人职位" name="contactPosition">
              <InputText v-model:value="customerService.formData.contactPosition" placeholder="请输入联系人职位" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人部门" name="contactDepartment">
              <InputText v-model:value="customerService.formData.contactDepartment" placeholder="请输入联系人部门" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="customerService.formData.contactPhone" placeholder="请输入联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model:value="customerService.formData.contactMobile" placeholder="请输入联系手机" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="customerService.formData.contactEmail" placeholder="请输入联系邮箱" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="省份" name="province">
              <InputText v-model:value="customerService.formData.province" placeholder="请输入省份" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="城市" name="city">
              <InputText v-model:value="customerService.formData.city" placeholder="请输入城市" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="区县" name="district">
              <InputText v-model:value="customerService.formData.district" placeholder="请输入区县" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="customerService.formData.address" placeholder="请输入详细地址" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="收货地址" name="shippingAddress">
              <InputText v-model:value="customerService.formData.shippingAddress" placeholder="请输入收货地址" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="发票地址" name="billingAddress">
              <InputText v-model:value="customerService.formData.billingAddress" placeholder="请输入发票地址" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 企业信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="营业执照号" name="businessLicense">
              <InputText v-model:value="customerService.formData.businessLicense" placeholder="请输入营业执照号" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="税务登记号" name="taxId">
              <InputText v-model:value="customerService.formData.taxId" placeholder="请输入税务登记号" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="开户银行" name="bankName">
              <InputText v-model:value="customerService.formData.bankName" placeholder="请输入开户银行" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="银行账号" name="bankAccount">
              <InputText v-model:value="customerService.formData.bankAccount" placeholder="请输入银行账号" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 状态和备注 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="状态" name="status">
              <DictSelect v-model:value="customerService.formData.status" key-code="customer_status" placeholder="请选择状态" width="100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="备注" name="remark">
              <a-textarea v-model:value="customerService.formData.remark" placeholder="请输入备注" :rows="3" />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
