import type { CustomerFormParam, CustomerPageParam, CustomerResult } from '@/api/business/customer/model/customer-form-model'
import { customerApi } from '@/api/business/customer/customer-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { customerColumns } from '../config/columns'

/**
 * 客户服务
 * 提供客户相关的业务逻辑和数据管理
 */
export class CustomerService extends BaseCrudService<CustomerResult, CustomerFormParam, CustomerPageParam> {
  constructor() {
    // 初始化服务
    super(
      '客户', // 业务名称
      customerColumns,
      {
        // 使用已有的API
        add: customerApi.addCustomer,
        queryPage: customerApi.customerPage,
        getDetail: customerApi.customerDetail,
        update: customerApi.updateCustomer,
        import: customerApi.importCustomer,
        export: customerApi.exportCustomer,
        delete: customerApi.deleteCustomer,
        batchDelete: customerApi.batchDeleteCustomer,
      },
    )
  }
}

// 单例模式
export const customerService = new CustomerService()
