import type { CustomerResult } from '@/api/business/customer/model/customer-form-model'
import DictValue from '@/components/utils-old/dict-value/index.vue'
import { h } from 'vue'

// 获取客户类型对应的颜色
export function getCustomerTypeColor(type?: string): string {
  if (!type)
    return ''
  // 根据类型返回不同颜色
  switch (type) {
    case 'REGULAR':
      return 'blue'
    case 'VIP':
      return 'gold'
    case 'STRATEGIC':
      return 'purple'
    case 'DISTRIBUTOR':
      return 'green'
    case 'AGENT':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取客户状态对应的颜色
export function getCustomerStatusColor(status?: string): string {
  if (!status)
    return ''
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'INACTIVE':
      return 'warning'
    case 'BLACKLIST':
      return 'error'
    case 'POTENTIAL':
      return 'processing'
    default:
      return 'default'
  }
}

// 列定义
export const customerColumns = [
  {
    title: '客户编码',
    dataIndex: 'customerCode',
    width: 120,
    ellipsis: true,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '客户类型',
    dataIndex: 'customerType',
    width: 120,
    customRender: ({ record }: { record: CustomerResult }) => {
      return h('a-tag', { color: getCustomerTypeColor(record.customerType) }, () => h(DictValue, { keyCode: 'customer_type', valueCode: record.customerType }),
      )
    },
  },
  {
    title: '信用等级',
    dataIndex: 'creditLevel',
    width: 100,
    customRender: ({ record }: { record: CustomerResult }) => {
      if (!record.creditLevel)
        return h('span', '-')
      return h(DictValue, { keyCode: 'credit_level', valueCode: record.creditLevel })
    },
  },
  {
    title: '联系人',
    dataIndex: 'contactName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 130,
  },
  {
    title: '联系手机',
    dataIndex: 'contactMobile',
    width: 130,
  },
  {
    title: '省份',
    dataIndex: 'province',
    width: 100,
  },
  {
    title: '城市',
    dataIndex: 'city',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: CustomerResult }) => {
      return h('a-tag', { color: getCustomerStatusColor(record.status) }, () => h(DictValue, { keyCode: 'customer_status', valueCode: record.status }),
      )
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]
