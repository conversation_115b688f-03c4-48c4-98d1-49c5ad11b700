<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import Pagination from '@/components/base-old/ax-pagination/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DictSelect } from '@/components/utils/Dict'
import CustomerDetail from './components/CustomerDetail.vue'
import CustomerForm from './components/CustomerForm.vue'
import { customerService } from './service/CustomerService'

// 提供 service
customerService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'customer:query'" layout="vertical">
      <FormGrid>
        <FormItem label="客户编码">
          <InputText v-model:value="customerService.queryParam.customerCode" placeholder="请输入客户编码" />
        </FormItem>
        <FormItem label="客户名称">
          <InputText v-model:value="customerService.queryParam.customerName" placeholder="请输入客户名称" />
        </FormItem>
        <FormItem label="信用等级">
          <DictSelect v-model:value="customerService.queryParam.creditLevel" key-code="credit_level" placeholder="请选择信用等级" />
        </FormItem>
        <FormItem label="联系人">
          <InputText v-model:value="customerService.queryParam.contactName" placeholder="请输入联系人" />
        </FormItem>
        <FormItem label="状态">
          <DictSelect v-model:value="customerService.queryParam.status" key-code="customer_status" placeholder="请选择状态" />
        </FormItem>
        <FormItem label="客户类型">
          <DictSelect
            v-model:value="customerService.queryParam.customerType" key-code="customer_type"
            placeholder="请选择客户类型" width="150px"
          />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="customer:add" batch-delete-config="customer:delete" import-config="customer:import" export-config="customer:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="customerService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="customerService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'customer:view'" label="查看" icon-type="EyeOutlined"
              @click="customerService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'customer:edit'" label="编辑" icon-type="EditOutlined"
              @click="customerService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'customer:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="customerService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 客户表单 -->
  <CustomerForm />
  <!-- 客户详情 -->
  <CustomerDetail />
</template>
