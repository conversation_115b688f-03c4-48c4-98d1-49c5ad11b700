<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import PurchaseOrderCreateModal from './components/PurchaseOrderCreateModal.vue'
import PurchaseOrderDetail from './components/PurchaseOrderDetail.vue'
import PurchaseOrderForm from './components/PurchaseOrderForm.vue'
import { purchaseOrderSkuService } from './service/purchaseOrderSkuService'
import { supplierSearchServiceFormEx } from './service/supplierSearchServiceFormEx'

// 提供 service
purchaseOrderSkuService.provide()
supplierSearchServiceFormEx.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'purchaseOrder:query'" layout="vertical">
      <FormGrid>
        <FormItem label="订单编号">
          <InputText v-model:value="purchaseOrderSkuService.queryParam.orderNo" placeholder="请输入订单编号" />
        </FormItem>
        <FormItem label="供应商名称">
          <InputText v-model:value="purchaseOrderSkuService.queryParam.supplierName" placeholder="请输入供应商名称" />
        </FormItem>
        <FormItem label="采购类型">
          <DictSelect v-model:value="purchaseOrderSkuService.queryParam.orderType" key-code="order_type" placeholder="请选择采购类型" />
        </FormItem>
        <FormItem label="订单状态">
          <DictSelect
            v-model:value="purchaseOrderSkuService.queryParam.orderStatus"
            key-code="order_status"
            placeholder="请选择订单状态"
          />
        </FormItem>
        <FormItem label="采购日期">
          <DateRangePicker
            v-model:from="purchaseOrderSkuService.queryParam.orderDateFrom"
            v-model:to="purchaseOrderSkuService.queryParam.orderDateTo"
          />
        </FormItem>
        <FormItem label="付款状态">
          <DictSelect
            v-model:value="purchaseOrderSkuService.queryParam.paymentStatus"
            key-code="payment_status"
            placeholder="请选择付款状态"
          />
        </FormItem>
        <FormItem label="采购员">
          <InputText v-model:value="purchaseOrderSkuService.queryParam.purchaserName" placeholder="请输入采购员姓名" />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <IconButton
          v-privilege="'purchaseOrder:add'"
          label="开单"
          icon-type="FileDoneOutlined"
          type="primary"
          @click="purchaseOrderSkuService.openCreateModal"
        />
        <AxTableOperateButtons
          batch-delete-config="purchaseOrder:delete"
          import-config="purchaseOrder:import"
          export-config="purchaseOrder:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="purchaseOrderSkuService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="purchaseOrderSkuService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'purchaseOrder:view'"
              label="查看"
              icon-type="EyeOutlined"
              @click="purchaseOrderSkuService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'purchaseOrder:edit'"
              label="编辑"
              icon-type="EditOutlined"
              @click="purchaseOrderSkuService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'purchaseOrder:delete'"
              label="删除"
              icon-type="DeleteOutlined"
              color="red"
              @click="purchaseOrderSkuService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 采购订单表单 -->
  <PurchaseOrderForm />
  <!-- 采购订单详情 -->
  <PurchaseOrderDetail />
  <!-- 采购订单开单 -->
  <PurchaseOrderCreateModal />
</template>
