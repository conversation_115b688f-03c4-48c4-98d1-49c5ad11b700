import type { PurchaseOrderResult } from '@/api/business/purchase-order/model/purchase-order-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag, DictValue } from '@/components/utils/Dict'
import { formatDateTime, formatMoney } from '@/utils/format'
import { h } from 'vue'

// 获取订单类型对应的颜色
export function getOrderTypeColor(type?: string): DictColor {
  if (!type)
    return 'default'
  // 根据类别返回不同颜色
  switch (type) {
    case 'STANDARD':
      return 'blue'
    case 'URGENT':
      return 'red'
    case 'BLANKET':
      return 'green'
    case 'CONSIGNMENT':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取订单状态对应的颜色
export function getOrderStatusColor(status?: string): DictColor {
  if (!status)
    return 'default'
  switch (status) {
    case 'DRAFT':
      return 'default'
    case 'SUBMITTED':
      return 'blue'
    case 'APPROVED':
      return 'green'
    case 'CONFIRMED':
      return 'blue'
    case 'PARTIAL_RECEIVED':
      return 'orange'
    case 'RECEIVED':
      return 'green'
    case 'COMPLETED':
      return 'green'
    case 'CANCELLED':
      return 'red'
    default:
      return 'default'
  }
}

// 获取付款状态对应的颜色
export function getPaymentStatusColor(status?: string): DictColor {
  if (!status)
    return 'default'
  switch (status) {
    case 'UNPAID':
      return 'red'
    case 'PARTIAL':
      return 'orange'
    case 'PAID':
      return 'green'
    case 'OVERDUE':
      return 'red'
    default:
      return 'default'
  }
}

// 列定义
export const purchaseOrderColumns = [
  {
    title: '采购订单编号',
    dataIndex: 'orderNo',
    width: 150,
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '供应商',
    dataIndex: 'supplierName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '采购类型',
    dataIndex: 'orderType',
    width: 100,
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return h(DictTag, {
        color: getOrderTypeColor(record.orderType),
        keyCode: 'order_type',
        valueCode: record.orderType,
      })
    },
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 120,
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return h(DictTag, {
        color: getOrderStatusColor(record.orderStatus),
        keyCode: 'order_status',
        valueCode: record.orderStatus,
      })
    },
  },
  {
    title: '采购日期',
    dataIndex: 'orderDate',
    width: 120,
    sorter: true,
  },
  {
    title: '预期交货日期',
    dataIndex: 'expectedDeliveryDate',
    width: 120,
    sorter: true,
  },
  {
    title: '最终金额',
    dataIndex: 'finalAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return formatMoney(record.finalAmount)
    },
  },
  {
    title: '已付金额',
    dataIndex: 'paidAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return formatMoney(record.paidAmount)
    },
  },
  {
    title: '付款状态',
    dataIndex: 'paymentStatus',
    width: 100,
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return h(DictTag, {
        color: getPaymentStatusColor(record.paymentStatus),
        keyCode: 'payment_status',
        valueCode: record.paymentStatus,
      })
    },
  },
  {
    title: '采购员',
    dataIndex: 'purchaserName',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
    customRender: ({ record }: { record: PurchaseOrderResult }) => {
      return formatDateTime(record.createTime)
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]
