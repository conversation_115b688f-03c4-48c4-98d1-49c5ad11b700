import type { PurchaseOrderItem } from '@/api/business/purchase-order-item/model/purchase-order-item-types'
import type { PurchaseOrderForm, PurchaseOrderPageParam, PurchaseOrderResult } from '@/api/business/purchase-order/model/purchase-order-types'
import type { SkuResult } from '@/api/business/sku/model/sku-types'
import { purchaseOrderApi } from '@/api/business/purchase-order/purchase-order-api'
import { skuApi } from '@/api/business/sku/sku-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { debounce } from 'lodash-es'
import { computed, nextTick, reactive, ref, watch } from 'vue'

import { purchaseOrderColumns } from '../columns'

// 已选sku类型
export interface SelectedSkuResult extends SkuResult {
  quantity: number
  unitPrice: number
  subtotal: number
}

// 订单表单数据接口
export interface OrderFormData {
  // 联系信息
  contactName: string
  contactPhone: string
  contactMobile: string
  // 地址信息
  deliveryAddress: string // 采购订单使用deliveryAddress
  // 订单信息
  orderType: string
  orderDate: string
  expectedDeliveryDate: string // 采购订单使用expectedDeliveryDate
  purchaserId?: number // 采购订单使用purchaserId
  purchaserName: string // 采购订单使用purchaserName
  // 付款信息
  paymentMethod: string
  paymentTerms: string
  paidAmount: number
  // 备注
  remark: string
}

/**
 * 采购订单SKU服务
 * 提供采购订单相关的业务逻辑和数据管理
 */
export class PurchaseOrderSkuService extends BaseCrudService<PurchaseOrderResult, PurchaseOrderForm, PurchaseOrderPageParam> {
  // ==================== 开单弹窗状态管理 ====================

  // 开单弹窗显示状态
  private _createModalOpen = ref(false)

  // Modal状态
  private _ModalStatus = ref<{ activeTab: 'products' | 'settlement' }>({ activeTab: 'products' })

  // 订单表单数据
  private _orderForm = reactive<OrderFormData>({
    // 联系信息
    contactName: '',
    contactPhone: '',
    contactMobile: '',
    // 地址信息
    deliveryAddress: '',
    // 订单信息
    orderType: 'STANDARD', // 采购订单默认类型
    orderDate: '',
    expectedDeliveryDate: '',
    purchaserId: undefined,
    purchaserName: '',
    // 付款信息
    paymentMethod: '',
    paymentTerms: '',
    paidAmount: 0,
    // 备注
    remark: '',
  })

  // sku搜索参数
  private _skuSearchParam = reactive({
    skuNameExt: '', // 使用扩展搜索字段
    status: '1', // 只获取上架sku
    pageParam: {
      pageNum: 1,
      pageSize: 10,
    },
  })

  // sku列表总数
  private _skuTotal = ref(0)

  // sku列表加载状态
  private _skuLoading = ref(false)

  // sku列表数据
  private _skuList = ref<SkuResult[]>([])

  // 已选sku列表
  private _selectedSkus = ref<SelectedSkuResult[]>([])

  // 已选sku列表数据（格式化后）
  private _selectedSkusForSubmit = ref<PurchaseOrderItem[]>([])

  // 响应式计算属性
  private _isSettlementTab = computed(() => this._ModalStatus.value.activeTab === 'settlement')
  private _totalAmount = computed(() => {
    return this._selectedSkus.value.reduce((sum, item) => sum + item.subtotal, 0)
  })

  // 计算剩余未付金额
  private _remainingAmount = computed(() => {
    const totalAmount = this._totalAmount.value
    const paidAmount = this._orderForm.paidAmount || 0
    return Math.max(0, totalAmount - paidAmount)
  })

  // 计算是否可以提交订单
  private _canSubmit = computed(() => {
    // 1. 必须有选中的SKU
    const hasSelectedProducts = this._selectedSkus.value.length > 0

    // 2. 必填字段都已填写
    const requiredFields = {
      contactName: !!this._orderForm.contactName,
      contactPhone: !!this._orderForm.contactPhone,
      deliveryAddress: !!this._orderForm.deliveryAddress,
      paymentMethod: !!this._orderForm.paymentMethod,
    }

    const allRequiredFilled = Object.values(requiredFields).every(Boolean)

    // 3. 已付金额不能超过总金额
    const paidAmountValid = (this._orderForm.paidAmount || 0) <= this._totalAmount.value

    return hasSelectedProducts && allRequiredFilled && paidAmountValid
  })

  // ==================== 防抖搜索相关 ====================

  // 防抖搜索函数
  private debouncedSearch = debounce(() => {
    this._executeSearch()
  }, 300)

  // ==================== Getters ====================

  get createModalOpen() {
    return this._createModalOpen.value
  }

  set createModalOpen(value: boolean) {
    this._createModalOpen.value = value
  }

  get modalStatus() {
    return this._ModalStatus.value
  }

  set modalStatus(value: { activeTab: 'products' | 'settlement' }) {
    this._ModalStatus.value = {
      ...this._ModalStatus.value,
      ...value,
    }
  }

  get activeTab() {
    return this._ModalStatus.value.activeTab
  }

  set activeTab(value: 'products' | 'settlement') {
    this._ModalStatus.value = {
      ...this._ModalStatus.value,
      activeTab: value,
    }
  }

  get isSettlementTab() {
    return this._isSettlementTab.value
  }

  get productList() {
    return this._skuList.value
  }

  get productSearchParam() {
    return this._skuSearchParam
  }

  get productTotal() {
    return this._skuTotal.value
  }

  get productLoading() {
    return this._skuLoading.value
  }

  get selectedSkus() {
    return this._selectedSkus.value
  }

  // 为了兼容性，添加 selectedProducts 别名
  get selectedProducts() {
    return this._selectedSkus.value
  }

  // 计算总金额
  get totalAmount() {
    return this._totalAmount.value
  }

  // 订单表单数据
  get orderForm() {
    return this._orderForm
  }

  // 剩余未付金额
  get remainingAmount() {
    return this._remainingAmount.value
  }

  // 是否可以提交订单
  get canSubmit() {
    return this._canSubmit.value
  }

  constructor() {
    // 初始化服务
    super(
      '采购订单SKU', // 业务名称
      purchaseOrderColumns as Record<string, unknown>[],
      {
        // 使用purchase-order的API
        add: purchaseOrderApi.addPurchaseOrder,
        queryPage: purchaseOrderApi.purchaseOrderPage,
        getDetail: purchaseOrderApi.purchaseOrderDetail,
        update: purchaseOrderApi.updatePurchaseOrder,
        import: purchaseOrderApi.importPurchaseOrder,
        export: purchaseOrderApi.exportPurchaseOrder,
        delete: purchaseOrderApi.deletePurchaseOrder,
        batchDelete: purchaseOrderApi.batchDeletePurchaseOrder,
      },
      // 初始查询参数
      {
        pageParam: {
          pageNum: 1,
          pageSize: 10,
        },
      } as Partial<PurchaseOrderPageParam>,
    )

    // 初始化搜索监听器 - 简化版本
    watch(
      () => this._skuSearchParam.skuNameExt,
      (newValue, oldValue) => {
        if (newValue !== oldValue) {
          this._skuSearchParam.pageParam.pageNum = 1

          if (newValue && newValue.trim()) {
            this.debouncedSearch()
          }
          else {
            this.debouncedSearch.cancel()
            this._executeSearch()
          }
        }
      },
      { immediate: false },
    )

    // 监听表单数据变化，自动同步供应商信息到订单表单
    watch(
      () => this.formData,
      () => {
        this.syncSupplierToOrderForm()
      },
      { deep: true, immediate: true },
    )
  }

  // ==================== 防抖搜索方法 ====================

  /**
   * 执行实际的搜索操作
   */
  private _executeSearch = async () => {
    try {
      this._skuLoading.value = true
      const response = await skuApi.skuPage({
        skuNameExt: this._skuSearchParam.skuNameExt,
        status: this._skuSearchParam.status,
        pageParam: this._skuSearchParam.pageParam,
      })

      if (response.data) {
        this._skuList.value = response.data.list || []
        this._skuTotal.value = response.data.total || 0
      }
    }
    catch (error) {
      console.error('搜索sku失败:', error)
    }
    finally {
      this._skuLoading.value = false
    }
  }

  // ==================== 开单弹窗相关方法 ====================

  /**
   * 打开开单弹窗
   */
  openCreateModal = () => {
    this._createModalOpen.value = true
    this.resetCreateModalData()
    this.searchProducts()
  }

  /**
   * 关闭开单弹窗
   */
  closeCreateModal = () => {
    this._createModalOpen.value = false
    this.resetCreateModalData()
  }

  /**
   * 处理关闭弹窗的完整逻辑
   */
  handleCloseModal = () => {
    // 1. 重置订单服务的表单数据（包括供应商信息）
    this.resetFormData()
    Object.assign(this.formData, {
      supplierId: undefined,
      supplierName: '',
      supplierCode: '',
      contactName: '',
      contactPhone: '',
      contactMobile: '',
      deliveryAddress: '',
      billingAddress: '',
    })

    // 2. 调用订单服务的关闭方法（清空商品选择和订单表单等）
    this.closeCreateModal()

    // 3. 强制触发响应式更新，确保UI完全同步
    nextTick(() => {
      // 确保所有计算属性重新计算，验证重置是否成功
      console.log('✅ 表单数据重置完成:', {
        已付金额: this.orderForm.paidAmount,
        收款账户: this.orderForm.paymentMethod,
        订单总计: this.totalAmount,
        选中商品数量: this.selectedProducts.length,
      })
    })
  }

  /**
   * 处理提交订单的完整逻辑
   */
  handleSubmitOrder = async () => {
    if (!this.canSubmit) {
      console.warn('订单数据不完整，无法提交')
      return
    }

    try {
      // TODO: 收集表单数据并提交
      // 暂时模拟提交逻辑
      console.log('提交订单数据:', {
        ...this.orderForm,
        subtotalAmount: this.totalAmount,
        finalAmount: this.totalAmount,
        orderItems: this.selectedProducts,
      })

      // 调用服务提交订单
      await this.submitOrder()

      console.log('✅ 订单提交成功')
    }
    catch (error) {
      console.error('❌ 订单提交失败:', error)
      throw error
    }
  }

  /**
   * 重置开单弹窗数据
   */
  private resetCreateModalData = () => {
    this.debouncedSearch.cancel() // 清理防抖任务

    // 重置订单表单数据
    Object.assign(this._orderForm, {
      contactName: '',
      contactPhone: '',
      contactMobile: '',
      deliveryAddress: '',
      orderType: 'STANDARD',
      orderDate: '',
      expectedDeliveryDate: '',
      purchaserId: undefined,
      purchaserName: '',
      paymentMethod: '',
      paymentTerms: '',
      paidAmount: 0,
      remark: '',
    })

    // 重置基础表单数据
    this.formData.orderType = 'STANDARD'
    this.formData.paidAmount = 0

    // 重置商品相关数据
    this._selectedSkus.value = []
    this._selectedSkusForSubmit.value = []
    this._skuSearchParam.skuNameExt = ''
    this._skuSearchParam.pageParam.pageNum = 1
    this._skuList.value = []
    this._skuTotal.value = 0

    // 重置标签页状态
    this._ModalStatus.value.activeTab = 'products'
  }

  /**
   * 搜索sku（保持向后兼容）
   * @deprecated 建议直接修改 productSearchParam.skuNameExt 触发自动搜索
   */
  searchProducts = async () => {
    this.debouncedSearch.cancel()
    await this._executeSearch()
  }

  /**
   * 重置搜索
   */
  resetSearch = () => {
    this.debouncedSearch.cancel()
    this._skuSearchParam.skuNameExt = ''
    this._skuSearchParam.pageParam.pageNum = 1
    this._executeSearch()
  }

  /**
   * 强制立即搜索（跳过防抖）
   */
  forceSearch = () => {
    this.debouncedSearch.cancel()
    this._skuSearchParam.pageParam.pageNum = 1
    this._executeSearch()
  }

  /**
   * 添加sku到已选列表
   */
  addProduct = (product: SkuResult) => {
    const selectedProduct: SelectedSkuResult = {
      ...product,
      quantity: 1,
      unitPrice: product.price ?? 0, // 使用SKU的价格作为默认单价
      subtotal: product.price ?? 0,
    }
    this._selectedSkus.value.push(selectedProduct)
  }

  addProductForSubmit = (product: SkuResult) => {
    const selectedProductForSubmit: PurchaseOrderItem = {
      ...product,
      orderId: this.formData.id,
      orderNo: this.formData.orderNo,
      lineNo: this._selectedSkusForSubmit.value.length + 1,
      orderQuantity: 1,
      unitPrice: product.price ?? 0, // 使用SKU的价格作为默认单价
      lineAmount: product.price ?? 0,
    }
    this._selectedSkusForSubmit.value.push(selectedProductForSubmit)
  }

  removeProductForSubmit = (index: number) => {
    this._selectedSkusForSubmit.value.splice(index, 1)
  }

  /**
   * 移除已选sku
   */
  removeProduct = (index: number) => {
    this._selectedSkus.value.splice(index, 1)
    this._selectedSkusForSubmit.value.splice(index, 1)
  }

  /**
   * 检查sku是否已被选中
   */
  isProductSelected = (product: SkuResult): boolean => {
    return this._selectedSkus.value.some(p => p.id === product.id)
  }

  /**
   * 切换sku选中状态（添加或移除）
   */
  switchSku = (product: SkuResult) => {
    const existingIndex = this._selectedSkus.value.findIndex(p => p.id === product.id)

    if (existingIndex >= 0) {
      // 如果sku已存在，移除它
      this.removeProduct(existingIndex)
      this.removeProductForSubmit(existingIndex)
    }
    else {
      // 如果sku不存在，添加它
      this.addProduct(product)
      this.addProductForSubmit(product)
    }
  }

  /**
   * 更新sku数量
   */
  updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1)
      return

    const product = this._selectedSkus.value[index]
    product.quantity = quantity
    product.subtotal = product.quantity * product.unitPrice

    const productForSubmit = this._selectedSkusForSubmit.value[index]
    productForSubmit.unitPrice = product.unitPrice
    productForSubmit.orderQuantity = quantity
    productForSubmit.lineAmount = productForSubmit.orderQuantity * productForSubmit.unitPrice!
  }

  /**
   * 更新sku单价
   */
  updateProductPrice = (index: number, unitPrice: number) => {
    if (unitPrice < 0)
      return

    const product = this._selectedSkus.value[index]
    product.unitPrice = unitPrice
    product.subtotal = product.quantity * product.unitPrice
  }

  /**
   * sku搜索分页变化
   */
  onProductPageChange = (page: number, pageSize?: number) => {
    this._skuSearchParam.pageParam.pageNum = page
    if (pageSize) {
      this._skuSearchParam.pageParam.pageSize = pageSize
    }
    this.searchProducts()
  }

  /**
   * 切换标签页
   */
  switchTab = (tab: 'products' | 'settlement') => {
    if (this._ModalStatus.value.activeTab === tab) {
      return
    }
    this._ModalStatus.value.activeTab = tab
  }

  // ==================== 结算相关业务逻辑 ====================

  /**
   * 已付金额变化处理
   */
  handlePaidAmountChange = (value: number | null) => {
    const newPaidAmount = value || 0
    const totalAmount = this._totalAmount.value

    if (newPaidAmount > totalAmount) {
      // 如果已付金额超过总金额，自动设置为总金额
      this._orderForm.paidAmount = totalAmount
    }
    else {
      this._orderForm.paidAmount = newPaidAmount
    }
  }

  /**
   * 已付金额输入框获得焦点时处理
   */
  handlePaidAmountFocus = () => {
    // 调用自动填充逻辑
    this.autoFillPaidAmount()
  }

  /**
   * 检查是否应该自动填充已付金额
   */
  get shouldAutoFillPaidAmount(): boolean {
    return !this._orderForm.paidAmount && this._totalAmount.value > 0
  }

  /**
   * 自动填充已付金额
   * 当已付金额为空且订单总金额大于0时，自动将总金额填入已付金额
   */
  autoFillPaidAmount = (): void => {
    if (this.shouldAutoFillPaidAmount) {
      this._orderForm.paidAmount = this._totalAmount.value
    }
  }

  /**
   * 同步供应商信息到订单表单
   */
  syncSupplierToOrderForm = () => {
    if (this.formData.supplierId) {
      // 同步供应商选择后的联系信息
      if (this.formData.contactName)
        this._orderForm.contactName = this.formData.contactName
      if (this.formData.contactPhone)
        this._orderForm.contactPhone = this.formData.contactPhone
      if (this.formData.contactMobile)
        this._orderForm.contactMobile = this.formData.contactMobile
      if (this.formData.deliveryAddress)
        this._orderForm.deliveryAddress = this.formData.deliveryAddress
    }
    else {
      // 清空供应商时，清空相关字段
      this._orderForm.contactName = ''
      this._orderForm.contactPhone = ''
      this._orderForm.contactMobile = ''
      this._orderForm.deliveryAddress = ''
    }
  }

  /**
   * 提交订单
   */
  submitOrder = async () => {
    if (!this._canSubmit.value) {
      throw new Error('订单数据不完整，无法提交')
    }

    if (this._selectedSkus.value.length === 0) {
      throw new Error('请选择sku')
    }

    // 构建采购订单数据并设置到表单数据中
    Object.assign(this.formData, {
      // 订单表单数据 (先设置，避免被后面的字段覆盖)
      ...this._orderForm,

      // 基础订单信息
      orderNo: '', // 后端生成
      orderType: 'STANDARD',
      orderStatus: 'DRAFT', // 使用purchase-order的状态枚举
      orderDate: new Date().toISOString().split('T')[0],

      // 金额信息
      subtotalAmount: this._totalAmount.value,
      finalAmount: this._totalAmount.value,
      discountAmount: 0,
      taxAmount: 0,
      shippingFee: 0,
      paidAmount: this._orderForm.paidAmount,

      // 默认值
      currency: 'CNY',
      exchangeRate: 1,
      paymentMethod: this._orderForm.paymentMethod,
      paymentTerms: 'PREPAID',
      orderSource: 'MANUAL',
      priorityLevel: 'NORMAL',
      invoiceStatus: 'PENDING',
      paymentStatus: 'UNPAID',

      // sku明细
      itemList: toRaw(this._selectedSkusForSubmit.value),
    })

    // 提交订单
    const success = await this.submitForm(() => {
      this.closeCreateModal()
      this.queryPage()
    })

    return success
  }
}

// 单例模式
export const purchaseOrderSkuService = new PurchaseOrderSkuService()
