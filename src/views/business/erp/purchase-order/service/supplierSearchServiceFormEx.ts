import type { SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { SupplierSearchService } from '@/components/business/AxSearch/SupplierSearch/service/SupplierSearchService'
import { message } from 'ant-design-vue'

export class SupplierSearchServiceFormEx extends SupplierSearchService {
  constructor() {
    super({
      allowAdd: true,
      allowEdit: true,
      allowDelete: true,
    })
  }

  onSupplierChange = async (value: unknown, option?: DefaultOptionType): Promise<void> => {
    if (!this.parentService) {
      return
    }

    console.log('onSupplierChange', value, option)

    // 基础供应商信息更新
    Object.assign(this.parentService.formData, {
      supplierId: option?.key as string | number | undefined,
      supplierName: value as string | undefined,
    })

    // 如果有供应商ID，获取详细信息并自动填充结算信息
    const supplierId = option?.key as number | undefined
    if (supplierId) {
      try {
        const response = await supplierApi.getSupplierDetail(supplierId)

        if (response.success && response.data) {
          const supplierDetail: SupplierResult = response.data

          // 自动填充结算信息到父服务的formData
          Object.assign(this.parentService.formData, {
            // 联系信息 - 注意字段映射差异
            contactName: supplierDetail.contactPerson || '', // 供应商使用contactPerson
            contactPhone: supplierDetail.contactPhone || '',
            contactMobile: supplierDetail.alternativePhone || '', // 使用备用电话作为手机

            // 收货地址信息 - 采购订单使用deliveryAddress
            deliveryAddress: supplierDetail.address || this.buildFullAddress(supplierDetail),

            // 供应商编码
            supplierCode: supplierDetail.supplierCode || '',
          })

          console.log('供应商信息已自动填充到结算信息', {
            supplierId,
            supplierName: supplierDetail.supplierName,
            contactName: supplierDetail.contactPerson,
            contactPhone: supplierDetail.contactPhone,
            contactMobile: supplierDetail.alternativePhone,
            deliveryAddress: supplierDetail.address || this.buildFullAddress(supplierDetail),
          })
        }
      }
      catch (error) {
        console.error('获取供应商详情失败:', error)
        message.error('获取供应商详情失败，请手动填写联系信息')
      }
    }
    else {
      // 如果没有选择供应商，清空相关字段
      Object.assign(this.parentService.formData, {
        contactName: '',
        contactPhone: '',
        contactMobile: '',
        deliveryAddress: '',
        supplierCode: '',
      })
    }
  }

  /**
   * 构建完整地址字符串
   * 将省市区详细地址拼接成完整的收货地址
   */
  private buildFullAddress = (supplier: SupplierResult): string => {
    const addressParts = [
      supplier.province,
      supplier.city,
      supplier.district,
      supplier.address,
    ].filter(part => part && part.trim() !== '')

    return addressParts.join(' ')
  }
}

export const supplierSearchServiceFormEx = new SupplierSearchServiceFormEx()
