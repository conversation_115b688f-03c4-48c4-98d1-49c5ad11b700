<script lang="ts" setup>
import type { PurchaseOrderService } from '../service/purchaseOrderService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime, formatMoney } from '@/utils/format'
import { inject } from 'vue'
import { getOrderStatusColor, getOrderTypeColor, getPaymentStatusColor } from '../columns'

// 获取服务实例
const purchaseOrderService = inject<PurchaseOrderService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="purchaseOrderService.detailOpen" :title="purchaseOrderService.detailTitle" :footer="null" width="1000px" @cancel="purchaseOrderService.closeDetail()">
    <Spin :spinning="purchaseOrderService.detailLoading">
      <Descriptions :column="3" :colon="false" bordered>
        <DescriptionsItem label="订单编号">
          <Text :value="purchaseOrderService.detailData.orderNo" />
        </DescriptionsItem>
        <DescriptionsItem label="采购类型">
          <DictTag
            :color="getOrderTypeColor(purchaseOrderService.detailData.orderType)"
            key-code="purchase_order_type"
            :value-code="purchaseOrderService.detailData.orderType"
          />
        </DescriptionsItem>
        <DescriptionsItem label="订单状态">
          <DictTag
            :color="getOrderStatusColor(purchaseOrderService.detailData.orderStatus)"
            key-code="purchase_order_status"
            :value-code="purchaseOrderService.detailData.orderStatus"
          />
        </DescriptionsItem>

        <DescriptionsItem label="供应商编码">
          <Text :value="purchaseOrderService.detailData.supplierCode" />
        </DescriptionsItem>
        <DescriptionsItem label="供应商名称" :span="2">
          <Text :value="purchaseOrderService.detailData.supplierName" />
        </DescriptionsItem>

        <DescriptionsItem label="采购日期">
          <Text :value="purchaseOrderService.detailData.orderDate" />
        </DescriptionsItem>
        <DescriptionsItem label="预期交货日期">
          <Text :value="purchaseOrderService.detailData.expectedDeliveryDate" />
        </DescriptionsItem>
        <DescriptionsItem label="预计完成日期">
          <Text :value="purchaseOrderService.detailData.estimatedCompletionDate" />
        </DescriptionsItem>

        <DescriptionsItem label="付款方式">
          <DictTag key-code="payment_method" :value-code="purchaseOrderService.detailData.paymentMethod" />
        </DescriptionsItem>
        <DescriptionsItem label="付款条件">
          <DictTag key-code="payment_terms" :value-code="purchaseOrderService.detailData.paymentTerms" />
        </DescriptionsItem>
        <DescriptionsItem label="付款状态">
          <DictTag
            :color="getPaymentStatusColor(purchaseOrderService.detailData.paymentStatus)"
            key-code="payment_status"
            :value-code="purchaseOrderService.detailData.paymentStatus"
          />
        </DescriptionsItem>

        <DescriptionsItem label="币种">
          <Text :value="purchaseOrderService.detailData.currency" />
        </DescriptionsItem>
        <DescriptionsItem label="汇率">
          <Text :value="purchaseOrderService.detailData.exchangeRate?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="发票状态">
          <DictTag key-code="invoice_status" :value-code="purchaseOrderService.detailData.invoiceStatus" />
        </DescriptionsItem>

        <DescriptionsItem label="商品小计">
          <Text :value="formatMoney(purchaseOrderService.detailData.subtotalAmount)" />
        </DescriptionsItem>
        <DescriptionsItem label="折扣金额">
          <Text :value="formatMoney(purchaseOrderService.detailData.discountAmount)" />
        </DescriptionsItem>
        <DescriptionsItem label="税额">
          <Text :value="formatMoney(purchaseOrderService.detailData.taxAmount)" />
        </DescriptionsItem>

        <DescriptionsItem label="运费">
          <Text :value="formatMoney(purchaseOrderService.detailData.shippingFee)" />
        </DescriptionsItem>
        <DescriptionsItem label="最终金额">
          <Text :value="formatMoney(purchaseOrderService.detailData.finalAmount)" />
        </DescriptionsItem>
        <DescriptionsItem label="已付金额">
          <Text :value="formatMoney(purchaseOrderService.detailData.paidAmount)" />
        </DescriptionsItem>

        <DescriptionsItem label="收货地址" :span="3">
          <Text :value="purchaseOrderService.detailData.deliveryAddress" />
        </DescriptionsItem>

        <DescriptionsItem label="发票地址" :span="3">
          <Text :value="purchaseOrderService.detailData.billingAddress" />
        </DescriptionsItem>

        <DescriptionsItem label="联系人">
          <Text :value="purchaseOrderService.detailData.contactName" />
        </DescriptionsItem>
        <DescriptionsItem label="联系电话">
          <Text :value="purchaseOrderService.detailData.contactPhone" />
        </DescriptionsItem>
        <DescriptionsItem label="联系手机">
          <Text :value="purchaseOrderService.detailData.contactMobile" />
        </DescriptionsItem>

        <DescriptionsItem label="采购员">
          <Text :value="purchaseOrderService.detailData.purchaserName" />
        </DescriptionsItem>
        <DescriptionsItem label="采购来源">
          <DictTag key-code="purchase_order_source" :value-code="purchaseOrderService.detailData.orderSource" />
        </DescriptionsItem>
        <DescriptionsItem label="优先级">
          <DictTag key-code="priority_level" :value-code="purchaseOrderService.detailData.priorityLevel" />
        </DescriptionsItem>

        <DescriptionsItem label="审批人">
          <Text :value="purchaseOrderService.detailData.approvalUserId?.toString()" />
        </DescriptionsItem>
        <DescriptionsItem label="审批时间">
          <Text :value="formatDateTime(purchaseOrderService.detailData.approvalTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="审批备注">
          <Text :value="purchaseOrderService.detailData.approvalRemark" />
        </DescriptionsItem>

        <DescriptionsItem label="备注" :span="3">
          <Text :value="purchaseOrderService.detailData.remark" />
        </DescriptionsItem>

        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(purchaseOrderService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(purchaseOrderService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
