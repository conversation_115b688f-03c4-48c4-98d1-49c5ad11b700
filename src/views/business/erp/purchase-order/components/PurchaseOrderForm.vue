<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { PurchaseOrderService } from '../service/purchaseOrderService'
import { CustomTextArea } from '@/components/base/CustomText'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { DatePicker } from '@/components/base/Picker'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { computed, inject, ref } from 'vue'
// 表单验证规则
import { rules } from './rule'

// 获取服务实例
const purchaseOrderService = inject<PurchaseOrderService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await purchaseOrderService.submitFormAndRefresh()
}

// 是否编辑模式
const isEditMode = computed(() => purchaseOrderService.formType === 'edit')
</script>

<template>
  <Modal
    :open="purchaseOrderService.formOpen"
    :title="purchaseOrderService.formTitle"
    :confirm-loading="purchaseOrderService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="purchaseOrderService.closeForm()"
  >
    <Spin :spinning="purchaseOrderService.formLoading">
      <Form ref="formRef" :model="purchaseOrderService.formData" :rules="rules" :label-col="{ span: 7 }" :wrapper-col="{ span: 16 }">
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="订单编号" name="orderNo">
              <InputText v-model:value="purchaseOrderService.formData.orderNo" placeholder="请输入订单编号" :disabled="isEditMode" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="采购类型" name="orderType">
              <DictSelect
                v-model:value="purchaseOrderService.formData.orderType"
                key-code="purchase_order_type"
                placeholder="请选择采购类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 供应商信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="供应商" name="supplierId">
              <!-- TODO: 这里后续需要改成供应商选择组件 -->
              <InputNumber v-model:value="purchaseOrderService.formData.supplierId" placeholder="请输入供应商ID" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="订单状态" name="orderStatus">
              <DictSelect
                v-model:value="purchaseOrderService.formData.orderStatus"
                key-code="purchase_order_status"
                placeholder="请选择订单状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 日期信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="采购日期" name="orderDate">
              <DatePicker v-model:value="purchaseOrderService.formData.orderDate" placeholder="请选择采购日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="预期交货日期" name="expectedDeliveryDate">
              <DatePicker v-model:value="purchaseOrderService.formData.expectedDeliveryDate" placeholder="请选择预期交货日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 付款信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="付款方式" name="paymentMethod">
              <DictSelect
                v-model:value="purchaseOrderService.formData.paymentMethod"
                key-code="payment_method"
                placeholder="请选择付款方式"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="付款条件" name="paymentTerms">
              <DictSelect
                v-model:value="purchaseOrderService.formData.paymentTerms"
                key-code="payment_terms"
                placeholder="请选择付款条件"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 货币信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="币种" name="currency">
              <InputText v-model:value="purchaseOrderService.formData.currency" placeholder="请输入币种" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="汇率" name="exchangeRate">
              <InputNumber v-model:value="purchaseOrderService.formData.exchangeRate" placeholder="请输入汇率" :min="0" :precision="4" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 金额信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="商品小计" name="subtotalAmount">
              <InputNumber v-model:value="purchaseOrderService.formData.subtotalAmount" placeholder="请输入商品小计" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="折扣金额" name="discountAmount">
              <InputNumber v-model:value="purchaseOrderService.formData.discountAmount" placeholder="请输入折扣金额" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="税额" name="taxAmount">
              <InputNumber v-model:value="purchaseOrderService.formData.taxAmount" placeholder="请输入税额" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="运费" name="shippingFee">
              <InputNumber v-model:value="purchaseOrderService.formData.shippingFee" placeholder="请输入运费" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最终金额" name="finalAmount">
              <InputNumber v-model:value="purchaseOrderService.formData.finalAmount" placeholder="请输入最终金额" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="已付金额" name="paidAmount">
              <InputNumber v-model:value="purchaseOrderService.formData.paidAmount" placeholder="请输入已付金额" :min="0" :precision="2" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="收货地址" name="deliveryAddress" :label-col="{ span: 3.5 }" :wrapper-col="{ span: 20 }">
              <CustomTextArea v-model:value="purchaseOrderService.formData.deliveryAddress" placeholder="请输入收货地址" :rows="2" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="发票地址" name="billingAddress" :label-col="{ span: 3.5 }" :wrapper-col="{ span: 20 }">
              <CustomTextArea v-model:value="purchaseOrderService.formData.billingAddress" placeholder="请输入发票地址" :rows="2" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人" name="contactName">
              <InputText v-model:value="purchaseOrderService.formData.contactName" placeholder="请输入联系人" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="purchaseOrderService.formData.contactPhone" placeholder="请输入联系电话" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model:value="purchaseOrderService.formData.contactMobile" placeholder="请输入联系手机" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="采购员" name="purchaserId">
              <!-- TODO: 这里后续需要改成员工选择组件 -->
              <InputNumber v-model:value="purchaseOrderService.formData.purchaserId" placeholder="请输入采购员ID" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 其他信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="采购来源" name="orderSource">
              <DictSelect
                v-model:value="purchaseOrderService.formData.orderSource"
                key-code="purchase_order_source"
                placeholder="请选择采购来源"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="优先级" name="priorityLevel">
              <DictSelect
                v-model:value="purchaseOrderService.formData.priorityLevel"
                key-code="priority_level"
                placeholder="请选择优先级"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="预计完成日期" name="estimatedCompletionDate">
              <DatePicker v-model:value="purchaseOrderService.formData.estimatedCompletionDate" placeholder="请选择预计完成日期" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="发票状态" name="invoiceStatus">
              <DictSelect
                v-model:value="purchaseOrderService.formData.invoiceStatus"
                key-code="invoice_status"
                placeholder="请选择发票状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="付款状态" name="paymentStatus">
              <DictSelect
                v-model:value="purchaseOrderService.formData.paymentStatus"
                key-code="payment_status"
                placeholder="请选择付款状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 备注 -->
        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="备注" name="remark" :label-col="{ span: 3.5 }" :wrapper-col="{ span: 20 }">
              <CustomTextArea v-model:value="purchaseOrderService.formData.remark" placeholder="请输入备注" :rows="3" allow-clear />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
