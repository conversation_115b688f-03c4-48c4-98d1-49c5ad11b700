<script lang="ts" setup>
import type { PurchaseOrderSkuService } from '../service/purchaseOrderSkuService'
import { CustomIconButton as IconButton } from '@/components/base/CustomIcon'
import { CustomText, CustomTextArea as TextArea, CustomTextTitle as Title } from '@/components/base/CustomText'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import SupplierSearch from '@/components/business/AxSearch/SupplierSearch/SupplierSearch.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { Modal, Pagination, Select, Table, TabPane, Tabs } from 'ant-design-vue'
import { inject } from 'vue'
import { supplierSearchServiceFormEx } from '../service/supplierSearchServiceFormEx'
import { createSelectedSkuColumns, createSkuColumns, paymentMethodOptions } from './columns'

// 注入订单服务
const orderSkuService = inject<PurchaseOrderSkuService>(CRUD_KEY)!
// 提供供应商搜索服务
supplierSearchServiceFormEx.provide()

// 创建SKU列配置
const skuColumns = createSkuColumns(orderSkuService)
// 创建已选商品表格列配置
const selectedSkuColumns = createSelectedSkuColumns(orderSkuService)
</script>

<template>
  <Modal
    v-model:open="orderSkuService.createModalOpen"
    title="开单"
    :width="orderSkuService.isSettlementTab ? '60vw' : '80vw'"
    class="relative overflow-hidden"
    :footer="null"
    @cancel="() => {
      supplierSearchServiceFormEx.resetListQuery()
      orderSkuService.handleCloseModal()
    }"
  >
    <div class="relative w-full h-75vh flex flex-col">
      <!-- 顶部供应商选择区域 -->
      <div class="w-full h-12 border-b border-gray-200 flex-shrink-0">
        <FlexRow justify="start">
          <FormItem label="供应商名称" style="margin-right: 24px;">
            <SupplierSearch />
          </FormItem>
        </FlexRow>
      </div>

      <!-- 底部区域：左侧Tabs内容 + 右侧订单明细 -->
      <div class="relative w-full flex-1 flex gap-4 min-h-0">
        <!-- 左侧：Tabs切换区域 -->
        <div
          class="relative h-full overflow-y-hidden"
          :style="{ width: orderSkuService.isSettlementTab ? '30vw' : '50vw' }"
        >
          <Tabs v-model:active-key="orderSkuService.activeTab" class="h-full">
            <!-- Tab 1: 商品选择 -->
            <TabPane key="products" tab="商品选择">
              <!-- 商品搜索 -->
              <FlexRow class="mb-4 flex-shrink-0">
                <InputText
                  v-model="orderSkuService.productSearchParam.skuNameExt"
                  placeholder="搜索商品名称或SKU编码"
                  class="w-72 mr-3"
                />
                <IconButton
                  label="重置"
                  icon-type="ReloadOutlined"
                  @click="() => orderSkuService.resetSearch()"
                />
              </FlexRow>

              <!-- Sku列表表格 -->
              <div class="flex-1 flex flex-col min-h-0">
                <Table
                  :columns="skuColumns"
                  :data-source="orderSkuService.productList"
                  :loading="orderSkuService.productLoading"
                  :scroll="{ y: 400, x: 400 }"
                  :pagination="false"
                  size="small"
                  row-key="id"
                />

                <!-- 商品分页 -->
                <div class="mt-4 text-center flex-shrink-0">
                  <Pagination
                    v-model:current="orderSkuService.productSearchParam.pageParam.pageNum"
                    v-model:page-size="orderSkuService.productSearchParam.pageParam.pageSize"
                    size="small"
                    :total="orderSkuService.productTotal"
                    :show-size-changer="true"
                    :show-quick-jumper="true"
                    :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
                    @change="orderSkuService.onProductPageChange"
                  />
                </div>
              </div>
            </TabPane>
            <!-- Tab 2: 结算信息 -->
            <TabPane key="settlement" tab="结算信息" class="h-full overflow-y-auto">
              <div class="h-full p-4">
                <Form layout="vertical" class="space-y-4">
                  <FormItem label="联系人" required>
                    <InputText
                      v-model="orderSkuService.orderForm.contactName"
                      disabled
                      placeholder="请输入供应商"
                      class="w-full"
                    />
                  </FormItem>

                  <FormItem label="联系电话" required>
                    <InputText
                      v-model="orderSkuService.orderForm.contactPhone"
                      disabled
                      placeholder="请输入供应商"
                      class="w-full"
                    />
                  </FormItem>

                  <FormItem label="收货地址" required>
                    <TextArea
                      v-model="orderSkuService.orderForm.deliveryAddress"
                      disabled
                      placeholder="请输入供应商"
                      :rows="1"
                      :auto-size="false"
                      style="resize: none;"
                      class="w-full"
                    />
                  </FormItem>

                  <FormItem label="付款账户" required>
                    <Select
                      v-model:value="orderSkuService.orderForm.paymentMethod"
                      :options="paymentMethodOptions"
                      placeholder="请选择付款账户"
                      class="w-full"
                    />
                  </FormItem>

                  <FormItem label="已付金额">
                    <InputNumber
                      v-model:value="orderSkuService.orderForm.paidAmount"
                      :min="0"
                      :max="orderSkuService.totalAmount"
                      :precision="2"
                      placeholder="请输入已付金额"
                      class="w-full"
                      :formatter="(value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                      :parser="(value: string) => value!.replace(/¥\s?|(,*)/g, '')"
                      @change="orderSkuService.handlePaidAmountChange"
                      @focus="orderSkuService.handlePaidAmountFocus"
                    />
                    <div v-if="(orderSkuService.orderForm.paidAmount || 0) > orderSkuService.totalAmount" class="text-red-500 text-xs mt-1">
                      已付金额不能超过总金额
                    </div>
                  </FormItem>
                </Form>
              </div>
            </TabPane>
          </Tabs>
        </div>

        <!-- 右侧：订单明细区域 -->
        <div class="relative h-full" style="width: 30vw;">
          <FlexRow justify="between" class="flex-shrink-0">
            <Title :level="1" value="订单明细" />
            <IconButton
              v-if="!orderSkuService.isSettlementTab"
              label="一键清空"
              icon-type="ClearOutlined"
              size="small"
              danger
              :disabled="orderSkuService.selectedProducts.length === 0"
              @click="() => orderSkuService.selectedSkus.splice(0)"
            />
          </FlexRow>

          <!-- 已选商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <Table
              :columns="selectedSkuColumns"
              :data-source="orderSkuService.selectedProducts"
              :pagination="false"
              :scroll="{ y: 300 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 总计 -->
          <div class="border-t border-gray-200 pt-4 flex-shrink-0">
            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="总计：" />
              <CustomText strong :value="`¥${orderSkuService.totalAmount.toFixed(2)}`" class="text-xl text-red-500" />
            </FlexRow>

            <!-- 已付金额 -->
            <FlexRow justify="between" class="mb-2">
              <CustomText value="已付金额：" />
              <CustomText :value="`¥${(orderSkuService.orderForm.paidAmount || 0).toFixed(2)}`" class="text-green-600" />
            </FlexRow>

            <!-- 剩余未付 -->
            <FlexRow justify="between" class="mb-4">
              <CustomText strong value="剩余未付：" />
              <CustomText strong :value="`¥${orderSkuService.remainingAmount.toFixed(2)}`" class="text-lg text-orange-500" />
            </FlexRow>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="border-t border-gray-200 p-4 flex-shrink-0">
        <FlexRow justify="end">
          <!-- 商品选择页按钮 -->
          <template v-if="!orderSkuService.isSettlementTab">
            <IconButton
              label="下一步"
              icon-type="ArrowRightOutlined"
              type="primary"
              @click="() => orderSkuService.switchTab('settlement')"
            />
          </template>

          <!-- 结算信息页按钮 -->
          <template v-else-if="orderSkuService.isSettlementTab">
            <IconButton
              label="取消"
              icon-type="CloseOutlined"
              class="mr-2"
              @click="orderSkuService.handleCloseModal"
            />
            <IconButton
              label="上一步"
              icon-type="ArrowLeftOutlined"
              class="mr-2"
              @click="() => orderSkuService.switchTab('products')"
            />
            <IconButton
              label="提交订单"
              icon-type="CheckOutlined"
              type="primary"
              :disabled="!orderSkuService.canSubmit"
              @click="orderSkuService.handleSubmitOrder"
            />
          </template>
        </FlexRow>
      </div>
    </div>
  </Modal>
</template>
