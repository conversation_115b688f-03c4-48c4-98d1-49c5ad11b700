import type { SkuResult } from '@/api/business/sku/model/sku-types'
import type { PurchaseOrderSkuService, SelectedSkuResult } from '../service/purchaseOrderSkuService'
import { CustomIconButton as IconButton } from '@/components/base/CustomIcon'
import { CustomColorText, CustomText } from '@/components/base/CustomText'
import { ProductNameWithSpec } from '@/components/business/ProductDisplay'
import { QuantityInput } from '@/components/business/TableInput'
import { h } from 'vue'

// 工厂函数：创建带有服务实例的 SKU 列配置
export function createSkuColumns(orderSkuService: PurchaseOrderSkuService) {
  const service = orderSkuService

  return [
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
    },
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 180,
    },
    {
      title: '商品编码',
      dataIndex: 'productCode',
      key: 'productCode',
      width: 120,
    },
    {
      title: '规格摘要',
      dataIndex: 'specSummary',
      key: 'specSummary',
      width: 150,
      customRender: ({ record }: { record: SkuResult }) => {
        return h(CustomText, { value: record.specSummary })
      },
    },
    {
      title: '库存',
      dataIndex: 'stock',
      key: 'stock',
      width: 80,
      customRender: ({ record }: { record: SkuResult }) => {
        const stock = record.stock || 0
        const color = stock > 0 ? '#52c41a' : '#ff4d4f'
        return h(CustomColorText, { value: stock, color })
      },
    },
    {
      title: '重量(kg)',
      dataIndex: 'weight',
      key: 'weight',
      width: 100,
      customRender: ({ record }: { record: SkuResult }) => {
        const weightStr = record.weight ? `${record.weight}kg` : undefined
        return h(CustomText, { value: weightStr })
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      customRender: ({ record }: { record: SkuResult }) => {
      // 这里可以使用 orderSkuService 进行操作
        const isSelected = service.isProductSelected(record)
        return h(IconButton, { label: isSelected ? '移除' : '添加', iconType: isSelected ? 'DeleteOutlined' : 'PlusOutlined', type: isSelected ? 'default' : 'primary', onClick: () => service.switchSku(record) })
      },
    },
  ]
}

// 工厂函数：已选SKU列配置
// 已选商品表格列配置
export function createSelectedSkuColumns(orderSkuService: PurchaseOrderSkuService) {
  const service = orderSkuService

  return [
    {
      title: '商品名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
      customRender: ({ record }: { record: SelectedSkuResult }) => {
        return h(ProductNameWithSpec, { productName: record.productName, specSummary: record.specSummary })
      },
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      customRender: ({ record, index }: { record: SelectedSkuResult, index: number }) => {
        return h(QuantityInput, { value: record.quantity, readonly: service.isSettlementTab, onChange: (value: number | null) => service.updateProductQuantity(index, value || 1) })
      },
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      customRender: ({ record }: { record: SelectedSkuResult }) => {
        return h(CustomText, { value: `¥${record.unitPrice.toFixed(2)}` })
      },
    },
    {
      title: '小计',
      dataIndex: 'subtotal',
      key: 'subtotal',
      width: 100,
      customRender: ({ record }: { record: SelectedSkuResult }) => {
        return h(CustomText, { value: `¥${record.subtotal.toFixed(2)}` })
      },
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right' as const,
      width: 80,
      customRender: ({ record: _record, index }: { record: SelectedSkuResult, index: number }) => {
        return h(IconButton, { style: { display: service.isSettlementTab ? 'none' : 'block' }, size: 'small', iconType: 'DeleteOutlined', danger: true, onClick: () => service.removeProduct(index) })
      },
    },
  ]
}

// 付款方式选项
export const paymentMethodOptions = [
  { label: '现金', value: 'CASH' },
  { label: '银行转账', value: 'BANK_TRANSFER' },
  { label: '信用卡', value: 'CREDIT_CARD' },
  { label: '支票', value: 'CHECK' },
  { label: '在线支付', value: 'ONLINE' },
] 