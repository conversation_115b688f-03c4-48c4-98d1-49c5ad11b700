import type { SalesOrderResult } from '@/api/business/sales-order/model/sales-order-types'
import type { TableColumnType } from 'ant-design-vue'
import { DictTag } from '@/components/utils/Dict'
import { formatDateTime, formatMoney } from '@/utils/format'
import { h } from 'vue'

/**
 * 销售单表格列配置
 */
export const salesOrderColumns: TableColumnType<SalesOrderResult>[] = [
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    width: 150,
    fixed: 'left',
  },
  {
    title: '开单方式',
    dataIndex: 'orderType',
    key: 'orderType',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'order_type', valueCode: record.orderType }),
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 150,
  },
  {
    title: '客户编码',
    dataIndex: 'customerCode',
    key: 'customerCode',
    width: 120,
  },
  {
    title: '订单日期',
    dataIndex: 'orderDate',
    key: 'orderDate',
    width: 120,
  },
  {
    title: '交货日期',
    dataIndex: 'deliveryDate',
    key: 'deliveryDate',
    width: 120,
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    key: 'orderStatus',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'order_status', valueCode: record.orderStatus }),
  },
  {
    title: '付款方式',
    dataIndex: 'paymentMethod',
    key: 'paymentMethod',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'payment_method', valueCode: record.paymentMethod }),
  },
  {
    title: '付款状态',
    dataIndex: 'paymentStatus',
    key: 'paymentStatus',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'payment_status', valueCode: record.paymentStatus }),
  },
  {
    title: '最终金额',
    dataIndex: 'finalAmount',
    key: 'finalAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }) => formatMoney(record.finalAmount ?? 0),
  },
  {
    title: '已付金额',
    dataIndex: 'paidAmount',
    key: 'paidAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }) => formatMoney(record.paidAmount ?? 0),
  },
  {
    title: '销售员',
    dataIndex: 'salesPersonName',
    key: 'salesPersonName',
    width: 100,
  },
  {
    title: '联系人',
    dataIndex: 'contactName',
    key: 'contactName',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    key: 'contactPhone',
    width: 120,
  },
  {
    title: '订单来源',
    dataIndex: 'orderSource',
    key: 'orderSource',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'order_source', valueCode: record.orderSource }),
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevel',
    key: 'priorityLevel',
    width: 80,
    customRender: ({ record }) => h(DictTag, { keyCode: 'priority_level', valueCode: record.priorityLevel }),
  },
  {
    title: '发票状态',
    dataIndex: 'invoiceStatus',
    key: 'invoiceStatus',
    width: 100,
    customRender: ({ record }) => h(DictTag, { keyCode: 'invoice_status', valueCode: record.invoiceStatus }),
  },
  {
    title: '币种',
    dataIndex: 'currency',
    key: 'currency',
    width: 80,
  },
  {
    title: '汇率',
    dataIndex: 'exchangeRate',
    key: 'exchangeRate',
    width: 100,
    align: 'right',
  },
  {
    title: '预计完成日期',
    dataIndex: 'estimatedCompletionDate',
    key: 'estimatedCompletionDate',
    width: 130,
  },
  {
    title: '审批时间',
    dataIndex: 'approvalTime',
    key: 'approvalTime',
    width: 150,
    customRender: ({ record }) => formatDateTime(record.approvalTime),
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ record }) => formatDateTime(record.createTime),
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 150,
    customRender: ({ record }) => formatDateTime(record.updateTime),
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 150,
    fixed: 'right',
  },
]
