import type { CustomerResult } from '@/api/business/customer/model/customer-types'
import type { SalesOrderForm } from '@/api/business/sales-order/model/sales-order-types'
import dayjs from 'dayjs'

export function initFormData(): SalesOrderForm {
  return {
    // 基础订单信息
    orderNo: '', // 订单编号，由后端生成
    orderType: 'SALES', // 默认销售单类型
    orderDate: dayjs().format('YYYY-MM-DD'), // 当前日期
    // orderDate: new Date().toISOString().split('T')[0],
    deliveryDate: '', // 交货日期，用户填写
    orderStatus: 'DRAFT', // 默认草稿状态

    // 客户信息
    customerId: undefined,
    customerName: '',
    customerCode: '',

    // 联系信息
    contactName: '',
    contactPhone: '',
    contactMobile: '',
    shippingAddress: '',
    billingAddress: '',

    // 销售员信息
    salesPersonId: undefined,
    salesPersonName: '',

    // 金额信息
    currency: 'CNY', // 默认人民币
    exchangeRate: 1, // 默认汇率1
    subtotalAmount: 0,
    discountAmount: 0,
    taxAmount: 0,
    shippingFee: 0,
    finalAmount: 0,
    paidAmount: 0,

    // 付款信息
    paymentMethod: '', // 用户选择付款方式
    paymentTerms: 'PREPAID', // 默认预付款
    paymentStatus: 'UNPAID', // 默认未付款

    // 订单属性
    orderSource: 'OFFLINE', // 默认线下订单
    priorityLevel: 'NORMAL', // 默认普通优先级
    estimatedCompletionDate: '',

    // 发票和审批信息
    invoiceStatus: 'PENDING', // 默认待开票
    approvalUserId: undefined,
    approvalTime: '',
    approvalRemark: '',

    // 备注
    remark: '',

    // 付款列表
    paymentList: [],

    // 订单明细
    orderItemList: [],
    itemList: [],
  }
}

export function getAddressInfo(customer: CustomerResult): string {
  if (!customer.addressInfoList || customer.addressInfoList === '[]') {
    return ''
  }

  try {
    const addressInfo: Record<string, string> = JSON.parse(customer.addressInfoList)

    return addressInfo.address || ''
  }
  catch {
    return ''
  }
}

export function validateRequiredFields<T extends Record<string, unknown>>(form: T, requiredFields: Array<keyof T>): boolean {
  return requiredFields.every((field) => {
    const value = form[field]
    return value !== null && value !== undefined && value !== '' && value !== 0
  })
}
