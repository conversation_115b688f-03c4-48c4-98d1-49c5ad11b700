import type { Rule } from 'ant-design-vue/es/form'

/**
 * 销售单表单验证规则
 */
export const rules: Record<string, Rule[]> = {
  orderNo: [
    { required: true, message: '请输入订单编号', trigger: 'blur' },
    { max: 50, message: '订单编号长度不能超过50个字符', trigger: 'blur' },
  ],
  orderType: [
    { required: true, message: '请选择开单方式', trigger: 'change' },
  ],
  customerName: [
    { required: true, message: '请输入客户名称', trigger: 'blur' },
    { max: 100, message: '客户名称长度不能超过100个字符', trigger: 'blur' },
  ],
  customerCode: [
    { max: 50, message: '客户编码长度不能超过50个字符', trigger: 'blur' },
  ],
  orderDate: [
    { required: true, message: '请选择订单日期', trigger: 'change' },
  ],
  deliveryDate: [
    { required: true, message: '请选择交货日期', trigger: 'change' },
  ],
  orderStatus: [
    { required: true, message: '请选择订单状态', trigger: 'change' },
  ],
  paymentMethod: [
    { required: true, message: '请选择付款方式', trigger: 'change' },
  ],
  paymentTerms: [
    { required: true, message: '请选择付款条件', trigger: 'change' },
  ],
  currency: [
    { max: 10, message: '币种长度不能超过10个字符', trigger: 'blur' },
  ],
  contactName: [
    { max: 50, message: '联系人长度不能超过50个字符', trigger: 'blur' },
  ],
  contactPhone: [
    { max: 20, message: '联系电话长度不能超过20个字符', trigger: 'blur' },
    { pattern: /^[\d-+()（）\s]*$/, message: '联系电话格式不正确', trigger: 'blur' },
  ],
  contactMobile: [
    { max: 20, message: '联系手机长度不能超过20个字符', trigger: 'blur' },
    { pattern: /^[\d-+()（）\s]*$/, message: '联系手机格式不正确', trigger: 'blur' },
  ],
  salesPersonName: [
    { max: 50, message: '销售员姓名长度不能超过50个字符', trigger: 'blur' },
  ],
  subtotalAmount: [
    { type: 'number', min: 0, message: '商品小计不能小于0', trigger: 'blur' },
  ],
  discountAmount: [
    { type: 'number', min: 0, message: '折扣金额不能小于0', trigger: 'blur' },
  ],
  taxAmount: [
    { type: 'number', min: 0, message: '税额不能小于0', trigger: 'blur' },
  ],
  shippingFee: [
    { type: 'number', min: 0, message: '运费不能小于0', trigger: 'blur' },
  ],
  finalAmount: [
    { type: 'number', min: 0, message: '最终金额不能小于0', trigger: 'blur' },
  ],
  exchangeRate: [
    { type: 'number', min: 0, message: '汇率不能小于0', trigger: 'blur' },
  ],
  paidAmount: [
    { type: 'number', min: 0, message: '已付金额不能小于0', trigger: 'blur' },
  ],
  shippingAddress: [
    { max: 500, message: '收货地址长度不能超过500个字符', trigger: 'blur' },
  ],
  billingAddress: [
    { max: 500, message: '发票地址长度不能超过500个字符', trigger: 'blur' },
  ],
  remark: [
    { max: 1000, message: '备注长度不能超过1000个字符', trigger: 'blur' },
  ],
  approvalRemark: [
    { max: 500, message: '审批备注长度不能超过500个字符', trigger: 'blur' },
  ],
}
