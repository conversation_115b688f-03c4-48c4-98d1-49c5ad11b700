<script lang="ts" setup>
import type { SalesOrderSkuService } from '../service/salesOrderSkuService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime, formatMoney } from '@/utils/format'
import { inject } from 'vue'

// 获取服务实例
const service = inject<SalesOrderSkuService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="service.detailOpen" :title="service.detailTitle" :footer="null" width="1200px" @cancel="service.closeDetail()">
    <Spin :spinning="service.detailLoading">
      <Descriptions :column="3" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="订单编号">
          <Text :value="service.detailData.orderNo" />
        </DescriptionsItem>
        <DescriptionsItem label="开单方式">
          <DictTag key-code="order_type" :value-code="service.detailData.orderType" />
        </DescriptionsItem>
        <DescriptionsItem label="订单状态">
          <DictTag key-code="order_status" :value-code="service.detailData.orderStatus" />
        </DescriptionsItem>

        <!-- 客户信息 -->
        <DescriptionsItem label="客户名称">
          <Text :value="service.detailData.customerName" />
        </DescriptionsItem>
        <DescriptionsItem label="客户编码">
          <Text :value="service.detailData.customerCode" />
        </DescriptionsItem>
        <DescriptionsItem label="联系人">
          <Text :value="service.detailData.contactName" />
        </DescriptionsItem>

        <!-- 联系方式 -->
        <DescriptionsItem label="联系电话">
          <Text :value="service.detailData.contactPhone" />
        </DescriptionsItem>
        <DescriptionsItem label="联系手机">
          <Text :value="service.detailData.contactMobile" />
        </DescriptionsItem>
        <DescriptionsItem label="销售员">
          <Text :value="service.detailData.salesPersonName" />
        </DescriptionsItem>

        <!-- 日期信息 -->
        <DescriptionsItem label="订单日期">
          <Text :value="service.detailData.orderDate" />
        </DescriptionsItem>
        <DescriptionsItem label="交货日期">
          <Text :value="service.detailData.deliveryDate" />
        </DescriptionsItem>
        <DescriptionsItem label="预计完成日期">
          <Text :value="service.detailData.estimatedCompletionDate" />
        </DescriptionsItem>

        <!-- 付款信息 -->
        <DescriptionsItem label="付款方式">
          <DictTag key-code="payment_method" :value-code="service.detailData.paymentMethod" />
        </DescriptionsItem>
        <DescriptionsItem label="付款条件">
          <DictTag key-code="payment_terms" :value-code="service.detailData.paymentTerms" />
        </DescriptionsItem>
        <DescriptionsItem label="币种">
          <Text :value="service.detailData.currency" />
        </DescriptionsItem>

        <!-- 金额信息 -->
        <DescriptionsItem label="商品小计">
          <Text :value="formatMoney(service.detailData.subtotalAmount ?? 0)" />
        </DescriptionsItem>
        <DescriptionsItem label="折扣金额">
          <Text :value="formatMoney(service.detailData.discountAmount ?? 0)" />
        </DescriptionsItem>
        <DescriptionsItem label="税额">
          <Text :value="formatMoney(service.detailData.taxAmount ?? 0)" />
        </DescriptionsItem>

        <DescriptionsItem label="运费">
          <Text :value="formatMoney(service.detailData.shippingFee ?? 0)" />
        </DescriptionsItem>
        <DescriptionsItem label="最终金额">
          <Text :value="formatMoney(service.detailData.finalAmount ?? 0)" />
        </DescriptionsItem>
        <DescriptionsItem label="汇率">
          <Text :value="service.detailData.exchangeRate" />
        </DescriptionsItem>

        <!-- 付款状态 -->
        <DescriptionsItem label="付款状态">
          <DictTag key-code="payment_status" :value-code="service.detailData.paymentStatus" />
        </DescriptionsItem>
        <DescriptionsItem label="已付金额">
          <Text :value="formatMoney(service.detailData.paidAmount ?? 0)" />
        </DescriptionsItem>
        <DescriptionsItem label="发票状态">
          <DictTag key-code="invoice_status" :value-code="service.detailData.invoiceStatus" />
        </DescriptionsItem>

        <!-- 其他信息 -->
        <DescriptionsItem label="订单来源">
          <DictTag key-code="order_source" :value-code="service.detailData.orderSource" />
        </DescriptionsItem>
        <DescriptionsItem label="优先级">
          <DictTag key-code="priority_level" :value-code="service.detailData.priorityLevel" />
        </DescriptionsItem>
        <DescriptionsItem label="审批时间">
          <Text :value="formatDateTime(service.detailData.approvalTime)" />
        </DescriptionsItem>

        <!-- 地址信息 -->
        <DescriptionsItem label="收货地址" :span="3">
          <Text :value="service.detailData.shippingAddress" />
        </DescriptionsItem>
        <DescriptionsItem label="发票地址" :span="3">
          <Text :value="service.detailData.billingAddress" />
        </DescriptionsItem>

        <!-- 备注信息 -->
        <DescriptionsItem label="备注" :span="3">
          <Text :value="service.detailData.remark" />
        </DescriptionsItem>
        <DescriptionsItem label="审批备注" :span="3">
          <Text :value="service.detailData.approvalRemark" />
        </DescriptionsItem>

        <!-- 系统信息 -->
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(service.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(service.detailData.updateTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="创建人">
          <Text :value="service.detailData.createdBy ?? ''" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
