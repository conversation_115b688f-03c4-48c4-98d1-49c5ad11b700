<script lang="ts" setup>
import type { SalesOrderSkuService } from '../service/salesOrderSkuService'
import { CustomIconButton as IconButton } from '@/components/base/CustomIcon'
import { CustomText, CustomTextArea as TextArea, CustomTextTitle as Title } from '@/components/base/CustomText'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatMoney } from '@/utils/format'
import { Modal, Select, Table } from 'ant-design-vue'
import { inject } from 'vue'
import { warehouseSearchServiceEx } from '../service/warehouseServiceEx'
import { createItemListColumns, paymentMethodOptions } from './columns'

// 注入订单服务
const orderSkuService = inject<SalesOrderSkuService>(CRUD_KEY)!
// 提供仓库搜索服务
warehouseSearchServiceEx.provide()
warehouseSearchServiceEx.parentService = orderSkuService

// 创建已选商品表格列配置
const selectedSkuColumns = createItemListColumns(orderSkuService)
</script>

<template>
  <Modal
    :open="orderSkuService.detailOpen"
    :title="orderSkuService.detailTitle"
    width="1200px"
    class="relative overflow-hidden"
    :footer="null"
    @cancel="() => orderSkuService.closeDetail()"
  >
    <div class="relative w-full h-75vh flex flex-col">
      <!-- 顶部客户选择区域 -->
      <div class="w-full h-12 border-b border-gray-200 flex-shrink-0">
        <FlexRow justify="start">
          <FormItem label="客户名称" style="margin-right: 24px;">
            <CustomText :value="orderSkuService.detailData.customerName" />
          </FormItem>
        </FlexRow>
      </div>

      <div class="relative w-full flex gap-4 min-h-0">
        <!-- 左侧：订单信息区域 -->
        <div
          class="relative w-1/2 h-full overflow-y-hidden "
        >
          <div class="h-full">
            <Form layout="vertical" class="space-y-4">
              <Title :level="1" value="订单信息" />
              <FormItem label="联系人" required>
                <InputText
                  v-model="orderSkuService.detailData.contactName"
                  disabled
                  placeholder="请输入客户"
                  class="w-full"
                />
              </FormItem>

              <FormItem label="联系电话" required>
                <InputText
                  v-model="orderSkuService.detailData.contactPhone"
                  disabled
                  placeholder="请输入客户"
                  class="w-full"
                />
              </FormItem>

              <FormItem label="收货地址" required>
                <TextArea
                  v-model="orderSkuService.detailData.shippingAddress"
                  disabled
                  placeholder="请输入客户"
                  :rows="1"
                  :auto-size="false"
                  style="resize: none;"
                  class="w-full"
                />
              </FormItem>

              <Title :level="1" value="新增收款信息" />
              <FormItem label="收款账户" required>
                <Select
                  v-model:value="orderSkuService.detailData.paymentMethod"
                  :options="paymentMethodOptions"
                  placeholder="请选择收款账户"
                  class="w-full"
                />
              </FormItem>

              <FormItem label="已付金额">
                <InputNumber
                  v-model:value="orderSkuService.detailData.paidAmount"
                  :min="0"
                  :max="orderSkuService.totalAmount"
                  :precision="2"
                  placeholder="请输入已付金额"
                  class="w-full"
                  :formatter="(value: string) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="(value: string) => value!.replace(/¥\s?|(,*)/g, '')"
                  @change="orderSkuService.handlePaidAmountChange"
                  @focus="orderSkuService.handlePaidAmountFocus"
                />
                <div v-if="(orderSkuService.detailData.paidAmount || 0) > orderSkuService.totalAmount" class="text-red-500 text-xs mt-1">
                  已付金额不能超过总金额
                </div>
              </FormItem>
            </Form>
          </div>
        </div>

        <!-- 右侧：订单明细区域 -->
        <div class="relative h-full w-1/2">
          <FlexRow justify="between" class="flex-shrink-0">
            <Title :level="1" value="订单明细" />
          </FlexRow>

          <!-- 已选商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <Table
              :columns="selectedSkuColumns"
              :data-source="orderSkuService.detailData.itemList || []"
              :pagination="false"
              :scroll="{ y: 300 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 总计 -->
          <div class="border-t border-gray-200 pt-4 flex-shrink-0">
            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="商品小计：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.subtotalAmount)" class="text-xl text-red-500" />
            </FlexRow>

            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="税额：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.taxAmount)" class="text-xl text-red-500" />
            </FlexRow>

            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="运费：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.shippingFee)" class="text-xl text-red-500" />
            </FlexRow>

            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="订单级折扣：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.discountAmount)" class="text-xl text-red-500" />
            </FlexRow>

            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="最终金额：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.finalAmount)" class="text-xl text-red-500" />
            </FlexRow>

            <!-- 已付金额 -->
            <FlexRow justify="between" class="mb-2">
              <CustomText value="已付金额：" />
              <CustomText :value="formatMoney(orderSkuService.detailData.paidAmount)" class="text-green-600" />
            </FlexRow>

            <!-- 剩余未付 -->
            <FlexRow justify="between" class="mb-4">
              <CustomText strong value="剩余未付：" />
              <CustomText strong :value="formatMoney(orderSkuService.detailData.finalAmount)" class="text-lg text-orange-500" />
            </FlexRow>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="w-full border-t border-gray-200 p-4 flex-shrink-0">
        <FlexRow justify="end">
          <IconButton
            label="取消"
            icon-type="CloseOutlined"
            class="mr-2"
            @click="() => orderSkuService.closeDetail()"
          />
          <IconButton
            label="更新订单"
            icon-type="CheckOutlined"
            type="primary"
            :disabled="!orderSkuService.canSubmit"
            @click="orderSkuService.handleSubmitOrder"
          />
        </flexrow>
      </div>
    </div>
  </modal>
</template>
