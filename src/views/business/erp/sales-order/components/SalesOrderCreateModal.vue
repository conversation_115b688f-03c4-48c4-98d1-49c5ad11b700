<script lang="ts" setup>
import type { SalesOrderSkuService } from '../service/salesOrderSkuService'
import { CustomIconButton as IconButton } from '@/components/base/CustomIcon'
import { CustomText, CustomTextTitle as Title } from '@/components/base/CustomText'
import { FlexRow } from '@/components/base/Flex'
import { FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import CustomerSearch from '@/components/business/AxSearch/CustomerSearch/CustomerSearch.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatMoney } from '@/utils/format'
import { Pagination, Table } from 'ant-design-vue'
import { inject } from 'vue'
import { customerSearchServiceFormEx } from '../service/customerSearchServiceFormEx'
import { createSelectedSkuColumns, createSkuColumns } from './columns'

// 注入订单服务
const orderSkuService = inject<SalesOrderSkuService>(CRUD_KEY)!
// 提供客户搜索服务
customerSearchServiceFormEx.provide()

// 创建SKU列配置
const skuColumns = createSkuColumns(orderSkuService)
// 创建已选商品表格列配置
const selectedSkuColumns = createSelectedSkuColumns(orderSkuService)
</script>

<template>
  <Modal
    v-model:open="orderSkuService.createModalOpen"
    title="开单"
    class="relative max-w-[1600px] mx-auto"
    :footer="null"
    width="1600px"
    @cancel="() => {
      customerSearchServiceFormEx.resetListQuery()
      orderSkuService.handleCloseModal()
    }"
  >
    <div class="relative h-75vh flex flex-col gap-4">
      <!-- 顶部客户选择区域 -->
      <div class="h-20 border-b border-gray-200 flex-shrink-0">
        <Form layout="vertical">
          <FormItem label="客户名称" class="w-[20vw]">
            <CustomerSearch />
          </FormItem>
        </Form>
      </div>

      <!-- 底部区域：左侧Tabs内容 + 右侧订单明细 -->
      <div class="relative flex gap-4 min-h-0">
        <!-- 左侧：Tabs切换区域 -->
        <div
          class="relative h-full overflow-y-hidden w-[50vw] min-w-[500px]"
        >
          <!-- Tab 1: 商品选择 -->
          <!-- 商品搜索 -->
          <FlexRow class="mb-4 flex-shrink-0">
            <div class="w-[20vw]">
              <InputText
                v-model="orderSkuService.productSearchParam.skuNameExt"
                placeholder="搜索商品名称或SKU编码"
              />
            </div>
            <IconButton
              label="重置"
              icon-type="ReloadOutlined"
              @click="() => orderSkuService.resetSearch()"
            />
          </FlexRow>

          <!-- Sku列表表格 -->
          <div class="flex-1 flex flex-col min-h-0">
            <Table
              :columns="skuColumns"
              :data-source="orderSkuService.productList"
              :loading="orderSkuService.productLoading"
              :scroll="{ y: 400, x: 400 }"
              :pagination="false"
              size="small"
              row-key="id"
            />

            <!-- 商品分页 -->
            <div class="mt-4 text-center flex-shrink-0">
              <Pagination
                v-model:current="orderSkuService.productSearchParam.pageParam.pageNum"
                v-model:page-size="orderSkuService.productSearchParam.pageParam.pageSize"
                size="small"
                :total="orderSkuService.productTotal"
                :show-size-changer="true"
                :show-quick-jumper="true"
                :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
                @change="orderSkuService.onProductPageChange"
              />
            </div>
          </div>
        </div>

        <!-- 右侧：订单明细区域 -->
        <div class="relative h-full" style="flex: 1; min-width: 300px;">
          <FlexRow justify="between" align="start" class="flex-shrink-0 h-12">
            <Title :level="1" value="订单明细" />
            <IconButton
              label="一键清空"
              icon-type="ClearOutlined"
              size="small"
              :disabled="orderSkuService.selectedProducts.length === 0"
              @click="orderSkuService.clearSelectedSkus"
            />
          </FlexRow>

          <!-- 已选商品列表 -->
          <div class="flex-1 mb-4 min-h-0">
            <Table
              :columns="selectedSkuColumns"
              :data-source="orderSkuService.selectedProducts"
              class="h-[300px] w-full"
              :pagination="false"
              :scroll="{ y: 280 }"
              size="small"
              row-key="id"
            />
          </div>

          <!-- 总计 -->
          <div class="border-t border-gray-200 pt-4 flex-shrink-0">
            <FlexRow justify="between" class="mb-2">
              <CustomText strong value="总计：" />
              <CustomText strong :value="formatMoney(orderSkuService.totalAmount)" class="text-xl text-red-500" />
            </FlexRow>

            <!-- 已付金额 -->
            <FlexRow justify="between" class="mb-2">
              <CustomText value="已付金额：" />
              <CustomText :value="formatMoney(orderSkuService.formData.paidAmount)" class="text-green-600" />
            </FlexRow>

            <!-- 剩余未付 -->
            <FlexRow justify="between" class="mb-4">
              <CustomText strong value="剩余未付：" />
              <CustomText strong :value="formatMoney(orderSkuService.remainingAmount)" class="text-lg text-orange-500" />
            </FlexRow>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="border-t border-gray-200 pt-6 flex-shrink-0">
        <FlexRow justify="end">
          <IconButton
            label="取消"
            icon-type="CloseOutlined"
            class="mr-2"
            @click="orderSkuService.handleCloseModal"
          />
          <IconButton
            label="提交订单"
            icon-type="CheckOutlined"
            type="primary"
            :disabled="!orderSkuService.canSubmit"
            @click="orderSkuService.handleSubmitOrder"
          />
        </FlexRow>
      </div>
    </div>
  </Modal>
</template>
