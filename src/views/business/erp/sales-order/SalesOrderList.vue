<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import CustomerSearch from '@/components/business/AxSearch/CustomerSearch/CustomerSearch.vue'
import { DictRadio, DictSelect } from '@/components/utils/Dict'
import SalesOrderCreateModal from './components/SalesOrderCreateModal.vue'
import SalesOrderDetailModal from './components/SalesOrderDetailModal.vue'
import { customerSearchServiceEx } from './service/customerSearchServiceEx'
import { salesOrderSkuService } from './service/salesOrderSkuService'

// 提供 service
salesOrderSkuService.provide()
customerSearchServiceEx.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'salesOrder:query'" layout="vertical">
      <FormGrid>
        <FormItem label="订单编号">
          <InputText v-model:value="salesOrderSkuService.queryParam.orderNo" placeholder="请输入订单编号" />
        </FormItem>
        <FormItem label="客户名称">
          <CustomerSearch />
        </FormItem>
        <FormItem label="付款方式">
          <DictSelect v-model:value="salesOrderSkuService.queryParam.paymentMethod" key-code="payment_method" placeholder="请选择付款方式" />
        </FormItem>
        <FormItem label="订单日期">
          <DateRangePicker v-model:from="salesOrderSkuService.queryParam.orderDateFrom" v-model:to="salesOrderSkuService.queryParam.orderDateTo" />
        </FormItem>
        <FormItem label="交货日期">
          <DateRangePicker v-model:from="salesOrderSkuService.queryParam.deliveryDateFrom" v-model:to="salesOrderSkuService.queryParam.deliveryDateTo" />
        </FormItem>
        <FormItem label="订单状态" :col="3">
          <DictRadio v-model:value="salesOrderSkuService.queryParam.orderStatus" key-code="order_status" @change="salesOrderSkuService.onSearch" />
        </FormItem>
        <FormItem />
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card>
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <IconButton
          v-privilege="'order:add'"
          label="开单"
          icon-type="PlusOutlined"
          type="primary"
          @click="salesOrderSkuService.openCreateModal"
        />
        <AxTableOperateButtons batch-delete-config="salesOrder:delete" import-config="salesOrder:import" export-config="salesOrder:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="salesOrderSkuService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="salesOrderSkuService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'salesOrder:view'" label="详情" icon-type="EyeOutlined"
              @click="salesOrderSkuService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'salesOrder:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="salesOrderSkuService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 开单弹窗 -->
  <SalesOrderCreateModal />
  <!-- 销售单详情 -->
  <SalesOrderDetailModal />
</template>
