import type { SalesOrderItem } from '@/api/business/sales-order-item/model/sales-order-item-types'
import type {
  SalesOrderForm,
  SalesOrderPageParam,
  SalesOrderResult,
} from '@/api/business/sales-order/model/sales-order-types'
import type { SkuResult } from '@/api/business/sku/model/sku-types'
import { salesOrderApi } from '@/api/business/sales-order/sales-order-api'
import { skuApi } from '@/api/business/sku/sku-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { debounce } from 'lodash-es'
import { salesOrderColumns } from '../config/columns'
import { initFormData, validateRequiredFields } from '../config/utils'

// 已选sku类型
export interface SelectedSkuResult extends SkuResult {
  quantity: number
  unitPrice: number
  subtotal: number
}

/**
 * 销售订单SKU服务
 * 提供销售订单相关的业务逻辑和数据管理
 */
export class SalesOrderSkuService extends BaseCrudService<SalesOrderResult, SalesOrderForm, SalesOrderPageParam> {
  private readonly REQUIRED_FILEDS: Array<keyof SalesOrderForm> = [
    'contactName',
    'customerId',
    'orderStatus',
  ]

  // ==================== 开单弹窗状态管理 ====================

  // 开单弹窗显示状态
  private _createModalOpen = ref(false)

  // sku搜索参数
  private _skuSearchParam = reactive({
    skuNameExt: '', // 使用扩展搜索字段
    status: '1', // 只获取上架sku
    pageParam: {
      pageNum: 1,
      pageSize: 10,
    },
  })

  // sku列表总数
  private _skuTotal = ref(0)

  // sku列表加载状态
  private _skuLoading = ref(false)

  // sku列表数据
  private _skuList = ref<SkuResult[]>([])

  // 已选sku列表数据
  private _selectedSkus = ref<SelectedSkuResult[]>([])

  // 已选sku列表数据（格式化后）
  private _selectedSkusForSubmit = ref<SalesOrderItem[]>([])

  private _totalAmount = computed(() => {
    return this._selectedSkus.value.reduce((sum, item) => sum + item.subtotal, 0)
  })

  // 计算剩余未付金额
  private _remainingAmount = computed(() => {
    const totalAmount = this._totalAmount.value
    const paidAmount = this.formData.paidAmount || 0
    return Math.max(0, totalAmount - paidAmount)
  })

  // 计算是否可以提交订单
  private _canSubmit = computed(() => {
    // 1. 必须有选中的SKU
    const hasSelectedProducts = this._selectedSkus.value.length > 0

    // 2. 必填字段都已填写
    const allRequiredFilled = validateRequiredFields(this.formData, this.REQUIRED_FILEDS)

    console.log(`hasSelectedProducts: ${hasSelectedProducts}, allRequiredFilled: ${allRequiredFilled}`)

    return hasSelectedProducts && allRequiredFilled
  })

  // ==================== 防抖搜索相关 ====================

  // 防抖搜索函数
  private debouncedSearch = debounce(() => {
    this._executeSearch()
  }, 300)

  // ==================== Getters ====================

  get createModalOpen() {
    return this._createModalOpen.value
  }

  set createModalOpen(value: boolean) {
    this._createModalOpen.value = value
  }

  get productList() {
    return this._skuList.value
  }

  get productSearchParam() {
    return this._skuSearchParam
  }

  get productTotal() {
    return this._skuTotal.value
  }

  get productLoading() {
    return this._skuLoading.value
  }

  get selectedSkus() {
    return this._selectedSkus.value
  }

  // 为了兼容性，添加 selectedProducts 别名
  get selectedProducts() {
    return this._selectedSkus.value
  }

  // 计算总金额
  get totalAmount() {
    return this._totalAmount.value
  }

  // 剩余未付金额
  get remainingAmount() {
    return this._remainingAmount.value
  }

  // 是否可以提交订单
  get canSubmit() {
    return this._canSubmit.value
  }

  constructor() {
    // 初始化服务
    super(
      '销售订单SKU', // 业务名称
      salesOrderColumns as Record<string, unknown>[],
      {
        // 使用sales-order的API
        add: salesOrderApi.addSalesOrder,
        queryPage: salesOrderApi.salesOrderPage,
        getDetail: salesOrderApi.salesOrderDetail,
        update: salesOrderApi.updateSalesOrder,
        import: salesOrderApi.importSalesOrder,
        export: salesOrderApi.exportSalesOrder,
        delete: salesOrderApi.deleteSalesOrder,
        batchDelete: salesOrderApi.batchDeleteSalesOrder,
      },
      // 初始查询参数
      {
        pageParam: {
          pageNum: 1,
          pageSize: 10,
        },
      } as Partial<SalesOrderPageParam>,
    )

    // 初始化搜索监听器 - 简化版本
    watch(
      () => this._skuSearchParam.skuNameExt,
      (newValue, oldValue) => {
        if (newValue !== oldValue) {
          this._skuSearchParam.pageParam.pageNum = 1

          if (newValue && newValue.trim()) {
            this.debouncedSearch()
          }
          else {
            this.debouncedSearch.cancel()
            this._executeSearch()
          }
        }
      },
      { immediate: false },
    )
  }

  // ==================== 防抖搜索方法 ====================

  /**
   * 执行实际的搜索操作
   */
  private _executeSearch = async () => {
    try {
      this._skuLoading.value = true
      const response = await skuApi.skuPage({
        skuNameExt: this._skuSearchParam.skuNameExt,
        status: this._skuSearchParam.status,
        pageParam: this._skuSearchParam.pageParam,
      })

      if (response.data) {
        this._skuList.value = response.data.list || []
        this._skuTotal.value = response.data.total || 0
      }
    }
    catch (error) {
      console.error('搜索sku失败:', error)
    }
    finally {
      this._skuLoading.value = false
    }
  }

  // ==================== 开单弹窗相关方法 ====================

  /**
   * 打开开单弹窗
   */
  openCreateModal = () => {
    this._createModalOpen.value = true
    this.resetCreateModalData()
    this.searchProducts()
  }

  /**
   * 关闭开单弹窗
   */
  closeCreateModal = () => {
    this._createModalOpen.value = false
    this.resetCreateModalData()
  }

  /**
   * 处理关闭弹窗的完整逻辑
   */
  handleCloseModal = () => {
    // 1. 重置订单服务的表单数据（包括客户信息）
    this.resetFormData()
    Object.assign(this.formData, {
      customerId: undefined,
      customerName: '',
      contactName: '',
      contactPhone: '',
      contactMobile: '',
      shippingAddress: '',
      billingAddress: '',
    })

    // 2. 调用订单服务的关闭方法（清空商品选择和订单表单等）
    this.closeCreateModal()

    // 3. 强制触发响应式更新，确保UI完全同步
    nextTick(() => {
      // 确保所有计算属性重新计算，验证重置是否成功
      console.log('✅ 表单数据重置完成:', {
        已付金额: this.formData.paidAmount,
        收款账户: this.formData.paymentMethod,
        订单总计: this.totalAmount,
        选中商品数量: this.selectedProducts.length,
      })
    })
  }

  /**
   * 处理提交订单的完整逻辑
   */
  handleSubmitOrder = async () => {
    if (!this.canSubmit) {
      console.warn('订单数据不完整，无法提交')
      return
    }

    try {
      // TODO: 收集表单数据并提交
      // 暂时模拟提交逻辑
      console.log('提交订单数据:', {
        ...this.formData,
        subtotalAmount: this.totalAmount,
        finalAmount: this.totalAmount,
        orderItems: this.selectedProducts,
      })

      // 调用服务提交订单
      const res = await this.submitOrder()

      if (res.success && res.data?.data?.id) {
        this.openDetailView(res.data.data.id as number)
      }

      console.log('✅ 订单提交成功', res)
    }
    catch (error) {
      console.error('❌ 订单提交失败:', error)
      throw error
    }
  }

  /**
   * 重置开单弹窗数据
   */
  private resetCreateModalData = () => {
    this.debouncedSearch.cancel() // 清理防抖任务

    this.resetFormData()

    // 重置商品相关数据
    this._selectedSkus.value = []
    this._skuSearchParam.skuNameExt = ''
    this._skuSearchParam.pageParam.pageNum = 1
    this._skuList.value = []
    this._skuTotal.value = 0
  }

  /**
   * 搜索sku（保持向后兼容）
   * @deprecated 建议直接修改 productSearchParam.skuNameExt 触发自动搜索
   */
  searchProducts = async () => {
    this.debouncedSearch.cancel()
    await this._executeSearch()
  }

  /**
   * 重置搜索
   */
  resetSearch = () => {
    this.debouncedSearch.cancel()
    this._skuSearchParam.skuNameExt = ''
    this._skuSearchParam.pageParam.pageNum = 1
    this._executeSearch()
  }

  /**
   * 强制立即搜索（跳过防抖）
   */
  forceSearch = () => {
    this.debouncedSearch.cancel()
    this._skuSearchParam.pageParam.pageNum = 1
    this._executeSearch()
  }

  /**
   * 添加sku到已选列表
   */
  addProduct = (product: SkuResult) => {
    const selectedProduct: SelectedSkuResult = {
      ...product,
      quantity: 1,
      unitPrice: product.price ?? 0, // 使用SKU的价格作为默认单价
      subtotal: product.price ?? 0,
    }
    this._selectedSkus.value.push(selectedProduct)
  }

  /**
   * 添加sku到已选列表
   */
  addProductForSubmit = (sku: SkuResult) => {
    const selectedProductForSubmit: SalesOrderItem = {
      skuId: sku.id,
      skuCode: sku.skuCode,
      productId: sku.productId,
      productName: sku.productName,
      productCode: sku.productCode,
      orderNo: this.formData.orderNo || '',
      quantity: 1,
      lineNo: 1,
      unitPrice: sku.price ?? 0, // 使用SKU的价格作为默认单价
      lineAmount: sku.price ?? 0,
    }
    this._selectedSkusForSubmit.value.push(selectedProductForSubmit)
  }

  /**
   * 移除已选sku
   */
  removeProduct = (index: number) => {
    this._selectedSkus.value.splice(index, 1)
    this._selectedSkusForSubmit.value.splice(index, 1)
  }

  /**
   * 移除已选sku
   */
  removeProductForSubmit = (index: number) => {
    this._selectedSkusForSubmit.value.splice(index, 1)
  }

  /**
   * 检查sku是否已被选中
   */
  isProductSelected = (product: SkuResult): boolean => {
    return this._selectedSkus.value.some(p => p.id === product.id)
  }

  /**
   * 检查sku是否可选
   */
  isProductSelectable = (product: SkuResult): boolean => {
    return !!product.stock && product.stock > 0
  }

  /**
   * 切换sku选中状态（添加或移除）
   */
  switchSku = (product: SkuResult) => {
    const existingIndex = this._selectedSkus.value.findIndex(p => p.id === product.id)

    if (existingIndex >= 0) {
      // 如果sku已存在，移除它
      this.removeProduct(existingIndex)
      this.removeProductForSubmit(existingIndex)
    }
    else {
      // 如果sku不存在，添加它
      this.addProduct(product)
      this.addProductForSubmit(product)
    }
  }

  /**
   * 更新sku数量
   */
  updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1)
      return

    const product = this._selectedSkus.value[index]
    product.quantity = quantity
    product.subtotal = product.quantity * product.unitPrice

    const productForSubmit = this._selectedSkusForSubmit.value[index]
    productForSubmit.unitPrice = product.unitPrice
    productForSubmit.quantity = quantity
    productForSubmit.lineAmount = productForSubmit.quantity * productForSubmit.unitPrice!
  }

  clearSelectedSkus = () => {
    this._selectedSkus.value = []
    this._selectedSkusForSubmit.value = []
  }

  /**
   * sku搜索分页变化
   */
  onProductPageChange = (page: number, pageSize?: number) => {
    this._skuSearchParam.pageParam.pageNum = page
    if (pageSize) {
      this._skuSearchParam.pageParam.pageSize = pageSize
    }
    this.searchProducts()
  }

  // ==================== 结算相关业务逻辑 ====================

  /**
   * 已付金额变化处理
   */
  handlePaidAmountChange = (value: number | null) => {
    const newPaidAmount = value || 0
    const totalAmount = this._totalAmount.value

    if (newPaidAmount > totalAmount) {
      // 如果已付金额超过总金额，自动设置为总金额
      this.formData.paidAmount = totalAmount
    }
    else {
      this.formData.paidAmount = newPaidAmount
    }
  }

  /**
   * 已付金额输入框获得焦点时处理
   */
  handlePaidAmountFocus = () => {
    // 调用自动填充逻辑
    this.autoFillPaidAmount()
  }

  /**
   * 检查是否应该自动填充已付金额
   */
  get shouldAutoFillPaidAmount(): boolean {
    return !this.formData.paidAmount && this._totalAmount.value > 0
  }

  /**
   * 自动填充已付金额
   * 当已付金额为空且订单总金额大于0时，自动将总金额填入已付金额
   */
  autoFillPaidAmount = (): void => {
    if (this.shouldAutoFillPaidAmount) {
      this.formData.paidAmount = this._totalAmount.value
    }
  }

  /**
   * 提交订单
   */
  submitOrder = async () => {
    if (!this._canSubmit.value) {
      throw new Error('订单数据不完整，无法提交')
    }

    if (this._selectedSkus.value.length === 0) {
      throw new Error('请选择sku')
    }

    // 构建销售订单数据并设置到表单数据中
    Object.assign(this.formData, {
      // 订单表单数据 (先设置，避免被后面的字段覆盖)
      ...this.formData,

      // sku明细
      itemList: this._selectedSkusForSubmit.value,

      // 付款列表
      paymentList: [],
    })

    // 提交订单
    return await this.submitForm(() => {
      this.closeCreateModal()
      this.queryPage()
    })
  }

  // ==================== (重写)获取默认表单数据 ====================

  getDefaultFormData = (): SalesOrderForm => {
    return initFormData()
  }
}

// 单例模式
export const salesOrderSkuService = new SalesOrderSkuService()
