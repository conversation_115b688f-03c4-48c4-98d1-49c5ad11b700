import type { SalesOrderSkuService } from './salesOrderSkuService'
import { WarehouseSearchService } from '@/components/business/AxSearch/WarehouseSearch/service/WarehouseSearchService'

class WarehouseSearchServiceEx extends WarehouseSearchService {
  parentService: SalesOrderSkuService | undefined = undefined

  constructor() {
    super()

    this.config = {
      ...this.config,
      allowAdd: false,
      allowEdit: false,
      allowDelete: false,
    }
  }
}

export const warehouseSearchServiceEx = new WarehouseSearchServiceEx()
