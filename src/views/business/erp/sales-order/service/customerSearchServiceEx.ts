import type { DefaultOptionType } from 'ant-design-vue/es/select'
import { CustomerSearchService } from '@/components/business/AxSearch/CustomerSearch/service/CustomerSearchService'

class CustomerSearchServiceEx extends CustomerSearchService {
  constructor() {
    super({
      allowAdd: false,
      allowEdit: false,
      allowDelete: false,
    })
  }

  onCustomerChange = (value: unknown, option?: DefaultOptionType): void => {
    if (!this.parentService) {
      return
    }
    Object.assign(this.parentService.queryParam, { customerId: option?.key as string | number | undefined })
  }
}

export const customerSearchServiceEx = new CustomerSearchServiceEx()
