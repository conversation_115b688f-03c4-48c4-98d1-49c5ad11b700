import type { CustomerResult } from '@/api/business/customer/model/customer-types'
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import { customerApi } from '@/api/business/customer/customer-api'
import { CustomerSearchService } from '@/components/business/AxSearch/CustomerSearch/service/CustomerSearchService'
import { getAddressInfo } from '../config/utils'

export class CustomerSearchServiceFormEx extends CustomerSearchService {
  constructor() {
    super({
      allowAdd: true,
      allowEdit: true,
      allowDelete: true,
    })
  }

  onCustomerChange = async (value: unknown, option?: DefaultOptionType): Promise<void> => {
    if (!this.parentService) {
      return
    }

    try {
      // 如果有客户ID，获取详细信息并自动填充结算信息
      const customerId = option?.key as number | undefined
      if (customerId) {
        try {
          const response = await customerApi.customerDetail(customerId)

          if (response.success && response.data) {
            const customerDetail: CustomerResult = response.data

            // 自动填充结算信息到父服务的formData
            Object.assign(this.parentService.formData, {
              // 客户信息
              customerId,
              customerName: customerDetail.customerName,
              customerCode: customerDetail.customerCode,
              // 联系信息
              contactName: customerDetail.contactName || '',
              contactPhone: customerDetail.contactPhone || '',
              contactMobile: customerDetail.contactMobile || '',
              // 收货地址信息
              shippingAddress: getAddressInfo(customerDetail),
              // 可选：同时填充发票地址
              billingAddress: getAddressInfo(customerDetail),
            })
          }
        }
        catch {
          throw new Error('获取客户详情失败，请手动填写联系信息')
        }
      }
      else {
        throw new Error('请选择客户')
      }
    }
    catch (error) {
      // 如果没有选择客户，清空相关字段
      Object.assign(this.parentService.formData, {
        customerId: undefined,
        customerName: '',
        customerCode: '',
        contactName: '',
        contactPhone: '',
        contactMobile: '',
        shippingAddress: '',
        billingAddress: '',
      })
      console.error('获取客户详情失败:', error)
    }
  }
}

export const customerSearchServiceFormEx = new CustomerSearchServiceFormEx()
