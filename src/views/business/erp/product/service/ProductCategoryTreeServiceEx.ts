import { AxProductCategoryTreeService } from '@/components/business/AxTree/AxProductCategoryTree/AxProductCategoryTreeService'

export class ProductCategoryTreeServiceEx extends AxProductCategoryTreeService {
  constructor() {
    super()
  }

  // ------------------------------ 混合业务逻辑 ------------------------------
  onProductCategoryTreeChange = (value: string | number | undefined): void => {
    if (this.parentService?.queryParam) {
      Object.assign(this.parentService.queryParam, { categoryId: value as string | number | undefined })
    }
  }
}
