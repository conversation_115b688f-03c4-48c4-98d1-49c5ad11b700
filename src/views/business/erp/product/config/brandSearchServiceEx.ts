import { BrandSearchService } from '@/components/business/AxSearch/BrandSearch'

export class BrandSearchServiceEx extends BrandSearchService {
  constructor() {
    super()
  }

  // ---------------------------- 重写方法 ----------------------------
  onBrandChange = (value: unknown): void => {
    if (this.parentService?.formOpen) {
      Object.assign(this.parentService.formParam, { brandId: value as string | number | undefined })
    }
  }
}

export const brandSearchServiceEx = new BrandSearchServiceEx()
