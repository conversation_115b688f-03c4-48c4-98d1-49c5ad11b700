// 假设 ProductServiceType 定义在此

export function formatStatusText(status: boolean) {
  return status ? '上架' : '下架'
}

/**
 * 主表格列定义（商品）
 * @param productService - 商品服务实例，用于自定义渲染
 */

export const productColumns: Record<string, unknown>[] = [
  {
    title: '商品编码',
    dataIndex: 'productCode',
    width: 120,
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 150,
    ellipsis: true,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 80,
  },
  {
    title: '主分类',
    dataIndex: 'categoryName',
    width: 120,
  },
  {
    title: '品牌',
    dataIndex: 'brandName',
    width: 120,
  },
  {
    title: '来源',
    dataIndex: 'sourceType',
    width: 100,
  },
  {
    title: '原材料',
    dataIndex: 'isRawMaterial',
    width: 80,
  },
  {
    title: '最小起订量',
    dataIndex: 'minOrderQty',
    width: 100,
  },
  // {
  //   title: 'SKU数量',
  //   dataIndex: 'skuCount',
  //   width: 100,
  //   customRender: ({ record }) => productService.getSkuCount(record.id),
  // },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 220,
  },
]

// SKU表格列定义
export const skuColumns: Record<string, unknown>[] = [
  {
    title: 'SKU编码',
    dataIndex: 'skuCode',
    width: 120,
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    ellipsis: true,
  },
  {
    title: '规格摘要',
    dataIndex: 'specSummary',
    width: 180,
    ellipsis: true,
  },
  {
    title: '库存',
    dataIndex: 'stock',
    width: 80,
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 100,
  },
  {
    title: '重量(kg)',
    dataIndex: 'weight',
    width: 90,
  },
  {
    title: '交付周期(天)',
    dataIndex: 'leadTime',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 160,
    sorter: true,
  },
]
