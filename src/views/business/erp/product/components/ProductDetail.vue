<script lang="ts" setup>
import type { ProductService } from '../service/ProductService'
import DictTag from '@/components/utils-old/dict-tag/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'

// 获取服务实例
const productService = inject<ProductService>(CRUD_KEY)!
// 获取状态标签颜色
function getStatusColor(status: boolean | undefined) {
  return status ? 'success' : 'error'
}
</script>

<template>
  <a-modal
    :open="productService.detailOpen"
    :title="productService.detailTitle"
    :footer="null"
    width="800px"
    @cancel="productService.closeDetail()"
  >
    <a-spin :spinning="productService.detailLoading">
      <a-descriptions :column="2" :colon="false" bordered>
        <a-descriptions-item label="商品编码" class="detail-item">
          {{ productService.detailData.productCode || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="商品名称" class="detail-item">
          {{ productService.detailData.name || '--' }}
        </a-descriptions-item>

        <a-descriptions-item label="单位" class="detail-item">
          <DictTag v-if="productService.detailData.unit" key-code="unit" :value-code="productService.detailData.unit" />
          <span v-else>--</span>
        </a-descriptions-item>
        <a-descriptions-item label="商品来源" class="detail-item">
          <DictTag v-if="productService.detailData.sourceType" key-code="product_source_type" :value-code="productService.detailData.sourceType" />
          <span v-else>--</span>
        </a-descriptions-item>

        <a-descriptions-item label="主分类" class="detail-item">
          {{ productService.detailData.categoryName || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="品牌" class="detail-item">
          {{ productService.detailData.brandName || '--' }}
        </a-descriptions-item>

        <a-descriptions-item label="是否原材料" class="detail-item">
          <a-tag :color="productService.detailData.isRawMaterial ? 'blue' : 'default'">
            {{ productService.detailData.isRawMaterial ? '是' : '否' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="最小起订量" class="detail-item">
          {{ productService.detailData.minOrderQty || '--' }}
        </a-descriptions-item>

        <a-descriptions-item label="商品描述" class="detail-item">
          {{ productService.detailData.description || '--' }}
        </a-descriptions-item>
        <a-descriptions-item label="状态" class="detail-item">
          <a-tag :color="getStatusColor(productService.detailData.status)">
            {{ productService.formatStatus(productService.detailData.status) }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="创建时间" class="detail-item">
          {{ formatDateTime(productService.detailData.createTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="更新时间" class="detail-item">
          {{ formatDateTime(productService.detailData.updateTime) }}
        </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.detail-item {
  font-size: 14px;
}
</style>
