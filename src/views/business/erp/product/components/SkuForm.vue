<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { ProductService } from '../service/ProductService'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { computed, inject, ref, watch } from 'vue'

// 获取服务实例
const productService = inject<ProductService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()
// 提交加载状态
const submitLoading = ref(false)

// 创建默认值空对象，避免v-model直接绑定可能为null的值
const defaultSku = computed(() => {
  return productService.currentEditingSku || {}
})

// 状态值的计算属性
const statusValue = computed({
  get: () => productService.currentEditingSku?.status ? '1' : '0',
  set: (val) => {
    if (productService.currentEditingSku) {
      productService.currentEditingSku.status = val === '1'
    }
  },
})

// 表单验证规则
const rules = {
  specSummary: [
    { required: true, message: '请输入规格组合', trigger: 'blur' },
    { max: 200, message: '规格组合最多200个字符', trigger: 'blur' },
  ],
  skuCode: [
    { max: 50, message: 'SKU编码最多50个字符', trigger: 'blur' },
  ],
  stock: [
    { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' },
  ],
  status: [],
  weight: [
    { type: 'number', min: 0, message: '重量不能小于0', trigger: 'blur' },
  ],
  leadTime: [
    { type: 'number', min: 0, message: '交付周期不能小于0', trigger: 'blur' },
  ],
}

// 提交表单
async function submitForm() {
  try {
    submitLoading.value = true
    if (formRef.value) {
      await formRef.value.validate()
      await productService.saveSkuData()
    }
  }
  catch (error) {
    console.error('表单验证失败:', error)
  }
  finally {
    submitLoading.value = false
  }
}

// 监听表单可见性，重置表单
watch(() => productService.skuFormVisible, (visible) => {
  if (!visible && formRef.value) {
    formRef.value.resetFields()
  }
})
</script>

<template>
  <a-modal
    :open="productService.skuFormVisible"
    :title="productService.skuFormTitle"
    :width="700"
    :mask-closable="false"
    :destroy-on-close="true"
    :confirm-loading="submitLoading"
    @ok="submitForm"
    @cancel="() => productService.closeSkuForm()"
  >
    <a-form
      ref="formRef"
      :model="defaultSku"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="SKU编码" name="skuCode">
            <a-input
              v-if="productService.currentEditingSku"
              v-model:value="productService.currentEditingSku.skuCode"
              placeholder="请输入SKU编码"
              :disabled="productService.skuFormType === 'edit'"
              allow-clear
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="规格组合" name="specSummary">
            <a-input
              v-if="productService.currentEditingSku"
              v-model:value="productService.currentEditingSku.specSummary"
              placeholder="请输入规格组合，如：颜色:红色,尺寸:XL"
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="库存" name="stock">
            <a-input-number
              v-if="productService.currentEditingSku"
              v-model:value="productService.currentEditingSku.stock"
              placeholder="请输入库存"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="状态" name="status">
            <a-select
              v-if="productService.currentEditingSku"
              v-model:value="statusValue"
              placeholder="请选择状态"
            >
              <a-select-option value="1">
                上架
              </a-select-option>
              <a-select-option value="0">
                下架
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="重量(kg)" name="weight">
            <a-input-number
              v-if="productService.currentEditingSku"
              v-model:value="productService.currentEditingSku.weight"
              placeholder="请输入重量"
              :min="0"
              :step="0.01"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="交付周期(天)" name="leadTime">
            <a-input-number
              v-if="productService.currentEditingSku"
              v-model:value="productService.currentEditingSku.leadTime"
              placeholder="请输入交付周期"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>
