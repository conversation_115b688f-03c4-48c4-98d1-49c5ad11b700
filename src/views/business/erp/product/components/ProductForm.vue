<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { ProductService } from '../service/ProductService'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'

// 获取服务实例
const productService = inject<ProductService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', type: 'string' },
    { max: 100, message: '商品名称最多100个字符', type: 'string' },
  ],
  productCode: [
    { max: 50, message: '商品编码最多50个字符', type: 'string' },
  ],
  description: [
    { max: 200, message: '描述最多200个字符', type: 'string' },
  ],
  unit: [
    { type: 'string' },
  ],
  status: [
    { type: 'boolean' },
  ],
  sourceType: [
    { type: 'string' },
  ],
}

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await productService.submitFormAndRefresh()
}
</script>

<template>
  <a-modal
    :open="productService.formOpen"
    :title="productService.formTitle"
    :confirm-loading="productService.submitLoading"
    width="800px"
    :mask-closable="false"
    :destroy-on-close="true"
    @ok="handleSubmit"
    @cancel="productService.closeForm()"
  >
    <a-spin :spinning="productService.formLoading">
      <a-form ref="formRef" :model="productService.formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="商品编码" name="productCode">
              <a-input
                v-model:value="productService.formData.productCode"
                placeholder="请输入商品编码"
                :disabled="productService.formType === 'edit'"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="商品名称" name="name">
              <a-input v-model:value="productService.formData.name" placeholder="请输入商品名称" allow-clear />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="单位" name="unit">
              <DictSelect v-model:value="productService.formData.unit" key-code="unit" placeholder="请选择单位" width="100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="商品来源" name="sourceType">
              <DictSelect
                v-model:value="productService.formData.sourceType"
                key-code="product_source_type"
                placeholder="请选择商品来源"
                width="100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="主分类" name="categoryId">
              <a-select
                v-model:value="productService.formData.categoryId"
                placeholder="请选择主分类"
                style="width: 100%"
                @change="(value) => productService.handleCategoryChange(Number(value))"
              >
                <a-select-option
                  v-for="category in productService.categoryOptions"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="品牌" name="brandId">
              <a-select
                v-model:value="productService.formData.brandId"
                placeholder="请选择品牌"
                style="width: 100%"
                @change="(value) => productService.handleBrandChange(Number(value))"
              >
                <a-select-option
                  v-for="brand in productService.brandOptions"
                  :key="brand.id"
                  :value="brand.id"
                >
                  {{ brand.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="是否原材料" name="isRawMaterial">
              <a-radio-group v-model:value="productService.formData.isRawMaterial">
                <a-radio :value="true" class="radio-style">
                  是
                </a-radio>
                <a-radio :value="false" class="radio-style">
                  否
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="最小起订量" name="minOrderQty">
              <a-input-number
                v-model:value="productService.formData.minOrderQty"
                :min="0"
                style="width: 100%"
                placeholder="请输入最小起订量"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="商品描述" name="description">
              <a-textarea
                v-model:value="productService.formData.description"
                placeholder="请输入商品描述"
                :auto-size="{ minRows: 2, maxRows: 6 }"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="status">
              <a-radio-group v-model:value="productService.formData.status">
                <a-radio :value="true" class="radio-style">
                  上架
                </a-radio>
                <a-radio :value="false" class="radio-style">
                  下架
                </a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
