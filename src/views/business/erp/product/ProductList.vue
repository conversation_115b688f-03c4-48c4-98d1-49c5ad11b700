<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import BooleanRadio from '@/components/base-old/boolean-radio/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Spin } from '@/components/base/Spin'
import { Tag } from '@/components/base/Tag'
import { BrandSearch } from '@/components/business/AxSearch/BrandSearch'
import { AxProductCategoryTree } from '@/components/business/AxTree/AxProductCategoryTree'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import DictValue from '@/components/utils-old/dict-value/index.vue'
import { formatBooleanColor } from '@/utils/boolean-util'
import { onBeforeUnmount, ref } from 'vue'
import ProductDetail from './components/ProductDetail.vue'
import ProductForm from './components/ProductForm.vue'
import SkuForm from './components/SkuForm.vue'
import { brandSearchServiceEx } from './config/brandSearchServiceEx'
import { formatStatusText, skuColumns } from './config/productColumns'
import { ProductCategoryTreeServiceEx } from './service/ProductCategoryTreeServiceEx'

import { productService } from './service/ProductService'

// 提供服务实例
productService.provide()
brandSearchServiceEx.provide()
const productCategoryTreeServiceEx = new ProductCategoryTreeServiceEx()
productCategoryTreeServiceEx.provide()

// 组件销毁前清空SKU缓存和状态
onBeforeUnmount(() => {
  productService.clearState()
})
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'product:query'" layout="vertical">
      <FormGrid>
        <FormItem label="商品编码" class="smart-query-form-item">
          <InputText v-model:value="productService.queryParam.productCode" placeholder="请输入商品编码" />
        </FormItem>
        <FormItem label="商品名称" class="smart-query-form-item">
          <InputText v-model:value="productService.queryParam.name" placeholder="请输入商品名称" />
        </FormItem>
        <FormItem label="分类" class="smart-query-form-item">
          <AxProductCategoryTree />
        </FormItem>
        <FormItem label="品牌" class="smart-query-form-item">
          <BrandSearch />
        </FormItem>
        <FormItem label="状态" class="smart-query-form-item">
          <DictSelect v-model:value="productService.queryParam.status" key-code="product_status" placeholder="请选择状态" width="120px" />
        </FormItem>
        <FormItem label="是否原材料" class="smart-query-form-item">
          <BooleanRadio v-model:model-value="productService.queryParam.isRawMaterial" option-type="button" />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->

  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <div>
        <AxTableOperateButtons
          add-config="product:add"
          batch-delete-config="product:delete"
          import-config="product:import"
          export-config="product:export"
        />
        <IconButton v-privilege="'product:query'" label="部分展开" icon-type="PlusOutlined" @click="productService.expandAllRows(true)" />
        <IconButton v-privilege="'product:query'" label="全部收起" icon-type="PlusOutlined" @click="productService.expandAllRows(false)" />
      </div>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="productService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="productService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->

    <Table>
      <template #expandedRowRender="{ record }">
        <Spin :spinning="productService.isSkuLoading(record.id)">
          <Table :need-inject-service="false" :data-source="productService.getSkuData(record.id)" :columns="skuColumns" :show-pagination="false" row-key="id" :show-row-selection="false">
            <template #bodyCell="{ column, record: skuRecord }">
              <template v-if="column.dataIndex === 'status'">
                <Tag :color="formatBooleanColor(skuRecord.status)" :label="formatStatusText(skuRecord.status)" />
              </template>
              <template v-if="column.dataIndex === 'stock'">
                <InputNumber
                  v-model:value="skuRecord.stock"
                  :min="0"
                  :allow-clear="false"
                  style="width: 80px"
                  @blur="productService.updateSkuStock(skuRecord, skuRecord.stock || 0)"
                />
              </template>
              <template v-if="column.dataIndex === 'price'">
                <InputNumber
                  v-model:value="skuRecord.price"
                  :min="0"
                  :precision="2"
                  :allow-clear="false"
                  style="width: 100px"
                  @blur="productService.updateSkuPrice(skuRecord, skuRecord.price || 0)"
                />
              </template>
            </template>
          </Table>
        </Spin>
      </template>

      <template #bodyCell="{ column, record }">
        <!-- 是否原材料列 -->
        <template v-if="column.dataIndex === 'isRawMaterial'">
          <Tag :color="record.isRawMaterial ? 'blue' : 'default'" :label="record.isRawMaterial ? '是' : '否'" />
        </template>

        <!-- 来源列 -->
        <template v-if="column.dataIndex === 'sourceType'">
          <DictValue key-code="product_source_type" :value-code="record.sourceType" />
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <Tag :color="productService.getStatusColor(record.status)" :label="productService.formatStatus(record.status)" />
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start" gap="1px">
            <IconAnchor
              v-privilege="'product:add'" label="新增" icon-type="PlusOutlined"
              @click="productService.openAddSkuForm(record.id)"
            />
            <IconAnchor
              v-privilege="'product:view'" label="查看" icon-type="EyeOutlined"
              @click="productService.openDetailView(record.id)"
            />
            <IconAnchor
              v-privilege="'product:edit'" label="编辑" icon-type="EditOutlined"
              @click="productService.openEditForm(record.id)"
            />
            <IconAnchor
              v-privilege="'product:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="productService.deleteEntity(record.id)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 商品表单 -->
  <ProductForm />
  <!-- 商品详情 -->
  <ProductDetail />
  <!-- SKU表单 -->
  <SkuForm />
</template>
