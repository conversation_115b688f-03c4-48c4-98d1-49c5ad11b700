import type { StockCheckResult } from '@/api/business/stock-check/model/stock-check-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 格式化数字
function formatNumber(value?: number, precision = 0): string {
  if (value === null || value === undefined)
    return '-'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  })
}

// 获取盘点状态对应的颜色
export function getCheckStatusColor(status?: string): DictColor {
  if (!status)
    return 'default'
  switch (status) {
    case 'DRAFT':
      return 'default'
    case 'IN_PROGRESS':
      return 'blue'
    case 'COMPLETED':
      return 'green'
    case 'APPROVED':
      return 'purple'
    case 'CANCELLED':
      return 'red'
    default:
      return 'default'
  }
}

// 获取盘点类型对应的颜色
export function getCheckTypeColor(type?: string): DictColor {
  if (!type)
    return 'default'
  switch (type) {
    case 'FULL':
      return 'blue'
    case 'PARTIAL':
      return 'orange'
    case 'CYCLE':
      return 'green'
    case 'SPOT':
      return 'purple'
    default:
      return 'default'
  }
}

// 库存盘点列配置
export const stockCheckColumns = [
  {
    title: '盘点单号',
    dataIndex: 'checkNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: '盘点名称',
    dataIndex: 'checkName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '仓库名称',
    dataIndex: 'warehouseName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '盘点类型',
    dataIndex: 'checkType',
    width: 100,
    customRender: ({ record }: { record: StockCheckResult }) => {
      return h(DictTag, {
        color: getCheckTypeColor(record.checkType),
        keyCode: 'check_type',
        valueCode: record.checkType,
      })
    },
  },
  {
    title: '盘点状态',
    dataIndex: 'checkStatus',
    width: 100,
    customRender: ({ record }: { record: StockCheckResult }) => {
      return h(DictTag, {
        color: getCheckStatusColor(record.checkStatus),
        keyCode: 'check_status',
        valueCode: record.checkStatus,
      })
    },
  },
  {
    title: '盘点范围',
    dataIndex: 'checkScope',
    width: 100,
    customRender: ({ record }: { record: StockCheckResult }) => {
      return h(DictTag, {
        keyCode: 'check_scope',
        valueCode: record.checkScope,
      })
    },
  },
  {
    title: '盘点日期',
    dataIndex: 'checkDate',
    width: 120,
  },
  {
    title: '总商品数',
    dataIndex: 'totalItems',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return formatNumber(record.totalItems)
    },
  },
  {
    title: '已盘点数',
    dataIndex: 'checkedItems',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return formatNumber(record.checkedItems)
    },
  },
  {
    title: '盘盈商品',
    dataIndex: 'profitItems',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return formatNumber(record.profitItems)
    },
  },
  {
    title: '盘亏商品',
    dataIndex: 'lossItems',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return formatNumber(record.lossItems)
    },
  },
  {
    title: '盘盈金额',
    dataIndex: 'totalProfitAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return record.totalProfitAmount ? `¥${formatNumber(record.totalProfitAmount, 2)}` : '-'
    },
  },
  {
    title: '盘亏金额',
    dataIndex: 'totalLossAmount',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: StockCheckResult }) => {
      return record.totalLossAmount ? `¥${formatNumber(record.totalLossAmount, 2)}` : '-'
    },
  },
  {
    title: '盘点人',
    dataIndex: 'checkerName',
    width: 100,
  },
  {
    title: '开始时间',
    dataIndex: 'checkStartTime',
    width: 160,
  },
  {
    title: '结束时间',
    dataIndex: 'checkEndTime',
    width: 160,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 240,
  },
]
