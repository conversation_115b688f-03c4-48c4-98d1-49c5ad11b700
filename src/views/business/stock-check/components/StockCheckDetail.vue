<script lang="ts" setup>
import type { StockCheckService } from '../service/StockCheckService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { getCheckStatusColor, getCheckTypeColor } from '../columns'

// 获取服务实例
const stockCheckService = inject<StockCheckService>(CRUD_KEY)!

// 格式化数字
function formatNumber(value?: number, precision = 0): string {
  if (value === null || value === undefined)
    return '-'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  })
}
</script>

<template>
  <Modal
    :open="stockCheckService.detailOpen"
    :title="stockCheckService.detailTitle"
    :footer="null"
    width="1400px"
    @cancel="stockCheckService.closeDetail()"
  >
    <Spin :spinning="stockCheckService.detailLoading">
      <Descriptions :column="3" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="盘点单号">
          <Text :value="stockCheckService.detailData.checkNo" />
        </DescriptionsItem>
        <DescriptionsItem label="盘点名称">
          <Text :value="stockCheckService.detailData.checkName" />
        </DescriptionsItem>
        <DescriptionsItem label="盘点状态">
          <DictTag
            :color="getCheckStatusColor(stockCheckService.detailData.checkStatus)"
            key-code="check_status"
            :value-code="stockCheckService.detailData.checkStatus"
          />
        </DescriptionsItem>

        <DescriptionsItem label="仓库编码">
          <Text :value="stockCheckService.detailData.warehouseCode" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库名称">
          <Text :value="stockCheckService.detailData.warehouseName" />
        </DescriptionsItem>
        <DescriptionsItem label="盘点类型">
          <DictTag
            :color="getCheckTypeColor(stockCheckService.detailData.checkType)"
            key-code="check_type"
            :value-code="stockCheckService.detailData.checkType"
          />
        </DescriptionsItem>

        <DescriptionsItem label="盘点范围">
          <DictTag
            key-code="check_scope"
            :value-code="stockCheckService.detailData.checkScope"
          />
        </DescriptionsItem>
        <DescriptionsItem label="盘点日期">
          <Text :value="stockCheckService.detailData.checkDate || '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="盘点人">
          <Text :value="stockCheckService.detailData.checkerName" />
        </DescriptionsItem>

        <!-- 数量统计 -->
        <DescriptionsItem label="总商品数">
          <Text :value="formatNumber(stockCheckService.detailData.totalItems)" />
        </DescriptionsItem>
        <DescriptionsItem label="已盘点数">
          <Text :value="formatNumber(stockCheckService.detailData.checkedItems)" />
        </DescriptionsItem>
        <DescriptionsItem label="盘盈商品数">
          <Text :value="formatNumber(stockCheckService.detailData.profitItems)" />
        </DescriptionsItem>

        <DescriptionsItem label="盘亏商品数">
          <Text :value="formatNumber(stockCheckService.detailData.lossItems)" />
        </DescriptionsItem>
        <DescriptionsItem label="盘盈总金额">
          <Text :value="stockCheckService.detailData.totalProfitAmount ? `¥${formatNumber(stockCheckService.detailData.totalProfitAmount, 2)}` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="盘亏总金额">
          <Text :value="stockCheckService.detailData.totalLossAmount ? `¥${formatNumber(stockCheckService.detailData.totalLossAmount, 2)}` : '-'" />
        </DescriptionsItem>

        <!-- 时间信息 -->
        <DescriptionsItem label="开始时间">
          <Text :value="stockCheckService.detailData.checkStartTime || '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="结束时间">
          <Text :value="stockCheckService.detailData.checkEndTime || '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="审批时间">
          <Text :value="stockCheckService.detailData.approvalTime || '-'" />
        </DescriptionsItem>

        <!-- 商品范围信息 -->
        <DescriptionsItem v-if="stockCheckService.detailData.productCategoryIds" label="商品分类ID" :span="3">
          <Text :value="stockCheckService.detailData.productCategoryIds" />
        </DescriptionsItem>

        <DescriptionsItem v-if="stockCheckService.detailData.productIds" label="商品ID列表" :span="3">
          <Text :value="stockCheckService.detailData.productIds" />
        </DescriptionsItem>

        <!-- 审批信息 -->
        <DescriptionsItem v-if="stockCheckService.detailData.approvalRemark" label="审批备注" :span="3">
          <Text :value="stockCheckService.detailData.approvalRemark" />
        </DescriptionsItem>

        <!-- 备注信息 -->
        <DescriptionsItem v-if="stockCheckService.detailData.remark" label="备注" :span="3">
          <Text :value="stockCheckService.detailData.remark" />
        </DescriptionsItem>

        <!-- 系统信息 -->
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(stockCheckService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(stockCheckService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
