<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { StockCheckService } from '../service/StockCheckService'
import { CustomTextArea } from '@/components/base/CustomText'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { DatePicker } from '@/components/base/Picker'
import { Spin } from '@/components/base/Spin'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from './rule'

// 获取服务实例
const stockCheckService = inject<StockCheckService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await stockCheckService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="stockCheckService.formOpen"
    :title="stockCheckService.formTitle"
    :confirm-loading="stockCheckService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1400px"
    @ok="handleSubmit"
    @cancel="stockCheckService.closeForm()"
  >
    <Spin :spinning="stockCheckService.formLoading">
      <Form
        ref="formRef"
        :model="stockCheckService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="盘点单号" name="checkNo">
              <InputText
                v-model:value="stockCheckService.formData.checkNo"
                placeholder="请输入盘点单号"
                :disabled="stockCheckService.formType === 'edit'"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘点名称" name="checkName">
              <InputText
                v-model:value="stockCheckService.formData.checkName"
                placeholder="请输入盘点名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘点状态" name="checkStatus">
              <DictSelect
                v-model:value="stockCheckService.formData.checkStatus"
                key-code="check_status"
                placeholder="请选择盘点状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="仓库ID" name="warehouseId">
              <InputNumber
                v-model:value="stockCheckService.formData.warehouseId"
                placeholder="请输入仓库ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="仓库编码" name="warehouseCode">
              <InputText
                v-model:value="stockCheckService.formData.warehouseCode"
                placeholder="请输入仓库编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="仓库名称" name="warehouseName">
              <InputText
                v-model:value="stockCheckService.formData.warehouseName"
                placeholder="请输入仓库名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="盘点类型" name="checkType">
              <DictSelect
                v-model:value="stockCheckService.formData.checkType"
                key-code="check_type"
                placeholder="请选择盘点类型"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘点范围" name="checkScope">
              <DictSelect
                v-model:value="stockCheckService.formData.checkScope"
                key-code="check_scope"
                placeholder="请选择盘点范围"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘点日期" name="checkDate">
              <DatePicker
                v-model:value="stockCheckService.formData.checkDate"
                placeholder="请选择盘点日期"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 人员信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="盘点人ID" name="checkerId">
              <InputNumber
                v-model:value="stockCheckService.formData.checkerId"
                placeholder="请输入盘点人ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘点人姓名" name="checkerName">
              <InputText
                v-model:value="stockCheckService.formData.checkerName"
                placeholder="请输入盘点人姓名"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="审批人ID" name="approvalUserId">
              <InputNumber
                v-model:value="stockCheckService.formData.approvalUserId"
                placeholder="请输入审批人ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 数量统计信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="总商品数" name="totalItems">
              <InputNumber
                v-model:value="stockCheckService.formData.totalItems"
                placeholder="请输入总商品数"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="已盘点数" name="checkedItems">
              <InputNumber
                v-model:value="stockCheckService.formData.checkedItems"
                placeholder="请输入已盘点数"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘盈商品数" name="profitItems">
              <InputNumber
                v-model:value="stockCheckService.formData.profitItems"
                placeholder="请输入盘盈商品数"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="盘亏商品数" name="lossItems">
              <InputNumber
                v-model:value="stockCheckService.formData.lossItems"
                placeholder="请输入盘亏商品数"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘盈总金额" name="totalProfitAmount">
              <InputNumber
                v-model:value="stockCheckService.formData.totalProfitAmount"
                placeholder="请输入盘盈总金额"
                :min="0"
                :precision="2"
                :step="0.01"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="盘亏总金额" name="totalLossAmount">
              <InputNumber
                v-model:value="stockCheckService.formData.totalLossAmount"
                placeholder="请输入盘亏总金额"
                :min="0"
                :precision="2"
                :step="0.01"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 时间信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="开始时间" name="checkStartTime">
              <DatePicker
                v-model:value="stockCheckService.formData.checkStartTime"
                placeholder="请选择开始时间"
                show-time
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="结束时间" name="checkEndTime">
              <DatePicker
                v-model:value="stockCheckService.formData.checkEndTime"
                placeholder="请选择结束时间"
                show-time
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="审批时间" name="approvalTime">
              <DatePicker
                v-model:value="stockCheckService.formData.approvalTime"
                placeholder="请选择审批时间"
                show-time
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 范围信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="商品分类ID列表" name="productCategoryIds">
              <CustomTextArea
                v-model:value="stockCheckService.formData.productCategoryIds"
                placeholder="请输入商品分类ID列表（JSON格式）"
                :rows="3"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="商品ID列表" name="productIds">
              <CustomTextArea
                v-model:value="stockCheckService.formData.productIds"
                placeholder="请输入商品ID列表（JSON格式）"
                :rows="3"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 备注信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="审批备注" name="approvalRemark">
              <CustomTextArea
                v-model:value="stockCheckService.formData.approvalRemark"
                placeholder="请输入审批备注"
                :rows="3"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="备注" name="remark">
              <CustomTextArea
                v-model:value="stockCheckService.formData.remark"
                placeholder="请输入备注"
                :rows="3"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
