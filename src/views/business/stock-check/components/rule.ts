import type { Rule } from 'ant-design-vue/es/form'

// 库存盘点表单验证规则
export const rules: Record<string, Rule[]> = {
  checkNo: [
    { required: true, message: '请输入盘点单号', trigger: 'blur' },
    { max: 50, message: '盘点单号不能超过50个字符', trigger: 'blur' },
  ],
  checkName: [
    { required: true, message: '请输入盘点名称', trigger: 'blur' },
    { max: 100, message: '盘点名称不能超过100个字符', trigger: 'blur' },
  ],
  warehouseId: [
    { required: true, message: '请输入仓库ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '仓库ID必须为正整数', trigger: 'blur' },
  ],
  warehouseCode: [
    { required: true, message: '请输入仓库编码', trigger: 'blur' },
    { max: 50, message: '仓库编码不能超过50个字符', trigger: 'blur' },
  ],
  warehouseName: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' },
    { max: 100, message: '仓库名称不能超过100个字符', trigger: 'blur' },
  ],
  checkType: [
    { required: true, message: '请选择盘点类型', trigger: 'change' },
  ],
  checkStatus: [
    { required: true, message: '请选择盘点状态', trigger: 'change' },
  ],
  checkScope: [
    { required: true, message: '请选择盘点范围', trigger: 'change' },
  ],
  checkDate: [
    { required: true, message: '请选择盘点日期', trigger: 'change' },
  ],
  checkerId: [
    { type: 'number', min: 1, message: '盘点人ID必须为正整数', trigger: 'blur' },
  ],
  checkerName: [
    { max: 50, message: '盘点人姓名不能超过50个字符', trigger: 'blur' },
  ],
  approvalUserId: [
    { type: 'number', min: 1, message: '审批人ID必须为正整数', trigger: 'blur' },
  ],
  totalItems: [
    { type: 'number', min: 0, message: '总商品数不能为负数', trigger: 'blur' },
  ],
  checkedItems: [
    { type: 'number', min: 0, message: '已盘点数不能为负数', trigger: 'blur' },
  ],
  profitItems: [
    { type: 'number', min: 0, message: '盘盈商品数不能为负数', trigger: 'blur' },
  ],
  lossItems: [
    { type: 'number', min: 0, message: '盘亏商品数不能为负数', trigger: 'blur' },
  ],
  totalProfitAmount: [
    { type: 'number', min: 0, message: '盘盈总金额不能为负数', trigger: 'blur' },
  ],
  totalLossAmount: [
    { type: 'number', min: 0, message: '盘亏总金额不能为负数', trigger: 'blur' },
  ],
  productCategoryIds: [
    { max: 1000, message: '商品分类ID列表不能超过1000个字符', trigger: 'blur' },
  ],
  productIds: [
    { max: 1000, message: '商品ID列表不能超过1000个字符', trigger: 'blur' },
  ],
  approvalRemark: [
    { max: 500, message: '审批备注不能超过500个字符', trigger: 'blur' },
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' },
  ],
}
