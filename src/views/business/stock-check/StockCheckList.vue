<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import StockCheckDetail from './components/StockCheckDetail.vue'
import StockCheckForm from './components/StockCheckForm.vue'
import { stockCheckService } from './service/StockCheckService'

// 提供 service
stockCheckService.provide()

// 定义记录类型
interface StockCheckRecord {
  id: number | string
  checkStatus: string
  [key: string]: unknown
}

// 处理审批操作
function handleApprove(_record: StockCheckRecord) {
  // TODO: 实现审批逻辑
}

// 处理完成操作
function handleComplete(_record: StockCheckRecord) {
  // TODO: 实现完成逻辑
}
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'stockCheck:query'">
      <FormGrid>
        <FormItem label="盘点单号">
          <InputText v-model:value="stockCheckService.queryParam.checkNo" placeholder="请输入盘点单号" />
        </FormItem>
        <FormItem label="盘点名称">
          <InputText v-model:value="stockCheckService.queryParam.checkName" placeholder="请输入盘点名称" />
        </FormItem>
        <FormItem label="仓库名称">
          <InputText v-model:value="stockCheckService.queryParam.warehouseName" placeholder="请输入仓库名称" />
        </FormItem>
        <FormItem label="盘点类型">
          <DictSelect
            v-model:value="stockCheckService.queryParam.checkType"
            key-code="check_type"
            placeholder="请选择盘点类型"
          />
        </FormItem>
        <FormItem label="盘点状态">
          <DictSelect
            v-model:value="stockCheckService.queryParam.checkStatus"
            key-code="check_status"
            placeholder="请选择盘点状态"
          />
        </FormItem>
        <FormItem label="盘点范围">
          <DictSelect
            v-model:value="stockCheckService.queryParam.checkScope"
            key-code="check_scope"
            placeholder="请选择盘点范围"
          />
        </FormItem>
        <FormItem label="盘点人">
          <InputText v-model:value="stockCheckService.queryParam.checkerName" placeholder="请输入盘点人" />
        </FormItem>
        <FormItem label="盘点日期">
          <DateRangePicker
            v-model:from="stockCheckService.queryParam.checkDateFrom"
            v-model:to="stockCheckService.queryParam.checkDateTo"
          />
        </FormItem>
        <FormItem label="开始时间">
          <DateRangePicker
            v-model:from="stockCheckService.queryParam.checkStartTimeFrom"
            v-model:to="stockCheckService.queryParam.checkStartTimeTo"
          />
        </FormItem>
        <FormItem label="结束时间">
          <DateRangePicker
            v-model:from="stockCheckService.queryParam.checkEndTimeFrom"
            v-model:to="stockCheckService.queryParam.checkEndTimeTo"
          />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker
            v-model:from="stockCheckService.queryParam.createTimeFrom"
            v-model:to="stockCheckService.queryParam.createTimeTo"
          />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="stockCheck:add"
          batch-delete-config="stockCheck:delete"
          import-config="stockCheck:import"
          export-config="stockCheck:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockCheckService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockCheckService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stockCheck:view'" label="查看" icon-type="EyeOutlined"
              @click="stockCheckService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockCheck:edit'" label="编辑" icon-type="EditOutlined"
              @click="stockCheckService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stockCheck:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="stockCheckService.deleteEntity(record.id!)"
            />
            <IconAnchor
              v-if="record.checkStatus === 'DRAFT'"
              v-privilege="'stockCheck:approve'"
              label="审批"
              icon-type="CheckOutlined"
              color="green"
              @click="handleApprove(record)"
            />
            <IconAnchor
              v-if="record.checkStatus === 'IN_PROGRESS'"
              v-privilege="'stockCheck:complete'"
              label="完成"
              icon-type="CheckCircleOutlined"
              color="blue"
              @click="handleComplete(record)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 库存盘点表单 -->
  <StockCheckForm />
  <!-- 库存盘点详情 -->
  <StockCheckDetail />
</template>
