import type { StockCheckForm, StockCheckPageParam, StockCheckResult } from '@/api/business/stock-check/model/stock-check-types'
import { stockCheckApi } from '@/api/business/stock-check/stock-check-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { stockCheckColumns } from '../columns'

/**
 * 库存盘点服务
 * 提供库存盘点相关的业务逻辑和数据管理
 */
export class StockCheckService extends BaseCrudService<StockCheckResult, StockCheckForm, StockCheckPageParam> {
  constructor() {
    // 初始化服务
    super(
      '库存盘点', // 业务名称
      stockCheckColumns,
      {
        // 使用已有的API
        add: stockCheckApi.addStockCheck,
        queryPage: stockCheckApi.stockCheckPage,
        getDetail: stockCheckApi.stockCheckDetail,
        update: stockCheckApi.updateStockCheck,
        import: stockCheckApi.importStockCheck,
        export: stockCheckApi.exportStockCheck,
        delete: stockCheckApi.deleteStockCheck,
        batchDelete: stockCheckApi.batchDeleteStockCheck,
        downloadTemplate: stockCheckApi.exportStockCheck,
      },
    )
  }
}

// 单例模式
export const stockCheckService = new StockCheckService()
