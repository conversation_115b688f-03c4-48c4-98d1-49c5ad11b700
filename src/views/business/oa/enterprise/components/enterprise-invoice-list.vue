<!--
  * 企业 发票信息
-->
<script setup lang="ts">
import { invoiceApi } from '@/api/business/oa/invoice-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { smartSentry } from '@/lib/smart-sentry'
import { message, Modal } from 'ant-design-vue'
import { reactive, ref, watch } from 'vue'
import InvoiceOperateModal from './enterprise-invoice-operate-modal.vue'

const props = defineProps({
  enterpriseId: {
    type: Number,
    default: null,
  },
})

const columns = ref([
  {
    title: 'ID',
    width: 50,
    dataIndex: 'invoiceId',
  },
  {
    title: '开票抬头',
    dataIndex: 'invoiceHeads',
    ellipsis: true,
  },
  {
    title: '纳税人识别号',
    dataIndex: 'taxpayerIdentificationNumber',
    ellipsis: true,
  },
  {
    title: '银行账号',
    width: 100,
    dataIndex: 'accountNumber',
    ellipsis: true,
  },
  {
    title: '开户行',
    width: 120,
    dataIndex: 'bankName',
  },
  {
    title: '状态',
    width: 80,
    dataIndex: 'disabledFlag',
  },
  {
    title: '备注',
    width: 100,
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    width: 100,
    dataIndex: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 100,
  },
])

const queryFormState = {
  enterpriseId: props.enterpriseId,
  keywords: '',
  endTime: null,
  startTime: null,
  pageNum: 1,
  pageSize: PAGE_SIZE,
  searchCount: true,
}
const queryForm = reactive({ ...queryFormState })
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const operateModal = ref()

// 日期选择
const searchDate = ref()

function dateChange(dates, dateStrings) {
  queryForm.startTime = dateStrings[0]
  queryForm.endTime = dateStrings[1]
}

function resetQuery() {
  searchDate.value = []
  Object.assign(queryForm, queryFormState)
  ajaxQuery()
}

function onSearch() {
  queryForm.pageNum = 1
  ajaxQuery()
}

async function ajaxQuery() {
  try {
    tableLoading.value = true
    const responseModel = await invoiceApi.pageQuery(queryForm)
    const list = responseModel.data.list
    total.value = responseModel.data.total
    tableData.value = list
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    tableLoading.value = false
  }
}

function confirmDelete(invoiceId) {
  Modal.confirm({
    title: '确定要删除吗？',
    content: '删除后，该信息将不可恢复',
    okText: '删除',
    okType: 'danger',
    onOk() {
      del(invoiceId)
    },
    cancelText: '取消',
    onCancel() {},
  })
}

async function del(invoiceId) {
  try {
    SmartLoading.show()
    await invoiceApi.delete(invoiceId)
    message.success('删除成功')
    ajaxQuery()
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    SmartLoading.hide()
  }
}

function addOrUpdate(rowData) {
  operateModal.value.showModal(rowData)
}

watch(
  () => props.enterpriseId,
  (value) => {
    if (value) {
      queryForm.enterpriseId = value
      ajaxQuery()
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字" class="smart-query-form-item">
        <a-input v-model:value="queryForm.keywords" style="width: 300px" placeholder="开票抬头/银行账户/创建人" />
      </a-form-item>

      <a-form-item label="创建时间" class="smart-query-form-item">
        <a-space direction="vertical" :size="12">
          <a-range-picker v-model:value="searchDate" @change="dateChange" />
        </a-space>
      </a-form-item>

      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>

        <a-button type="primary" class="smart-margin-left20" @click="addOrUpdate()">
          <template #icon>
            <PlusOutlined />
          </template>
          新建发票
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="false">
    <a-row justify="end">
      <TableOperator v-model="columns" class="smart-margin-bottom5" :table-id="TABLE_ID_CONST.BUSINESS.OA.ENTERPRISE_INVOICE" :refresh="ajaxQuery" />
    </a-row>
    <a-table :scroll="{ x: 1300 }" size="small" :data-source="tableData" :columns="columns" row-key="invoiceId" :pagination="false" bordered>
      <template #bodyCell="{ text, record, index, column }">
        <template v-if="column.dataIndex === 'disabledFlag'">
          {{ record.disabledFlag ? '禁用' : '启用' }}
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button type="link" @click="addOrUpdate(record)">
              编辑
            </a-button>
            <a-button type="link" danger @click="confirmDelete(record.invoiceId)">
              删除
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="table-pagination">
      <a-pagination
        v-model:current="queryForm.pageNum"
        v-model:page-size="queryForm.pageSize"
        show-size-changer
        show-quick-jumper
        show-less-items
        :page-size-options="PAGE_SIZE_OPTIONS"
        :default-page-size="queryForm.pageSize"
        :total="total"
        :show-total="(total) => `共${total}条`"
        @change="ajaxQuery"
        @show-size-change="ajaxQuery"
      />
    </div>
    <!-- 新建编辑modal -->
    <InvoiceOperateModal ref="operateModal" :enterprise-id="enterpriseId" @reload-list="ajaxQuery" />
  </a-card>
</template>
