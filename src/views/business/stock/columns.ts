import type { StockResult } from '@/api/business/stock/model/stock-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { DictTag } from '@/components/utils/Dict'
import { h } from 'vue'

// 格式化数字
function formatNumber(value?: number, precision = 0): string {
  if (value === null || value === undefined)
    return '-'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  })
}

// 获取库存状态对应的颜色
export function getStockStatusColor(status?: string): DictColor {
  if (!status)
    return 'default'
  switch (status) {
    case 'NORMAL':
      return 'green'
    case 'LOW_STOCK':
      return 'orange'
    case 'OUT_OF_STOCK':
      return 'red'
    case 'OVERSTOCKED':
      return 'blue'
    case 'FROZEN':
      return 'purple'
    default:
      return 'default'
  }
}

// 库存列配置
export const stockColumns = [
  {
    title: '仓库名称',
    dataIndex: 'warehouseName',
    width: 120,
    ellipsis: true,
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    ellipsis: true,
  },
  {
    title: 'SKU编码',
    dataIndex: 'skuCode',
    width: 120,
  },
  {
    title: '规格摘要',
    dataIndex: 'specSummary',
    width: 150,
    ellipsis: true,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 80,
  },
  {
    title: '当前库存',
    dataIndex: 'currentStock',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return formatNumber(record.currentStock)
    },
  },
  {
    title: '可用库存',
    dataIndex: 'availableStock',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return formatNumber(record.availableStock)
    },
  },
  {
    title: '预留库存',
    dataIndex: 'reservedStock',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return formatNumber(record.reservedStock)
    },
  },
  {
    title: '在途库存',
    dataIndex: 'inTransitStock',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return formatNumber(record.inTransitStock)
    },
  },
  {
    title: '安全库存',
    dataIndex: 'minStock',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return formatNumber(record.minStock)
    },
  },
  {
    title: '平均成本',
    dataIndex: 'avgCost',
    width: 100,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return record.avgCost ? `¥${formatNumber(record.avgCost, 2)}` : '-'
    },
  },
  {
    title: '库存总价值',
    dataIndex: 'totalValue',
    width: 120,
    align: 'right',
    customRender: ({ record }: { record: StockResult }) => {
      return record.totalValue ? `¥${formatNumber(record.totalValue, 2)}` : '-'
    },
  },
  {
    title: '库存状态',
    dataIndex: 'stockStatus',
    width: 100,
    customRender: ({ record }: { record: StockResult }) => {
      return h(DictTag, {
        color: getStockStatusColor(record.stockStatus),
        keyCode: 'stock_status',
        valueCode: record.stockStatus,
      })
    },
  },
  {
    title: '最后入库',
    dataIndex: 'lastInDate',
    width: 120,
  },
  {
    title: '最后出库',
    dataIndex: 'lastOutDate',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]
