<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import StockDetail from './components/StockDetail.vue'
import StockForm from './components/StockForm.vue'
import { stockService } from './service/StockService'

// 提供 service
stockService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'stock:query'">
      <FormGrid>
        <FormItem label="仓库名称">
          <InputText v-model:value="stockService.queryParam.warehouseName" placeholder="请输入仓库名称" />
        </FormItem>
        <FormItem label="商品名称">
          <InputText v-model:value="stockService.queryParam.productName" placeholder="请输入商品名称" />
        </FormItem>
        <FormItem label="SKU编码">
          <InputText v-model:value="stockService.queryParam.skuCode" placeholder="请输入SKU编码" />
        </FormItem>
        <FormItem label="库存状态">
          <DictSelect
            v-model:value="stockService.queryParam.stockStatus"
            key-code="stock_status"
            placeholder="请选择库存状态"
          />
        </FormItem>
        <FormItem label="当前库存">
          <InputNumber
            v-model:value="stockService.queryParam.currentStock"
            placeholder="请输入当前库存"
            :min="0"
          />
        </FormItem>
        <FormItem label="最后入库日期">
          <DateRangePicker
            v-model:from="stockService.queryParam.lastInDateFrom"
            v-model:to="stockService.queryParam.lastInDateTo"
          />
        </FormItem>
        <FormItem label="最后出库日期">
          <DateRangePicker
            v-model:from="stockService.queryParam.lastOutDateFrom"
            v-model:to="stockService.queryParam.lastOutDateTo"
          />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker
            v-model:from="stockService.queryParam.createTimeFrom"
            v-model:to="stockService.queryParam.createTimeTo"
          />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons
          add-config="stock:add"
          batch-delete-config="stock:delete"
          import-config="stock:import"
          export-config="stock:export"
        />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="stockService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="stockService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'stock:view'" label="查看" icon-type="EyeOutlined"
              @click="stockService.openDetailView(record.id!)"
            />
            <!-- <IconAnchor
              v-privilege="'stock:edit'" label="编辑" icon-type="EditOutlined"
              @click="stockService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'stock:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="stockService.deleteEntity(record.id!)"
            /> -->
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 库存表单 -->
  <StockForm />
  <!-- 库存详情 -->
  <StockDetail />
</template>
