import type { StockForm, StockPageParam, StockResult } from '@/api/business/stock/model/stock-types'
import { stockApi } from '@/api/business/stock/stock-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { stockColumns } from '../columns'

/**
 * 库存服务
 * 提供库存相关的业务逻辑和数据管理
 */
export class StockService extends BaseCrudService<StockResult, StockForm, StockPageParam> {
  constructor() {
    // 初始化服务
    super(
      '库存', // 业务名称
      stockColumns,
      {
        // 使用已有的API
        add: stockApi.addStock,
        queryPage: stockApi.stockPage,
        getDetail: stockApi.stockDetail,
        update: stockApi.updateStock,
        import: stockApi.importStock,
        export: stockApi.exportStock,
        delete: stockApi.deleteStock,
        batchDelete: stockApi.batchDeleteStock,
        downloadTemplate: stockApi.exportStock,
      },
    )
  }
}

// 单例模式
export const stockService = new StockService()
