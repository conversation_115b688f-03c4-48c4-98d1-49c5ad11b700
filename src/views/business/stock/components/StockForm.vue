<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { StockService } from '../service/StockService'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { DatePicker } from '@/components/base/Picker'
import { Spin } from '@/components/base/Spin'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from './rule'

// 获取服务实例
const stockService = inject<StockService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await stockService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="stockService.formOpen"
    :title="stockService.formTitle"
    :confirm-loading="stockService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
    @ok="handleSubmit"
    @cancel="stockService.closeForm()"
  >
    <Spin :spinning="stockService.formLoading">
      <Form
        ref="formRef"
        :model="stockService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="仓库ID" name="warehouseId">
              <InputNumber
                v-model:value="stockService.formData.warehouseId"
                placeholder="请输入仓库ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="仓库编码" name="warehouseCode">
              <InputText
                v-model:value="stockService.formData.warehouseCode"
                placeholder="请输入仓库编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="仓库名称" name="warehouseName">
              <InputText
                v-model:value="stockService.formData.warehouseName"
                placeholder="请输入仓库名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="商品ID" name="productId">
              <InputNumber
                v-model:value="stockService.formData.productId"
                placeholder="请输入商品ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="商品编码" name="productCode">
              <InputText
                v-model:value="stockService.formData.productCode"
                placeholder="请输入商品编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="商品名称" name="productName">
              <InputText
                v-model:value="stockService.formData.productName"
                placeholder="请输入商品名称"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="SKU ID" name="skuId">
              <InputNumber
                v-model:value="stockService.formData.skuId"
                placeholder="请输入SKU ID"
                :min="1"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="SKU编码" name="skuCode">
              <InputText
                v-model:value="stockService.formData.skuCode"
                placeholder="请输入SKU编码"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="规格摘要" name="specSummary">
              <InputText
                v-model:value="stockService.formData.specSummary"
                placeholder="请输入规格摘要"
                allow-clear
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="单位" name="unit">
              <InputText
                v-model:value="stockService.formData.unit"
                placeholder="请输入单位"
                allow-clear
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="库存状态" name="stockStatus">
              <DictSelect
                v-model:value="stockService.formData.stockStatus"
                key-code="stock_status"
                placeholder="请选择库存状态"
                width="100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="冻结数量" name="freezeQuantity">
              <InputNumber
                v-model:value="stockService.formData.freezeQuantity"
                placeholder="请输入冻结数量"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 库存数量信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="当前库存" name="currentStock">
              <InputNumber
                v-model:value="stockService.formData.currentStock"
                placeholder="请输入当前库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="可用库存" name="availableStock">
              <InputNumber
                v-model:value="stockService.formData.availableStock"
                placeholder="请输入可用库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="预留库存" name="reservedStock">
              <InputNumber
                v-model:value="stockService.formData.reservedStock"
                placeholder="请输入预留库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="在途库存" name="inTransitStock">
              <InputNumber
                v-model:value="stockService.formData.inTransitStock"
                placeholder="请输入在途库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="最小库存" name="minStock">
              <InputNumber
                v-model:value="stockService.formData.minStock"
                placeholder="请输入最小库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="最大库存" name="maxStock">
              <InputNumber
                v-model:value="stockService.formData.maxStock"
                placeholder="请输入最大库存"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="再订货点" name="reorderPoint">
              <InputNumber
                v-model:value="stockService.formData.reorderPoint"
                placeholder="请输入再订货点"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="再订货量" name="reorderQuantity">
              <InputNumber
                v-model:value="stockService.formData.reorderQuantity"
                placeholder="请输入再订货量"
                :min="0"
                :precision="0"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="平均成本" name="avgCost">
              <InputNumber
                v-model:value="stockService.formData.avgCost"
                placeholder="请输入平均成本"
                :min="0"
                :precision="2"
                :step="0.01"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="最新成本" name="lastCost">
              <InputNumber
                v-model:value="stockService.formData.lastCost"
                placeholder="请输入最新成本"
                :min="0"
                :precision="2"
                :step="0.01"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="库存总价值" name="totalValue">
              <InputNumber
                v-model:value="stockService.formData.totalValue"
                placeholder="请输入库存总价值"
                :min="0"
                :precision="2"
                :step="0.01"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 日期信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="最后入库日期" name="lastInDate">
              <DatePicker
                v-model:value="stockService.formData.lastInDate"
                placeholder="请选择最后入库日期"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="最后出库日期" name="lastOutDate">
              <DatePicker
                v-model:value="stockService.formData.lastOutDate"
                placeholder="请选择最后出库日期"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="最后盘点日期" name="lastCheckDate">
              <DatePicker
                v-model:value="stockService.formData.lastCheckDate"
                placeholder="请选择最后盘点日期"
                style="width: 100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
