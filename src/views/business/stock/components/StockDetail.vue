<script lang="ts" setup>
import type { StockService } from '../service/StockService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { getStockStatusColor } from '../columns'

// 获取服务实例
const stockService = inject<StockService>(CRUD_KEY)!

// 格式化数字
function formatNumber(value?: number, precision = 0): string {
  if (value === null || value === undefined)
    return '-'
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  })
}
</script>

<template>
  <Modal
    :open="stockService.detailOpen"
    :title="stockService.detailTitle"
    :footer="null"
    width="1200px"
    @cancel="stockService.closeDetail()"
  >
    <Spin :spinning="stockService.detailLoading">
      <Descriptions :column="3" :colon="false" bordered>
        <!-- 基本信息 -->
        <DescriptionsItem label="仓库编码">
          <Text :value="stockService.detailData.warehouseCode" />
        </DescriptionsItem>
        <DescriptionsItem label="仓库名称">
          <Text :value="stockService.detailData.warehouseName" />
        </DescriptionsItem>
        <DescriptionsItem label="商品编码">
          <Text :value="stockService.detailData.productCode" />
        </DescriptionsItem>

        <DescriptionsItem label="商品名称">
          <Text :value="stockService.detailData.productName" />
        </DescriptionsItem>
        <DescriptionsItem label="SKU编码">
          <Text :value="stockService.detailData.skuCode" />
        </DescriptionsItem>
        <DescriptionsItem label="规格摘要">
          <Text :value="stockService.detailData.specSummary" />
        </DescriptionsItem>

        <DescriptionsItem label="单位">
          <Text :value="stockService.detailData.unit" />
        </DescriptionsItem>
        <DescriptionsItem label="库存状态">
          <DictTag
            :color="getStockStatusColor(stockService.detailData.stockStatus)"
            key-code="stock_status"
            :value-code="stockService.detailData.stockStatus"
          />
        </DescriptionsItem>
        <DescriptionsItem label="冻结数量">
          <Text :value="formatNumber(stockService.detailData.freezeQuantity)" />
        </DescriptionsItem>

        <!-- 库存数量信息 -->
        <DescriptionsItem label="当前库存">
          <Text :value="formatNumber(stockService.detailData.currentStock)" />
        </DescriptionsItem>
        <DescriptionsItem label="可用库存">
          <Text :value="formatNumber(stockService.detailData.availableStock)" />
        </DescriptionsItem>
        <DescriptionsItem label="预留库存">
          <Text :value="formatNumber(stockService.detailData.reservedStock)" />
        </DescriptionsItem>

        <DescriptionsItem label="在途库存">
          <Text :value="formatNumber(stockService.detailData.inTransitStock)" />
        </DescriptionsItem>
        <DescriptionsItem label="最小库存">
          <Text :value="formatNumber(stockService.detailData.minStock)" />
        </DescriptionsItem>
        <DescriptionsItem label="最大库存">
          <Text :value="formatNumber(stockService.detailData.maxStock)" />
        </DescriptionsItem>

        <DescriptionsItem label="再订货点">
          <Text :value="formatNumber(stockService.detailData.reorderPoint)" />
        </DescriptionsItem>
        <DescriptionsItem label="再订货量">
          <Text :value="formatNumber(stockService.detailData.reorderQuantity)" />
        </DescriptionsItem>
        <DescriptionsItem label="平均成本">
          <Text :value="stockService.detailData.avgCost ? `¥${formatNumber(stockService.detailData.avgCost, 2)}` : '-'" />
        </DescriptionsItem>

        <DescriptionsItem label="最新成本">
          <Text :value="stockService.detailData.lastCost ? `¥${formatNumber(stockService.detailData.lastCost, 2)}` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="库存总价值">
          <Text :value="stockService.detailData.totalValue ? `¥${formatNumber(stockService.detailData.totalValue, 2)}` : '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="最后入库日期">
          <Text :value="stockService.detailData.lastInDate || '-'" />
        </DescriptionsItem>

        <DescriptionsItem label="最后出库日期">
          <Text :value="stockService.detailData.lastOutDate || '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="最后盘点日期">
          <Text :value="stockService.detailData.lastCheckDate || '-'" />
        </DescriptionsItem>
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(stockService.detailData.createTime)" />
        </DescriptionsItem>

        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(stockService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
