import type { Rule } from 'ant-design-vue/es/form'

// 库存表单验证规则
export const rules: Record<string, Rule[]> = {
  warehouseId: [
    { required: true, message: '请输入仓库ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '仓库ID必须为正整数', trigger: 'blur' },
  ],
  warehouseCode: [
    { required: true, message: '请输入仓库编码', trigger: 'blur' },
    { max: 50, message: '仓库编码不能超过50个字符', trigger: 'blur' },
  ],
  warehouseName: [
    { required: true, message: '请输入仓库名称', trigger: 'blur' },
    { max: 100, message: '仓库名称不能超过100个字符', trigger: 'blur' },
  ],
  productId: [
    { required: true, message: '请输入商品ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '商品ID必须为正整数', trigger: 'blur' },
  ],
  productCode: [
    { required: true, message: '请输入商品编码', trigger: 'blur' },
    { max: 50, message: '商品编码不能超过50个字符', trigger: 'blur' },
  ],
  productName: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { max: 100, message: '商品名称不能超过100个字符', trigger: 'blur' },
  ],
  skuId: [
    { required: true, message: '请输入SKU ID', trigger: 'blur' },
    { type: 'number', min: 1, message: 'SKU ID必须为正整数', trigger: 'blur' },
  ],
  skuCode: [
    { required: true, message: '请输入SKU编码', trigger: 'blur' },
    { max: 50, message: 'SKU编码不能超过50个字符', trigger: 'blur' },
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' },
    { max: 20, message: '单位不能超过20个字符', trigger: 'blur' },
  ],
  currentStock: [
    { required: true, message: '请输入当前库存', trigger: 'blur' },
    { type: 'number', min: 0, message: '当前库存不能为负数', trigger: 'blur' },
  ],
  stockStatus: [
    { required: true, message: '请选择库存状态', trigger: 'change' },
  ],
  // 其他字段的验证规则可以根据需要添加
  minStock: [
    { type: 'number', min: 0, message: '最小库存不能为负数', trigger: 'blur' },
  ],
  maxStock: [
    { type: 'number', min: 0, message: '最大库存不能为负数', trigger: 'blur' },
  ],
  avgCost: [
    { type: 'number', min: 0, message: '平均成本不能为负数', trigger: 'blur' },
  ],
  lastCost: [
    { type: 'number', min: 0, message: '最新成本不能为负数', trigger: 'blur' },
  ],
}
