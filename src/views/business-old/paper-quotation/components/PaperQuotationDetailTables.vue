<script lang="ts" setup>
import type { PaperQuotationItemModel } from '@/api/business/paper-quotation-item/model/paper-quotation-item-model'
import { paperApi } from '@/api/business/paper/paper-api'
import { dictApi } from '@/api/support/dict-api'
import { computed, ref, watch } from 'vue'

// 扩展PaperQuotationItemModel类型，添加paperCategory和paperSpec字段

// 定义纸价明细表格对象类型
interface QuotationTable {
  category: string
  items: PaperQuotationItemModel[]
}

// 定义类别选项类型
interface CategoryOption {
  value: string
  label: string
}

// 定义字典项类型
interface DictItem {
  valueCode: string
  valueName: string
  [key: string]: any
}

const props = defineProps({
  // 明细数据
  modelValue: {
    type: Array as () => PaperQuotationItemModel[],
    default: () => [],
  },
  // 纸张报价ID，编辑模式下必传
  quotationId: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['update:modelValue', 'categoriesChange'])

// 纸张列表
const paperList = ref<any[]>([])

// 纸价明细表格数组
const quotationTables = ref<QuotationTable[]>([])

// 所有可选类别
const allCategoryOptions = ref<CategoryOption[]>([])

// 已选择的类别列表
const selectedCategories = ref<string[]>([])

// 计算属性：获取选中的类别数组，用于传递给父组件
const selectedCategoriesArray = computed(() => {
  return selectedCategories.value
})

// 监听选中类别变化，通知父组件
watch(
  selectedCategoriesArray,
  (newVal) => {
    emit('categoriesChange', newVal)
  },
  { deep: true },
)

// 获取纸张列表
async function fetchPaperList() {
  try {
    const res = await paperApi.paperList({
      status: 1,
      code: '',
      paperCategory: '',
    })
    if (res.data && res.data) {
      paperList.value = res.data
    }
  }
  catch {
    // 错误处理
  }
}

// 监听modelValue变化，更新表格数据
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 根据类别分组
      const groupedByCategory: Record<string, PaperQuotationItemModel[]> = {}

      newVal.forEach((item) => {
        if (item.paperCategory) {
          if (!groupedByCategory[item.paperCategory]) {
            groupedByCategory[item.paperCategory] = []
          }
          groupedByCategory[item.paperCategory].push(item)
        }
      })

      // 转换为表格数组
      const tables: QuotationTable[] = []
      for (const category in groupedByCategory) {
        tables.push({
          category,
          items: groupedByCategory[category],
        })
      }

      if (tables.length > 0) {
        quotationTables.value = tables
        // 更新已选择的类别
        selectedCategories.value = tables.map(table => table.category)
      }
      else {
        quotationTables.value = []
        selectedCategories.value = []
      }
    }
    else {
      quotationTables.value = []
      selectedCategories.value = []
    }
  },
  { deep: true, immediate: true },
)

// 初始化类别选项
async function initCategoryOptions() {
  try {
    // 使用系统的字典API获取paper_category_corrugated_shape的数据
    const dictRes = await dictApi.valueList('paper_category_corrugated_shape')
    if (dictRes && dictRes.data && Array.isArray(dictRes.data) && dictRes.data.length > 0) {
      allCategoryOptions.value = dictRes.data.map((item: DictItem) => ({
        value: item.valueCode,
        label: item.valueName,
      }))
    }
  }
  catch {
    // 错误处理
  }
}

// 处理类别选择/取消
function handleCategoryToggle(categoryValue: string) {
  const index = selectedCategories.value.indexOf(categoryValue)

  if (index > -1) {
    // 如果已选中，则取消选中
    selectedCategories.value.splice(index, 1)

    // 从表格数组中移除对应的表格
    const tableIndex = quotationTables.value.findIndex(table => table.category === categoryValue)
    if (tableIndex > -1) {
      quotationTables.value.splice(tableIndex, 1)
    }
  }
  else {
    // 如果未选中，则添加选中
    selectedCategories.value.push(categoryValue)

    // 获取选中类别的标签文本（valueName）
    const selectedOption = allCategoryOptions.value.find(option => option.value === categoryValue)
    const categoryLabel = selectedOption ? selectedOption.label : categoryValue

    // 根据选择的类别标签筛选纸张
    const filteredPapers = paperList.value.filter((paper: any) => {
      // 使用类别标签（valueName）匹配纸张的description
      const categoryMatches = paper.description && paper.description.includes(categoryLabel)
      return categoryMatches
    })

    const newItems: PaperQuotationItemModel[] = []

    // 为每个匹配的纸张创建一个明细项
    filteredPapers.forEach((paper: any, index: number) => {
      newItems.push({
        quotationId: props.quotationId || 0,
        paperId: paper.id,
        paperDescription: paper.description || paper.name || '',
        paperCategory: categoryValue,
        corrugatedShape: paper.spec || '',
        unit: 'SQUARE_METER',
        unitPrice: 0,
        sort: index + 1,
        createTime: undefined,
        updateTime: undefined,
      })
    })

    // 如果没有找到匹配的纸张，添加一个空行
    if (filteredPapers.length === 0) {
      newItems.push({
        quotationId: props.quotationId || 0,
        paperId: 0,
        paperDescription: `${categoryLabel} 自定义`, // 设置一个默认的描述
        paperCategory: categoryValue,
        corrugatedShape: '',
        unit: 'SQUARE_METER',
        unitPrice: 0,
        sort: 1,
        createTime: undefined,
        updateTime: undefined,
        deletedFlag: false,
      })
    }

    // 添加新表格
    quotationTables.value.push({
      category: categoryValue,
      items: newItems,
    })
  }

  updateModelValue()
}

// 更新单价
function updateUnitPrice(tableIndex: number, itemIndex: number, value: number) {
  quotationTables.value[tableIndex].items[itemIndex].unitPrice = value
  updateModelValue()
}

// 更新modelValue
function updateModelValue() {
  // 合并所有表格的items
  const allItems: PaperQuotationItemModel[] = []
  quotationTables.value.forEach((table) => {
    if (table.category && table.items.length > 0) {
      allItems.push(...table.items)
    }
  })

  emit('update:modelValue', allItems)
}

// 初始化
fetchPaperList()
initCategoryOptions()
</script>

<template>
  <div>
    <!-- 顶部类别选择器 -->
    <div class="category-selector">
      <a-radio-group button-style="solid">
        <a-radio-button
          v-for="option in allCategoryOptions"
          :key="option.value"
          :value="option.value"
          :checked="selectedCategories.includes(option.value)"
          :class="{ 'selected-category': selectedCategories.includes(option.value) }"
          @click="handleCategoryToggle(option.value)"
        >
          {{ option.label }}
        </a-radio-button>
      </a-radio-group>
    </div>

    <!-- 表格容器 -->
    <div class="quotation-tables-container">
      <div
        v-for="(table, tableIndex) in quotationTables"
        :key="table.category"
        class="quotation-table-wrapper"
      >
        <div class="quotation-table">
          <div class="table-header">
            <h3>{{ allCategoryOptions.find(opt => opt.value === table.category)?.label || table.category }}</h3>
          </div>
          <a-table
            size="small"
            :data-source="table.items"
            :pagination="false"
            bordered
          >
            <a-table-column title="序号" align="center" width="60px">
              <template #default="{ index }">
                {{ index + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="纸张" data-index="paperDescription">
              <template #default="{ record }">
                {{ record.paperDescription }}
              </template>
            </a-table-column>
            <a-table-column title="单价(元/m²)" data-index="unitPrice" width="120px">
              <template #default="{ record, index }">
                <a-input-number
                  :value="record.unitPrice"
                  style="width: 100%"
                  :min="0"
                  :step="0.01"
                  :precision="2"
                  placeholder="请输入单价"
                  @change="value => updateUnitPrice(tableIndex, index, Number(value))"
                />
              </template>
            </a-table-column>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.category-selector {
  margin-bottom: 16px;
}

.selected-category {
  background-color: #1890ff !important;
  color: #fff !important;
  border-color: #1890ff !important;
}

.quotation-tables-container {
  column-count: 3;
  column-gap: 16px;
  margin-bottom: 16px;
}

.quotation-table-wrapper {
  break-inside: avoid;
  margin-bottom: 16px;
  display: inline-block;
  width: 100%;
}

.quotation-table {
  border: 1px solid #f0f0f0;
  height: auto;
}

.table-header {
  padding: 8px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
