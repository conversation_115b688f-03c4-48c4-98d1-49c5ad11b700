<script lang="ts" setup>
import type { PaperQuotationItemResult } from '@/api/business/paper-quotation-item/model/paper-quotation-item-form-model'
import type { PaperQuotationResult } from '@/api/business/paper-quotation/model/paper-quotation-form-model'
import { paperQuotationApi } from '@/api/business/paper-quotation/paper-quotation-api'
import { dictApi } from '@/api/support/dict-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import { smartSentry } from '@/lib/smart-sentry'
import { message } from 'ant-design-vue'
import { onMounted, reactive, ref, watch } from 'vue'

// 扩展PaperQuotationResult类型，添加paperQuotationItemList字段
interface ExtendedPaperQuotationResult extends Omit<PaperQuotationResult, 'paperQuotationItemList'> {
  paperQuotationItemList: PaperQuotationItemResult[];
  approvalUserName?: string;
  createdByName?: string;
}

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  quotationId: {
    type: Number,
    default: 0,
  },
})

// 定义组件事件
const emit = defineEmits(['update:open'])

// 报价单详情数据
const quotationDetail = reactive<Partial<ExtendedPaperQuotationResult>>({
  quotationNo: '',
  supplierId: '',
  supplierName: '',
  quotationDate: '',
  quotationType: '',
  status: '',
  paperQuotationItemList: []
})

// 报价单明细数据
const quotationItems = ref<PaperQuotationItemResult[]>([])

// 加载状态
const loading = ref(false)

// 报价类型字典数据
const quotationTypeDict = ref<Array<{ valueCode: string, valueName: string }>>([])
// 报价单状态字典数据
const quotationStatusDict = ref<Array<{ valueCode: string, valueName: string }>>([])
// 纸张单位字典数据
const paperUnitDict = ref<Array<{ valueCode: string, valueName: string }>>([])

// 监听报价单ID变化
watch(
  () => props.quotationId,
  (newVal) => {
    if (newVal && props.open) {
      getQuotationDetail()
    }
  },
)

// 监听对话框可见性变化
watch(
  () => props.open,
  (newVal) => {
    if (newVal && props.quotationId) {
      getQuotationDetail()
    }
  },
)

// 获取报价单详情
async function getQuotationDetail() {
  if (!props.quotationId) {
    return
  }

  loading.value = true
  try {
    SmartLoading.show()
    const res = await paperQuotationApi.getPaperQuotationDetail(props.quotationId)
    if (res.data) {
      // 完全重置quotationDetail对象，先清空再赋值
      for (const key in quotationDetail) {
        delete (quotationDetail as any)[key]
      }
      // 使用Object.assign直接复制，这样更安全
      Object.assign(quotationDetail, res.data)
      // 使用paperQuotationItemList作为明细数据
      quotationItems.value = res.data.paperQuotationItemList || []
    }
  }
  catch (error) {
    smartSentry.captureError(error)
    message.error('获取报价单详情失败')
  }
  finally {
    loading.value = false
    SmartLoading.hide()
  }
}

// 获取字典数据
async function fetchDictData() {
  try {
    // 获取报价类型字典
    const quotationTypeRes = await dictApi.valueList('quotation_type')
    if (quotationTypeRes.data) {
      quotationTypeDict.value = quotationTypeRes.data as Array<{ valueCode: string, valueName: string }>
    }

    // 获取报价单状态字典
    const quotationStatusRes = await dictApi.valueList('quotation_status')
    if (quotationStatusRes.data) {
      quotationStatusDict.value = quotationStatusRes.data as Array<{ valueCode: string, valueName: string }>
    }

    // 获取纸张单位字典
    const paperUnitRes = await dictApi.valueList('paper_unit')
    if (paperUnitRes.data) {
      paperUnitDict.value = paperUnitRes.data as Array<{ valueCode: string, valueName: string }>
    }
  }
  catch (error) {
    smartSentry.captureError(error)
    console.error('获取字典数据失败', error)
  }
}

// 获取报价类型名称
function getQuotationTypeName(typeCode: string | undefined) {
  if (!typeCode)
    return '-'
  const type = quotationTypeDict.value.find(item => item.valueCode === typeCode)
  return type ? type.valueName : typeCode
}

// 获取报价单状态名称
function getQuotationStatusName(statusCode: string | undefined) {
  if (!statusCode)
    return '-'
  const status = quotationStatusDict.value.find(item => item.valueCode === statusCode)
  return status ? status.valueName : statusCode
}

// 获取纸张单位名称
function getPaperUnitName(unitCode: string | undefined) {
  if (!unitCode)
    return '-'
  const unit = paperUnitDict.value.find(item => item.valueCode === unitCode)
  return unit ? unit.valueName : unitCode
}

// 关闭对话框
function handleClose() {
  emit('update:open', false)
}

// 获取报价单状态标签颜色
function getStatusColor(status: string | undefined): string {
  if (!status)
    return 'default'
  switch (status) {
    case 'DRAFT':
      return 'default'
    case 'SUBMITTED':
      return 'processing'
    case 'APPROVED':
      return 'success'
    case 'REJECTED':
      return 'error'
    case 'EXPIRED':
      return 'warning'
    default:
      return 'default'
  }
}

// 获取报价类型标签颜色
function getTypeColor(type: string | undefined): string {
  if (!type)
    return 'default'
  switch (type) {
    case 'CORRUGATED':
      return 'blue'
    case 'RAW':
      return 'green'
    default:
      return 'default'
  }
}

// 格式化百分比
function formatPercent(value: string | number | undefined) {
  if (value === undefined || value === null) {
    return '-'
  }
  // 将字符串转换为数字
  const numValue = typeof value === 'string' ? Number.parseFloat(value) : value
  return `${(numValue * 100).toFixed(2)}%`
}

onMounted(() => {
  fetchDictData()
  if (props.open && props.quotationId) {
    getQuotationDetail()
  }
})
</script>

<template>
  <a-modal
    title="报价单详情"
    :open="open"
    :footer="null"
    width="900px"
    :mask-closable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <a-descriptions bordered :column="3">
        <a-descriptions-item label="报价单号" :span="1">
          {{ quotationDetail.quotationNo || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="供应商名称" :span="2">
          {{ quotationDetail.supplierName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="报价日期" :span="1">
          {{ quotationDetail.quotationDate || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="报价类型" :span="1">
          <a-tag :color="getTypeColor(quotationDetail.quotationType)">
            {{ getQuotationTypeName(quotationDetail.quotationType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态" :span="1">
          <a-tag :color="getStatusColor(quotationDetail.status)">
            {{ getQuotationStatusName(quotationDetail.status) }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <a-divider orientation="left">
        计算参数
      </a-divider>
      <a-descriptions bordered :column="3">
        <a-descriptions-item label="供应商折扣率" :span="1">
          {{ formatPercent(quotationDetail.supplierDiscount) }}
        </a-descriptions-item>
        <a-descriptions-item label="税率" :span="1">
          {{ formatPercent(quotationDetail.taxRate) }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" :span="1">
          {{ quotationDetail.remark || '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <a-divider orientation="left">
        加工费用
      </a-divider>
      <a-descriptions bordered :column="3">
        <a-descriptions-item label="每平米2层加工费" :span="1">
          {{ quotationDetail.processingFeeTwoLayer ? `¥${quotationDetail.processingFeeTwoLayer}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="每平米3层加工费" :span="1">
          {{ quotationDetail.processingFeeThreeLayer ? `¥${quotationDetail.processingFeeThreeLayer}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="每平米4层加工费" :span="1">
          {{ quotationDetail.processingFeeFourLayer ? `¥${quotationDetail.processingFeeFourLayer}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="每平米5层加工费" :span="1">
          {{ quotationDetail.processingFeeFiveLayer ? `¥${quotationDetail.processingFeeFiveLayer}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="每平米6层加工费" :span="1">
          {{ quotationDetail.processingFeeSixLayer ? `¥${quotationDetail.processingFeeSixLayer}` : '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="每平米7层加工费" :span="1">
          {{ quotationDetail.processingFeeSevenLayer ? `¥${quotationDetail.processingFeeSevenLayer}` : '-' }}
        </a-descriptions-item>
      </a-descriptions>

      <a-divider orientation="left">
        纸价明细
      </a-divider>
      <a-table
        size="small"
        :data-source="quotationItems"
        :pagination="false"
        bordered
      >
        <a-table-column title="序号" align="center" width="80px">
          <template #default="{ index }">
            {{ index + 1 }}
          </template>
        </a-table-column>
        <a-table-column title="纸张描述" data-index="paperDescription" />
        <a-table-column title="单位" data-index="unit" width="100px">
          <template #default="{ record }">
            {{ getPaperUnitName(record.unit) }}
          </template>
        </a-table-column>
        <a-table-column title="单价(元)" data-index="unitPrice" width="120px">
          <template #default="{ record }">
            {{ record.unitPrice ? `¥${record.unitPrice}` : '-' }}
          </template>
        </a-table-column>
      </a-table>

      <a-divider orientation="left">
        审批信息
      </a-divider>
      <a-descriptions bordered :column="3">
        <a-descriptions-item label="审批人" :span="1">
          {{ quotationDetail.approvalUserName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="审批时间" :span="1">
          {{ quotationDetail.approvalTime || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="审批备注" :span="1">
          {{ quotationDetail.approvalRemark || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </a-spin>
  </a-modal>
</template>
