<script lang="ts" setup>
import type { PaperQuotationItemModel } from '@/api/business/paper-quotation-item/model/paper-quotation-item-model'
import type { PaperQuotationFormParam, PaperQuotationResult } from '@/api/business/paper-quotation/model/paper-quotation-form-model'
import type { Rule } from 'ant-design-vue/es/form'
import { paperQuotationApi } from '@/api/business/paper-quotation/paper-quotation-api'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { dictApi } from '@/api/support/dict-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import { smartSentry } from '@/lib/smart-sentry'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { computed, reactive, ref, watch } from 'vue'
import PaperQuotationDetailTables from './PaperQuotationDetailTables.vue'

// 定义类别选项类型
interface CategoryOption {
  value: string
  label: string
}

// 定义字典项类型
interface DictItem {
  valueCode: string
  valueName: string
  [key: string]: any
}

// 定义组件属性
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  formType: {
    type: String as () => 'add' | 'edit',
    default: 'add',
  },
  formData: {
    type: Object as () => Partial<PaperQuotationResult>,
    default: () => ({}),
  },
})

// 定义组件事件
const emit = defineEmits(['update:open', 'success'])

// 表单引用
const formRef = ref()

// 日期值
const dateValue = ref<dayjs.Dayjs | undefined>(undefined)

// 定义默认表单状态
const defaultFormState: Partial<PaperQuotationFormParam> = {
  supplierId: '',
  supplierName: '',
  quotationDate: '',
  quotationType: 'CORRUGATED',
  supplierDiscount: '0.88',
  taxRate: '0.94',
  processingFeeTwoLayer: '0',
  processingFeeThreeLayer: '0',
  processingFeeFourLayer: '0',
  processingFeeFiveLayer: '0',
  processingFeeSixLayer: '0',
  processingFeeSevenLayer: '0',
  remark: '',
  status: 'DRAFT',
  paperQuotationItemList: [],
  paperCategoryCorrugatedShape: '',
}

// 表单数据
const formState = reactive<Partial<PaperQuotationFormParam>>({ ...defaultFormState })

// 表单校验规则
const rules: Record<string, Rule[]> = {
  supplierId: [
    { required: true, message: '请选择供应商', trigger: 'change' },
  ],
  quotationDate: [
    { required: true, message: '请选择报价日期', trigger: 'change' },
  ],
  supplierDiscount: [
    { required: true, message: '请输入供应商折扣率', trigger: 'blur' },
  ],
  taxRate: [
    { required: true, message: '请输入税率', trigger: 'blur' },
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符', trigger: 'blur' },
  ],
}

// 对话框标题
const modalTitle = computed(() => {
  return props.formType === 'add' ? '新增报价单' : '编辑报价单'
})

// 表单提交按钮loading
const confirmLoading = ref(false)

// 供应商列表
const supplierList = ref<Array<{ id: string | number, supplierName: string }>>([])

// 所有可选类别
const allCategoryOptions = ref<CategoryOption[]>([])

// 监听表单数据变化
watch(
  () => props.formData,
  async (newVal) => {
    if (newVal) {
      // 确保已获取类别选项
      if (allCategoryOptions.value.length === 0) {
        await initCategoryOptions()
      }

      // 重置表单
      Object.assign(formState, { ...defaultFormState })

      // 填充表单数据
      const formData = { ...newVal }

      // 处理paperCategoryCorrugatedShape字段
      if (Array.isArray(formData.paperCategoryCorrugatedShape)) {
        formData.paperCategoryCorrugatedShape = JSON.stringify(formData.paperCategoryCorrugatedShape)
      }
      else if (typeof formData.paperCategoryCorrugatedShape === 'string' && formData.paperCategoryCorrugatedShape.startsWith('[')) {
        try {
          // 验证是否是有效的JSON字符串
          JSON.parse(formData.paperCategoryCorrugatedShape)
        }
        catch (e) {
          console.error('解析paperCategoryCorrugatedShape失败:', e)
          formData.paperCategoryCorrugatedShape = '[]'
        }
      }
      else {
        // 如果不是数组也不是JSON字符串，设置为空数组的字符串表示
        formData.paperCategoryCorrugatedShape = '[]'
      }

      Object.assign(formState, formData)

      // 处理日期格式
      if (newVal.quotationDate && typeof newVal.quotationDate === 'string') {
        formState.quotationDate = newVal.quotationDate
        // 设置日期选择器的值
        dateValue.value = dayjs(newVal.quotationDate)
      }
      else {
        dateValue.value = undefined
      }

      // 处理明细数据
      if (newVal.paperQuotationItemList && Array.isArray(newVal.paperQuotationItemList) && newVal.paperQuotationItemList.length > 0) {
        // 将paperQuotationItemList转换为带有额外字段的格式
        const items = newVal.paperQuotationItemList.map((item: any) => {
          // 从纸张描述中提取类别和规格信息
          let paperCategory = ''
          const corrugatedShape = ''

          // 尝试从描述中提取类别信息
          if (item.paperDescription) {
            // 从字符串解析类别数组
            let categoriesArray: string[] = []
            try {
              if (typeof formState.paperCategoryCorrugatedShape === 'string' && formState.paperCategoryCorrugatedShape) {
                categoriesArray = JSON.parse(formState.paperCategoryCorrugatedShape)
              }
            }
            catch (e) {
              console.error('解析类别数组失败:', e)
              categoriesArray = []
            }

            if (Array.isArray(categoriesArray)) {
              for (const category of categoriesArray) {
                // 查找字典中对应的类别名称
                const categoryOption = allCategoryOptions.value.find((opt: CategoryOption) => opt.value === category)
                if (categoryOption && item.paperDescription.includes(categoryOption.label)) {
                  paperCategory = category
                  break
                }
              }
            }
          }

          return {
            ...item,
            paperCategory, // 设置提取的类别
            corrugatedShape, // 设置提取的规格
          }
        })

        formState.paperQuotationItemList = items
      }
      // 如果没有明细数据，添加一个默认的空行
      else if (!formState.paperQuotationItemList || formState.paperQuotationItemList.length === 0) {
        formState.paperQuotationItemList = [
          {
            quotationId: 0,
            paperId: 0,
            paperDescription: '',
            paperCategory: '',
            corrugatedShape: '',
            unit: 'SQUARE_METER',
            unitPrice: 0,
            sort: 1,
            deletedFlag: 0,
          } as PaperQuotationItemModel,
        ]
      }
    }
  },
  { deep: true, immediate: true },
)

// 监听弹窗可见性
watch(
  () => props.open,
  (newVal) => {
    if (!newVal) {
      // 关闭弹窗时，完全清空表单
      resetFormToDefault()
    }
    else if (newVal && props.formType === 'add') {
      // 如果是打开弹窗且是新增模式，也要确保表单被清空
      resetFormToDefault()
    }
  },
)

// 监听表单类型变化
watch(
  () => props.formType,
  (newVal) => {
    if (newVal === 'add') {
      // 切换到新增模式时，重置表单
      resetFormToDefault()
    }
  },
)

// 重置表单到默认状态
function resetFormToDefault() {
  // 重置表单字段验证状态
  formRef.value?.resetFields()

  // 重置日期选择器
  dateValue.value = undefined

  // 清空当前表单状态
  for (const key in formState) {
    if (Object.prototype.hasOwnProperty.call(formState, key)) {
      delete (formState as any)[key]
    }
  }

  // 复制默认状态
  Object.assign(formState, JSON.parse(JSON.stringify(defaultFormState)))

  // 确保明细表有一个默认空行
  formState.paperQuotationItemList = [
    {
      quotationId: 0,
      paperId: 0,
      paperDescription: '',
      paperCategory: '',
      corrugatedShape: '',
      unit: 'SQUARE_METER',
      unitPrice: 0,
      sort: 1,
      deletedFlag: 0,
    } as PaperQuotationItemModel,
  ]

  // 重置类别与楞型关系
  formState.paperCategoryCorrugatedShape = '[]'
}

// 获取供应商列表
async function fetchSupplierList() {
  try {
    const res = await supplierApi.supplierList({
      supplierName: '',
      supplierType: '',
      status: '',
    })
    if (res.data && res.data) {
      supplierList.value = res.data as Array<{ id: string | number, supplierName: string }>
    }
  }
  catch (error) {
    smartSentry.captureError(error)
    console.error('获取供应商列表失败', error)
  }
}

// 选择供应商
function handleSupplierChange(value: any, option: any) {
  formState.supplierName = String(value)
  // 确保正确获取供应商ID
  if (option && option.id) {
    formState.supplierId = String(option.id)
  }
  else {
    // 如果option中没有id，从供应商列表中查找
    const selectedSupplier = supplierList.value.find(supplier => supplier.supplierName === value)
    if (selectedSupplier) {
      formState.supplierId = String(selectedSupplier.id)
    }
  }

  // 清空已有的纸价明细
  formState.paperQuotationItemList = []
}

// 关闭对话框
function handleCancel() {
  emit('update:open', false)
  // 重置表单状态在watch中处理
}

// 处理类别变化
function handleCategoriesChange(categories: string[]) {
  formState.paperCategoryCorrugatedShape = JSON.stringify(categories)
}

// 提交表单
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      // 验证明细表
      if (!formState.paperQuotationItemList || formState.paperQuotationItemList.length === 0) {
        message.error('请至少添加一条纸价明细')
        return
      }

      // 验证明细表的必填项
      const invalidItems = formState.paperQuotationItemList.filter((item: any) => {
        // 检查是否有纸张信息
        const hasPaperInfo = item.paperId > 0 || (item.paperCategory && item.paperDescription)

        // 检查单价是否有效（允许为0）
        const hasValidPrice = item.unitPrice !== undefined && item.unitPrice !== null && item.unitPrice !== ''

        return !hasPaperInfo || !hasValidPrice
      })

      if (invalidItems.length > 0) {
        message.error('请完善纸价明细信息')
        return
      }

      confirmLoading.value = true
      try {
        SmartLoading.show()

        // 准备提交的数据
        const submitData = { ...formState } as any // 使用any类型避免类型错误

        // 处理paperCategoryCorrugatedShape字段，确保是JSON字符串格式
        if (typeof submitData.paperCategoryCorrugatedShape !== 'string') {
          if (Array.isArray(submitData.paperCategoryCorrugatedShape)) {
            submitData.paperCategoryCorrugatedShape = JSON.stringify(submitData.paperCategoryCorrugatedShape)
          }
          else {
            submitData.paperCategoryCorrugatedShape = '[]'
          }
        }
        else if (!submitData.paperCategoryCorrugatedShape) {
          submitData.paperCategoryCorrugatedShape = '[]'
        }

        if (props.formType === 'add') {
          await paperQuotationApi.addPaperQuotation(submitData as PaperQuotationFormParam)
          message.success('新增报价单成功')
        }
        else {
          await paperQuotationApi.updatePaperQuotation(submitData as PaperQuotationFormParam)
          message.success('编辑报价单成功')
        }
        emit('success')
        handleCancel()
      }
      catch (error) {
        smartSentry.captureError(error)
        message.error(props.formType === 'add' ? '新增报价单失败' : '编辑报价单失败')
      }
      finally {
        confirmLoading.value = false
        SmartLoading.hide()
      }
    })
    .catch((error: any) => {
      console.error('表单校验失败:', error)
    })
}

// 处理日期变化
function handleDateChange(date: dayjs.Dayjs | string | undefined, dateString: string) {
  if (typeof date === 'string') {
    dateValue.value = dayjs(date)
  }
  else {
    dateValue.value = date
  }
  formState.quotationDate = dateString
}

// 初始化类别选项
async function initCategoryOptions() {
  try {
    // 使用系统的字典API获取paper_category_corrugated_shape的数据
    const dictRes = await dictApi.valueList('paper_category_corrugated_shape')
    if (dictRes && dictRes.data && Array.isArray(dictRes.data) && dictRes.data.length > 0) {
      allCategoryOptions.value = dictRes.data.map((item: DictItem) => ({
        value: item.valueCode,
        label: item.valueName,
      }))
    }
  }
  catch (error) {
    smartSentry.captureError(error)
    console.error('初始化类别选项失败', error)
  }
}

// 初始化
fetchSupplierList()
initCategoryOptions()
</script>

<template>
  <a-modal
    :title="modalTitle" :open="open" :confirm-loading="confirmLoading" width="1000px" :mask-closable="false"
    :destroy-on-close="true" @ok="handleSubmit" @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="供应商" name="supplierId">
            <a-select
              v-model:value="formState.supplierName" placeholder="请选择供应商"
              :options="supplierList.map(item => ({ value: item.supplierName, label: item.supplierName, id: item.id }))"
              :disabled="formType === 'edit'" @change="handleSupplierChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="报价日期" name="quotationDate">
            <a-date-picker
              v-model:value="dateValue" style="width: 100%" format="YYYY-MM-DD" placeholder="请选择报价日期"
              @change="handleDateChange"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="供应商折扣率" name="supplierDiscount">
            <a-input-number
              v-model:value="formState.supplierDiscount" style="width: 100%" :min="0" :max="1"
              :step="0.01" :precision="2" placeholder="请输入供应商折扣率"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="税率" name="taxRate">
            <a-input-number
              v-model:value="formState.taxRate" style="width: 100%" :min="0" :max="1" :step="0.01"
              :precision="2" placeholder="请输入税率"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="每平米2层加工费" name="processingFeeTwoLayer">
            <a-input-number
              v-model:value="formState.processingFeeTwoLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米2层加工费"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="每平米3层加工费" name="processingFeeThreeLayer">
            <a-input-number
              v-model:value="formState.processingFeeThreeLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米3层加工费"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="每平米4层加工费" name="processingFeeFourLayer">
            <a-input-number
              v-model:value="formState.processingFeeFourLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米4层加工费"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="每平米5层加工费" name="processingFeeFiveLayer">
            <a-input-number
              v-model:value="formState.processingFeeFiveLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米5层加工费"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="每平米6层加工费" name="processingFeeSixLayer">
            <a-input-number
              v-model:value="formState.processingFeeSixLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米6层加工费"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="每平米7层加工费" name="processingFeeSevenLayer">
            <a-input-number
              v-model:value="formState.processingFeeSevenLayer" style="width: 100%" :min="0" :step="0.01"
              :precision="2" placeholder="请输入每平米7层加工费"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formState.remark" :auto-size="{ minRows: 2, maxRows: 6 }" placeholder="请输入备注信息" />
      </a-form-item>

      <a-divider orientation="left">
        纸价明细
      </a-divider>

      <PaperQuotationDetailTables
        v-model="formState.paperQuotationItemList"
        @categories-change="handleCategoriesChange"
      />
    </a-form>
  </a-modal>
</template>
