<script lang="ts" setup>
import type { PageResult } from '@/api/base-model/page-model'
import type { SortItemModel } from '@/api/base-model/sort-item-model'
import type { PaperQuotationPageParam, PaperQuotationResult } from '@/api/business/paper-quotation/model/paper-quotation-form-model'
import type { TableColumnsType } from 'ant-design-vue'
import { paperQuotationApi } from '@/api/business/paper-quotation/paper-quotation-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import DictValue from '@/components/utils-old/dict-value/index.vue'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'

import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import { smartSentry } from '@/lib/smart-sentry'
import {
  DeleteOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { onMounted, reactive, ref } from 'vue'
import PaperQuotationDetail from './components/PaperQuotationDetail.vue'
import PaperQuotationForm from './components/PaperQuotationForm.vue'

// 表格列配置
const columns = ref<TableColumnsType>([
  {
    title: '报价单号',
    dataIndex: 'quotationNo',
    width: 180,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 200,
  },
  {
    title: '报价日期',
    dataIndex: 'quotationDate',
    width: 120,
  },
  {
    title: '报价类型',
    dataIndex: 'quotationType',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 180,
    fixed: 'right' as const,
  },
])

// 全选相关
const selectedRowKeyList = ref<number[]>([])
const selectedRowList = ref<PaperQuotationResult[]>([])

// 日期范围状态
const dateRange = ref<[string, string]>(['', ''])

// 表格相关
const tableLoading = ref<boolean>(false)
const tableData = ref<PaperQuotationResult[]>([])
const total = ref(0)

// 查询表单初始值
const queryFormState: PaperQuotationPageParam = {
  pageParam: {
    pageNum: 1,
    pageSize: 10,
  },
  // 添加必要的PaperQuotationModel属性
  quotationNo: '',
  supplierId: '',
  supplierName: '',
  quotationDate: '',
  quotationType: '',
  paperCategoryCorrugatedShape: '',
  status: '',
  // 添加查询用的时间范围属性
  quotationDateFrom: '',
  quotationDateTo: '',
  approvalTimeFrom: '',
  approvalTimeTo: '',
  createTimeFrom: '',
  createTimeTo: '',
  updateTimeFrom: '',
  updateTimeTo: '',
}

// 查询表单数据
const queryForm = reactive<PaperQuotationPageParam>({ ...queryFormState })

// 弹窗控制
const formVisible = ref(false)
const formType = ref<'add' | 'edit'>('add')
const formData = ref<Partial<PaperQuotationResult>>({})

// 查看详情弹窗控制
const detailVisible = ref(false)
const currentQuotationId = ref(0)

// 处理排序变更
function onChange(_pagination: any, _filters: any, sorter: any, { action }: { action: string }) {
  if (action === 'sort') {
    const { order, field } = sorter
    const column = camelToUnderscore(field)

    // 初始化排序列表（如果不存在）
    if (!queryForm.pageParam.sortItemList) {
      queryForm.pageParam.sortItemList = []
    }

    const findIndex = queryForm.pageParam.sortItemList.findIndex(e => e.column === column)

    if (findIndex !== -1) {
      queryForm.pageParam.sortItemList.splice(findIndex, 1)
    }

    if (order) {
      const asc = order === 'ascend'
      queryForm.pageParam.sortItemList.push({
        column,
        asc,
      } as SortItemModel)
    }
    queryData()
  }
}

// 打开添加对话框
function openAddModal() {
  formType.value = 'add'
  formData.value = {}
  formVisible.value = true
}

// 打开编辑对话框
function openEditModal(record: PaperQuotationResult) {
  formType.value = 'edit'
  // 先显示加载中的状态
  formVisible.value = true
  SmartLoading.show()

  // 调用API获取详细数据
  paperQuotationApi.getPaperQuotationDetail(record.id as number)
    .then((res) => {
      if (res.data) {
        formData.value = res.data
      }
    })
    .catch((error) => {
      smartSentry.captureError(error)
      message.error('获取报价单详情失败')
      formVisible.value = false
    })
    .finally(() => {
      SmartLoading.hide()
    })
}

// 表单提交成功回调
function handleFormSuccess() {
  queryData()
}

// 处理查看详情
function handleViewDetail(record: PaperQuotationResult) {
  currentQuotationId.value = record.id || 0
  detailVisible.value = true
}

// 处理删除
function handleDelete(record: PaperQuotationResult) {
  Modal.confirm({
    title: '确认删除',
    icon: () => h(ExclamationCircleOutlined),
    content: `确定要删除报价单 "${record.quotationNo}" 吗？`,
    okText: '删除',
    okType: 'danger',
    onOk: async () => {
      try {
        SmartLoading.show()
        await paperQuotationApi.deletePaperQuotation(record.id as number)
        message.success('删除成功')
        queryData()
      }
      catch (error) {
        smartSentry.captureError(error)
        message.error('删除失败')
      }
      finally {
        SmartLoading.hide()
      }
    },
    cancelText: '取消',
    onCancel() { },
  })
}

// 批量删除
function confirmBatchDelete() {
  if (selectedRowKeyList.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    icon: () => h(ExclamationCircleOutlined),
    content: `确定要删除选中的 ${selectedRowKeyList.value.length} 条报价单吗？`,
    okText: '删除',
    okType: 'danger',
    onOk: async () => {
      try {
        SmartLoading.show()
        await paperQuotationApi.batchDeletePaperQuotation(selectedRowKeyList.value)
        message.success('批量删除成功')
        selectedRowKeyList.value = []
        queryData()
      }
      catch (error) {
        smartSentry.captureError(error)
        message.error('批量删除失败')
      }
      finally {
        SmartLoading.hide()
      }
    },
    cancelText: '取消',
    onCancel() { },
  })
}

// 选择行变更
function onSelectChange(selectedRowKeys: (string | number)[], selectedRows: PaperQuotationResult[]) {
  selectedRowKeyList.value = selectedRowKeys as number[]
  selectedRowList.value = selectedRows
}

// 重置查询条件
function resetQuery() {
  const pageSize = queryForm.pageParam.pageSize
  Object.assign(queryForm, { ...queryFormState })
  queryForm.pageParam.pageSize = pageSize
  queryData()
}

// 搜索
function onSearch() {
  queryForm.pageParam.pageNum = 1
  // 更新查询条件中的日期
  queryForm.quotationDateFrom = dateRange.value[0]
  queryForm.quotationDateTo = dateRange.value[1]
  queryData()
}

// 查询数据
async function queryData() {
  tableLoading.value = true
  try {
    const params = {
      ...queryForm,
      pageParam: {
        ...queryForm.pageParam,
        sortItemList: queryForm.pageParam.sortItemList || [],
      },
    } as PaperQuotationPageParam
    const queryResult = await paperQuotationApi.paperQuotationPage(params)
    if (queryResult.data) {
      if (typeof queryResult.data === 'object' && 'list' in queryResult.data && Array.isArray(queryResult.data.list)) {
        tableData.value = (queryResult.data as PageResult<PaperQuotationResult>).list
        total.value = (queryResult.data as PageResult<PaperQuotationResult>).total
      }
      else if (Array.isArray(queryResult.data)) {
        tableData.value = queryResult.data
        total.value = queryResult.data.length
      }
      else {
        tableData.value = []
        total.value = 0
      }
    }
  }
  catch (error) {
    smartSentry.captureError(error)
    message.error('查询报价单失败')
  }
  finally {
    tableLoading.value = false
  }
}

// 工具函数：驼峰转下划线
function camelToUnderscore(str: string) {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase()
}
// 初始化查询
onMounted(queryData)
</script>

<template>
  <!---------- 查询表单 begin ----------->
  <a-form v-privilege="'paperQuotation:query'" class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="报价单号" class="smart-query-form-item">
        <a-input v-model:value="queryForm.quotationNo" style="width: 200px" placeholder="请输入报价单号" allow-clear />
      </a-form-item>

      <a-form-item label="供应商名称" class="smart-query-form-item">
        <a-input v-model:value="queryForm.supplierName" style="width: 200px" placeholder="请输入供应商名称" allow-clear />
      </a-form-item>

      <a-form-item label="报价日期" class="smart-query-form-item">
        <a-range-picker v-model:value="dateRange" style="width: 240px" :placeholder="['开始日期', '结束日期']" />
      </a-form-item>

      <a-form-item label="报价类型" class="smart-query-form-item">
        <DictSelect
          v-model:value="queryForm.quotationType" key-code="quotation_type" placeholder="请选择报价类型"
          width="150px"
        />
      </a-form-item>

      <a-form-item label="状态" class="smart-query-form-item">
        <DictSelect v-model:value="queryForm.status" key-code="quotation_status" placeholder="请选择状态" width="150px" />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单 end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button v-privilege="'paperQuotation:add'" type="primary" @click="openAddModal">
          <template #icon>
            <PlusOutlined />
          </template>
          新增
        </a-button>

        <a-button
          v-privilege="'paperQuotation:delete'" danger :disabled="selectedRowKeyList.length === 0"
          @click="confirmBatchDelete"
        >
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :table-id="TABLE_ID_CONST.BUSINESS.ERP.PAPER_QUOTATION" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small" :data-source="tableData" :columns="columns" row-key="id" bordered :pagination="false"
      :loading="tableLoading" :scroll="{ x: 1500 }"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }" @change="onChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 报价类型列 -->
        <template v-if="column.dataIndex === 'quotationType'">
          <DictValue dict-key="quotation_type" :value-code="record.quotationType" />
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag
            :color="record.status === 'APPROVED' ? 'success'
              : record.status === 'DRAFT' ? 'default'
                : record.status === 'SUBMITTED' ? 'processing'
                  : record.status === 'REJECTED' ? 'error'
                    : record.status === 'EXPIRED' ? 'warning' : 'default'
            "
          >
            <DictValue dict-key="quotation_status" :value-code="record.status" />
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button
              v-privilege="'paperQuotation:update'" type="link"
              @click="openEditModal(record as PaperQuotationResult)"
            >
              编辑
            </a-button>
            <a-button
              v-privilege="'paperQuotation:query'" type="link"
              @click="handleViewDetail(record as PaperQuotationResult)"
            >
              查看
            </a-button>
            <a-dropdown :trigger="['click']">
              <a class="ant-dropdown-link" @click.prevent>
                更多
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-button
                      v-privilege="'paperQuotation:delete'" danger type="link"
                      @click="handleDelete(record as PaperQuotationResult)"
                    >
                      删除
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </template>
      </template>
    </a-table>

    <div class="table-pagination">
      <a-pagination
        v-model:current="queryForm.pageParam.pageNum" v-model:page-size="queryForm.pageParam.pageSize"
        show-size-changer show-quick-jumper show-less-items :page-size-options="PAGE_SIZE_OPTIONS"
        :default-page-size="queryForm.pageParam.pageSize" :total="total" :show-total="(total) => `共${total}条`"
        @change="queryData" @show-size-change="queryData"
      />
    </div>

    <!-- 新增/编辑表单弹窗 -->
    <PaperQuotationForm
      v-model:open="formVisible" :form-data="formData" :form-type="formType"
      @success="handleFormSuccess"
    />

    <!-- 查看详情弹窗 -->
    <PaperQuotationDetail v-model:open="detailVisible" :quotation-id="currentQuotationId" />
  </a-card>
</template>
