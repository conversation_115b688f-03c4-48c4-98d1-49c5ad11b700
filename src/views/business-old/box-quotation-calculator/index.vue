<script lang="ts" setup>
import { ref } from 'vue'
import QuoteCalculator from './components/QuoteCalculator.vue'
import QuoteCalculatorModal from './components/QuoteCalculatorModal.vue'

// 控制全屏计算器显示
const showFullScreenCalculator = ref(false)
</script>

<template>
  <div class="mx-auto container ">
    <div class="calculator-wrapper">
      <QuoteCalculator />
      <!-- 全屏计算器弹窗 -->
      <QuoteCalculatorModal v-model="showFullScreenCalculator" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.new-calculator-container {
  display: grid;
  margin: 0 auto;
  grid-template-columns: 560px 400px 400px;
  grid-gap: 16px;
}

.container {
  max-width: 100%;
  padding: 0 1rem;
}

.calculator-wrapper {
  min-height: calc(100vh - 120px);
  overflow-x: auto;
}

.calculator-content {
  min-width: fit-content;
  padding-bottom: 16px;
}
</style>
