import type { BoxCalculatorData, PaperBoard } from '../../utils/types'
import { BoxService } from '@/views/business-old/box-quotation-calculator/service/box-service'
import { computed, ref, watch } from 'vue'
import { formatNumber, PRECISION_CONFIG } from '../../utils/format-utils'

const boxConfigService = BoxService.getInstance()
export function initPriceWatchers(form: BoxCalculatorData) {
  const boxQuantity = computed(() => Number(form.boxQuantity || 1))

  // 使用ref存储原始水印价格基础值
  const originalWatermarkPrice = ref(form.watermarkPrice || 0)

  // 计算水印价格基于颜色数量的附加费用
  const watermarkPriceComputed = computed(() => {
    const { watermarkColors } = form
    const basePrice = originalWatermarkPrice.value
    if (!basePrice) {
      return 0
    }
    return basePrice + ((Number(watermarkColors) || 0) * 0.05)
  })

  // 水印费用计算
  const watermarkCost = computed(() => {
    // 注意这里使用计算出的值
    if (!watermarkPriceComputed.value || !form.boxArea) {
      return { unitPrice: 0, totalPrice: 0 }
    }
    const unitPrice = watermarkPriceComputed.value * form.boxArea

    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 模切费用计算
  const dieCuttingCost = computed(() => {
    if (!form.dieCuttingQuantity || !form.dieCuttingPrice) {
      return { unitPrice: 0, totalPrice: 0 }
    }

    // 添加修边位系数计算
    // 修边位系数 = 1 + ((修边位 - 12) * 0.02)
    const edgeCutFactor = form.trimPosition ? 1 + ((Number(form.trimPosition) - 12) * 0.02) : 1

    // 模切费用 = 模切单价 / 模切数量 * 修边位系数
    const unitPrice = form.dieCuttingPrice / Number(form.dieCuttingQuantity) * edgeCutFactor
    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 印版费用计算
  const printingPlateCost = computed(() => {
    if (!form.printingPlatePrice || !boxConfigService.boxConfig.needPrintingPlate) {
      return { unitPrice: 0, totalPrice: 0 }
    }
    const unitPrice = form.printingPlatePrice * Number(form.boxArea)
    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 刀版费用计算
  const dieCuttingPlateCost = computed(() => {
    if (!form.dieCuttingPlatePrice || !boxConfigService.boxConfig.needDieCutting) {
      return { unitPrice: 0, totalPrice: 0 }
    }
    const unitPrice = form.dieCuttingPlatePrice * Number(form.boxArea)
    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 封箱费用计算
  const boxClosingCost = computed(() => {
    const boxClosingUnitPrice = form.boxClosingType === 'nailing' ? form.boxNailingUnitPrice : form.boxGluingUnitPrice

    if (!boxClosingUnitPrice) {
      return { unitPrice: 0, totalPrice: 0 }
    }
    return {
      unitPrice: formatNumber(boxClosingUnitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(boxClosingUnitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 纸板费用计算
  const paperboardCost = computed(() => {
    const { paperboardList, includeTax, taxRate = 0.94, boxQuantity = 1 } = form
    if (!paperboardList?.length) {
      return { unitPrice: 0, totalPrice: 0 }
    }
    const totalCost = paperboardList.reduce((sum: number, paper: PaperBoard) =>
      sum + (paper.squareMeterPrice || 0), 0)
    const unitPrice = !includeTax ? totalCost * taxRate : totalCost
    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * Number(boxQuantity), PRECISION_CONFIG.price),
    }
  })

  // 印刷原纸费用计算
  const rawPaperCost = computed(() => {
    const { printingMethod, rawPaper, corrugatedArea = 0 } = form
    if (printingMethod !== 'colorPrint' || !rawPaper) {
      return { unitPrice: 0, totalPrice: 0 }
    }

    const unitPrice = corrugatedArea * (rawPaper.squareMeterPrice || 0)
    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 印刷工艺费用计算
  const printingProcessCost = computed(() => {
    const { printingMethod, printingProcessList, corrugatedArea = 0 } = form
    if (printingMethod !== 'colorPrint' || !printingProcessList?.length) {
      return { unitPrice: 0, totalPrice: 0 }
    }

    const totalProcessPrice = printingProcessList.reduce(
      (sum: number, process: { price?: number }) => {
        if (typeof process === 'string') {
          return sum
        }
        return sum + Number(process.price || 0)
      },
      0,
    )
    const unitPrice = corrugatedArea * totalProcessPrice

    return {
      unitPrice: formatNumber(unitPrice, PRECISION_CONFIG.price),
      totalPrice: formatNumber(unitPrice * boxQuantity.value, PRECISION_CONFIG.price),
    }
  })

  // 合计费用计算 - 使用computed值避免循环依赖
  const boxCost = computed(() => {
    let total = 0

    // 直接使用computed的值而不是form中的值
    total += Number(paperboardCost.value.unitPrice) || 0
    total += Number(rawPaperCost.value.unitPrice) || 0
    total += Number(dieCuttingCost.value.unitPrice) || 0
    total += Number(watermarkCost.value.unitPrice) || 0
    total += Number(boxClosingCost.value.unitPrice) || 0
    total += Number(printingProcessCost.value.unitPrice) || 0
    total += Number(printingPlateCost.value.unitPrice) || 0
    total += Number(dieCuttingPlateCost.value.unitPrice) || 0
    // 应用供应商折扣

    return {
      unitPrice: formatNumber(total, PRECISION_CONFIG.price),
      totalPrice: formatNumber(total * Number(boxQuantity.value), PRECISION_CONFIG.price),
    }
  })

  // 统一监听并更新所有价格
  const stopPriceWatch = watch(
    [
      watermarkPriceComputed,
      watermarkCost,
      dieCuttingCost,
      boxClosingCost,
      paperboardCost,
      rawPaperCost,
      printingPlateCost,
      dieCuttingPlateCost,
      printingProcessCost,
      boxCost,
    ],
    ([
      _newWatermarkPriceComputed,
      newWatermarkCost,
      newDieCuttingCost,
      newBoxClosingCost,
      newPaperboardCost,
      newRawPaperCost,
      newPrintingPlateCost,
      newDieCuttingPlateCost,
      newPrintingProcessCost,
      newBoxCost,
    ]) => {
      // 批量更新所有价格，减少重复渲染
      Object.assign(form, {
        watermarkPrice: _newWatermarkPriceComputed,
        watermarkUnitPrice: newWatermarkCost.unitPrice,
        watermarkTotalPrice: newWatermarkCost.totalPrice,
        dieCuttingUnitPrice: newDieCuttingCost.unitPrice,
        dieCuttingTotalPrice: newDieCuttingCost.totalPrice,
        boxClosingUnitPrice: newBoxClosingCost.unitPrice,
        boxClosingTotalPrice: newBoxClosingCost.totalPrice,
        paperboardUnitPrice: newPaperboardCost.unitPrice,
        paperboardTotalPrice: newPaperboardCost.totalPrice,
        rawPaperUnitPrice: newRawPaperCost.unitPrice,
        rawPaperTotalPrice: newRawPaperCost.totalPrice,
        printingProcessUnitPrice: newPrintingProcessCost.unitPrice,
        printingProcessTotalPrice: newPrintingProcessCost.totalPrice,
        boxUnitPrice: newBoxCost.unitPrice,
        boxTotalPrice: newBoxCost.totalPrice,
        printingPlateUnitPrice: newPrintingPlateCost.unitPrice,
        printingPlateTotalPrice: newPrintingPlateCost.totalPrice,
        dieCuttingPlateUnitPrice: newDieCuttingPlateCost.unitPrice,
        dieCuttingPlateTotalPrice: newDieCuttingPlateCost.totalPrice,
      })
    },
    { immediate: true },
  )

  // 初始化时，保存原始的水印价格基础值
  if (form.watermarkPrice) {
    originalWatermarkPrice.value = form.watermarkPrice
  }

  // 监听watermarkColors变化，更新watermarkPrice
  const stopColorsWatch = watch(
    () => form.watermarkColors,
    (newColors) => {
      if (originalWatermarkPrice.value) {
        form.watermarkPrice = originalWatermarkPrice.value + ((Number(newColors) || 0) * 0.05)
      }
    },
  )

  // 返回清理函数
  return () => {
    stopPriceWatch()
    stopColorsWatch()
  }
}
