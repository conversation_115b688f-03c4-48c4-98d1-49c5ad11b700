import type { BoxCalculatorData, PaperBoard } from '../../utils/types'
import { BoxService } from '@/views/business-old/box-quotation-calculator/service/box-service'
import { computed, watch, watchEffect } from 'vue'
import { formatNumber } from '../../utils/format-utils'
import { DEFAULT_FORMULAS, FormulaEngine } from '../../utils/formula/formula_engine'
import { calculateCorrugatedWidth } from '../../utils/ups_calculate'
import { useCalculatorConfig } from '../calculatorConfig'

const boxConfigService = BoxService.getInstance()

export function initDimensionWatchers(form: BoxCalculatorData) {
  const config = useCalculatorConfig()

  // 基础尺寸计算 - 使用配置的公式
  const totalLength = computed(() => {
    if (!form.boxLength || !form.boxWidth || !form.boxHeight) {
      return 0
    }
    // 优先使用选中箱型的公式，如果没有则使用默认公式
    const formula = boxConfigService.boxConfig?.totalLengthFormula || DEFAULT_FORMULAS.totalLength

    // 使用公式引擎计算总长
    const result = FormulaEngine.evaluate(formula, form)
    return formatNumber(result)
  })

  const boxDoorWidth = computed(() => {
    if (!form.boxLength || !form.boxWidth || !form.boxHeight) {
      return 0
    }

    // 优先使用选中箱型的公式，如果没有则使用默认公式
    const formula = boxConfigService.boxConfig?.doorWidthFormula || DEFAULT_FORMULAS.boxDoorWidth

    // 使用公式引擎计算门宽
    const result = FormulaEngine.evaluate(formula, form)
    return formatNumber(result)
  })

  const boxArea = computed(() => {
    const tLength = totalLength.value
    const doorWidth = boxDoorWidth.value
    if (!tLength || !doorWidth) {
      return 0
    }

    // 使用统一的格式化函数，保留指定小数位
    return formatNumber(tLength * doorWidth / 1000000)
  })

  const boxVolume = computed(() => {
    const { boxLength, boxWidth, boxHeight } = form
    if (!boxLength || !boxWidth || !boxHeight) {
      return 0
    }

    // 使用统一的格式化函数，保留指定小数位
    return formatNumber(Number(boxLength) * Number(boxWidth) * Number(boxHeight) / 1000000000)
  })

  const corrugatedArea = computed(() => {
    if (!boxArea.value || !form.corrugatedCount) {
      return 0
    }

    // 使用统一的格式化函数，保留指定小数位
    return formatNumber(Number(boxArea.value) / Number(form.corrugatedCount))
  })

  // 统一监听并更新所有计算结果
  const stopDimensionWatch = watch(
    [totalLength, boxDoorWidth, boxArea, boxVolume, corrugatedArea],
    ([newTotalLength, newBoxDoorWidth, newBoxArea, newBoxVolume, newCorrugatedArea]) => {
      Object.assign(form, {
        totalLength: newTotalLength,
        boxDoorWidth: newBoxDoorWidth,
        boxArea: newBoxArea,
        boxVolume: newBoxVolume,
        corrugatedArea: newCorrugatedArea,
      })
    },
    { immediate: true },
  )

  // 监听基础尺寸变化，计算瓦纸门宽
  const stopDoorWidthWatch = watchEffect(() => {
    if (form.boxDoorWidth && form.boxDoorWidth > 0) {
      calculateCorrugatedWidth()
    }
  })

  // 计算模切门宽
  const stopDieCuttingDoorWidthWatch = watch(
    () => [form.boxDoorWidth, form.dieCuttingQuantity, form.trimPosition],
    ([boxDoorWidth, dieCuttingQuantity, trimPosition]) => {
      if (boxDoorWidth && dieCuttingQuantity) {
        // 使用公式: boxDoorWidth × dieCuttingQuantity + trimPosition
        const doorWidth = Number(boxDoorWidth) * Number(dieCuttingQuantity) + Number(trimPosition || 0)
        form.dieCuttingDoorWidth = formatNumber(doorWidth)
      }
    },
    { immediate: true },
  )

  // 摇盖加分计算
  const stopWaveCapWatch = watch(
    () => form.paperboardLayerCount,
    (newCount) => {
      if (newCount) {
        form.waveCapAddition = newCount * 2
      }
    },
    { immediate: true },
  )

  // 瓦纸宽度范围计算
  const stopWidthRangeWatch = watch(
    () => form.paperboardList,
    (newPaperboardList) => {
      if (!newPaperboardList?.length) {
        form.corrugatedWidthRange = config.getCalculatorDefaults().corrugatedWidthRange ?? ''
        return
      }

      const corrugatedPapers = newPaperboardList.filter((p: PaperBoard) => p.corrugatedShape)
      if (!corrugatedPapers.length) {
        form.corrugatedWidthRange = config.getCalculatorDefaults().corrugatedWidthRange ?? ''
        return
      }

      const isBCType = corrugatedPapers.every((p: PaperBoard) => ['B', 'C'].includes(p.corrugatedShape ?? ''))
      form.corrugatedWidthRange = isBCType ? '1450-2800mm' : '1450-2500mm'

      // 当纸板列表变化时，重新计算瓦纸门宽
      if (form.boxDoorWidth && form.boxDoorWidth > 0) {
        calculateCorrugatedWidth()
      }
    },
    { immediate: true, deep: true },
  )

  // 监听箱型变化，当箱型变化时重新计算尺寸
  const stopBoxWatcher = watch(
    () => boxConfigService.selectedBox,
    (newBox) => {
      if (newBox && form.boxLength && form.boxWidth && form.boxHeight) {
        // 如果已有尺寸，主动触发一次计算更新
        form.totalLength = totalLength.value
        form.boxDoorWidth = boxDoorWidth.value
        form.boxArea = boxArea.value
        form.boxVolume = boxVolume.value
      }
    },
    { immediate: true, deep: true },
  )

  // 返回清理函数
  return () => {
    stopDimensionWatch()
    stopWaveCapWatch()
    stopWidthRangeWatch()
    stopDoorWidthWatch()
    stopDieCuttingDoorWidthWatch()
    stopBoxWatcher()
  }
}
