import type { BoxCalculatorData } from '../utils/types'
import { reactive } from 'vue'
import { useCalculatorConfig } from './calculatorConfig'
import { initDimensionWatchers } from './watchers/dimension_watcher'
import { initPriceWatchers } from './watchers/price_watcher'

class CalculatorStore {
  readonly form: BoxCalculatorData
  private disposers: (() => void)[] = []

  private static instance: CalculatorStore | null = null

  // 单例模式获取实例的静态方法
  static getInstance(): CalculatorStore {
    if (!this.instance) {
      this.instance = new CalculatorStore()
    }
    return this.instance
  }

  private constructor() {
    // 初始化基础表单数据
    this.form = reactive(this.getBaseData())

    // 直接初始化计算器
    this.init()
  }

  // 初始化计算器
  private async init() {
    try {
      // 配置已在自己的构造函数中初始化，这里直接使用
      this.initForm()

      // 初始化计算逻辑
      this.initCalculation()
    }
    catch (error: unknown) {
      console.error('初始化计算器失败', error)
    }
  }

  // 初始化表单数据
  private initForm() {
    const config = useCalculatorConfig()
    const boxClosingType = this.form.boxClosingType || 'nailing'

    // 合并默认值到表单，保留现有值
    Object.assign(this.form, {
      ...this.getBaseData(),
      ...config.getCalculatorDefaults(),
      boxClosingType,
      boxClosingUnitPrice: boxClosingType === 'nailing'
        ? config.getCalculatorDefaults().boxNailingUnitPrice
        : config.getCalculatorDefaults().boxGluingUnitPrice,
    })
  }

  // 只包含基础数据的初始状态
  private getBaseData(): BoxCalculatorData {
    return {
      printingMethod: 'watermark',
      watermarkColors: 0,
      paperboardLayerCount: 3,
      corrugatedCount: 1,
      facePaperCount: 1,
      taxRate: 0.94,
      includeTax: true,
      boxClosingType: 'nailing',
      supplierDiscount: 1,
      paperboardList: [],
      printingProcessList: [],
    }
  }

  // 添加清理函数
  private addDisposer(disposer: () => void) {
    this.disposers.push(disposer)
  }

  // 清理所有监听器
  private clearDisposers() {
    this.disposers.forEach(dispose => dispose?.())
    this.disposers = []
  }

  // 初始化计算逻辑
  private initCalculation() {
    try {
      // 清理之前的监听
      this.clearDisposers()

      // 初始化新的监听，传递包装后的对象
      const dimensionStop = initDimensionWatchers(this.form)
      const priceStop = initPriceWatchers(this.form)

      // 保存清理函数
      if (dimensionStop)
        this.addDisposer(dimensionStop)
      if (priceStop)
        this.addDisposer(priceStop)
    }
    catch (error: unknown) {
      console.error('初始化计算逻辑失败', error)
    }
  }
}

// 导出获取单例的函数
function useCalculatorStore() {
  return CalculatorStore.getInstance()
}

export function useCalculatorForm() {
  return useCalculatorStore().form
}
