import type { NumberConfig } from '@/api/business/param-json/model/param-config'
import type { BoxCalculatorData } from '../utils/types'
import { paramJsonApi } from '@/api/business/param-json/param-json-api'
import { reactive } from 'vue'

// 数字类型的配置默认值
const DEFAULT_NUMBER_VALUES: Partial<Record<keyof BoxCalculatorData, number>> = {
  watermarkPrice: 0.2,
  boxNailingUnitPrice: 0.5,
  boxGluingUnitPrice: 0.3,
}

// 其他类型的配置默认值
const DEFAULT_OTHER_VALUES: Partial<BoxCalculatorData> = {
  corrugatedWidthRange: '1450-2500mm',
}

class CalculatorConfigStore {
  private static instance: CalculatorConfigStore | null = null
  readonly configs: NumberConfig[]
  private initialized = false

  private constructor() {
    this.configs = reactive([])
    // 使用同步API获取配置
    try {
      const res = paramJsonApi.getBoxQuotationParamSync()
      if (res.success && res.data) {
        this.configs.push(...res.data)
        this.initialized = true
      }
      else {
        throw new Error('获取配置失败')
      }
    }
    catch (error) {
      console.error('初始化配置失败', error)
      throw error
    }
  }

  static getInstance(): CalculatorConfigStore {
    if (!this.instance) {
      this.instance = new CalculatorConfigStore()
    }
    return this.instance
  }

  // 获取配置是否已初始化
  get isInitialized(): boolean {
    return this.initialized
  }

  // 获取计算器默认值
  getCalculatorDefaults(): Partial<BoxCalculatorData> {
    if (!this.initialized) {
      throw new Error('配置未初始化')
    }
    return {
      tongueLength: this.getDefaultValue('tongueLength'),
      trimPosition: this.getDefaultValue('trimPosition'),
      watermarkPrice: this.getDefaultValue('watermarkPrice') ?? DEFAULT_NUMBER_VALUES.watermarkPrice,
      waveCapAddition: this.getDefaultValue('waveCapAddition'),
      dieCuttingPrice: this.getDefaultValue('dieCuttingUnitPrice'),
      boxNailingUnitPrice: this.getDefaultValue('boxNailingUnitPrice') ?? DEFAULT_NUMBER_VALUES.boxNailingUnitPrice,
      boxGluingUnitPrice: this.getDefaultValue('boxGluingUnitPrice') ?? DEFAULT_NUMBER_VALUES.boxGluingUnitPrice,
      dieCuttingPlatePrice: this.getDefaultValue('dieCuttingPlatePrice'),
      printingPlatePrice: this.getDefaultValue('printingPlatePrice'),
      ...DEFAULT_OTHER_VALUES,
    }
  }

  // 获取输入组件配置
  getInputConfig(code: string) {
    const config = this.getConfigByCode(code)
    if (!config)
      return null

    return {
      min: config.min,
      max: config.max,
      step: config.step,
      precision: config.decimalPlaces,
    }
  }

  // 通过代码获取配置
  private getConfigByCode(code: string): NumberConfig | undefined {
    if (!code || !this.configs || this.configs.length === 0)
      return undefined
    return this.configs.find(p => p.code === code)
  }

  // 获取参数默认值
  private getDefaultValue(code: string): number | undefined {
    const config = this.getConfigByCode(code)
    return config?.value
  }

  // 获取所有配置的 getter
  get allConfigs(): NumberConfig[] {
    return this.configs
  }

  // 获取配置参数
  async fetchParams(): Promise<NumberConfig[]> {
    try {
      const res = await paramJsonApi.getBoxQuotationParam()
      if (res.success && res.data) {
        this.configs.length = 0
        this.configs.push(...res.data)
        return this.configs
      }
      throw new Error('获取配置失败')
    }
    catch (error) {
      console.error('获取配置失败', error)
      throw error
    }
  }

  // 保存配置参数
  async saveParams(params: NumberConfig[]): Promise<boolean> {
    if (!params || !Array.isArray(params) || params.length === 0) {
      throw new Error('保存配置时参数无效')
    }

    try {
      const res = await paramJsonApi.updateBoxQuotationParam(params)
      if (res.success) {
        this.configs.length = 0
        this.configs.push(...params)
        return true
      }
      throw new Error('保存配置失败')
    }
    catch (error) {
      console.error('保存配置失败', error)
      throw error
    }
  }
}

// 导出单例实例
const calculatorConfig = CalculatorConfigStore.getInstance()

// 导出使用函数
export function useCalculatorConfig() {
  return calculatorConfig
}
