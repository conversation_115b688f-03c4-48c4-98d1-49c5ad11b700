/**
 * 精度配置
 */
export const PRECISION_CONFIG = {
  // 尺寸计算结果精度 (如面积、体积等)
  dimension: 5,
  // 价格计算结果精度
  price: 3,
}

/**
 * 格式化数字，保留指定小数位数
 * @param value 要格式化的数值
 * @param precision 保留的小数位数，默认使用尺寸精度
 * @returns 格式化后的数值
 */
export function formatNumber(value: number, precision: number = PRECISION_CONFIG.dimension): number {
  return Number(value.toFixed(precision))
}

/**
 * 格式化价格，保留两位小数
 * @param value 要格式化的价格
 * @returns 格式化后的价格
 */
export function formatPrice(value: number): number {
  return formatNumber(value, PRECISION_CONFIG.price)
}
