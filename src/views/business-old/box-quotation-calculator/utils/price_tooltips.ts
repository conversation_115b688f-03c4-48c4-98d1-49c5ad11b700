import { computed } from 'vue'
import { useCalculatorForm } from '../stores/calculatorStore'

// 价格相关的tooltip提示
export const priceTooltips = computed(() => {
  const form = useCalculatorForm()

  return {
    // 水印费用
    watermarkUnitPrice: `水印费用 = 瓦纸面积(${form.corrugatedArea}㎡) × 水印单价(${form.watermarkPrice}元/㎡)`,

    // 模切费用
    dieCuttingCost: `模切费用 = 模切单价(${form.dieCuttingPrice}元/张) ÷ 模切数量(${form.dieCuttingQuantity ? Number(form.dieCuttingQuantity) || 1 : '未选择'}) ${form.dieCuttingQuantity && Number(form.dieCuttingQuantity) !== 0 ? `× 修边位系数(${(1 + ((Number(form.trimPosition) - 12) * 0.02))})` : ''}`,

    // 封箱费用
    boxClosingCost: `封箱单价 = ${form.boxClosingType === 'nailing' ? '钉箱单价' : '粘箱单价'}`,

    // 纸板费用
    paperboardCost: `纸板费用计算公式：纸板费用总和${form.includeTax ? ' × 税率' : ''}`,

    // 原纸费用
    rawPaperCost: `原纸费用 = 瓦纸面积(${form.corrugatedArea}㎡) × 原纸单价(${form.rawPaper?.squareMeterPrice || 0}元/㎡)`,

    // 印刷工艺费用
    printingProcessCost: `印刷工艺费用 = 瓦纸面积(${form.corrugatedArea}㎡) × 工艺单价总和(${(form.printingProcessList || []).reduce(
      (sum, process) => sum + Number(process.price || 0),
      0,
    )}元/㎡)`,
  }
})
