// 中英文字段映射表
export const FIELD_MAPPING = {
  // 基本尺寸
  长: 'boxLength',
  宽: 'boxWidth',
  高: 'boxHeight',
  舌头长度: 'tongueLength',
  修边位: 'trimPosition',
  摇盖加分: 'waveCapAddition',
  纸板层数: 'paperboardLayerCount',
  瓦楞数: 'corrugatedCount',
  面积: 'boxArea',
  体积: 'boxVolume',
  总长: 'totalLength',
  门宽: 'boxDoorWidth',

  // 价格相关字段
  水印颜色数: 'watermarkColors',
  水印单价: 'watermarkPrice',
  模切单价: 'dieCuttingPrice',
  模切数量: 'dieCuttingQuantity',
}

/**
 * 中文公式翻译器
 */
export class FormulaTranslator {
  /**
   * 将中文公式转换为英文公式
   * @param chineseFormula 中文公式
   * @returns 转换后的英文公式
   */
  static translateToEnglish(chineseFormula: string): string {
    if (!chineseFormula)
      return ''

    let englishFormula = chineseFormula

    // 替换所有的字段名
    // 按照长度降序排序字段名，以确保先匹配最长的
    const sortedFields = Object.keys(FIELD_MAPPING).sort((a, b) => b.length - a.length)

    for (const cnField of sortedFields) {
      const enField = FIELD_MAPPING[cnField as keyof typeof FIELD_MAPPING]
      englishFormula = englishFormula.replace(new RegExp(cnField, 'g'), enField)
    }

    // 清理公式中的空格
    englishFormula = englishFormula.replace(/\s+/g, '')

    return englishFormula
  }

  /**
   * 验证中文公式是否合法
   * @param chineseFormula 中文公式
   * @returns 是否合法
   */
  static validateChineseFormula(chineseFormula: string): boolean {
    try {
      const englishFormula = this.translateToEnglish(chineseFormula)

      // 检查是否有未翻译的中文字符
      if (/[\u4E00-\u9FA5]/.test(englishFormula)) {
        return false
      }

      // 检查括号是否成对
      const bracketsBalance = englishFormula.split('').reduce((balance, char) => {
        if (char === '(')
          return balance + 1
        if (char === ')')
          return balance - 1
        return balance
      }, 0)

      if (bracketsBalance !== 0) {
        return false
      }

      return true
    }
    catch {
      return false
    }
  }

  /**
   * 检测并提取公式中未知的中文词汇
   * @param chineseFormula 中文公式
   * @returns 未知的中文词汇数组
   */
  static detectUnknownTerms(chineseFormula: string): string[] {
    // 清理公式中的空格
    const formula = chineseFormula.replace(/\s+/g, '')

    // 正则表达式匹配中文词汇
    const chineseTermsRegex = /[\u4E00-\u9FA5]+/g
    const allChineseTerms = formula.match(chineseTermsRegex) || []

    // 过滤出未知的中文词汇
    const unknownTerms = allChineseTerms.filter((term) => {
      // 检查该词汇是否在已知映射中
      return !(term in FIELD_MAPPING)
    })

    return [...new Set(unknownTerms)]
  }
}

// 示例
// console.log(FormulaTranslator.translateToEnglish('(长+宽)*2+舌头长度'))
// 输出: (boxLength+boxWidth)*2+tongueLength
