// 公式类型定义
// 引入翻译器
import { FormulaTranslator } from './formula_translator'

export interface Formula {
  id: string
  type: 'totalLength' | 'boxDoorWidth' | 'custom' // 公式类型
  formula: string
}

/**
 * 公式引擎 - 解析并执行公式
 * 支持任何数据类型
 */
export class FormulaEngine {
  // 缓存解析后的公式，提高性能
  private static formulaCache = new Map<string, (form: object) => number>()

  /**
   * 执行公式
   * @param formula 公式字符串（支持中文或英文）
   * @param form 数据表单对象
   * @returns 计算结果，如果计算失败则抛出错误
   */
  static evaluate(
    formula: string,
    form: object,
  ): number {
    try {
      // 先翻译公式（如果是中文）
      const translatedFormula = FormulaTranslator.translateToEnglish(formula)

      // 从公式中提取所有字段
      const requiredFields = this.extractFieldsFromFormula(translatedFormula)

      // 验证必要字段
      this.validateRequiredFields(form, requiredFields)

      // 预处理公式字符串
      const processedFormula = this.preprocessFormula(translatedFormula)

      // 尝试从缓存获取已编译的公式
      let evalFn = this.formulaCache.get(processedFormula)

      // 如果缓存中没有，则编译公式
      if (!evalFn) {
        evalFn = this.compile(processedFormula)
        this.formulaCache.set(processedFormula, evalFn)
      }

      // 执行公式并返回结果
      const result = evalFn(form)

      if (typeof result !== 'number' || Number.isNaN(result)) {
        throw new TypeError('公式计算结果不是有效数字')
      }

      return result
    }
    catch (error) {
      console.error('公式执行错误:', error, formula)
      throw error // 抛出错误，由调用者处理
    }
  }

  /**
   * 验证对象中是否包含所有必需字段
   * @param form 要验证的对象
   * @param requiredFields 必需字段列表
   * @throws 如果缺少必需字段则抛出错误
   */
  private static validateRequiredFields(form: object, requiredFields: string[]): void {
    const missingFields = requiredFields.filter((field) => {
      // 使用安全的属性访问检查
      return !(field in form) || (form as Record<string, unknown>)[field] === undefined || (form as Record<string, unknown>)[field] === null
    })

    if (missingFields.length > 0) {
      throw new Error(`公式计算缺少必要字段: ${missingFields.join(', ')}`)
    }
  }

  /**
   * 从公式中提取字段名
   * @param formula 公式字符串
   * @returns 字段名数组
   */
  static extractFieldsFromFormula(formula: string): string[] {
    if (!formula)
      return []

    // 去除所有空格，便于正则匹配
    const cleanFormula = formula.replace(/\s+/g, '')

    // 匹配标准变量名的正则表达式
    const variableRegex = /[a-z_]\w*/gi

    // 找出所有可能的变量
    const matches = cleanFormula.match(variableRegex) || []

    // 过滤掉JavaScript保留字和可能的数学函数名
    const reserved = new Set([
      // JavaScript关键字
      'return',
      'if',
      'else',
      'for',
      'while',
      'function',
      'var',
      'let',
      'const',
      // 常见数学函数
      'Math',
      'min',
      'max',
      'floor',
      'ceil',
      'round',
      'abs',
      'sin',
      'cos',
      'tan',
      'Number',
    ])

    // 去重并返回有效变量
    return [...new Set(matches)].filter(v => !reserved.has(v))
  }

  /**
   * 预处理公式字符串
   * @param formula 原始公式字符串
   * @returns 处理后的公式字符串
   */
  private static preprocessFormula(formula: string): string {
    if (!formula) {
      return ''
    }

    // 去除所有空格
    let processed = formula.trim().replace(/\s+/g, '')

    // 提取公式中的字段名
    const fieldNames = this.extractFieldsFromFormula(formula)

    // 如果有字段，为它们添加Number转换
    if (fieldNames.length > 0) {
      const fieldPattern = new RegExp(`(${fieldNames.join('|')})\\b`, 'g')
      processed = processed.replace(fieldPattern, 'Number($1||0)')
    }

    // 添加常见的操作符分隔
    processed = processed
      // 确保括号有适当间隔
      .replace(/\(/g, ' ( ')
      .replace(/\)/g, ' ) ')
      // 确保数学运算符有适当间隔
      .replace(/([+\-*/%])/g, ' $1 ')
      // 清理多余空格
      .replace(/\s+/g, ' ')
      .trim()

    return processed
  }

  /**
   * 编译公式为函数
   * @param formula 预处理后的公式字符串
   * @returns 编译后的函数
   */
  private static compile(formula: string): (form: object) => number {
    try {
      // 创建公式执行函数
      // eslint-disable-next-line no-new-func
      return new Function('form', `
        try {
          with (form) {
            return ${formula};
          }
        } catch (e) {
          console.error('公式执行异常:', e);
          throw e; // 重新抛出异常，让调用者处理
        }
      `) as (form: object) => number
    }
    catch (error) {
      console.error('公式编译错误:', error)
      throw error // 抛出错误，由调用者处理
    }
  }
}

// 默认公式 - 使用简洁格式
export const DEFAULT_FORMULAS = {
  totalLength: '(boxLength+boxWidth)*2+tongueLength',
  boxDoorWidth: 'boxHeight+boxWidth+waveCapAddition',
}
