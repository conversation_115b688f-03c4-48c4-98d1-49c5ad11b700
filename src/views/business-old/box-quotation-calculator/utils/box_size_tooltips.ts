import { computed } from 'vue'
import { useCalculatorForm } from '../stores/calculatorStore'

// 直接创建tooltips计算属性，不需要包装在类中
export const boxSizeTooltips = computed(() => {
  const form = useCalculatorForm()

  return {
    totalLength: `纸箱总长 = (纸箱长 + 纸箱宽) × 2 + 舌头宽度\n= (${form.boxLength} + ${form.boxWidth}) × 2 + ${form.tongueLength}\n= ${form.totalLength} mm`,
    doorWidth: `纸箱门宽 = 纸箱宽 + 纸箱高 + 摇盖加分\n= ${form.boxWidth} + ${form.boxHeight} + ${form.waveCapAddition}\n= ${form.boxDoorWidth} mm`,
    dieCuttingFrameWidth: `模切门宽 = 纸箱门宽 × 模切数 + 修边位\n= ${form.boxDoorWidth} × ${form.dieCuttingQuantity} + ${form.trimPosition}\n= ${form.dieCuttingDoorWidth} mm`,
    corrugatedWidth: `瓦纸门宽 = 纸箱门宽\n= ${form.boxDoorWidth} mm`,
    facePaperWidth: `面纸门宽 = 纸箱门宽\n= ${form.boxDoorWidth} mm`,
    area: `纸箱面积 = 纸箱总长 × 纸箱门宽 ÷ 1000000\n= ${form.totalLength} × ${form.boxDoorWidth} ÷ 1000000\n= ${form.boxArea} m²`,
    corrugatedArea: `瓦纸面积 = 纸箱面积 ÷ 瓦纸开数\n= ${form.boxArea} ÷ ${form.corrugatedCount}\n= ${form.corrugatedArea} m²`,
    volume: `纸箱体积 = 纸箱长 × 纸箱宽 × 纸箱高 ÷ 1000000000\n= ${form.boxLength} × ${form.boxWidth} × ${form.boxHeight} ÷ 1000000000\n= ${form.boxVolume} m³`,
  }
})
