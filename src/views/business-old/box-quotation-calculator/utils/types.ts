import type { BoxQuotationFormParam } from '@/api/business/box-quotation/model/box-quotation-form-model'

export interface BoxCalculatorData extends BoxQuotationFormParam {
  // 独有信息
  corrugatedWidthRange?: string // 瓦纸门宽范围
  facePaperWidthRange?: string // 面纸门宽范围
  boxNailingUnitPrice?: number // 钉箱单价(元)
  boxGluingUnitPrice?: number // 粘箱单价(元)
}

export interface PaperBoard {
  paperQuotationItemId?: number
  label?: string
  paperStructureLayer?: string
  corrugatedShape?: string
  corrugatedIndex?: number
  squareMeterPrice?: number

}
export interface RawPaper {
  paperQuotationItemId?: number
  description?: string
  squareMeterPrice?: number

}
