import type { PaperBoard } from './types'
import { useCalculatorForm } from '../stores/calculatorStore'

/**
 * 纸箱开数计算结果的接口类型
 * 开数：指一张原料可以制作的纸箱数量
 */
interface SheetUpsCalculationResult {
  count: number
  boxDoorWidth: number
  widthWithCount: number
  edgeCut: number
  theoretical: number
  actual: number
  error: number
  isValid: boolean
  errorMsg: string
  isOptimal: boolean
}

interface WidthCalculationConfig {
  minWidth: number
  maxWidth: number
  widthRange: string
  type: 'corrugated' | 'facePaper'
}

interface WidthCalculationResult {
  optimalCount: number
  theoreticalWidth: number
  actualWidth: number
}

// 修边位配置
interface EdgeCutConfig {
  width: number
  value: number
}

const EDGE_CUT_CONFIG: EdgeCutConfig[] = [
  { width: 1800, value: 18 },
  { width: 2500, value: 22 },
  { width: Infinity, value: 25 },

]

// 获取修边位值
function getEdgeCut(widthWithCount: number): number {
  return EDGE_CUT_CONFIG.find(config => widthWithCount <= config.width)?.value || EDGE_CUT_CONFIG[0].value
}

// 提取常量
const MAX_COUNT = 5

// 修改 isBCType 函数，使用 some 代替 every
function isBCType(paperboardList: PaperBoard[]): boolean {
  if (!paperboardList || paperboardList.length === 0) {
    return false
  }
  return paperboardList.some(p => p.paperStructureLayer === 'corrugated' && (p.corrugatedShape === 'B' || p.corrugatedShape === 'C'))
}

// 纸张配置
const PAPER_CONFIG = {
  corrugated: {
    normal: {
      minWidth: 1450,
      maxWidth: 2500,
      widthRange: '1450-2500mm',
    },
    bc: {
      minWidth: 1450,
      maxWidth: 2800,
      widthRange: '1450-2800mm',
    },
  },
  facePaper: {
    minWidth: 700,
    maxWidth: 1350,
    widthRange: '700-1350mm',
  },
} as const

// 获取使用宽度
function getUseWidth(dieCuttingQuantity: number | undefined, dieCuttingDoorWidth: number | undefined, defaultWidth: number): number {
  return dieCuttingQuantity && Number(dieCuttingQuantity) > 0
    ? Number(dieCuttingDoorWidth || 0)
    : defaultWidth
}

// 获取瓦纸配置
function getCorrugatedConfig(isBC: boolean): WidthCalculationConfig {
  const baseConfig = isBC ? PAPER_CONFIG.corrugated.bc : PAPER_CONFIG.corrugated.normal
  return {
    ...baseConfig,
    type: 'corrugated',
  }
}

// 获取面纸配置
function getFacePaperConfig(): WidthCalculationConfig {
  return {
    ...PAPER_CONFIG.facePaper,
    type: 'facePaper',
  }
}

// 统一的门宽计算函数
function calculatePaperWidth(useWidth: number, config: WidthCalculationConfig) {
  const form = useCalculatorForm()

  // 设置门宽范围
  form[config.type === 'corrugated' ? 'corrugatedWidthRange' : 'facePaperWidthRange'] = config.widthRange

  // 计算最优开数和门宽
  const result = findOptimalWidth(useWidth, config)

  // 更新计算结果
  if (config.type === 'corrugated') {
    form.corrugatedCount = result.optimalCount
    form.corrugatedDoorWidth = result.actualWidth
    // 只在印刷方式为彩印时计算面纸门宽
    if (form.printingMethod === 'colorPrint') {
      calculateFacePaperWidth()
    }
  }
  else {
    form.facePaperCount = result.optimalCount
    form.actualFacePaperDoorWidth = result.actualWidth
  }
}

// 修改 calculateAllWidthData 和 findOptimalWidth 函数中的 maxCount
function calculateAllWidthData(boxDoorWidth: number, config: WidthCalculationConfig, currentOptimalCount: number): SheetUpsCalculationResult[] {
  const countsData: SheetUpsCalculationResult[] = []

  for (let count = 1; count <= MAX_COUNT; count++) {
    // 计算修边位
    const widthWithCount = boxDoorWidth * count
    const edgeCut = getEdgeCut(widthWithCount)

    // 计算理论门宽
    const theoretical = widthWithCount + edgeCut

    // 计算真实门宽（向上取整到50的倍数）
    let actual = Math.ceil(theoretical / 50) * 50

    // 检查是否在有效范围内
    let isValid = true
    let errorMsg = ''

    if (actual > config.maxWidth) {
      isValid = false
      errorMsg = '超出最大值'
      actual = 0
    }
    else if (actual < config.minWidth) {
      actual = config.minWidth
      errorMsg = '低于最小值'
    }

    // 计算误差
    const error = isValid ? Math.abs(actual - theoretical) : 0

    countsData.push({
      count,
      boxDoorWidth,
      widthWithCount,
      edgeCut,
      theoretical,
      actual,
      error,
      isValid,
      errorMsg,
      isOptimal: count === currentOptimalCount,
    })
  }

  return countsData
}

function findOptimalWidth(boxDoorWidth: number, config: WidthCalculationConfig): WidthCalculationResult {
  let optimalCount = 1
  let minError = Number.MAX_VALUE
  let theoreticalWidth = 0
  let actualWidth = 0

  for (let count = 1; count <= MAX_COUNT; count++) {
    const widthWithCount = boxDoorWidth * count
    const edgeCut = getEdgeCut(widthWithCount)

    const theoretical = widthWithCount + edgeCut
    let actual = Math.ceil(theoretical / 50) * 50

    if (actual > config.maxWidth) {
      continue
    }
    else if (actual < config.minWidth) {
      actual = config.minWidth
    }

    const error = Math.abs(actual - theoretical)

    if (error < minError) {
      minError = error
      optimalCount = count
      theoreticalWidth = theoretical
      actualWidth = actual
    }
  }

  return {
    optimalCount,
    theoreticalWidth,
    actualWidth,
  }
}

// 计算瓦纸开数和门宽
export function calculateCorrugatedWidth() {
  const form = useCalculatorForm()
  const { dieCuttingQuantity, boxDoorWidth, dieCuttingDoorWidth, paperboardList } = form

  // 确保门宽已计算
  if (!boxDoorWidth) {
    return
  }

  // 获取BC瓦类型状态
  const isBC = paperboardList && paperboardList.length > 0
    ? isBCType(paperboardList)
    : false

  const config = getCorrugatedConfig(isBC)
  const useWidth = getUseWidth(dieCuttingQuantity, dieCuttingDoorWidth, boxDoorWidth)

  calculatePaperWidth(useWidth, config)
}

// 计算面纸门宽和开数
export function calculateFacePaperWidth() {
  const form = useCalculatorForm()
  const { dieCuttingQuantity, boxDoorWidth, dieCuttingDoorWidth } = form

  if (!boxDoorWidth) {
    return
  }

  const config = getFacePaperConfig()
  const useWidth = getUseWidth(dieCuttingQuantity, dieCuttingDoorWidth, boxDoorWidth)

  calculatePaperWidth(useWidth, config)
}

// 提供瓦纸开数计算数据
export function corrugatedUnitPerSheetData() {
  const form = useCalculatorForm()
  const { boxHeight, trimPosition, waveCapAddition, dieCuttingQuantity, dieCuttingDoorWidth } = form

  // 计算门宽
  const boxDoorWidth = Number(boxHeight) + Number(trimPosition) + Number(waveCapAddition || 0)
  if (!boxDoorWidth) {
    return []
  }

  const useWidth = getUseWidth(dieCuttingQuantity, dieCuttingDoorWidth, boxDoorWidth)
  const isBC = isBCType(form.paperboardList || [])
  const config = getCorrugatedConfig(isBC)

  // 获取最优开数
  const result = findOptimalWidth(useWidth, config)

  // 计算所有开数的数据并返回
  return calculateAllWidthData(useWidth, config, result.optimalCount)
}

// 提供面纸开数计算数据
export function facePaperUnitPerSheetData() {
  const form = useCalculatorForm()
  const { dieCuttingQuantity, printingMethod, boxDoorWidth, dieCuttingDoorWidth } = form
  if (!boxDoorWidth || printingMethod !== 'colorPrint') {
    return []
  }

  const config = getFacePaperConfig()
  const useWidth = getUseWidth(dieCuttingQuantity, dieCuttingDoorWidth, boxDoorWidth)

  // 获取最优开数
  const result = findOptimalWidth(useWidth, config)

  // 计算所有开数的数据并返回
  return calculateAllWidthData(useWidth, config, result.optimalCount)
}
