import { SupplierSelectServiceImpl } from '@/components/business-old/ax-supplier-select/service'

/**
 * 供应商配置服务 - 单例模式
 * 管理纸板供应商选择和配置
 */
class SupplierService extends SupplierSelectServiceImpl {
  private static supplierService: SupplierService

  // 私有构造函数
  private constructor() {
    super()
    // 设置默认查询条件
    this.setQueryParam({
      supplierType: 'paper_board_supplier', // 默认为纸板供应商
      status: 'active',
    })
  }

  // 获取单例实例
  static getInstance(): SupplierService {
    return this.supplierService || (this.supplierService = new SupplierService())
  }

  /**
   * 清除所有选中的供应商和配置
   */
  reset(): void {
    this.selectedSupplier = undefined
  }

  /**
   * 获取供应商折扣率
   */
  getDiscountRate(): number {
    return this.supplierConfig.discountRate || 0
  }

  /**
   * 获取供应商税率
   */
  getTaxRate(): number {
    return this.supplierConfig.taxRate || 0
  }

  /**
   * 获取指定层数的加工费
   * @param layer 层数
   * @returns 加工费信息，如果不存在则返回默认值
   */
  getProcessingFee(layer: number): { value: number, unit: string } {
    const fee = this.supplierConfig.processingFee?.find(item => item.layer === layer)
    return fee || { value: 0, unit: '元/平方米' }
  }
}

export default SupplierService.getInstance()
