<script setup lang="ts">
import FieldItem from '../../core/FieldItem.vue'
import FieldTooltip from '../../core/FieldTooltip.vue'
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import SectionTitle from '../../core/SectionTitle.vue'
import { useCalculatorConfig } from '../../stores/calculatorConfig'
import { useCalculatorForm } from '../../stores/calculatorStore'
import ParamConfigButton from '../button/ParamConfigButton.vue'

const form = useCalculatorForm()
const configStore = useCalculatorConfig()
</script>

<template>
  <div class="">
    <SectionTitle title="计算参数">
      <template #actions>
        <ParamConfigButton />
      </template>
    </SectionTitle>
    <!-- 是否含税 -->
    <FieldItem label="是否含税">
      <template #value>
        <BooleanRadio v-model="form.includeTax" padding="13" true-text="含税" false-text="免税" />
      </template>
    </FieldItem>

    <!-- 只在选择含税时显示税率输入框 -->
    <FieldItem v-if="!form.includeTax" label="厂家税率">
      <template #tooltip>
        <FieldTooltip title="厂家税率应用于纸板费用计算：含税价格 = 不含税价格 × 税率" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.taxRate"
          :use-step-controls="true"
          v-bind="configStore.getInputConfig('taxRate')"
        />
      </template>
    </FieldItem>

    <!-- 摇盖加分 -->
    <FieldItem label="摇盖加分 (mm)">
      <template #tooltip>
        <FieldTooltip :title="`摇盖加分根据瓦纸层数自动计算：1层为2mm，2层为4mm，当前层数：${form.paperboardLayerCount || 0}层`" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.waveCapAddition"
          :use-step-controls="true"
          v-bind="configStore.getInputConfig('waveCapAddition')"
        />
      </template>
    </FieldItem>

    <!-- 舌头宽度 -->
    <FieldItem label="舌头宽度 (mm)">
      <template #tooltip>
        <FieldTooltip :title="`舌头宽度影响纸箱总长计算，标准盒型下：纸箱总长 = (长(${form.boxLength || 0}mm) + 宽(${form.boxWidth || 0}mm)) × 2 + 舌头宽度(${form.tongueLength || 0}mm)`" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.tongueLength"
          :use-step-controls="true"
          v-bind="configStore.getInputConfig('tongueLength')"
        />
      </template>
    </FieldItem>

    <!-- 修边位 -->
    <FieldItem
      v-if="form.dieCuttingQuantity && Number(form.dieCuttingQuantity) !== 0"
      label="修边位 (mm)"
    >
      <template #tooltip>
        <FieldTooltip title="修边位影响模切费用计算，取值范围12-18mm" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.trimPosition"
          :use-step-controls="true"
          v-bind="configStore.getInputConfig('trimPosition')"
        />
      </template>
    </FieldItem>
  </div>
</template>
