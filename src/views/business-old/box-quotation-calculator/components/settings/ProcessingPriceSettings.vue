<script setup lang="ts">
import DictRadio from '@/components/utils-old/dict-radio/index.vue'
import { BoxService } from '@/views/business-old/box-quotation-calculator/service/box-service'
import FieldItem from '../../core/FieldItem.vue'
import FieldTooltip from '../../core/FieldTooltip.vue'
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import { useCalculatorForm } from '../../stores/calculatorStore'

const form = useCalculatorForm()
const boxConfigService = BoxService.getInstance()
</script>

<template>
  <div class="">
    <!-- 封箱方式 -->
    <FieldItem label="封箱方式">
      <template #value>
        <DictRadio
          v-model:value="form.boxClosingType"
          dict-code="box_closing_type"
          :padding="13"
        />
      </template>
    </FieldItem>

    <!-- 封箱单价 -->
    <FieldItem label="封箱单价(元/个)">
      <template #value>
        <NumericKeypadInput
          v-model:value="form.boxClosingUnitPrice"
          :use-step-controls="true"
          placeholder="请输入封箱单价"
          :precision="2"
          :min="0"
          :step="0.1"
        />
      </template>
    </FieldItem>

    <!-- 水印色数 -->
    <FieldItem label="水印色数(色)">
      <template #tooltip>
        <FieldTooltip :title="`水印色数影响水印单价：基础单价(0.2元/㎡) + 水印色数(${form.watermarkColors}) × 0.05元/㎡`" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.watermarkColors"
          :use-step-controls="true"
          :min="0" :max="6" :step="1"
          :precision="0"
        />
      </template>
    </FieldItem>

    <!-- 水印单价 -->
    <FieldItem label="水印单价(元/m²)">
      <template #tooltip>
        <FieldTooltip :title="`水印单价 = 基础单价(0.2元/㎡) + 水印色数(${form.watermarkColors}) × 0.05元/㎡`" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.watermarkPrice"
          :use-step-controls="true" :min="0" :step="0.01" :precision="2"
        />
      </template>
    </FieldItem>

    <!-- 模切单价 -->
    <FieldItem label="模切单价(元/张)">
      <template #tooltip>
        <FieldTooltip :title="`模切单价用于计算模切费用：模切费用 = 模切单价(${form.dieCuttingPrice?.toFixed(2)}元/张) ÷ 模切数量(${form.dieCuttingQuantity ? Number(form.dieCuttingQuantity) || 1 : '未选择'}) ${form.dieCuttingQuantity && Number(form.dieCuttingQuantity) !== 0 ? `× 修边位系数(${(1 + ((Number(form.trimPosition) - 12) * 0.02)).toFixed(2)})` : ''}`" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.dieCuttingPrice"
          :use-step-controls="true" :min="0" :step="0.01" :precision="2"
        />
      </template>
    </FieldItem>

    <!-- 印版单价 -->
    <FieldItem v-if="boxConfigService.boxConfig.needPrintingPlate" label="印版单价(元/m²)">
      <template #tooltip>
        <FieldTooltip title="用于计算印版费用" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.printingPlatePrice"
          :use-step-controls="true" :min="0" :step="0.01" :precision="2"
        />
      </template>
    </FieldItem>

    <!-- 刀版单价 -->
    <FieldItem v-if="boxConfigService.boxConfig.needDieCutting" label="刀版单价(元/m²)">
      <template #tooltip>
        <FieldTooltip title="用于计算刀版费用" />
      </template>
      <template #value>
        <NumericKeypadInput
          v-model:value="form.dieCuttingPlatePrice"
          :use-step-controls="true" :min="0" :step="0.01" :precision="2"
        />
      </template>
    </FieldItem>
  </div>
</template>

<style scoped>
</style>
