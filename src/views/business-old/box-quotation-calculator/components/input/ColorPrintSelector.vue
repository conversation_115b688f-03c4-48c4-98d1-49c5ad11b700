<script setup lang="ts">
import type { PaperQuotationItemModel } from '@/api/business/paper-quotation-item/model/paper-quotation-item-model'
import SupplierSelect from '@/components/business-old/ax-supplier-select/index.vue'
import SupplierPaperSelect from '@/components/business-old/supplier-paper-select/index.vue'
import { computed, onMounted, watch } from 'vue'
import FieldItem from '../../core/FieldItem.vue'
import supplierService from '../../service/supplier-service'
import { useCalculatorForm } from '../../stores/calculatorStore'

const form = useCalculatorForm()

// 提供供应商服务
supplierService.provide()

// 判断是否显示组件
const isVisible = computed(() => form.printingMethod === 'colorPrint')

// 设置供应商类型为原纸供应商
onMounted(() => {
  // 设置查询参数为原纸供应商
  supplierService.setQueryParam({
    supplierType: 'raw_paper_supplier',
    status: 'active',
  })
})

// 监听服务中的选中供应商变化
watch(() => supplierService.selectedSupplier, (newSupplier) => {
  if (newSupplier) {
    form.rawPaperSupplierId = newSupplier.id
    form.rawPaper = undefined
    form.printingProcessList = []
  }
  else {
    // 清空选择
    form.rawPaperSupplierId = undefined
    form.rawPaper = undefined
  }
})

// 处理纸张选择
function handlePaperSelected(paper: PaperQuotationItemModel) {
  // 将选中的纸张保存到表单状态作为完整的RawPaper对象
  form.rawPaper = {
    paperQuotationItemId: paper.id,
    description: paper.paperDescription || '',
    squareMeterPrice: paper.unitPrice || 0,
  }
}

function clearPaper() {
  // 当值为空时，清空rawPaper
  form.rawPaper = undefined
}
</script>

<template>
  <template v-if="isVisible">
    <!-- 原纸供应商选择 -->
    <FieldItem label="原纸供应商">
      <template #value>
        <SupplierSelect />
      </template>
    </FieldItem>

    <FieldItem v-if="supplierService.selectedSupplier" label="印刷原纸">
      <template #value>
        <SupplierPaperSelect
          :supplier-id="supplierService.selectedSupplier?.id || 0"
          paper-category="raw_paper"
          placeholder="请选择印刷原纸"
          @clear="clearPaper"
          @select="handlePaperSelected"
        />
      </template>
    </FieldItem>
  </template>
</template>

<style scoped>
</style>
