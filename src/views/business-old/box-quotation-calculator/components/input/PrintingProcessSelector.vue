<script setup lang="ts">
import type { PrintingProcessModel } from '@/api/business/printing-process/model/printing-process-model'
import PrintingProcessMultiSelect from '@/components/business-old/printing-process-multi-select/index.vue'
import { computed } from 'vue'
import FieldItem from '../../core/FieldItem.vue'
import { useCalculatorForm } from '../../stores/calculatorStore'

const form = useCalculatorForm()

// 判断是否显示组件
const isVisible = computed(() => form.printingMethod === 'colorPrint')

// 处理印刷工艺变化
function handlePrintingProcessChange(processes: PrintingProcessModel[]) {
  form.printingProcessList = processes
}
</script>

<template>
  <!-- 印刷工艺选择 -->
  <FieldItem v-if="isVisible" label="印刷工艺">
    <template #value>
      <PrintingProcessMultiSelect
        v-model="form.printingProcessList"
        :max-count="10"
        :min-count="1"
        @change="handlePrintingProcessChange"
      />
    </template>
  </FieldItem>
</template>
