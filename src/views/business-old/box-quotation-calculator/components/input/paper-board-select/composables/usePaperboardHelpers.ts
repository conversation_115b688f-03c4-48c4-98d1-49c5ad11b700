import type { PaperQuotationItemModel } from '@/api/business/paper-quotation-item/model/paper-quotation-item-model'
import type { BoxCalculatorData, PaperBoard } from '@/views/business-old/box-quotation-calculator/utils/types'
import type { Ref } from 'vue'
import { computed, nextTick } from 'vue'

export function usePaperboardHelpers(
  form: BoxCalculatorData,
  paperOptionsStatus: Ref<Record<string, boolean>>,
) {
  // 创建一个计算属性，检查纸张是否可选
  const getPaperOptionStatus = computed(() => {
    return (key: string) => {
      return paperOptionsStatus.value[key] === false
    }
  })

  // 通用函数：重置选择器状态
  function resetPaperOptionsStatus() {
    paperOptionsStatus.value = {}
  }

  // 通用函数：获取纸板列表的安全副本
  function getPaperboardListCopy(): PaperBoard[] {
    return [...(form.paperboardList || [])]
  }

  // 通用函数：重置纸板相关数据
  function resetPaperboardData(resetLayerCount = false) {
    try {
      // 根据参数决定是否重置纸板层数
      if (resetLayerCount) {
        form.paperboardLayerCount = undefined
      }

      // 重置楞型选择
      form.corrugatedShapeGroup = undefined

      // 重置纸板列表
      form.paperboardList = []

      // 重置纸张选择器状态
      resetPaperOptionsStatus()

      // 如果需要重置层数，但同时需要设置默认值
      if (resetLayerCount && form.printingMethod) {
        nextTick(() => {
          // 设置默认纸板层数为3
          form.paperboardLayerCount = 3
        })
      }
    }
    catch (error) {
      console.error('重置纸板数据失败:', error)
    }
  }

  // 生成纸板组件的唯一标识符（用于状态管理）
  function getPaperboardKey(paperboard: Partial<PaperBoard>): string {
    return `${paperboard.paperStructureLayer}-${paperboard.corrugatedIndex || 0}-${paperboard.corrugatedShape || ''}`
  }

  // 处理纸张选择器加载完成，记录是否有可选项
  function handlePaperLoaded(papers: PaperQuotationItemModel[], paperboard: Partial<PaperBoard>): void {
    try {
      const key = getPaperboardKey(paperboard)
      // 记录选择器状态
      paperOptionsStatus.value[key] = papers && papers.length > 0
    }
    catch (error) {
      console.error('处理纸张加载状态失败:', error)
    }
  }

  // 获取纸张选择器的占位符文本
  function getPaperPlaceholder(paperboard: Partial<PaperBoard>): string {
    try {
      const key = getPaperboardKey(paperboard)

      // 如果已经记录了这个选择器没有选项
      if (paperOptionsStatus.value[key] === false) {
        // 获取纸张类型名称
        const paperType = getPaperTypeName(paperboard)
        return `该供应商没有${paperType}`
      }

      return `请选择${paperboard.label?.replace('选择', '') || '纸张'}`
    }
    catch (error) {
      console.error('获取占位符文本失败:', error)
      return '请选择纸张'
    }
  }

  // 获取纸张类型的显示名称
  function getPaperTypeName(paperboard: Partial<PaperBoard>): string {
    if (!paperboard.label)
      return '纸张'

    // 提取纸张类型名称，移除"选择"前缀
    const typeName = paperboard.label.replace('选择', '')

    // 如果是瓦纸，显示楞型
    if (paperboard.paperStructureLayer === 'corrugated' && paperboard.corrugatedShape) {
      return `${typeName}(${paperboard.corrugatedShape}楞)`
    }

    return typeName
  }

  // 查找paperboard在列表中的索引
  function findPaperboardIndex(paperboard: Partial<PaperBoard>): number {
    if (!form.paperboardList || form.paperboardList.length === 0)
      return -1

    return form.paperboardList.findIndex(
      p => p.paperStructureLayer === paperboard.paperStructureLayer
        && (paperboard.corrugatedIndex === undefined
          ? p.corrugatedIndex === 0
          : p.corrugatedIndex === paperboard.corrugatedIndex),
    )
  }

  // 处理纸张选择
  function handlePaperSelected(paper: PaperQuotationItemModel, paperboard: Partial<PaperBoard>) {
    try {
      // 确保paperBoardList是数组
      form.paperboardList ??= []

      // 组装纸板项数据
      const paperBoardItem: PaperBoard = {
        label: paperboard.label || '',
        paperStructureLayer: paperboard.paperStructureLayer || '',
        corrugatedShape: paperboard.corrugatedShape || '',
        corrugatedIndex: paperboard.corrugatedIndex || 0,
        paperQuotationItemId: paper.id,
        squareMeterPrice: paper.unitPrice || 0,
      }

      // 查找并更新或添加
      const existingIndex = findPaperboardIndex(paperboard)

      if (existingIndex >= 0) {
        form.paperboardList[existingIndex] = paperBoardItem
      }
      else {
        form.paperboardList.push(paperBoardItem)
      }
    }
    catch (error) {
      console.error('选择纸张失败:', error)
    }
  }

  // 处理清除纸张选择的逻辑
  function handleClearPaper(paperboard: Partial<PaperBoard>): void {
    try {
      // 移除匹配的项
      form.paperboardList ??= []

      const index = findPaperboardIndex(paperboard)
      if (index >= 0) {
        form.paperboardList.splice(index, 1)
      }
    }
    catch (error) {
      console.error('清除纸张选择失败:', error)
    }
  }

  return {
    resetPaperOptionsStatus,
    getPaperboardListCopy,
    resetPaperboardData,
    handlePaperLoaded,
    getPaperPlaceholder,
    getPaperTypeName,
    handlePaperSelected,
    handleClearPaper,
    getPaperOptionStatus,
  }
}
