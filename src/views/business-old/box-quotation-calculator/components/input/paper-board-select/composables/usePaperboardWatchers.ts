import type { BoxCalculatorData, PaperBoard } from '@/views/business-old/box-quotation-calculator/utils/types'
import type { ComputedRef } from 'vue'
import { getDictOptions } from '@/utils/dict-util'
import { nextTick, watch } from 'vue'

type ResetFunc = () => void
type GetCopyFunc = () => PaperBoard[]
type ResetDataFunc = (resetLayerCount?: boolean) => void

export function usePaperboardWatchers(
  form: BoxCalculatorData,
  paperComponentParams: ComputedRef<Partial<PaperBoard>[]>,
  resetPaperOptionsStatus: ResetFunc,
  getPaperboardListCopy: GetCopyFunc,
  _resetPaperboardData: ResetDataFunc,
) {
  // 合并后的业务类型监听器：重置数据并设置默认值
  watch(
    () => form.printingMethod,
    () => {
      // 不再调用resetPaperboardData，而是手动修改必要的状态
      try {
        // 保存供应商ID
        const savedSupplierId = form.paperboardSupplierId

        // 清空数据但保留供应商
        form.paperboardList = []
        form.paperboardSupplierId = savedSupplierId
        resetPaperOptionsStatus()

        // 先设置纸板层数，再处理楠型选择
        // 这样可以避免层数变化触发的监听器覆盖楠型选择
        if (form.printingMethod) {
          // 直接设置纸板层数，而不是使用setTimeout
          form.paperboardLayerCount = 3
        }
        else {
          // 重置纸板层数
          form.paperboardLayerCount = undefined
        }

        // 直接获取当前层数对应的楠型字典编码
        const dictCode = `${form.paperboardLayerCount || '3'}_layer_corrugated_shape`

        // 直接获取字典选项并设置第一个选项
        getDictOptions(dictCode).then((options) => {
          if (options && options.length > 0) {
            // 直接设置第一个选项，避免中间状态
            form.corrugatedShapeGroup = String(options[0].value)
          }
        }).catch((error) => {
          console.error(`获取楠型字典数据失败: ${dictCode}`, error)
        })
      }
      catch (error) {
        console.error('处理印刷方式变化失败:', error)
      }
    },
    { immediate: true },
  )

  // 当纸板层数改变时 - 智能保留可复用的选择
  watch(
    () => form.paperboardLayerCount,
    (newLayerCount, oldLayerCount) => {
      try {
        // 仅在层数变化时重置楞型选择，让字典组件处理默认选择
        if (newLayerCount !== oldLayerCount) {
          // 在任何层数变化时都设置楞型选择
          // 这样可以确保在切换纸板层数再切回来时，楞型选择也能正确设置
          // 直接获取当前层数对应的楞型字典编码
          const dictCode = `${newLayerCount}_layer_corrugated_shape`

          // 直接获取字典选项并设置第一个选项
          getDictOptions(dictCode).then((options) => {
            if (options && options.length > 0) {
              // 直接设置第一个选项，避免中间状态
              form.corrugatedShapeGroup = String(options[0].value)
            }
          }).catch((error) => {
            console.error(`获取楞型字典数据失败: ${dictCode}`, error)
          })
        }

        // 如果没有供应商选择或未选择纸板，则直接清空
        if (!form.paperboardSupplierId
          || !form.paperboardList
          || form.paperboardList.length === 0) {
          form.paperboardList = []
          resetPaperOptionsStatus()
          return
        }

        // 重置纸张选择器状态
        resetPaperOptionsStatus()

        // 延迟到下一个渲染周期，确保paperComponentParams已更新
        nextTick(() => {
          // 保存现有纸板列表
          const currentPaperBoardList = getPaperboardListCopy()
          const components = paperComponentParams.value
          const newPaperBoardList: PaperBoard[] = []

          // 遍历新配置的组件，尝试保留可复用的选择
          components.forEach((component) => {
            // 在现有列表中查找相同类型的选择
            const existingItem = currentPaperBoardList.find(
              p => p.paperStructureLayer === component.paperStructureLayer
                && (component.paperStructureLayer !== 'corrugated' // 非瓦纸类型
                  || component.corrugatedIndex === p.corrugatedIndex), // 瓦纸需匹配索引
            )

            // 如果找到可复用的选择，则复用
            if (existingItem && existingItem.paperQuotationItemId) {
              // 如果是瓦纸，可能需要更新楞型
              if (component.paperStructureLayer === 'corrugated') {
                newPaperBoardList.push({
                  ...existingItem,
                  corrugatedShape: component.corrugatedShape || '',
                  corrugatedIndex: component.corrugatedIndex || 0,
                })
              }
              else {
                // 非瓦纸直接保留
                newPaperBoardList.push(existingItem)
              }
            }
          })

          // 更新纸板列表
          form.paperboardList = newPaperBoardList
        })
      }
      catch (error) {
        console.error('处理纸板层数变化失败:', error)
        // 出错时重置数据确保界面不会卡住
        form.paperboardList = []
        resetPaperOptionsStatus()
      }
    },
  )

  // 当楞型变化时 - 智能保留未变化的选择
  watch(
    () => form.corrugatedShapeGroup,
    (newValue, oldValue) => {
      try {
        // 如果没有供应商选择，不处理
        if (!form.paperboardSupplierId)
          return

        // 如果paperboardList为空，不处理
        if (!form.paperboardList || form.paperboardList.length === 0)
          return

        // 重置纸张选择器状态
        resetPaperOptionsStatus()

        // 获取新旧楞型
        const newShapes = (newValue || '').split('')
        const oldShapes = (oldValue || '').split('')

        // 保存现有纸板列表
        const currentPaperBoardList = getPaperboardListCopy()
        const newPaperBoardList: PaperBoard[] = []

        // 获取当前组件配置
        const components = paperComponentParams.value

        // 遍历组件配置，保留未变化的选择
        components.forEach((component) => {
          if (component.paperStructureLayer !== 'corrugated') {
            // 非瓦纸类型，保留原有选择
            const existingItem = currentPaperBoardList.find(
              p => p.paperStructureLayer === component.paperStructureLayer,
            )
            if (existingItem && existingItem.paperQuotationItemId) {
              newPaperBoardList.push(existingItem)
            }
          }
          else {
            // 瓦纸类型，检查楞型是否变化
            const corrugatedIndex = component.corrugatedIndex || 0
            const newShape = component.corrugatedShape || ''

            // 查找旧配置中相同位置的瓦纸
            const existingItem = currentPaperBoardList.find(
              p => p.paperStructureLayer === 'corrugated'
                && p.corrugatedIndex === corrugatedIndex,
            )

            // 如果找到匹配项，检查楞型是否相同
            // 比较oldShapes和newShapes中对应索引的楞型是否相同
            const oldShapeAtIndex = oldShapes[corrugatedIndex]
            const newShapeAtIndex = newShapes[corrugatedIndex]

            // 如果楞型未变化且有选择，则保留
            if (existingItem && existingItem.paperQuotationItemId
              && oldShapeAtIndex && newShapeAtIndex
              && oldShapeAtIndex === newShapeAtIndex) {
              // 保留选择但更新楞型描述
              newPaperBoardList.push({
                ...existingItem,
                corrugatedShape: newShape,
              })
            }
          }
        })

        // 更新纸板列表
        form.paperboardList = newPaperBoardList
      }
      catch (error) {
        console.error('处理楞型变化失败:', error)
        // 出错时重置数据确保界面不会卡住
        form.paperboardList = []
        resetPaperOptionsStatus()
      }
    },
  )
}
