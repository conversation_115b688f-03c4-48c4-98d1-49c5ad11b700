import type { BoxCalculatorData, PaperBoard } from '@/views/business-old/box-quotation-calculator/utils/types'
import type { Ref } from 'vue'
import { getDictOptions } from '@/utils/dict-util'
import { computed, watch } from 'vue'
import { usePaperboardHelpers } from './usePaperboardHelpers'

export function usePaperboardConfig(form: BoxCalculatorData) {
  // 将数字类型的paperboardLayerCount转换为字符串，用于与DictRadio组件绑定
  const paperboardLayerCountStr = computed({
    get: () => String(form.paperboardLayerCount || '3'),
    set: (val) => {
      form.paperboardLayerCount = Number(val)
    },
  })

  // 根据印刷方式计算使用的纸板层数字典编码
  const boxLayerNumberDictCode = computed(() => {
    return form.printingMethod === 'colorPrint'
      ? 'color_print_box_layer_number'
      : 'water_print_box_layer_number'
  })

  // 根据纸板层数计算使用的楞型字典编码
  const corrugatedShapeDictCode = computed(() => {
    const layerNumber = form.paperboardLayerCount || '3'
    return `${layerNumber}_layer_corrugated_shape`
  })

  // 获取供应商ID为数字类型
  const supplierId = computed(() => {
    return form.paperboardSupplierId || 0
  })

  // 解析楞型字符串，例如"EB"拆分为['E', 'B']
  const corrugatedShapes = computed(() => {
    const shape = form.corrugatedShapeGroup || ''
    // 如果是单个字符，则返回该字符的数组
    if (shape.length <= 1)
      return [shape]

    // 否则将字符串拆分为单个字符的数组
    return shape.split('')
  })

  // 定义不同层数对应的纸张组件列表
  const paperComponentParams = computed<Partial<PaperBoard>[]>(() => {
    const layerNumber = Number(form.paperboardLayerCount || '0')

    // 如果层数不在2-7之间，返回空数组
    if (layerNumber < 2 || layerNumber > 7) {
      return []
    }

    // 根据不同层数定义需要渲染的纸张选择器
    const components: Partial<PaperBoard>[] = []
    const shapes = corrugatedShapes.value

    // 记录瓦纸在组合中的索引
    let flutingIndex = 0

    // 定义纸张配置模式
    switch (layerNumber) {
      case 2:
        // 2层: 瓦里
        components.push(
          {
            label: '选择瓦纸',
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用单个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
      case 3:
        // 3层: 面瓦里
        components.push(
          {
            label: '选择面纸',
            paperStructureLayer: 'face',
          },
          {
            label: '选择瓦纸',
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用单个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
      case 4:
        // 4层: 瓦芯瓦里
        components.push(
          {
            label: `选择瓦纸(${shapes[0]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用第一个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸',
            paperStructureLayer: 'core',
            corrugatedShape: 'middle',
          },
          {
            label: `选择瓦纸(${shapes[1]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[1] || '', // 使用第二个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
      case 5:
        // 5层: 面瓦芯瓦里
        components.push(
          {
            label: '选择面纸',
            paperStructureLayer: 'face',
          },
          {
            label: `选择瓦纸(${shapes[0]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用第一个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸',
            corrugatedShape: 'middle',
            paperStructureLayer: 'core',
          },
          {
            label: `选择瓦纸(${shapes[1]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[1] || '', // 使用第二个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
      case 6:
        // 6层: 瓦芯瓦芯瓦里
        components.push(
          {
            label: `选择瓦纸(${shapes[0]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用第一个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸(外)',
            paperStructureLayer: 'core_outer',
            corrugatedShape: 'middle',
          },
          {
            label: `选择瓦纸(${shapes[1]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[1] || '', // 使用第二个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸(内)',
            paperStructureLayer: 'core_inner',
            corrugatedShape: 'middle',
          },
          {
            label: `选择瓦纸(${shapes[2]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[2] || '', // 使用第三个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
      case 7:
        // 7层: 面瓦芯瓦芯瓦里
        components.push(
          {
            label: '选择面纸',
            paperStructureLayer: 'face',
          },
          {
            label: `选择瓦纸(${shapes[0]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[0] || '', // 使用第一个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸(外)',
            corrugatedShape: 'middle',
            paperStructureLayer: 'core_outer',
          },
          {
            label: `选择瓦纸(${shapes[1]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[1] || '', // 使用第二个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择芯纸(内)',
            paperStructureLayer: 'core_inner',
            corrugatedShape: 'middle',
          },
          {
            label: `选择瓦纸(${shapes[2]})`,
            paperStructureLayer: 'corrugated',
            corrugatedShape: shapes[2] || '', // 使用第三个楞型
            corrugatedIndex: flutingIndex++,
          },
          {
            label: '选择里纸',
            paperStructureLayer: 'inner',
          },
        )
        break
    }

    return components
  })

  // 监听楠型字典编码变化，总是自动选择第一个
  watch(() => corrugatedShapeDictCode.value, async (newDictCode) => {
    // 字典编码变化时处理
    // 总是自动选择第一个，确保在切换纸板层数再切回来时，楠型选择也能正确设置
    if (newDictCode) {
      try {
        // 获取字典选项
        const options = await getDictOptions(newDictCode)
        if (options && options.length > 0) {
          // 选择第一个选项
          form.corrugatedShapeGroup = String(options[0].value)
        }
      }
      catch (error) {
        console.error(`获取楠型字典数据失败: ${newDictCode}`, error)
      }
    }
  })

  return {
    paperboardLayerCountStr,
    boxLayerNumberDictCode,
    corrugatedShapeDictCode,
    supplierId,
    corrugatedShapes,
    paperComponentParams,
  }
}

export function getDependencies(form: BoxCalculatorData, paperOptionsStatus: Ref<Record<string, boolean>>) {
  // 从usePaperboardConfig中获取配置
  const configDeps = usePaperboardConfig(form)

  // 从usePaperboardHelpers中获取工具函数
  const helperDeps = usePaperboardHelpers(form, paperOptionsStatus)

  // 返回所有需要的依赖
  return {
    ...configDeps,
    ...helperDeps,
  }
}
