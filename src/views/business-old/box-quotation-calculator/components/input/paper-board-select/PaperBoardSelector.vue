<script setup lang="tsx">
import type { SupplierResult } from '@/api/business/supplier/model/supplier-types'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import SupplierPaperSelect from '@/components/business-old/supplier-paper-select/index.vue'
import DictRadio from '@/components/utils-old/dict-radio/index.vue'
import { useCalculatorForm } from '@/views/business-old/box-quotation-calculator/stores/calculatorStore'
import { supplierService } from '@/views/business/erp/supplier/service/SupplierService'
import { onMounted, ref, watch } from 'vue'
import FieldItem from '../../../core/FieldItem.vue'
import { getDependencies } from './composables/usePaperboardConfig'
import { usePaperboardWatchers } from './composables/usePaperboardWatchers'

// 获取计算器表单数据
const form = useCalculatorForm()

// 提供服务
supplierService.provide()

// 用于存储各纸张选择器是否有可选项的状态
const paperOptionsStatus = ref<Record<string, boolean>>({})

// 获取所有依赖项
const {
  paperboardLayerCountStr,
  boxLayerNumberDictCode,
  corrugatedShapeDictCode,
  supplierId,
  paperComponentParams,
  resetPaperOptionsStatus,
  getPaperboardListCopy,
  resetPaperboardData,
  handlePaperLoaded,
  getPaperPlaceholder,
  getPaperOptionStatus,
  handlePaperSelected,
  handleClearPaper,
} = getDependencies(form, paperOptionsStatus)

// 供应商改变时，重置所有纸张选择
function handleSupplierSelected(supplier: SupplierResult) {
  try {
    form.paperboardSupplierId = supplier.id
    resetPaperOptionsStatus()
    form.paperboardList = []
  }
  catch (error) {
    console.error('选择供应商失败:', error)
  }
}

// 监听服务中的选中供应商变化
watch(() => supplierService.selectedSupplier, (newSupplier) => {
  if (newSupplier) {
    handleSupplierSelected(newSupplier)
  }
  else {
    form.paperboardSupplierId = undefined
    form.paperboardList = []
  }
}, { immediate: true }) // 增加immediate属性以确保初始状态一致

// 初始化监听器
usePaperboardWatchers(
  form,
  paperComponentParams,
  resetPaperOptionsStatus,
  getPaperboardListCopy,
  resetPaperboardData,
)

// 组件挂载时，如果已有供应商ID，则尝试从服务中获取
onMounted(async () => {
  // 确保服务的查询参数已经设置为纸板供应商
  supplierService.setQueryParam({
    supplierType: 'paper_board_supplier',
    status: 'active',
  })

  // 已有供应商ID但服务中没有选中供应商，则尝试加载
  if (form.paperboardSupplierId && !supplierService.selectedSupplier) {
    try {
      const res = await supplierApi.getSupplierDetail(form.paperboardSupplierId)
      if (res.success && res.data) {
        supplierService.selectedSupplier = res.data
      }
    }
    catch (error) {
      console.error('加载供应商详情失败:', error)
    }
  }
})
</script>

<template>
  <!-- 纸板层数选择 -->
  <FieldItem label="纸板层数" value-align="left">
    <template #value>
      <DictRadio
        v-model:value="paperboardLayerCountStr"
        :dict-code="boxLayerNumberDictCode"
        :padding="9.3"
      />
    </template>
  </FieldItem>

  <!-- 楞型选择 -->
  <FieldItem label="楞型选择" value-align="left">
    <template #value>
      <DictRadio
        :key="corrugatedShapeDictCode"
        v-model:value="form.corrugatedShapeGroup"
        :dict-code="corrugatedShapeDictCode"
        :default-first="true"
        :padding="10"
      />
    </template>
  </FieldItem>

  <!-- 纸板供应商选择 -->
  <FieldItem label="纸板供应商">
    <template #value>
      <SupplierSelect />
    </template>
  </FieldItem>

  <!-- 动态渲染纸张选择器 -->
  <template v-if="form.paperboardSupplierId && paperComponentParams.length > 0">
    <FieldItem
      v-for="paperboard in paperComponentParams"
      :key="`paper-${paperboard.paperStructureLayer}-${paperboard.corrugatedIndex || 0}`"
      :label="paperboard.label || ''"
    >
      <template #value>
        <SupplierPaperSelect
          :supplier-id="supplierId"
          :paper-structure-layer="paperboard.paperStructureLayer || ''"
          :corrugated-shape="paperboard.corrugatedShape || ''"
          :model-value="form.paperboardList?.find(p =>
            p.paperStructureLayer === paperboard.paperStructureLayer
            && (paperboard.corrugatedIndex === undefined
              ? p.corrugatedIndex === 0
              : p.corrugatedIndex === paperboard.corrugatedIndex))?.paperQuotationItemId"
          :placeholder="getPaperPlaceholder(paperboard)"
          :disabled="getPaperOptionStatus(`${paperboard.paperStructureLayer}-${paperboard.corrugatedIndex || 0}-${paperboard.corrugatedShape || ''}`)"
          @clear="handleClearPaper(paperboard)"
          @select="paper => handlePaperSelected(paper, paperboard)"
          @loaded="papers => handlePaperLoaded(papers, paperboard)"
        />
      </template>
    </FieldItem>
  </template>
</template>

<style scoped>
</style>
