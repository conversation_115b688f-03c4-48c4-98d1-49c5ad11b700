<script setup lang="ts">
import FieldItem from '../../core/FieldItem.vue'
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import { BoxService } from '../../service/box-service'
import { useCalculatorForm } from '../../stores/calculatorStore'

// 获取表单数据
const form = useCalculatorForm()
// 直接调用单例的provide方法
BoxService.getInstance().provide()
</script>

<template>
  <FieldItem label="箱型">
    <template #value>
      <BoxSelect />
    </template>
  </FieldItem>

  <!-- 尺寸输入 -->
  <FieldItem label="尺寸(mm)">
    <template #value>
      <div class="grid cols-3 gap-8px h-32px">
        <NumericKeypadInput
          v-model:value="form.boxLength"
          class="w-full h-full"
          placeholder="长"
          :precision="0"
          :min="1"
          :step="1"
        />
        <NumericKeypadInput
          v-model:value="form.boxWidth"
          class="w-full h-full"
          placeholder="宽"
          :precision="0"
          :min="1"
          :step="1"
        />
        <NumericKeypadInput
          v-model:value="form.boxHeight"
          class="w-full h-full"
          placeholder="高"
          :precision="0"
          :min="1"
          :step="1"
        />
      </div>
    </template>
  </FieldItem>

  <!-- 自定义尺寸输入，只在自定义盒型时显示 -->
  <!-- <FieldItem v-if="boxId === 0" label="自定义(mm)">
    <template #value>
      <div class="grid cols-2 gap-8px h-32px">
        <NumericKeypadInput
          v-model:value="form.customTotalLength"
          class="w-full h-full"
          placeholder="纸箱总长"
          :precision="0"
          :min="1"
          :step="1"
        />
        <NumericKeypadInput
          v-model:value="form.customDoorWidth"
          class="w-full h-full"
          placeholder="纸箱门宽"
          :precision="0"
          :min="1"
          :step="1"
        />
      </div>
    </template>
  </FieldItem> -->
</template>
