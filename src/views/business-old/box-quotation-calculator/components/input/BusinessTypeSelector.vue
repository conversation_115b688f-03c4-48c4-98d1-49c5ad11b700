<script setup lang="ts">
import CustomerSelect from '@/components/business-old/customer-select/index.vue'
import DictRadio from '@/components/utils-old/dict-radio/index.vue'
import FieldItem from '../../core/FieldItem.vue'
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import { useCalculatorForm } from '../../stores/calculatorStore'

const form = useCalculatorForm()
</script>

<template>
  <!-- 客户选择 -->
  <FieldItem label="选择客户">
    <template #value>
      <CustomerSelect
        v-model:model-value="form.customerId"
        placeholder="请选择客户"
      />
    </template>
  </FieldItem>

  <!-- 业务类型 -->
  <FieldItem label="业务类型">
    <template #value>
      <div class="h-32px flex items-center">
        <div class="flex items-center gap-2">
          <DictRadio
            v-model:value="form.printingMethod"
            dict-code="printing_method"
            :padding="10"
          />
        </div>
        <div class="flex-1 ml-8px gap-8px grid cols-2">
          <NumericKeypadInput
            v-model:value="form.boxQuantity"
            class="w-full h-full"
            placeholder="纸箱数"
            :precision="0"
            :min="1"
            :step="1"
          />
          <a-select
            v-model:value="form.dieCuttingQuantity"
            class="w-full h-full"
            placeholder="模切数"
            allow-clear
            :options="[
              { label: '0.5', value: 0.5 },
              { label: '1', value: 1 },
              { label: '2', value: 2 },
              { label: '3', value: 3 },
              { label: '4', value: 4 },
              { label: '5', value: 5 },
              { label: '6', value: 6 },
              { label: '7', value: 7 },
              { label: '8', value: 8 },
              { label: '9', value: 9 },
              { label: '10', value: 10 },
            ]"
          />
        </div>
      </div>
    </template>
  </FieldItem>
</template>
