<script lang="ts" setup>
import { computed } from 'vue'
import QuoteCalculator from './QuoteCalculator.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

// 使用计算属性处理visible状态，避免直接修改prop
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

function closeModal() {
  emit('update:modelValue', false)
}
</script>

<template>
  <AModal
    v-model:open="visible"
    :footer="null"
    width="90vw"
    :destroy-on-close="false"
    class="quote-calculator-modal"
    @cancel="closeModal"
  >
    <template #title>
      <div class="modal-title">
        纸箱报价计算器
      </div>
    </template>
    <QuoteCalculator />
  </AModal>
</template>

<style lang="scss" scoped>
.quote-calculator-modal {
  :deep(.ant-modal-content) {
    height: 80vh;
    overflow-y: auto;
  }

  .modal-title {
    font-size: 18px;
    font-weight: bold;
  }
}
</style> 