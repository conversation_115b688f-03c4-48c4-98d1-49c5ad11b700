<script setup lang="ts">
import { boxQuotationApi } from '@/api/business/box-quotation/box-quotation-api'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { useCalculatorForm } from '../../stores/calculatorStore'

defineOptions({
  name: 'SaveButton',
})

// 获取计算器表单数据
const form = useCalculatorForm()

// 保存按钮loading状态
const saveLoading = ref(false)

// 获取当前应该渲染的纸张组件数量
function getRequiredPaperCount(layerCount?: number): number {
  if (!layerCount)
    return 0

  // 根据层数返回应该有的纸张组件数量
  switch (layerCount) {
    case 2: return 2 // 瓦里
    case 3: return 3 // 面瓦里
    case 4: return 4 // 瓦芯瓦里
    case 5: return 5 // 面瓦芯瓦里
    case 6: return 6 // 瓦芯瓦芯瓦里
    case 7: return 7 // 面瓦芯瓦芯瓦里
    default: return 0
  }
}

// 获取提示信息
const tooltipMessage = computed(() => {
  if (!form.customerId)
    return '请选择客户'

  if (!form.boxQuantity || form.boxQuantity <= 0)
    return '请输入纸箱数量'

  if (!form.boxArea || form.boxArea <= 0)
    return '请确保纸箱尺寸正确'

  if (!form.paperboardSupplierId)
    return '请选择纸板供应商'

  // 检查纸板层数
  if (!form.paperboardLayerCount)
    return '请选择纸板层数'

  // 检查纸板列表是否存在
  if (!form.paperboardList || form.paperboardList.length === 0)
    return '请选择纸板'

  // 获取当前应该有的纸张组件数量
  const requiredCount = getRequiredPaperCount(Number(form.paperboardLayerCount))

  // 检查实际纸张数量是否等于所需数量
  if (form.paperboardList.length < requiredCount) {
    return `请选择所有纸板(已选${form.paperboardList.length}/${requiredCount})`
  }

  return '请完善计算信息'
})

// 计算按钮是否禁用
const isDisabled = computed(() => {
  // 检查基本条件
  const hasCustomer = !!form.customerId
  const hasBoxQuantity = !!form.boxQuantity && form.boxQuantity > 0
  const hasBoxArea = !!form.boxArea && form.boxArea > 0
  const hasPaperboardSupplier = !!form.paperboardSupplierId
  const hasPaperboardLayerCount = !!form.paperboardLayerCount

  // 如果基本条件不满足，直接返回禁用
  if (!(hasCustomer && hasBoxQuantity && hasBoxArea && hasPaperboardSupplier && hasPaperboardLayerCount)) {
    return true
  }

  // 检查纸板列表是否存在
  if (!form.paperboardList || form.paperboardList.length === 0) {
    return true
  }

  // 获取当前应该有的纸张组件数量
  const requiredCount = getRequiredPaperCount(Number(form.paperboardLayerCount))

  // 检查实际纸张数量是否等于所需数量
  return form.paperboardList.length < requiredCount
})

// 处理保存
async function handleSave() {
  // 如果按钮被禁用，直接返回
  if (isDisabled.value) {
    message.warning(tooltipMessage.value)
    return
  }

  if (saveLoading.value)
    return // 防止重复点击

  saveLoading.value = true
  try {
    // 构建报价参数
    const params = {
      ...form,
    }

    // 调用API保存报价
    const res = await boxQuotationApi.addBoxQuotation(params)
    if (!res.success) {
      throw new Error(res.msg || '保存报价失败')
    }

    // 成功提示
    message.success('保存报价成功')
  }
  catch (error) {
    console.error('保存报价出错:', error)
    message.error(error instanceof Error ? error.message : '保存报价失败，请稍后重试')
  }
  finally {
    saveLoading.value = false
  }
}

// 暴露handleSave方法和saveLoading状态，方便父组件使用
defineExpose({
  handleSave,
  saveLoading,
  isDisabled,
})
</script>

<template>
  <a-tooltip v-if="isDisabled" :title="tooltipMessage">
    <a-button type="primary" :loading="saveLoading" :disabled="isDisabled" @click="handleSave">
      保存
    </a-button>
  </a-tooltip>
  <a-button v-else type="primary" :loading="saveLoading" @click="handleSave">
    保存
  </a-button>
</template>

<style scoped>
</style>
