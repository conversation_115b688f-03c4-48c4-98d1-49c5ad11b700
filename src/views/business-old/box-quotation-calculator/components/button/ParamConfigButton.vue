<script setup lang="ts">
import { SettingOutlined } from '@ant-design/icons-vue'
import { onMounted, ref } from 'vue'
import NumericKeypadInput from '../../core/NumericKeypadInput.vue'
import { useCalculatorConfig } from '../../stores/calculatorConfig'

const configStore = useCalculatorConfig()
const modalVisible = ref(false)
const loading = ref(false)

// 刷新配置参数
async function refreshParams() {
  try {
    loading.value = true
    await configStore.fetchParams()
  }
  catch {
    // 错误已在 store 中处理
  }
  finally {
    loading.value = false
  }
}

async function handleOpenModal() {
  try {
    // 每次打开弹窗都重新请求数据
    modalVisible.value = true
    await refreshParams()
  }
  catch {
    // 错误已在 store 中处理
  }
}

async function handleSave() {
  try {
    loading.value = true
    await configStore.saveParams(configStore.allConfigs)
    modalVisible.value = false
  }
  catch {
    // 错误已在 store 中处理
  }
  finally {
    loading.value = false
  }
}

function handleCloseModal() {
  modalVisible.value = false
}

// 组件挂载时初始化参数
onMounted(refreshParams)
</script>

<template>
  <a-tooltip title="配置计算器参数">
    <a-button type="link" @click="handleOpenModal">
      <template #icon>
        <SettingOutlined />
      </template>
    </a-button>
  </a-tooltip>

  <a-modal
    v-model:open="modalVisible"
    title="计算器配置"
    width="700px"
    :mask-closable="false"
    :keyboard="true"
    :get-container="false"
    :confirm-loading="loading"
    @cancel="handleCloseModal"
  >
    <template #footer>
      <a-button @click="handleCloseModal">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleSave">
        保存
      </a-button>
    </template>
    <a-spin :spinning="loading">
      <a-table
        :data-source="configStore.allConfigs"
        :pagination="false"
        size="small"
        bordered
      >
        <a-table-column
          title="参数名称"
          data-index="name"
          :width="30"
          :ellipsis="true"
        >
          <template #default="{ text }">
            <span style="white-space: nowrap;">{{ text }}</span>
          </template>
        </a-table-column>
        <a-table-column
          title="默认值"
          :width="30"
          align="center"
        >
          <template #default="{ record }">
            <NumericKeypadInput
              v-model:value="record.value"
              :min="record.min"
              :max="record.max"
              :step="record.step"
              :precision="record.decimalPlaces"
              :use-step-controls="true"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="步长"
          :width="30"
          align="center"
        >
          <template #default="{ record }">
            <NumericKeypadInput
              v-model:value="record.step"
              :min="0"
              :max="record.max"
              :step="record.decimalPlaces === 0 ? 1 : record.decimalPlaces === 1 ? 0.1 : 0.01"
              :precision="record.decimalPlaces"
              :use-step-controls="true"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="最小值"
          :width="30"
          align="center"
        >
          <template #default="{ record }">
            <NumericKeypadInput
              v-model:value="record.min"
              :min="0"
              :max="record.max"
              :step="record.step"
              :precision="record.decimalPlaces"
              :use-step-controls="true"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="最大值"
          :width="30"
          align="center"
        >
          <template #default="{ record }">
            <NumericKeypadInput
              v-model:value="record.max"
              :min="record.min"
              :max="Number.MAX_SAFE_INTEGER"
              :step="record.step"
              :precision="record.decimalPlaces"
              :use-step-controls="true"
            />
          </template>
        </a-table-column>
      </a-table>
    </a-spin>
  </a-modal>
</template>
