<script setup lang="ts">
import { theme } from 'ant-design-vue'
import { computed, ref } from 'vue'
import FieldItem from '../../core/FieldItem.vue'
import FieldTooltip from '../../core/FieldTooltip.vue'
import SectionTitle from '../../core/SectionTitle.vue'
import { useCalculatorForm } from '../../stores/calculatorStore'
import { boxSizeTooltips } from '../../utils/box_size_tooltips'
import { corrugatedUnitPerSheetData, facePaperUnitPerSheetData } from '../../utils/ups_calculate'
import CountsCalculatorDialog from '../CountsCalculatorDialog.vue'

const { useToken } = theme
const { token } = useToken()
const form = useCalculatorForm()

// 控制开数计算详情对话框显示
const showCorrugatedDetailDialog = ref(false)
const showFacePaperDetailDialog = ref(false)

// 计算属性：瓦纸开数数据
const corrugatedCountsData = computed(() => corrugatedUnitPerSheetData())

// 计算属性：面纸开数数据
const facePaperCountsData = computed(() => facePaperUnitPerSheetData())

// 显示瓦纸开数计算详情
function handleShowCorrugatedDetail() {
  showCorrugatedDetailDialog.value = true
}

// 显示面纸开数计算详情
function handleShowFacePaperDetail() {
  showFacePaperDetailDialog.value = true
}
</script>

<template>
  <div class="">
    <SectionTitle title="尺寸计算" />

    <!-- 纸箱总长 -->
    <FieldItem label="纸箱总长 (mm)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.totalLength" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.totalLength }}</span>
      </template>
    </FieldItem>

    <!-- 纸箱门宽 -->
    <FieldItem label="纸箱门宽 (mm)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.doorWidth" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.boxDoorWidth }}</span>
      </template>
    </FieldItem>

    <!-- 模切门宽 -->
    <FieldItem
      v-if="form.dieCuttingQuantity && Number(form.dieCuttingQuantity) > 0"
      label="模切门宽 (mm)"
      tooltip-position="right"
    >
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.dieCuttingFrameWidth" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.dieCuttingDoorWidth }}</span>
      </template>
    </FieldItem>

    <!-- 瓦纸门宽 -->
    <FieldItem label="瓦纸门宽 (mm)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.corrugatedWidth" />
      </template>
      <template #value>
        <span class="text-primary cursor-pointer underline" @click="handleShowCorrugatedDetail">
          {{ form.corrugatedDoorWidth }}
        </span>
      </template>
    </FieldItem>

    <!-- 面纸门宽 -->
    <FieldItem
      v-if="form.printingMethod === 'colorPrint'"
      label="面纸门宽 (mm)"
      tooltip-position="right"
    >
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.facePaperWidth" />
      </template>
      <template #value>
        <span class="text-primary cursor-pointer underline" @click="handleShowFacePaperDetail">
          {{ form.actualFacePaperDoorWidth }}
        </span>
      </template>
    </FieldItem>

    <!-- 纸箱面积 -->
    <FieldItem label="纸箱面积 (m²)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.area" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.boxArea }}</span>
      </template>
    </FieldItem>

    <!-- 瓦纸面积 -->
    <FieldItem label="瓦纸面积 (m²)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.corrugatedArea" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.corrugatedArea }}</span>
      </template>
    </FieldItem>

    <!-- 纸箱体积 -->
    <FieldItem label="纸箱体积 (m³)" tooltip-position="right">
      <template #tooltip>
        <FieldTooltip :title="boxSizeTooltips.volume" />
      </template>
      <template #value>
        <span class="text-primary">{{ form.boxVolume }}</span>
      </template>
    </FieldItem>

    <!-- 瓦纸开数计算详情弹窗 -->
    <CountsCalculatorDialog
      v-model:model-value="showCorrugatedDetailDialog"
      :all-counts-data="corrugatedCountsData"
      type="corrugated"
    />

    <!-- 面纸开数计算详情弹窗 -->
    <CountsCalculatorDialog
      v-model:model-value="showFacePaperDetailDialog"
      :all-counts-data="facePaperCountsData"
      type="facePaper"
    />
  </div>
</template>

<style scoped lang="less">
.text-primary {
  color: v-bind('token.colorPrimary');
}
</style>
