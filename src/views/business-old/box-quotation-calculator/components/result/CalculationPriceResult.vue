<script setup lang="ts">
import type { ColumnsType } from 'ant-design-vue/es/table'
import { BoxService } from '@/views/business-old/box-quotation-calculator/service/box-service'
import { theme } from 'ant-design-vue'
import { computed } from 'vue'
import SectionTitle from '../../core/SectionTitle.vue'
import { useCalculatorForm } from '../../stores/calculatorStore'
import { priceTooltips } from '../../utils/price_tooltips'

// 定义表格行数据类型
interface TableRowData {
  key: string
  name: string
  unitPrice: string
  totalPrice: string
  tooltip: string
  isSummary?: boolean
}

defineOptions({
  name: 'CalculationPriceResult',
})

const { useToken } = theme
const { token } = useToken()

const form = useCalculatorForm()
const boxConfig = BoxService.getInstance().boxConfig
// 定义表格列
const columns: ColumnsType<TableRowData> = [
  {
    title: '费用项目',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '单价/个',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
  },
  {
    title: '总价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
  },
]

// 构建表格数据
const tableData = computed<TableRowData[]>(() => {
  // 确保priceTooltips.value存在再使用
  const tooltips = priceTooltips.value || {}

  const data: TableRowData[] = [
    {
      key: 'watermark',
      name: '水印费用',
      unitPrice: `¥${form.watermarkUnitPrice}`,
      totalPrice: `¥${form.watermarkTotalPrice}`,
      tooltip: tooltips.watermarkUnitPrice || '水印费用',
    },
  ]

  // 只在模切数不为0的情况下展示模切费用
  if (form.dieCuttingQuantity && Number(form.dieCuttingQuantity) > 0) {
    data.push({
      key: 'dieCutting',
      name: '模切费用',
      unitPrice: `¥${form.dieCuttingUnitPrice}`,
      totalPrice: `¥${form.dieCuttingTotalPrice}`,
      tooltip: tooltips.dieCuttingCost || '模切费用',
    })
  }

  data.push(
    {
      key: 'boxClosing',
      name: '封箱费用',
      unitPrice: `¥${form.boxClosingUnitPrice}`,
      totalPrice: `¥${form.boxClosingTotalPrice}`,
      tooltip: tooltips.boxClosingCost || '封箱费用',
    },
    {
      key: 'paperboard',
      name: '纸板费用',
      unitPrice: `¥${form.paperboardUnitPrice}`,
      totalPrice: `¥${form.paperboardTotalPrice}`,
      tooltip: tooltips.paperboardCost || '纸板费用',
    },
  )

  if (boxConfig.needPrintingPlate) {
    data.push({
      key: 'printingPlate',
      name: '印版费用',
      unitPrice: `¥${form.printingPlateUnitPrice}`,
      totalPrice: `¥${form.printingPlateTotalPrice}`,
      tooltip: tooltips.printingPlateCost || '印版费用',
    })
  }

  if (boxConfig.needDieCutting) {
    data.push({
      key: 'dieCuttingPlate',
      name: '刀版费用',
      unitPrice: `¥${form.dieCuttingPlateUnitPrice}`,
      totalPrice: `¥${form.dieCuttingPlateTotalPrice}`,
      tooltip: tooltips.dieCuttingPlateCost || '刀版费用',
    })
  }

  // 彩印业务类型特有项目
  if (form.printingMethod === 'colorPrint') {
    data.push({
      key: 'rawPaper',
      name: '原纸费用',
      unitPrice: `¥${form.rawPaperUnitPrice}`,
      totalPrice: `¥${form.rawPaperTotalPrice}`,
      tooltip: tooltips.rawPaperCost || '原纸费用',
    })

    // 有印刷工艺时添加
    if (form.printingProcessList && form.printingProcessList.length > 0) {
      data.push({
        key: 'printingProcess',
        name: '印刷工艺',
        unitPrice: `¥${form.printingProcessUnitPrice}`,
        totalPrice: `¥${form.printingProcessTotalPrice}`,
        tooltip: tooltips.printingProcessCost || '印刷工艺费用',
      })
    }
  }

  // 添加合计行
  data.push({
    key: 'total',
    name: '合计',
    unitPrice: `¥${form.boxUnitPrice}`,
    totalPrice: `¥${form.boxTotalPrice}`,
    tooltip: '',
    isSummary: true,
  })

  return data
})
</script>

<template>
  <div>
    <SectionTitle title="纸箱价格" />
    <a-table
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      :bordered="true"
      size="small"
      class="price-table"
    >
      <!-- 使用更简单的bodyCell自定义渲染 -->
      <template #bodyCell="{ column, record }">
        <!-- 单价列处理 -->
        <template v-if="column.dataIndex === 'unitPrice'">
          <!-- 合计行特殊处理 -->
          <template v-if="record.isSummary">
            <span :style="{ color: token.colorPrimary, fontWeight: 'bold' }">
              {{ record.unitPrice }}
            </span>
          </template>
          <!-- 有tooltip时显示tooltip -->
          <template v-else-if="record.tooltip && record.tooltip.length > 0">
            <a-tooltip
              placement="top"
              :title="record.tooltip"
              :color="token.colorPrimary"
              :mouse-enter-delay="0.1"
              overlay-class-name="price-tooltip"
            >
              <a class="hover-info">{{ record.unitPrice }}</a>
            </a-tooltip>
          </template>
          <!-- 无tooltip时直接显示 -->
          <template v-else>
            {{ record.unitPrice }}
          </template>
        </template>

        <!-- 合计行其他列特殊处理 -->
        <template v-else-if="record.isSummary">
          <span :style="{ color: token.colorPrimary, fontWeight: 'bold' }">
            {{ record[column.dataIndex as keyof TableRowData] }}
          </span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style scoped>
.price-table {
  margin-top: 16px;
  margin-bottom: 24px;
}

.hover-info {
  color: v-bind('token.colorPrimary');
  text-decoration: underline;
  text-decoration-style: dotted;
}

:deep(.ant-table-row:last-child) {
  background-color: v-bind('token.colorBgLayout');
}

:deep(.price-tooltip) {
  max-width: 400px;
}
</style>
