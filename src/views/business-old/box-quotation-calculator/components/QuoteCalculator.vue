<script lang="ts" setup>
import { computed } from 'vue'
import { useCalculatorForm } from '../stores/calculatorStore'
import BasicDataInput from './input/BasicDataInput.vue'
import CalculationPriceResult from './result/CalculationPriceResult.vue'
import CalculationSizeResult from './result/CalculationSizeResult.vue'
import ParameterSettings from './settings/ParameterSettings.vue'
import ProcessingPriceSettings from './settings/ProcessingPriceSettings.vue'

const form = useCalculatorForm()

const canCalculate = computed(() => {
  const { boxLength, boxWidth, boxHeight } = form
  return boxLength && boxWidth && boxHeight
})
</script>

<template>
  <div class="mx-auto ">
    <div class="new-calculator-container">
      <ACard :bordered="false" :hoverable="false">
        <BasicDataInput />
      </ACard>

      <ACard :bordered="false" :hoverable="false">
        <ParameterSettings />
        <ADivider />
        <ProcessingPriceSettings />
      </ACard>

      <ACard :bordered="false" :hoverable="false">
        <template v-if="canCalculate">
          <CalculationSizeResult />
          <ADivider />
          <CalculationPriceResult />
        </template>
        <div v-else class="flex justify-center items-center min-h-400px">
          <AEmpty description="请输入有效的尺寸" />
        </div>
      </ACard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.new-calculator-container {
  display: grid;
  margin: 0 auto;
  grid-template-columns: 431px 350px 350px;
  grid-gap: 16px;
}
</style>
