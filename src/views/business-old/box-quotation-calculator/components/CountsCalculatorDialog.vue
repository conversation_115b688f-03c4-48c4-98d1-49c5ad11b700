<script setup lang="ts">
import { theme } from 'ant-design-vue'
import { computed } from 'vue'

// 定义计数数据接口
export interface CountDataItem {
  count: number
  boxDoorWidth: number
  widthWithCount: number
  edgeCut: number
  theoretical: number
  actual: number
  error: number
  isValid: boolean
  isOptimal: boolean
  errorMsg?: string
}

defineOptions({
  name: 'CountsCalculatorDialog',
})

// 定义Props
const props = withDefaults(defineProps<{
  modelValue: boolean
  allCountsData: CountDataItem[]
  title?: string
  type?: 'corrugated' | 'facePaper'
}>(), {
  modelValue: false,
  allCountsData: () => [],
  title: '开数计算详情',
  type: 'corrugated',
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const { token } = theme.useToken()

// 计算属性：最佳开数
const optimalCount = computed(() => {
  return props.allCountsData.find(item => item.isOptimal) || null
})

// 计算属性：对话框标题
const dialogTitle = computed(() => {
  return props.type === 'corrugated' ? '瓦纸门宽计算详情' : '面纸门宽计算详情'
})

// 计算属性：说明文本
const description = computed(() => {
  return props.type === 'corrugated' ? '计算最优瓦纸开数和门宽' : '计算最优面纸开数和门宽'
})

// 处理对话框关闭
function handleClose() {
  emit('update:modelValue', false)
}
</script>

<template>
  <AModal
    :open="modelValue"
    :footer="null"
    width="840px"
    @cancel="handleClose"
    @update:open="(val) => emit('update:modelValue', val)"
  >
    <div class="counts-detail-container">
      <div class="detail-header">
        <h2 class="mb-4 text-center text-lg font-bold" :style="{ color: token.colorText }">
          {{ dialogTitle }}
        </h2>
        <div class="mb-6 text-center text-sm" :style="{ color: token.colorTextSecondary }">
          {{ description }}
        </div>
      </div>

      <div class="detail-table">
        <ATable
          :data-source="allCountsData"
          :columns="[
            { title: '开数', dataIndex: 'count', key: 'count', width: 60, align: 'center' },
            {
              title: '门宽×开数',
              dataIndex: 'widthWithCount',
              key: 'widthWithCount',
              width: 120,
              align: 'center',
              customRender: ({ record }) => {
                return `${Math.round(record.boxDoorWidth)}×${record.count}=${Math.round(record.widthWithCount)}`
              },
            },
            { title: '修边位', dataIndex: 'edgeCut', key: 'edgeCut', width: 80, align: 'center' },
            {
              title: '理论门宽',
              dataIndex: 'theoretical',
              key: 'theoretical',
              width: 90,
              align: 'center',
              customRender: ({ text }) => Math.round(text),
            },
            {
              title: '真实门宽',
              dataIndex: 'actual',
              key: 'actual',
              width: 90,
              align: 'center',
              customRender: ({ text, record }) => {
                if (record.isValid) {
                  return text
                }
                return {
                  children: text,
                  props: {
                    style: { color: '#909399' },
                  },
                }
              },
            },
            {
              title: '误差',
              dataIndex: 'error',
              key: 'error',
              width: 80,
              align: 'center',
              customRender: ({ text, record }) => {
                if (record.isValid) {
                  return Math.round(text)
                }
                return text
              },
            },
            {
              title: '备注',
              dataIndex: 'errorMsg',
              key: 'errorMsg',
              width: 100,
              align: 'center',
              customRender: ({ text, record }) => {
                if (record.isOptimal) {
                  return {
                    children: '最佳',
                    props: {
                      style: { color: token.colorPrimary, fontWeight: 'bold' },
                    },
                  }
                }
                if (text) {
                  return {
                    children: text,
                    props: {
                      style: { color: token.colorTextDescription },
                    },
                  }
                }
                return text
              },
            },
          ]"
          :pagination="false"
          size="small"
          :row-class-name="(record) => record.isOptimal ? 'optimal-row' : ''"
          bordered
        />
      </div>

      <div class="detail-footer mt-4 pt-4 border-t border-gray-200">
        <div class="text-sm" :style="{ color: token.colorTextSecondary }">
          <div class="mb-2">
            <div class="font-medium" :style="{ color: token.colorText }">
              计算说明:
            </div>
            <div>1. {{ type === 'corrugated' ? '瓦纸' : '面纸' }}门宽开数基于行业标准尺寸计算</div>
            <div>2. 实际门宽(mm) = 标准尺寸(mm) ÷ 开数</div>
            <div>3. 推荐使用误差最小且在合理范围内的开数</div>
          </div>
          <div v-if="optimalCount" class="mt-2">
            <div class="font-medium" :style="{ color: token.colorPrimary }">
              最佳选择: {{ optimalCount.count }}开 (门宽{{ optimalCount.actual }}mm)
            </div>
          </div>
        </div>
      </div>
    </div>
  </AModal>
</template>

<style scoped lang="less">
.counts-detail-container {
  padding: 0 8px;
}

.detail-header {
  margin-bottom: 16px;
}

.detail-table {
  margin-bottom: 16px;
}

.detail-footer {
  color: #666;
}

:deep(.optimal-row) {
  background-color: v-bind('`${token.colorPrimary}10`');
}
</style>
