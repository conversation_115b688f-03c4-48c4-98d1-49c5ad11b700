<script setup lang="ts">
import { chatStreamApi } from '@/api/business/chat/chat-stream-api'
import { SendOutlined } from '@ant-design/icons-vue'
import { But<PERSON>, Modal, Spin } from 'ant-design-vue'
import { B<PERSON>ble, Sender, Welcome } from 'ant-design-x-vue'

import { ref } from 'vue'

defineOptions({
  name: 'AiAssistant',
})

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits(['update:visible'])

// 聊天记录，只保留本轮会话
const messages = ref<{ id: string, content: string, role: 'ai' | 'local', loading?: boolean }[]>([])
// 输入内容
const inputContent = ref('')
// 会话ID，直接用随机生成
const sessionId = ref(`session_${Math.random().toString(36).substring(2, 10)}`)
// 加载状态
const loading = ref(false)

// 聊天气泡设置
const roles = {
  ai: {
    placement: 'start',
    typing: { step: 5, interval: 20 },
    styles: {
      content: {
        borderRadius: '16px',
      },
    },
  },
  local: {
    placement: 'end',
    variant: 'shadow',
  },
} as const

// 发送消息
async function handleSend(content: string) {
  if (!content.trim())
    return

  // 添加用户消息
  const msgUserId = Date.now().toString()
  messages.value.push({
    id: msgUserId,
    content,
    role: 'local',
  })

  // 添加AI正在输入的消息
  const aiId = Date.now().toString() + 1
  messages.value.push({
    id: aiId,
    content: '',
    role: 'ai',
    loading: true,
  })

  loading.value = true
  inputContent.value = ''

  try {
    // 创建EventSource实例，流式接收AI回复
    const eventSource = chatStreamApi.createEventSourceChat(
      content,
      sessionId.value,
      // 接收消息回调
      (data: string) => {
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.id === aiId) {
          lastMessage.content += data
          lastMessage.loading = false
        }
      },
      // 连接打开回调
      () => {
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && lastMessage.id === aiId) {
          lastMessage.content = ''
        }
      },
      // 错误处理回调
      () => {
        eventSource.close()
        const lastMessage = messages.value[messages.value.length - 1]
        if (lastMessage && !lastMessage.content) {
          lastMessage.content = 'AI回复出错，请稍后重试'
        }
        lastMessage.loading = false
        loading.value = false
      },
    )
    // 响应完成后关闭loading
    eventSource.addEventListener('error', () => {
      loading.value = false
    })
  }
  catch {
    loading.value = false
    // 显示错误消息
    const lastMessage = messages.value[messages.value.length - 1]
    if (lastMessage && lastMessage.id.endsWith('1')) {
      lastMessage.content = '抱歉，发生错误，请稍后再试。'
      lastMessage.loading = false
    }
  }
}

// 关闭弹窗
function handleClose() {
  emit('update:visible', false)
}
</script>

<template>
  <Modal
    :open="props.visible"
    title="纸箱报价AI助手"
    width="650px"
    :footer="null"
    destroy-on-close
    :mask-closable="false"
    @cancel="handleClose"
    @update:open="(val: boolean) => emit('update:visible', val)"
  >
    <Spin :spinning="loading" tip="AI正在思考...">
      <div class="ai-assistant-container">
        <!-- 欢迎信息 -->
        <Welcome
          v-if="messages.length === 0"
          variant="borderless"
          title="你好，我是纸箱报价AI助手"
          description="我可以回答关于纸箱报价计算的问题，帮助你提高工作效率！"
        />

        <!-- 聊天记录 -->
        <div v-else class="message-container">
          <Bubble.List
            :items="messages"
            :roles="roles"
          />
        </div>

        <!-- 输入框 -->
        <div class="input-container">
          <Sender
            v-model:value="inputContent"
            :loading="loading"
            :auto-size="true"
            placeholder="请输入您的问题..."
            @submit="handleSend"
          >
            <template #suffix>
              <Button
                type="primary"
                :disabled="!inputContent.trim() || loading"
                @click="() => handleSend(inputContent)"
              >
                <SendOutlined />
              </Button>
            </template>
          </Sender>
        </div>
      </div>
    </Spin>
  </Modal>
</template>

<style scoped>
.ai-assistant-container {
  display: flex;
  flex-direction: column;
  height: 550px;
  position: relative;
}

.message-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  margin-bottom: 16px;
  background-color: rgba(0, 0, 0, 0.01);
  border-radius: 8px;
}

.input-container {
  padding: 0 16px 16px;
}
</style>
