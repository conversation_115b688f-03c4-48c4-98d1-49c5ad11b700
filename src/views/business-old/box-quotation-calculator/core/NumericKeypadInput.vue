<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { message, theme } from 'ant-design-vue'
import { omit as _omit } from 'lodash-es'
import { computed, ref, useAttrs, useId, useTemplateRef } from 'vue'

defineOptions({
  name: 'NumericKeypad',
})

defineProps<{
  cursorPosition?: number
  /** 是否使用步进控制器样式 */
  useStepControls?: boolean
}>()

const $attrs = _omit(useAttrs(), ['class', 'style'])
// 从$attrs中获取min和max值
const minValue = computed(() => $attrs.min !== undefined ? Number($attrs.min) : -Infinity)
const maxValue = computed(() => $attrs.max !== undefined ? Number($attrs.max) : Infinity)
// 从$attrs中获取precision值
const precision = computed(() => $attrs.precision !== undefined ? Number($attrs.precision) : undefined)
// 从$attrs中获取step值
const stepValue = computed(() => $attrs.step !== undefined ? Number($attrs.step) : 1)

const inputId = useId()
const bindVal = defineModel<string | number>('value')

// 获取主题 token
const { useToken } = theme
const { token } = useToken()

// 数字输入键盘的实现
const panelOpen = ref(false)
const keypadRef = useTemplateRef<HTMLDivElement>('keypadRef')

// 数字键盘按钮
const keypadButtons = [
  ['7', '8', '9'],
  ['4', '5', '6'],
  ['1', '2', '3'],
  ['.', '0', 'DEL'],
]

onClickOutside(keypadRef, () => {
  panelOpen.value = false
}, { ignore: [`.input-${inputId}`] })

function handleInputFocus() {
  panelOpen.value = true
}

function handleInputBlur(e: FocusEvent) {
  // 检查失焦事件的相关目标是否是数字键盘面板
  const relatedTarget = e.relatedTarget as HTMLElement
  if (relatedTarget && keypadRef.value && keypadRef.value.contains(relatedTarget)) {
    // 如果失焦转移到了数字键盘面板，不关闭面板
    return
  }
  // 如果失焦转移到其他元素，关闭面板
  panelOpen.value = false
}

// 限制值在min和max范围内
function limitValueInRange(value: string): string {
  if (value === '' || value === '.')
    return value

  const numValue = Number(value)
  if (Number.isNaN(numValue))
    return ''

  if (numValue < minValue.value) {
    message.warning(`不能小于最小值 ${minValue.value}`)
    return String(minValue.value)
  }
  if (numValue > maxValue.value) {
    message.warning(`不能大于最大值 ${maxValue.value}`)
    return String(maxValue.value)
  }

  // 处理精度限制
  if (precision.value !== undefined) {
    const [intPart, decimalPart] = value.split('.')
    if (decimalPart && decimalPart.length > precision.value) {
      return `${intPart}.${decimalPart.slice(0, precision.value)}`
    }
  }

  return value
}

function handleKeyPress(key: string) {
  // 将当前值转为字符串
  let currentValue = String(bindVal.value || '')

  if (key === 'DEL') {
    // 删除操作
    if (currentValue.length > 0) {
      // 由于无法获取光标位置，直接删除最后一个字符
      currentValue = currentValue.substring(0, currentValue.length - 1)
    }
  }
  else {
    // 数字或小数点
    // 如果是小数点，检查是否已存在小数点
    if (key === '.' && currentValue.includes('.')) {
      return
    }
    // 如果precision为0，不允许输入小数点
    if (key === '.' && precision.value === 0) {
      return
    }

    // 检查是否超过精度限制
    if (precision.value !== undefined && currentValue.includes('.')) {
      const [, decimalPart] = currentValue.split('.')
      if (decimalPart && decimalPart.length >= precision.value && key !== 'DEL') {
        return
      }
    }

    // 直接在末尾添加数字或小数点
    currentValue = currentValue + key
  }

  // 限制值在范围内
  currentValue = limitValueInRange(currentValue)

  // 更新绑定值
  bindVal.value = currentValue
}

// 检查是否已达到最小值或最大值
const isMinDisabled = computed(() => {
  const currentValue = typeof bindVal.value === 'number' ? bindVal.value : Number(bindVal.value || 0)
  return currentValue <= minValue.value
})

const isMaxDisabled = computed(() => {
  const currentValue = typeof bindVal.value === 'number' ? bindVal.value : Number(bindVal.value || 0)
  return currentValue >= maxValue.value
})

// 处理步进按钮点击事件
function handleStepDecrease(e: Event) {
  e.preventDefault()
  e.stopPropagation()

  if (isMinDisabled.value) {
    message.warning(`已达到最小值 ${minValue.value}`)
    return
  }

  const currentValue = typeof bindVal.value === 'number' ? bindVal.value : Number(bindVal.value || 0)
  let newValue = currentValue - stepValue.value

  // 确保值不小于最小值
  if (newValue < minValue.value) {
    newValue = minValue.value
  }

  // 处理精度
  if (precision.value !== undefined) {
    const factor = 10 ** precision.value
    newValue = Math.round(newValue * factor) / factor
  }

  bindVal.value = newValue
}

function handleStepIncrease(e: Event) {
  e.preventDefault()
  e.stopPropagation()

  if (stepValue.value <= 0) {
    message.warning('步长必须大于0')
    return
  }

  if (isMaxDisabled.value) {
    message.warning(`已达到最大值 ${maxValue.value}`)
    return
  }

  const currentValue = typeof bindVal.value === 'number' ? bindVal.value : Number(bindVal.value || 0)
  let newValue = currentValue + stepValue.value

  // 确保值不大于最大值
  if (newValue > maxValue.value) {
    newValue = maxValue.value
  }

  // 处理精度
  if (precision.value !== undefined) {
    const factor = 10 ** precision.value
    newValue = Math.round(newValue * factor) / factor
  }

  bindVal.value = newValue
}

// 按钮提示文本
const minButtonTitle = computed(() => isMinDisabled.value ? `最小值为 ${minValue.value}` : '')
const maxButtonTitle = computed(() => isMaxDisabled.value ? `最大值为 ${maxValue.value}` : '')
</script>

<template>
  <APopover
    :open="panelOpen"
    trigger="click"
    placement="bottom"
    :auto-focus="false"
  >
    <AInputNumber
      v-bind="$attrs"
      v-model:value="bindVal"
      :bordered="!useStepControls"
      class="w-32 "
      :class="[`input-${inputId}`, { 'step-control-input': useStepControls }]"
      :controls="!useStepControls"
      :style="{ '--border-color': token.colorBorder, '--border-radius': `${token.borderRadius}px`, '--primary-color': token.colorPrimary }"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
    >
      <template v-if="useStepControls" #addonBefore>
        <div
          class="step-control-btn"
          :class="{ disabled: isMinDisabled }"
          :title="minButtonTitle"
          @click="handleStepDecrease"
        >
          <i class="i-ant-design:minus-outlined" :style="{ fontSize: token.fontSize }" />
        </div>
      </template>
      <template v-if="useStepControls" #addonAfter>
        <div
          class="step-control-btn"
          :class="{ disabled: isMaxDisabled }"
          :title="maxButtonTitle"
          @click="handleStepIncrease"
        >
          <i class="i-ant-design:plus-outlined" :style="{ fontSize: token.fontSize }" />
        </div>
      </template>
    </AInputNumber>
    <template #content>
      <div ref="keypadRef" class="numeric-keypad" :style="{ '--numeric-keypad-primary-color': token.colorPrimary, '--numeric-keypad-primary-bg': token.colorPrimaryBg, '--numeric-keypad-primary-bg-hover': token.colorPrimaryBgHover, '--numeric-keypad-primary-active': token.colorPrimaryActive, '--numeric-keypad-primary-text-hover': token.colorPrimaryTextHover, '--numeric-keypad-text-disabled': token.colorTextDisabled }">
        <div class="keypad-grid">
          <div v-for="(row, rowIndex) in keypadButtons" :key="`row-${rowIndex}`" class="keypad-row">
            <button
              v-for="(key, keyIndex) in row" :key="`key-${rowIndex}-${keyIndex}`"
              class="keypad-button select-none" :class="{ 'delete-key': key === 'DEL' }" @click="handleKeyPress(key)"
            >
              {{ key }}
            </button>
          </div>
        </div>
      </div>
    </template>
  </APopover>
</template>

<style scoped lang="less">
.numeric-keypad {
  .keypad-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .keypad-row {
      display: flex;
      gap: 8px;

      .keypad-button {
        width: 65px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid var(--numeric-keypad-primary-color);
        border-radius: 6px;
        background-color: #fff;
        font-size: 22px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        color: var(--numeric-keypad-primary-color);

        &:hover {
          background-color: var(--numeric-keypad-primary-bg);
        }

        &:active {
          background-color: var(--numeric-keypad-primary-bg-hover);
        }

        &.delete-key {
          background-color: var(--numeric-keypad-primary-color);
          color: #fff;
          font-size: 20px;

          &:hover {
            background-color: var(--numeric-keypad-primary-active);
          }

          &:active {
            background-color: var(--numeric-keypad-primary-text-hover);
          }
        }
      }
    }
  }
}

.step-control-input {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);

  &:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.2);
  }

  ::v-deep(.ant-input-number-input) {
    text-align: center;

    &:focus {
      border-color: var(--primary-color);
      box-shadow: none;
    }
  }

  ::v-deep(.ant-input-number-focused) {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.2);
  }

  ::v-deep(.ant-input-number-group-addon) {
    padding: 0;
    background-color: transparent;
    border: none;

    &:first-child {
      border-right: 1px solid var(--border-color);
    }

    &:last-child {
      border-left: 1px solid var(--border-color);
    }
  }

  .step-control-btn {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: var(--numeric-keypad-primary-bg);
    }

    &:active {
      background-color: var(--numeric-keypad-primary-bg-hover);
    }

    &.disabled {
      background-color: #f5f5f5;
      color: rgba(0, 0, 0, 0.25);
      cursor: not-allowed;
      pointer-events: all;

      &:hover {
        background-color: #f5f5f5;
      }
    }
  }
}
</style>
