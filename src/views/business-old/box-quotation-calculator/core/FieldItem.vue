<script setup lang="ts">
import { theme } from 'ant-design-vue'
import { isVNode } from 'vue'

defineOptions({
  name: 'FieldItem',
})

defineProps<{
  label: string
  value?: string | number | VNode
  align?: 'left' | 'right'
  valueAlign?: 'left' | 'right'
  tooltipPosition?: 'left' | 'right'
}>()

const { useToken } = theme
const { token } = useToken()
</script>

<template>
  <div class="field-item-wrapper">
    <div class="grid-layout">
      <div
        class="grid-label"
        :class="[align === 'right' ? 'text-right' : 'text-left']"
        :style="{ color: token.colorTextLabel }"
      >
        {{ label }}：
      </div>
      <div
        class="grid-value"
        :class="[valueAlign === 'left' ? 'justify-start' : 'justify-end']"
      >
        <div class="flex items-center gap-2 w-full" :class="valueAlign === 'left' ? 'justify-start' : 'justify-end'">
          <template v-if="tooltipPosition !== 'right'">
            <slot name="tooltip" />
          </template>
          <slot name="value">
            <Component :is="value" v-if="isVNode(value)" />
            <span v-else>{{ value }}</span>
          </slot>
          <template v-if="tooltipPosition === 'right'">
            <slot name="tooltip" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.field-item-wrapper {
  width: 100%;
  margin-bottom:8px;
}

.grid-layout {
  display: grid;
  grid-template-columns: 6em 1fr;
  gap: 8px;
  align-items: center;
}

.grid-label {
  font-weight: 500;
  white-space: nowrap;
}

.grid-value {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  :deep(.ant-tooltip) {
    display: inline-flex;
    align-items: center;
  }

  > .flex {
    flex: 1;
    min-width: 0;
  }
}
</style>
