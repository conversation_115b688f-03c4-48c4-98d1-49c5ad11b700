<script setup lang="ts">
import { theme } from 'ant-design-vue'

defineOptions({
  name: 'SectionTitle',
})

defineProps<{
  title: string
  description?: string
}>()

const { useToken } = theme
const { token } = useToken()
</script>

<template>
  <div class="">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-[var(--color)] mb-2 pl-4 border-l-4px border-[var(--color)]" :style="{ '--color': token.colorPrimary }">
          {{ title }}
        </h2>
        <p v-show="description" class="text-sm" :style="{ color: token.colorTextDescription }">
          {{ description }}
        </p>
      </div>
      <div class="flex items-center">
        <slot name="actions" />
      </div>
    </div>
    <ADivider class="my-4" />
  </div>
</template>
