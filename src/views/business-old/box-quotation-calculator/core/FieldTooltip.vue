<script setup lang="ts">
import type { ComputedRef } from 'vue'
import { theme } from 'ant-design-vue'

defineProps<{
  title: string | ComputedRef<string>
}>()

const { useToken } = theme
const { token } = useToken()
</script>

<template>
  <ATooltip
    placement="left"
    :title="typeof title === 'object' ? title.value : title"
    :color="token.colorPrimary"
    :overlay-inner-style="{ padding: '8px 12px', maxWidth: '400px' }"
  >
    <i
      class="i-ant-design:question-circle-outlined cursor-pointer text-lg"
      :style="{ color: token.colorPrimary }"
    />
  </ATooltip>
</template>
