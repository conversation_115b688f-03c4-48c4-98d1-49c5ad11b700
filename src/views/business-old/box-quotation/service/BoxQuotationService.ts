import type { BoxQuotationFormParam, BoxQuotationPageParam, BoxQuotationResult } from '@/api/business/box-quotation/model/box-quotation-form-model'
import { boxQuotationApi } from '@/api/business/box-quotation/box-quotation-api'
import { BaseCrudService } from '@/service/BaseCrudService'

/**
 * 纸箱报价服务
 * 提供纸箱报价相关的业务逻辑和数据管理
 */
class BoxQuotationService extends BaseCrudService<BoxQuotationResult, BoxQuotationFormParam, BoxQuotationPageParam> {
  constructor() {
    // 初始化服务
    super(
      '纸箱报价', // 业务名称
      [],
      {
        // 使用已有的API
        queryPage: boxQuotationApi.boxQuotationPage,
        getDetail: (id: number | string) => boxQuotationApi.boxQuotationDetail(Number(id)),
        delete: (id: number | string) => boxQuotationApi.deleteBoxQuotation(Number(id)),
        batchDelete: ids => boxQuotationApi.batchDeleteBoxQuotation(ids.map(id => Number(id))),
        export: boxQuotationApi.exportBoxQuotation,
      },
    )
  }

  /**
   * 获取业务类型标签颜色
   */
  getPrintingMethodColor(type?: string): string {
    switch (type) {
      case 'watermark':
        return 'blue'
      case 'colorPrint':
        return 'purple'
      default:
        return 'default'
    }
  }

  /**
   * 获取箱型标签颜色
   */
  getBoxTypeColor(type?: string): string {
    switch (type) {
      case 'standard':
        return 'green'
      case 'tailored':
        return 'orange'
      case 'telescope':
        return 'cyan'
      case 'folding':
        return 'magenta'
      default:
        return 'default'
    }
  }

  /**
   * 获取状态标签颜色
   */
  getStatusColor(status?: string): string {
    switch (status) {
      case 'DRAFT':
        return 'default'
      case 'SUBMITTED':
        return 'processing'
      case 'APPROVED':
        return 'success'
      case 'REJECTED':
        return 'error'
      default:
        return 'default'
    }
  }
}

// 创建单例实例并导出
export const boxQuotationService = new BoxQuotationService()
