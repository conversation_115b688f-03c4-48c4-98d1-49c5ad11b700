<script lang="ts" setup>
import type { TableColumnsType } from 'ant-design-vue'
import Pagination from '@/components/base-old/ax-pagination/index.vue'
import { AxTableOperateButtons } from '@/components/base/AxTable'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import DictValue from '@/components/utils-old/dict-value/index.vue'
import TableOperator from '@/components/utils-old/table-operator/index.vue'
import { TABLE_ID_CONST } from '@/constants/support/table-id-const'
import {
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { provide, ref, watch } from 'vue'
import BoxQuotationDetail from './components/BoxQuotationDetail.vue'
import { boxQuotationService } from './service/BoxQuotationService'

// 提供服务实例给子组件使用
provide('crudService', boxQuotationService)

// 日期范围值
const dateRangeValue = ref<[string, string] | undefined>(undefined)

// 监听日期变化更新查询参数
watch(dateRangeValue, (newVal) => {
  boxQuotationService.queryParam.quotationDateFrom = newVal?.[0] || undefined
  boxQuotationService.queryParam.quotationDateTo = newVal?.[1] || undefined
})

// 表格列定义
const columns = ref<TableColumnsType>([
  {
    title: '报价单号',
    dataIndex: 'quotationNo',
    width: 160,
  },
  {
    title: '客户名称',
    dataIndex: 'customerName',
    width: 200,
    ellipsis: true,
  },
  {
    title: '报价日期',
    dataIndex: 'quotationDate',
    width: 120,
    sorter: true,
  },
  {
    title: '印刷方式',
    dataIndex: 'printingMethod',
    width: 100,
  },
  {
    title: '箱型',
    dataIndex: 'boxType',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 120,
  },
])
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <a-form v-privilege="'boxQuotation:query'" class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="报价单号" class="smart-query-form-item">
        <a-input v-model:value="boxQuotationService.queryParam.quotationNo" style="width: 200px" placeholder="请输入报价单号" allow-clear />
      </a-form-item>

      <a-form-item label="客户名称" class="smart-query-form-item">
        <a-input v-model:value="boxQuotationService.queryParam.customerName" style="width: 200px" placeholder="请输入客户名称" />
      </a-form-item>

      <a-form-item label="业务类型" class="smart-query-form-item">
        <DictSelect
          v-model:value="boxQuotationService.queryParam.printingMethod" key-code="printing_method" placeholder="请选择印刷方式"
          width="150px"
        />
      </a-form-item>

      <a-form-item label="箱型" class="smart-query-form-item">
        <DictSelect v-model:value="boxQuotationService.queryParam.boxType" key-code="box_type" placeholder="请选择箱型" width="150px" />
      </a-form-item>

      <a-form-item label="状态" class="smart-query-form-item">
        <DictSelect v-model:value="boxQuotationService.queryParam.status" key-code="quotation_status" placeholder="请选择状态" width="150px" />
      </a-form-item>

      <a-form-item label="报价日期" class="smart-query-form-item">
        <a-range-picker
          v-model:value="dateRangeValue"
          style="width: 240px"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="boxQuotationService.onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="boxQuotationService.resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询参数 end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <AxTableOperateButtons
          :operate-param="{
            batchDelete: {
              privilege: 'boxQuotation:delete',
            },
            export: {
              privilege: 'boxQuotation:export',
            },
          }"
        />
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :table-id="TABLE_ID_CONST.BUSINESS.ERP.BOX_QUOTATION" :refresh="boxQuotationService.queryPage" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :data-source="boxQuotationService.getRawTableData()"
      :columns="columns"
      row-key="id"
      bordered
      :pagination="false"
      :loading="boxQuotationService.tableLoading"
      :scroll="{ x: 1000 }"
      :row-selection="{
        selectedRowKeys: boxQuotationService.selectedRowKeyList,
        onChange: boxQuotationService.onSelectChange,
      }"
      @change="boxQuotationService.onChange"
    >
      <template #bodyCell="{ column, record }">
        <!-- 业务类型列 -->
        <template v-if="column.dataIndex === 'printingMethod'">
          <a-tag :color="boxQuotationService.getPrintingMethodColor(record.printingMethod)">
            <DictValue key-code="printing_method" :value-code="record.printingMethod" />
          </a-tag>
        </template>

        <!-- 箱型列 -->
        <template v-if="column.dataIndex === 'boxType'">
          <a-tag :color="boxQuotationService.getBoxTypeColor(record.boxType)">
            <DictValue key-code="box_type" :value-code="record.boxType" />
          </a-tag>
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="boxQuotationService.getStatusColor(record.status)">
            <DictValue key-code="quotation_status" :value-code="record.status" />
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a v-privilege="'boxQuotation:view'" @click="boxQuotationService.openDetailView(record)">
              <EyeOutlined />
              查看
            </a>
            <a v-privilege="'boxQuotation:delete'" class="danger-link" @click="boxQuotationService.deleteEntity(record)">
              <DeleteOutlined />
              删除
            </a>
          </a-space>
        </template>
      </template>
    </a-table>
    <Pagination />
  </a-card>
  <!-- 纸箱报价详情 -->
  <BoxQuotationDetail />
</template>
