<script lang="ts" setup>
import DictTag from '@/components/utils-old/dict-tag/index.vue'
import { formatDateTime } from '@/utils/format'
import { boxQuotationService } from '../service/BoxQuotationService'

// 格式化金额
function formatMoney(value: number | undefined): string {
  if (value === undefined || value === null) {
    return '--'
  }
  return `¥${value.toFixed(2)}`
}
</script>

<template>
  <a-modal
    :open="boxQuotationService.detailOpen"
    :title="boxQuotationService.detailTitle"
    :footer="null"
    width="900px"
    @cancel="boxQuotationService.closeDetail()"
  >
    <a-spin :spinning="boxQuotationService.detailLoading">
      <a-tabs>
        <!-- 基本信息 -->
        <a-tab-pane key="basic" tab="基本信息">
          <a-card class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    报价单号
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.quotationNo || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    客户名称
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.customerName || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    报价日期
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.quotationDate || '--' }}
                  </div>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    业务类型
                  </div>
                  <div class="field-value">
                    <DictTag v-if="boxQuotationService.detailData.printingMethod" key-code="printing_method" :value-code="boxQuotationService.detailData.printingMethod" />
                    <span v-else>--</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    箱型
                  </div>
                  <div class="field-value">
                    <DictTag v-if="boxQuotationService.detailData.boxType" key-code="box_type" :value-code="boxQuotationService.detailData.boxType" />
                    <span v-else>--</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    状态
                  </div>
                  <div class="field-value">
                    <a-tag :color="boxQuotationService.getStatusColor(boxQuotationService.detailData.status)">
                      <DictTag v-if="boxQuotationService.detailData.status" key-code="quotation_status" :value-code="boxQuotationService.detailData.status" />
                      <span v-else>--</span>
                    </a-tag>
                  </div>
                </div>
              </a-col>

              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸箱尺寸(mm)
                  </div>
                  <div class="field-value">
                    <template v-if="boxQuotationService.detailData.boxLength && boxQuotationService.detailData.boxWidth && boxQuotationService.detailData.boxHeight">
                      {{ boxQuotationService.detailData.boxLength }} × {{ boxQuotationService.detailData.boxWidth }} × {{ boxQuotationService.detailData.boxHeight }}
                    </template>
                    <span v-else>--</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸箱数量
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.boxQuantity || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸板层数
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.paperboardLayerCount || '--' }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="供应商信息" class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸板供应商
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.paperboardSupplierName || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    原纸供应商
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.rawPaperSupplierName || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    供应商折扣率
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.supplierDiscount ? `${boxQuotationService.detailData.supplierDiscount}%` : '--' }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="其他信息" class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    是否含税
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.includeTax ? '是' : '否' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    税率
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.taxRate ? `${boxQuotationService.detailData.taxRate}%` : '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    封箱方式
                  </div>
                  <div class="field-value">
                    <DictTag v-if="boxQuotationService.detailData.boxClosingType" key-code="box_closing_type" :value-code="boxQuotationService.detailData.boxClosingType" />
                    <span v-else>--</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    创建时间
                  </div>
                  <div class="field-value">
                    {{ formatDateTime(boxQuotationService.detailData.createTime) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    更新时间
                  </div>
                  <div class="field-value">
                    {{ formatDateTime(boxQuotationService.detailData.updateTime) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    楞型组合
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.corrugatedShapeGroup || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="24">
                <div class="detail-field">
                  <div class="field-label">
                    备注
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.remark || '--' }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-tab-pane>

        <!-- 尺寸计算 -->
        <a-tab-pane key="dimension" tab="尺寸计算">
          <a-card class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col
                v-for="(item, index) in [
                  { label: '纸箱总长(mm)', value: boxQuotationService.detailData.totalLength },
                  { label: '纸箱门宽(mm)', value: boxQuotationService.detailData.boxDoorWidth },
                  { label: '模切门宽(mm)', value: boxQuotationService.detailData.dieCuttingDoorWidth },
                  { label: '模切数', value: boxQuotationService.detailData.dieCuttingQuantity },
                  { label: '瓦纸门宽(mm)', value: boxQuotationService.detailData.corrugatedDoorWidth },
                  { label: '实际瓦纸门宽(mm)', value: boxQuotationService.detailData.actualCorrugatedDoorWidth },
                  { label: '瓦纸开数', value: boxQuotationService.detailData.corrugatedCount },
                  { label: '面纸门宽(mm)', value: boxQuotationService.detailData.facePaperDoorWidth },
                  { label: '实际面纸门宽(mm)', value: boxQuotationService.detailData.actualFacePaperDoorWidth },
                  { label: '面纸开数', value: boxQuotationService.detailData.facePaperCount },
                  { label: '舌头宽度(mm)', value: boxQuotationService.detailData.tongueLength },
                  { label: '摇盖加分(mm)', value: boxQuotationService.detailData.waveCapAddition },
                  { label: '修边位(mm)', value: boxQuotationService.detailData.trimPosition },
                  { label: '纸箱面积(m²)', value: boxQuotationService.detailData.boxArea },
                  { label: '纸箱体积(m³)', value: boxQuotationService.detailData.boxVolume },
                  { label: '瓦纸面积(m²)', value: boxQuotationService.detailData.corrugatedArea },
                ]" :key="index" :span="8"
              >
                <div class="detail-field">
                  <div class="field-label">
                    {{ item.label }}
                  </div>
                  <div class="field-value">
                    {{ item.value || '--' }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-tab-pane>

        <!-- 价格计算 -->
        <a-tab-pane key="price" tab="价格计算">
          <a-card title="原材料价格" class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸板单价(元/个)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.paperboardUnitPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸板总价(元)
                  </div>
                  <div class="field-value strong-value">
                    {{ formatMoney(boxQuotationService.detailData.paperboardTotalPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    原纸单价(元/个)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.rawPaperUnitPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    原纸总价(元)
                  </div>
                  <div class="field-value strong-value">
                    {{ formatMoney(boxQuotationService.detailData.rawPaperTotalPrice) }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="加工价格" class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸板加工费单价(元/个)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.paperboardProcessingUnitPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    水印色数
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.watermarkColors || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    水印单价(元/m²)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.watermarkPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    水印总价(元)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.watermarkTotalPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    模切单价(元/个)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.dieCuttingUnitPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    模切总价(元)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.dieCuttingTotalPrice) }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>

          <a-card title="汇总价格" class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸箱单价(元/个)
                  </div>
                  <div class="field-value strong-value">
                    {{ formatMoney(boxQuotationService.detailData.boxUnitPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    纸箱总价(元)
                  </div>
                  <div class="field-value strong-value">
                    {{ formatMoney(boxQuotationService.detailData.boxTotalPrice) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    总成本(元)
                  </div>
                  <div class="field-value">
                    {{ formatMoney(boxQuotationService.detailData.totalCost) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    利润率
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.profitMargin ? `${boxQuotationService.detailData.profitMargin}%` : '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="detail-field">
                  <div class="field-label">
                    利润金额(元)
                  </div>
                  <div class="field-value profit-value">
                    {{ formatMoney(boxQuotationService.detailData.profitAmount) }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-tab-pane>

        <!-- 审批信息 -->
        <a-tab-pane v-if="boxQuotationService.detailData.status === 'APPROVED' || boxQuotationService.detailData.status === 'REJECTED'" key="approval" tab="审批信息">
          <a-card class="detail-card">
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <div class="detail-field">
                  <div class="field-label">
                    审批人
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.approvalUserId || '--' }}
                  </div>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="detail-field">
                  <div class="field-label">
                    审批时间
                  </div>
                  <div class="field-value">
                    {{ formatDateTime(boxQuotationService.detailData.approvalTime) }}
                  </div>
                </div>
              </a-col>
              <a-col :span="24">
                <div class="detail-field">
                  <div class="field-label">
                    审批备注
                  </div>
                  <div class="field-value">
                    {{ boxQuotationService.detailData.approvalRemark || '--' }}
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-modal>
</template>

<style lang="less" scoped>
.detail-card {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-field {
  margin-bottom: 8px;

  .field-label {
    color: rgba(0, 0, 0, 0.65);
    font-size: 13px;
    margin-bottom: 4px;
  }

  .field-value {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .strong-value {
    font-weight: 600;
  }

  .profit-value {
    font-weight: 600;
    color: #52c41a;
  }
}
</style>
