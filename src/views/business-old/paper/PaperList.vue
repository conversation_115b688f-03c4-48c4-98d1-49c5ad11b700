<script lang="ts" setup>
import ImportExcelModal from '@/components/base-old/ax-import-excel-modal/index.vue'
import { AxTableOperateButtons, AxTable as Table } from '@/components/base/AxTable'
import { ButtonGroup } from '@/components/base/Button'
import { Card } from '@/components/base/Card'
import {
  CustomIconAnchor as IconAnchor,
  CustomIconButton as IconButton,
} from '@/components/base/CustomIcon'
import { FlexRow } from '@/components/base/Flex'
import { Form, FormGrid, FormItem } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { DateRangePicker } from '@/components/base/Picker'
import { DictSelect } from '@/components/utils/Dict'
import PaperDetail from './components/PaperDetail.vue'
import PaperForm from './components/PaperForm.vue'
import { paperService } from './service/PaperService'

// 提供 service
paperService.provide()
</script>

<template>
  <!---------- 查询参数 begin ----------->
  <Card>
    <Form v-privilege="'paper:query'" layout="vertical">
      <FormGrid>
        <FormItem label="描述">
          <InputText v-model:value="paperService.queryParam.description" placeholder="请输入描述" />
        </FormItem>
        <FormItem label="克重">
          <DictSelect v-model:value="paperService.queryParam.weight" key-code="paper_weight" placeholder="请选择克重" />
        </FormItem>
        <FormItem label="楞形">
          <DictSelect
            v-model:value="paperService.queryParam.corrugatedShape" key-code="corrugated_shape"
            placeholder="请选择楞形"
          />
        </FormItem>
        <FormItem label="创建时间">
          <DateRangePicker v-model:from="paperService.queryParam.createTimeFrom" v-model:to="paperService.queryParam.createTimeTo" />
        </FormItem>
      </FormGrid>
    </Form>
  </Card>
  <!---------- 查询参数 end ----------->
  <Card :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <FlexRow>
      <FlexRow>
        <AxTableOperateButtons add-config="paper:add" batch-delete-config="paper:delete" import-config="paper:import" export-config="paper:export" />
      </FlexRow>
      <ButtonGroup>
        <IconButton label="查询" icon-type="SearchOutlined" type="primary" @click="paperService.onSearch" />
        <IconButton label="重置" icon-type="ReloadOutlined" @click="paperService.resetQuery" />
      </ButtonGroup>
    </FlexRow>
    <!---------- 表格操作行 end ----------->
    <br>
    <!-- 使用组件化的表格 -->
    <Table>
      <template #bodyCell="{ column, record }">
        <!-- 只为操作列添加模板插槽 -->
        <template v-if="column.dataIndex === 'action'">
          <FlexRow justify="start">
            <IconAnchor
              v-privilege="'paper:view'" label="查看" icon-type="EyeOutlined"
              @click="paperService.openDetailView(record.id!)"
            />
            <IconAnchor
              v-privilege="'paper:edit'" label="编辑" icon-type="EditOutlined"
              @click="paperService.openEditForm(record.id!)"
            />
            <IconAnchor
              v-privilege="'paper:delete'" label="删除" icon-type="DeleteOutlined" color="red"
              @click="paperService.deleteEntity(record.id!)"
            />
          </FlexRow>
        </template>
      </template>
    </Table>
  </Card>
  <!-- 导入弹窗 -->
  <ImportExcelModal />
  <!-- 纸张表单 -->
  <PaperForm />
  <!-- 纸张详情 -->
  <PaperDetail />
</template>
