<script lang="ts" setup>
import type { FormInstance } from 'ant-design-vue/es/form'
import type { PaperService } from '../service/PaperService'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
// 框架组件
import { Modal } from '@/components/base/Modal'
import { Radio, RadioGroup } from '@/components/base/Radio'
import { Spin } from '@/components/base/Spin'
import DictSelect from '@/components/utils-old/dict-select/index.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject, ref } from 'vue'
// 表单验证规则
import { rules } from './rule'

// 获取服务实例
const paperService = inject<PaperService>(CRUD_KEY)!
// 表单引用
const formRef = ref<FormInstance>()

// 表单提交
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await paperService.submitFormAndRefresh()
}
</script>

<template>
  <Modal
    :open="paperService.formOpen"
    :title="paperService.formTitle"
    :confirm-loading="paperService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    @ok="handleSubmit"
    @cancel="paperService.closeForm()"
  >
    <Spin :spinning="paperService.formLoading">
      <Form ref="formRef" layout="vertical" :model="paperService.formData" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="纸张编码" name="code">
              <InputText v-model:value="paperService.formData.code" placeholder="请输入纸张编码" :disabled="paperService.formType === 'edit'" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="纸张类别" name="paperCategory">
              <DictSelect
                v-model:value="paperService.formData.paperCategory" key-code="paper_category" placeholder="请选择纸张类别"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="克重(g)" name="weight">
              <DictSelect v-model:value="paperService.formData.weight" key-code="paper_weight" placeholder="请选择克重" width="100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="楞形" name="corrugatedShape">
              <DictSelect
                v-model:value="paperService.formData.corrugatedShape" key-code="corrugated_shape" placeholder="请选择楞形"
                width="100%"
              />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="纸张描述" name="description">
              <InputText v-model:value="paperService.formData.description" placeholder="请输入纸张描述" allow-clear />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="状态" name="status">
              <RadioGroup v-model:value="paperService.formData.status">
                <Radio :value="1" label="启用" class="radio-style" />
                <Radio :value="0" label="禁用" class="radio-style" />
              </RadioGroup>
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>

<style scoped>
.radio-style {
  margin-right: 16px;
}
</style>
