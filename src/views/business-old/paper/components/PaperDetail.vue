<script lang="ts" setup>
import type { PaperService } from '../service/PaperService'
import { CustomText as Text } from '@/components/base/CustomText'
import { Descriptions, DescriptionsItem } from '@/components/base/Descriptions'
import { Modal } from '@/components/base/Modal'

import { Spin } from '@/components/base/Spin'
// 框架组件
import { DictTag } from '@/components/utils/Dict'
// import DictTag from '@/components/support/dict-tag/BoxSelect.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { formatDateTime } from '@/utils/format'
import { inject } from 'vue'
import { formatStatus } from '../columns'

// 获取服务实例
const paperService = inject<PaperService>(CRUD_KEY)!
</script>

<template>
  <Modal :open="paperService.detailOpen" :title="paperService.detailTitle" :footer="null" @cancel="paperService.closeDetail()">
    <Spin :spinning="paperService.detailLoading">
      <Descriptions :column="2" :colon="false" bordered>
        <DescriptionsItem label="纸张编码">
          <Text :value="paperService.detailData.code" />
        </DescriptionsItem>
        <DescriptionsItem label="纸张类别">
          <DictTag key-code="paper_category" :value-code="paperService.detailData.paperCategory" />
        </DescriptionsItem>
        <DescriptionsItem label="克重(g)">
          <DictTag key-code="paper_weight" :value-code="paperService.detailData.weight" />
        </DescriptionsItem>
        <DescriptionsItem label="楞形">
          <DictTag key-code="corrugated_shape" :value-code="paperService.detailData.corrugatedShape" />
        </DescriptionsItem>
        <DescriptionsItem label="纸张描述">
          <Text :value="paperService.detailData.description" />
        </DescriptionsItem>
        <DescriptionsItem label="状态">
          <DictTag key-code="paper_status" :value-code="formatStatus(paperService.detailData.status) || ''" />
        </DescriptionsItem>
        <DescriptionsItem label="创建时间">
          <Text :value="formatDateTime(paperService.detailData.createTime)" />
        </DescriptionsItem>
        <DescriptionsItem label="更新时间">
          <Text :value="formatDateTime(paperService.detailData.updateTime)" />
        </DescriptionsItem>
      </Descriptions>
    </Spin>
  </Modal>
</template>
