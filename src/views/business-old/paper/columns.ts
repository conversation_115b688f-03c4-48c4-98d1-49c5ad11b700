import type { PaperResult } from '@/api/business/paper/model/paper-types'
import type { DictColor } from '@/components/utils/Dict/type'
import { ColorPreview } from '@/components/utils/Color'
import { DictTag, DictValue } from '@/components/utils/Dict'
import { h } from 'vue'

// 获取纸张类别对应的颜色
export function getPaperCategoryColor(category?: string): DictColor {
  if (!category)
    return 'default'
  // 根据类别返回不同颜色
  switch (category) {
    case '1':
      return 'blue'
    case '2':
      return 'green'
    case '3':
      return 'orange'
    default:
      return 'default'
  }
}

// 获取状态对应的颜色
export function getStatusColor(status?: number | boolean): DictColor {
  return status === 1 ? 'green' : 'red'
}

// 格式化状态
export function formatStatus(status?: number | boolean): string {
  return status === 1 ? '启用' : '禁用'
}

// 列定义
export const paperColumns = [
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    ellipsis: true,
  },
  {
    title: '纸张编码',
    dataIndex: 'code',
    width: 100,
  },
  {
    title: '纸张类别',
    dataIndex: 'category',
    width: 120,
    customRender: ({ record }: { record: PaperResult }) => {
      return h(DictTag, { color: getPaperCategoryColor(record.paperCategory), keyCode: 'paper_category', valueCode: record.paperCategory })
    },
  },
  {
    title: '颜色',
    dataIndex: 'color',
    width: 100,
    customRender: ({ record }: { record: PaperResult }) => {
      return h(ColorPreview, { color: record.color })
    },
  },
  {
    title: '克重(g)',
    dataIndex: 'weight',
    width: 100,
    customRender: ({ record }: { record: PaperResult }) => {
      return h(DictValue, { keyCode: 'paper_weight', valueCode: record.weight })
    },
  },
  {
    title: '楞形',
    dataIndex: 'corrugatedShape',
    width: 100,
    customRender: ({ record }: { record: PaperResult }) => {
      return h(DictValue, { keyCode: 'corrugated_shape', valueCode: record.corrugatedShape })
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ record }: { record: PaperResult }) => {
      return h(DictTag, { color: getStatusColor(record.status), keyCode: 'paper_status', valueCode: formatStatus(record.status) },
      )
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]

// 操作列渲染函数
