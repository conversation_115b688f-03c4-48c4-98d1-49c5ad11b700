import type { PaperForm, PaperPageParam, PaperResult } from '@/api/business/paper/model/paper-types'
import { paperApi } from '@/api/business/paper/paper-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { paperColumns } from '../columns'

/**
 * 纸张服务
 * 提供纸张相关的业务逻辑和数据管理
 */
export class PaperService extends BaseCrudService<PaperResult, PaperForm, PaperPageParam> {
  constructor() {
    // 初始化服务
    super(
      '纸张', // 业务名称
      paperColumns,
      {
        // 使用已有的API
        add: paperApi.addPaper,
        queryPage: paperApi.paperPage,
        getDetail: paperApi.paperDetail,
        update: paperApi.updatePaper,
        import: paperApi.importPaper,
        export: paperApi.exportPaper,
        delete: paperApi.deletePaper,
        batchDelete: paperApi.batchDeletePaper,
        downloadTemplate: paperApi.exportPaper,
      },
    )
  }
}

// 单例模式
export const paperService = new PaperService()
