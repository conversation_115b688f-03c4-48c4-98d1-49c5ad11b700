<!--
  * 角色 树形结构
-->
<script setup lang="ts">
import { roleMenuApi } from '@/api/system/role-menu-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import { smartSentry } from '@/lib/smart-sentry'
import { useRoleStore } from '@/store/modules/system/role'
import { message } from 'ant-design-vue'
import { inject, ref, watch } from 'vue'
import RoleTreeCheckbox from './role-tree-checkbox.vue'
import { isEmpty } from 'lodash-es'

const roleStore = useRoleStore()
const tree = ref()
const selectRoleId = inject('selectRoleId')

watch(selectRoleId, () => getRoleSelectedMenu(), {
  immediate: true,
})

async function getRoleSelectedMenu() {
  if (!selectRoleId.value) {
    return
  }
  const res = await roleMenuApi.getRoleSelectedMenu(selectRoleId.value)
  const data = res.data
  if (isEmpty(roleStore.treeMap)) {
    roleStore.initTreeMap(data.menuTreeList || [])
  }
  roleStore.initCheckedData(data.selectedMenuId || [])
  tree.value = data.menuTreeList
}
async function saveChange() {
  const checkedData = roleStore.checkedData
  if (isEmpty(checkedData)) {
    message.error('还未选择任何权限')
    return
  }
  const params = {
    roleId: selectRoleId.value,
    menuIdList: checkedData,
  }
  SmartLoading.show()
  try {
    await roleMenuApi.updateRoleMenu(params)
    message.success('保存成功')
  }
  catch (error) {
    smartSentry.captureError(error)
  }
  finally {
    SmartLoading.hide()
  }
}
</script>

<template>
  <div>
    <div class="tree-header">
      <p>设置角色对应的功能操作、后台管理权限</p>
      <a-button v-if="selectRoleId" v-privilege="'system:role:menu:update'" type="primary" @click="saveChange">
        保存
      </a-button>
    </div>
    <!-- 功能权限勾选部分 -->
    <RoleTreeCheckbox :tree="tree" />
  </div>
</template>

<style scoped lang="less">
  @import 'index.less';
</style>
