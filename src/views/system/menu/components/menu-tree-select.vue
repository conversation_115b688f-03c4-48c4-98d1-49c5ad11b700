<!--
  * 菜单 表单 树形下拉框
-->
<script setup lang="ts">
import { menuApi } from '@/api/system/menu-api'
import { onMounted, ref } from 'vue'

const props = defineProps({
  value: Number,
})

const emit = defineEmits(['update:value'])
let treeData = ref([])
async function queryMenuTree() {
  let res = await menuApi.queryMenuTree(true)
  treeData.value = res.data
}

onMounted(queryMenuTree)

function treeSelectChange(e) {
  emit('update:value', e)
}

defineExpose({
  queryMenuTree,
})
</script>

<template>
  <a-tree-select
    :value="props.value"
    :tree-data="treeData"
    :field-names="{ label: 'menuName', key: 'menuId', value: 'menuId' }"
    show-search
    style="width: 100%"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    placeholder="请选择菜单"
    allow-clear
    tree-default-expand-all
    tree-node-filter-prop="menuName"
    @change="treeSelectChange"
  />
</template>
