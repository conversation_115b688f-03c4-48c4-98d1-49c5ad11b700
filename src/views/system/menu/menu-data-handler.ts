/*
 * 此文件是处理 菜单数据的类，主要用于：
 * 1、菜单树形表格的构造
 * 2、菜单的前端过滤
 */

/**
 * 过滤菜单
 * @param {*} menuList
 * @param {*} queryForm
 * @returns
 */
export function filterMenuByQueryForm(menuList, queryForm) {
  if (!menuList || menuList.length === 0) {
    return []
  }

  const filterResult = []
  for (const menu of menuList) {
    if (isMenuExistKeywords(menu, queryForm.keywords) && isMenuExistMenuType(menu, queryForm.menuType) && isMenuExistMenuFlag(menu, queryForm)) {
      filterResult.push(menu)
    }
  }
  return filterResult
}

/**
 * 构建菜单表格树形数据
 */
export function buildMenuTableTree(menuList) {
  const topMenuList = []
  const menuIdSet = new Set()
  for (const menu of menuList) {
    menuIdSet.add(menu.menuId)
  }

  for (const menu of menuList) {
    const parentId = menu.parentId
    // 不存在父节点，则为顶级菜单
    if (!menuIdSet.has(parentId)) {
      topMenuList.push(menu)
    }
  }

  recursiveMenuTree(menuList, topMenuList)
  return topMenuList
}

/**
 * 递归遍历菜单树形数据
 * @param {*} menuList
 * @param {*} parentArray
 */
function recursiveMenuTree(menuList, parentArray) {
  for (const parent of parentArray) {
    const children = menuList.filter(e => e.parentId === parent.menuId)
    if (children.length > 0) {
      parent.children = children
      recursiveMenuTree(menuList, parent.children)
    }
  }
}

/**
 * 过滤菜单状态
 * @param {*} menu
 * @param {*} queryForm
 * @returns
 */
function isMenuExistMenuFlag(menu, queryForm) {
  let frameFlagCondition = false
  if (!_isNil(queryForm.frameFlag)) {
    frameFlagCondition = !_isNil(menu.frameFlag) && menu.frameFlag === (queryForm.frameFlag === 1)
  }
  else {
    frameFlagCondition = true
  }

  let cacheFlagCondition = false
  if (!_isNil(queryForm.cacheFlag)) {
    cacheFlagCondition = !_isNil(menu.cacheFlag) && menu.cacheFlag === (queryForm.cacheFlag === 1)
  }
  else {
    cacheFlagCondition = true
  }

  let visibleFlagCondition = false
  if (!_isNil(queryForm.visibleFlag)) {
    visibleFlagCondition = !_isNil(menu.visibleFlag) && menu.visibleFlag === (queryForm.visibleFlag === 1)
  }
  else {
    visibleFlagCondition = true
  }

  let disabledFlagCondition = false
  if (!_isNil(queryForm.disabledFlag)) {
    disabledFlagCondition = !_isNil(menu.disabledFlag) && menu.disabledFlag === (queryForm.disabledFlag === 1)
  }
  else {
    disabledFlagCondition = true
  }

  return frameFlagCondition && cacheFlagCondition && visibleFlagCondition && disabledFlagCondition
}

/**
 * 过滤菜单类型
 * @param {*} menu
 * @param {*} menuType
 * @returns
 */
function isMenuExistMenuType(menu, menuType) {
  if (!menuType) {
    return true
  }

  if (menu.menuType && menu.menuType === menuType) {
    return true
  }
  return false
}

/**
 * 过滤关键字
 */
function isMenuExistKeywords(menu: any, keywords: string) {
  if (!keywords) {
    return true
  }

  if (menu.component && menu.component.includes(keywords)) {
    return true
  }

  if (menu.menuName && menu.menuName.includes(keywords)) {
    return true
  }
  if (menu.path && menu.path.includes(keywords)) {
    return true
  }
  if (menu.apiPerms && menu.apiPerms.includes(keywords)) {
    return true
  }
  if (menu.webPerms && menu.webPerms.includes(keywords)) {
    return true
  }
  return false
}
