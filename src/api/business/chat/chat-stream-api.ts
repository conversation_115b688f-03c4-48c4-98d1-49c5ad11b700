import type { ResponseModel } from '@/api/base-model/response-model'
import { getRequest } from '@/lib/http-request'

/**
 * 聊天流式API，适配SpringAiFunctionDemo的/ai/spring/stream-demo接口
 */
export const chatStreamApi = {
  /**
   * 通过EventSource发送流式聊天请求
   * @param question 问题内容
   * @param sessionId 会话ID
   * @param onMessage 接收消息的回调函数
   * @param onOpen 连接打开的回调函数
   * @param onError 连接错误的回调函数
   * @returns 返回EventSource实例
   */
  createEventSourceChat: (
    question: string,
    sessionId: string,
    onMessage: (data: string) => void,
    onOpen?: (event: Event) => void,
    onError?: (event: Event) => void,
  ): EventSource => {
    // 获取baseURL
    const baseURL = import.meta.env.VITE_APP_API_URL || ''
    // 适配SpringAiFunctionDemo的接口
    const url = `${baseURL}/ai/spring/stream-demo?question=${encodeURIComponent(question)}&sessionId=${sessionId}`

    // 创建EventSource，带上withCredentials选项以支持认证
    const eventSource = new EventSource(url, { withCredentials: true })

    // 注册事件
    if (onOpen) {
      eventSource.onopen = onOpen
    }

    eventSource.onmessage = (event) => {
      onMessage(event.data)
    }

    if (onError) {
      eventSource.onerror = onError
    }
    else {
      eventSource.onerror = (_event) => {
        eventSource.close()
      }
    }

    return eventSource
  },

  /**
   * 中断流式响应（清除会话记忆）
   * @param conversationId 会话ID
   */
  abortStreamResponse: (conversationId: string): Promise<ResponseModel<boolean>> => {
    // 适配SpringAiFunctionDemo的清除记忆接口
    return getRequest<ResponseModel<boolean>>(`/ai/spring/clear-memory?sessionId=${conversationId}`, {})
  },
}
