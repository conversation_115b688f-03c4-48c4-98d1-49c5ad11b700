import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 聊天消息模型
 */
export interface ChatModel extends BaseModel {
  /**
   * 消息ID
   */
  id?: string

  /**
   * 消息角色：用户或助手
   */
  role?: 'user' | 'assistant'

  /**
   * 消息内容
   */
  content?: string

  /**
   * 会话ID
   */
  conversationId?: string

  /**
   * 消息创建时间
   */
  createTime?: string

  /**
   * 用户ID
   */
  userId?: string

  /**
   * 消息状态
   * 0: 待处理
   * 1: 处理中
   * 2: 已完成
   * 3: 失败
   */
  status?: number
}
