import type { ChatModel } from './chat-model'

/**
 * 聊天消息对象
 */
export interface ChatMessage {
  /**
   * 消息ID
   */
  id: string

  /**
   * 消息角色
   */
  role: 'user' | 'assistant'

  /**
   * 消息内容
   */
  text: string
}

/**
 * 聊天请求参数
 */
export interface ChatRequestParam {
  /**
   * 用户查询内容
   */
  query: string

  /**
   * 用户标识
   */
  user: string

  /**
   * 会话ID
   */
  conversation_id: string
}

/**
 * 聊天消息查询参数
 */
export interface ChatQueryParam extends ChatModel {
  /**
   * 会话ID
   */
  conversationId?: string
}

/**
 * 聊天查询结果
 */
export interface ChatResult extends ChatModel {
  /**
   * 用户名称
   */
  username?: string
}

/**
 * 聊天流式响应事件类型
 */
export enum ChatStreamEventType {
  /**
   * 消息块
   */
  MESSAGE_CHUNK = 'message_chunk',

  /**
   * 消息结束
   */
  MESSAGE_END = 'message_end',

  /**
   * 错误
   */
  ERROR = 'error',
}
