import type { PageParam } from '@/api/base-model/page-model'
import type { PriceModel } from '@/api/business/price/model/price-model'
import type { ProductAttrModel } from '@/api/business/product-attr/model/product-attr-model'
import type { ProductCategoryRelationModel } from '@/api/business/product-category-relation/model/product-category-relation-model'
import type { ProductModel } from '@/api/business/product/model/product-model'
import type { SkuSpecRelationModel } from '@/api/business/sku-spec-relation/model/sku-spec-relation-model'
import type { SkuModel } from '@/api/business/sku/model/sku-model'
import type { SkuResult } from '../../sku/model/sku-form-model'

/**
 * 商品基础信息查询参数
 */
export interface ProductQueryParam extends ProductModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string

  /**
   * 关联分类ID
   */
  relatedCategoryId?: number

  /**
   * 品牌编码
   */
  brandCode?: string

  /**
   * 商品状态列表 (多选)
   */
  statusList?: string[]

  /**
   * 商品来源列表 (多选)
   */
  sourceTypeList?: string[]

  /**
   * SKU数量最小值
   */
  skuCountMin?: number

  /**
   * SKU数量最大值
   */
  skuCountMax?: number

  /**
   * 删除标志：0-未删除，1-已删除
   * 覆盖父接口的属性，确保类型一致
   */
  deletedFlag?: boolean
}

/**
 * 商品基础信息分页查询参数
 */
export interface ProductPageParam extends ProductQueryParam {
  pageParam: PageParam
}

/**
 * 商品基础信息表单参数类型
 */
export interface ProductFormParam extends ProductModel {
  /**
   * 商品多分类关联列表
   */
  categoryRelations?: ProductCategoryRelationModel[]

  /**
   * 商品属性列表
   */
  attributes?: ProductAttrModel[]
}

/**
 * 商品规格模型
 */
export interface Prodspec {
  /**
   * 规格ID
   */
  id?: number

  /**
   * 规格名称
   */
  name: string

  /**
   * 排序
   */
  sort?: number

  /**
   * 规格值列表
   */
  values?: SpecValueModel[]
}

/**
 * 商品基础信息查询结果类型
 */
export interface ProductResult extends ProductModel {
  /**
   * 商品多分类关联列表
   */
  categoryRelations?: ProductCategoryRelationModel[]

  /**
   * 商品属性列表
   */
  attributes?: ProductAttrModel[]

  /**
   * 规格列表
   */
  specs?: Prodspec[]

  /**
   * SKU列表
   */
  skus?: ProductSkuModel[]

  /**
   * 商品SKU列表（分页查询返回）
   */
  skuList?: SkuResult[]
}

/**
 * 规格值模型（自定义）
 */
export interface SpecValueModel {
  /**
   * 规格值ID
   */
  id?: number

  /**
   * 规格ID
   */
  specId?: number

  /**
   * 规格值
   */
  value: string

  /**
   * 规格值状态：0-禁用，1-启用
   */
  status?: number

  /**
   * 排序
   */
  sort?: number
}
