import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品基础信息类型定义
 */
export interface ProductModel extends BaseModel {
  /**
   * 商品基础信息ID
   */
  id?: number

  /**
   * 商品编码
   */
  productCode?: string

  /**
   * 商品名称
   */
  name?: string

  /**
   * 商品描述
   */
  description?: string

  /**
   * 单位
   */
  unit?: string

  /**
   * 分类ID | t_category的id
   */
  categoryId?: number

  /**
   * 分类名称
   */
  categoryName?: string

  /**
   * 品牌ID | erp_brand的id
   */
  brandId?: number

  /**
   * 品牌名称
   */
  brandName?: string

  /**
   * 字典 | 商品状态 product_status：0-下架，1-上架，2-审核中，3-已禁用
   */
  status?: string

  /**
   * 字典 | 商品来源 source_type：1-自产，2-外购，3-委外加工，4-虚拟品
   */
  sourceType?: string

  /**
   * 是否原材料：0-否，1-是
   */
  isRawMaterial?: boolean

  /**
   * 最小起订量
   */
  minOrderQty?: number

}
