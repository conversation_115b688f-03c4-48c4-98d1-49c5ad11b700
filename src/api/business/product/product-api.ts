import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  ProductFormParam,
  ProductPageParam,
  ProductQueryParam,
  ProductResult,
} from './model/product-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const productApi = {

  // 分页查询商品基础信息
  productPage: (param: ProductPageParam): Promise<ResponseModel<PageResult<ProductResult>>> => {
    return postRequest<ResponseModel<PageResult<ProductResult>>, ProductQueryParam>('/product/productPage', param)
  },
  // 列表查询商品基础信息
  productList: (param: ProductQueryParam): Promise<ResponseModel<ProductResult[]>> => {
    return postRequest<ResponseModel<ProductResult[]>, ProductQueryParam>('/product/productList', param)
  },
  // 获取商品基础信息详情
  productDetail: (productId: number): Promise<ResponseModel<ProductResult>> => {
    return getRequest<ResponseModel<ProductResult>>(`/product/productDetail/${productId}`, {})
  },
  // 添加商品基础信息
  addProduct: (param: ProductFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductFormParam>('/product/addProduct', param)
  },
  // 更新商品基础信息
  updateProduct: (param: ProductFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductFormParam>('/product/updateProduct', param)
  },
  // 删除商品基础信息
  deleteProduct: (productId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/product/deleteProduct/${productId}`, {})
  },
  // 批量删除商品基础信息
  batchDeleteProduct: (productIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/product/batchDeleteProduct', productIdList)
  },

  // 导入商品基础信息
  importProduct: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/product/importProductExcel', file)
  },
  // 导出商品基础信息
  exportProduct: (param?: ProductQueryParam): void => {
    return getDownload('/product/exportProductExcel', param || {})
  },
}
