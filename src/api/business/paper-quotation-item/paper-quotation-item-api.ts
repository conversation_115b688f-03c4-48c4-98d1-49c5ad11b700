/* */
import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { PaperQuotationItemAddParam, PaperQuotationItemPageParam, PaperQuotationItemQueryParam, PaperQuotationItemResult, PaperQuotationItemUpdateParam } from './model/paper-quotation-item-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const paperQuotationItemApi = {
  // 添加纸张报价详情
  addPaperQuotationItem: (param: PaperQuotationItemAddParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperQuotationItemAddParam>('/paperQuotationItem/addPaperQuotationItem', param)
  },
  // 删除纸张报价详情
  deletePaperQuotationItem: (paperQuotationItemId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/paperQuotationItem/deletePaperQuotationItem/${paperQuotationItemId}`, {})
  },
  // 批量删除纸张报价详情
  batchDeletePaperQuotationItem: (paperQuotationItemIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/paperQuotationItem/batchDeletePaperQuotationItem', paperQuotationItemIdList)
  },
  // 分页查询纸张报价详情
  paperQuotationItemPage: (param: PaperQuotationItemPageParam): Promise<ResponseModel<PageResult<PaperQuotationItemResult>>> => {
    return postRequest<ResponseModel<PageResult<PaperQuotationItemResult>>, PaperQuotationItemQueryParam>('/paperQuotationItem/paperQuotationItemPage', param)
  },
  // 列表查询纸张报价详情
  paperQuotationItemList: (param: PaperQuotationItemQueryParam): Promise<ResponseModel<PaperQuotationItemResult[]>> => {
    return postRequest<ResponseModel<PaperQuotationItemResult[]>, PaperQuotationItemQueryParam>('/paperQuotationItem/paperQuotationItemList', param)
  },

  // 更新纸张报价详情
  updatePaperQuotationItem: (param: PaperQuotationItemUpdateParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperQuotationItemUpdateParam>('/paperQuotationItem/updatePaperQuotationItem', param)
  },
  // 获取纸张报价详情详情
  getPaperQuotationItemDetail: (paperQuotationItemId: number): Promise<ResponseModel<PaperQuotationItemResult>> => {
    return getRequest<ResponseModel<PaperQuotationItemResult>>(`/paperQuotationItem/paperQuotationItemDetail/${paperQuotationItemId}`, {})
  },
  // 导入纸张报价详情
  importPaperQuotationItem: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/paperQuotationItem/importPaperQuotationItemExcel', file)
  },
  // 导出纸张报价详情
  exportPaperQuotationItem: (param?: PaperQuotationItemQueryParam): void => {
    return getDownload('/paperQuotationItem/exportPaperQuotationItemExcel', param || {})
  },
}
