import type { PageParamModel } from '@/api/base-model/page-param-model'
import type { PaperQuotationItemModel } from './paper-quotation-item-model'

/**
 * 纸张报价详情查询参数
 */
export interface PaperQuotationItemQueryParam {
  /**
   * 创建时间始
   */
  createTimeFrom?: string

  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string

  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 纸张报价详情分页查询参数
 */
export interface PaperQuotationItemPageParam extends PaperQuotationItemQueryParam {
  pageParam: PageParamModel
}

/**
 * 纸张报价详情添加参数类型
 */
export interface PaperQuotationItemAddParam extends PaperQuotationItemModel {

}

/**
 * 纸张报价详情更新参数类型
 */
export interface PaperQuotationItemUpdateParam extends PaperQuotationItemModel {

}

/**
 * 纸张报价详情查询结果类型
 */
export interface PaperQuotationItemResult extends PaperQuotationItemModel {

}
