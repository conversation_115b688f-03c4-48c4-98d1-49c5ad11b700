import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 纸张报价详情表类型定义
 */
export interface PaperQuotationItemModel extends BaseModel {
  /**
   * 纸张报价详情表ID
   */
  id?: number

  /**
   * 报价单ID
   */
  quotationId?: number

  /**
   * 纸张ID
   */
  paperId?: number

  /**
   * 纸张描述
   */
  paperDescription?: string

  /**
   * 字典|paper_category 纸张类别
   */
  paperCategory?: string

  /**
   * 字典|corrugated_shape 楞形
   */
  corrugatedShape?: string

  /**
   * 字典 | paper_unit 单位：SQUARE_METER-平米，TON-吨，SHEET-张
   */
  unit?: string

  /**
   * 单价
   */
  unitPrice?: number

  /**
   * 排序
   */
  sort?: number

}
