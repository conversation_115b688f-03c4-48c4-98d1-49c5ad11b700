import type {PageParam} from '@/api/base-model/page-model'
import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存信息类型定义
 */
export interface StockModel extends BaseModel {
  id?: number // 库存信息ID

  warehouseId?: number // 仓库ID | erp_warehouse表的id

  warehouseCode?: string // 仓库编码

  warehouseName?: string // 仓库名称

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  currentStock?: number // 当前库存

  availableStock?: number // 可用库存 (当前库存-预留库存)

  reservedStock?: number // 预留库存 (已下单未发货)

  inTransitStock?: number // 在途库存 (已采购未入库)

  minStock?: number // 最小库存 (安全库存)

  maxStock?: number // 最大库存

  reorderPoint?: number // 再订货点

  reorderQuantity?: number // 再订货量

  avgCost?: number // 平均成本

  lastCost?: number // 最新成本

  totalValue?: number // 库存总价值 (当前库存×平均成本)

  stockStatus?: string // 字典 | 库存状态 stock_status：NORMAL-正常，LOW_STOCK-库存不足，OUT_OF_STOCK-缺货，OVERSTOCKED-库存过多，FROZEN-冻结

  freezeQuantity?: number // 冻结数量

  lastInDate?: string // 最后入库日期

  lastOutDate?: string // 最后出库日期

  lastCheckDate?: string // 最后盘点日期

}
/**
 * 库存信息查询参数
 */
export interface StockQueryParam extends StockModel {

                lastInDateFrom?: string // 最后入库日期始

                lastInDateTo?: string // 最后入库日期至

                lastOutDateFrom?: string // 最后出库日期始

                lastOutDateTo?: string // 最后出库日期至

                lastCheckDateFrom?: string // 最后盘点日期始

                lastCheckDateTo?: string // 最后盘点日期至

                createTimeFrom?: string // 创建时间始

                createTimeTo?: string // 创建时间至

                updateTimeFrom?: string // 更新时间始

                updateTimeTo?: string // 更新时间至
}

/**
 * 库存信息分页查询参数
 */
export interface StockPageParam extends StockQueryParam {
    pageParam: PageParam
}

/**
 * 库存信息表单类型
 */
export interface StockForm extends StockModel {

}

/**
 * 库存信息查询结果类型
 */
export interface StockResult extends StockModel {

}
