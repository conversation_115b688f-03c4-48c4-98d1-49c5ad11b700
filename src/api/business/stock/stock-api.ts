import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  StockForm,
  StockPageParam,
  StockQueryParam,
  StockResult,
} from './model/stock-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const stockApi = {

  // 分页查询库存信息
  stockPage: (param: StockPageParam): Promise<ResponseModel<PageResult<StockResult>>> => {
    return postRequest<ResponseModel<PageResult<StockResult>>, StockQueryParam>('/stock/stockPage', param)
  },
  // 列表查询库存信息
  stockList: (param: StockQueryParam): Promise<ResponseModel<StockForm[]>> => {
    return postRequest<ResponseModel<StockForm[]>, StockQueryParam>('/stock/stockList', param)
  },
  // 获取库存信息详情
  stockDetail: (stockId: number): Promise<ResponseModel<StockResult>> => {
    return getRequest<ResponseModel<StockResult>>(`/stock/stockDetail/${stockId}`, {})
  },
  // 添加库存信息
  addStock: (param: StockForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockForm>('/stock/addStock', param)
  },
  // 更新库存信息
  updateStock: (param: StockForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockForm>('/stock/updateStock', param)
  },
  // 删除库存信息
  deleteStock: (stockId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/stock/deleteStock/${stockId}`, {})
  },
  // 批量删除库存信息
  batchDeleteStock: (stockIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/stock/batchDeleteStock', stockIdList)
  },

  // 导入库存信息
  importStock: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/stock/importStockExcel', file)
  },
  // 导出库存信息
  exportStock: (param?: StockQueryParam): void => {
    return getDownload('/stock/exportStockExcel', param || {})
  },
}
