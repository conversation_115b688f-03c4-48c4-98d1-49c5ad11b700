import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 采购订单明细类型定义
 */
export interface PurchaseOrderItem extends BaseModel {
  id?: number // 采购订单明细ID

  orderId?: number // 采购订单ID | erp_purchase_order表的id

  orderNo?: string // 采购订单编号

  lineNo?: number // 行号

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  productSpec?: string // 商品规格

  productUnit?: string // 计量单位

  orderQuantity?: number // 采购数量

  receivedQuantity?: number // 已收货数量

  unitPrice?: number // 单价

  discountRate?: number // 折扣率

  discountAmount?: number // 折扣金额

  taxRate?: number // 税率

  taxAmount?: number // 税额

  lineAmount?: number // 行金额 (数量*单价-折扣+税额)

  deliveryDate?: string // 要求交货日期

  warehouseId?: number // 收货仓库ID | erp_warehouse表的id

  warehouseName?: string // 收货仓库名称

  qualityStandard?: string // 质量标准

  technicalRequirement?: string // 技术要求

  remark?: string // 备注

}
/**
 * 采购订单明细查询参数
 */
export interface PurchaseOrderItemQueryParam extends PurchaseOrderItem {

  deliveryDateFrom?: string // 要求交货日期始

  deliveryDateTo?: string // 要求交货日期至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 采购订单明细分页查询参数
 */
export interface PurchaseOrderItemPageParam extends PurchaseOrderItemQueryParam {
  pageParam: PageParam
}

/**
 * 采购订单明细表单类型
 */
export interface PurchaseOrderItemForm extends PurchaseOrderItem {

}

/**
 * 采购订单明细查询结果类型
 */
export interface PurchaseOrderItemResult extends PurchaseOrderItem {

}
