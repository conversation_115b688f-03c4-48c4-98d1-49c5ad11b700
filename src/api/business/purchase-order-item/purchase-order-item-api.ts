import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PurchaseOrderItemForm,
  PurchaseOrderItemPageParam,
  PurchaseOrderItemQueryParam,
  PurchaseOrderItemResult,
} from './model/purchase-order-item-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const purchaseOrderItemApi = {

  // 分页查询采购订单明细
  purchaseOrderItemPage: (param: PurchaseOrderItemPageParam): Promise<ResponseModel<PageResult<PurchaseOrderItemResult>>> => {
    return postRequest<ResponseModel<PageResult<PurchaseOrderItemResult>>, PurchaseOrderItemQueryParam>('/purchaseOrderItem/purchaseOrderItemPage', param)
  },
  // 列表查询采购订单明细
  purchaseOrderItemList: (param: PurchaseOrderItemQueryParam): Promise<ResponseModel<PurchaseOrderItemForm[]>> => {
    return postRequest<ResponseModel<PurchaseOrderItemForm[]>, PurchaseOrderItemQueryParam>('/purchaseOrderItem/purchaseOrderItemList', param)
  },
  // 获取采购订单明细详情
  purchaseOrderItemDetail: (purchaseOrderItemId: number): Promise<ResponseModel<PurchaseOrderItemResult>> => {
    return getRequest<ResponseModel<PurchaseOrderItemResult>>(`/purchaseOrderItem/purchaseOrderItemDetail/${purchaseOrderItemId}`, {})
  },
  // 添加采购订单明细
  addPurchaseOrderItem: (param: PurchaseOrderItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchaseOrderItemForm>('/purchaseOrderItem/addPurchaseOrderItem', param)
  },
  // 更新采购订单明细
  updatePurchaseOrderItem: (param: PurchaseOrderItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchaseOrderItemForm>('/purchaseOrderItem/updatePurchaseOrderItem', param)
  },
  // 删除采购订单明细
  deletePurchaseOrderItem: (purchaseOrderItemId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/purchaseOrderItem/deletePurchaseOrderItem/${purchaseOrderItemId}`, {})
  },
  // 批量删除采购订单明细
  batchDeletePurchaseOrderItem: (purchaseOrderItemIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/purchaseOrderItem/batchDeletePurchaseOrderItem', purchaseOrderItemIdList)
  },

  // 导入采购订单明细
  importPurchaseOrderItem: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/purchaseOrderItem/importPurchaseOrderItemExcel', file)
  },
  // 导出采购订单明细
  exportPurchaseOrderItem: (param?: PurchaseOrderItemQueryParam): void => {
    return getDownload('/purchaseOrderItem/exportPurchaseOrderItemExcel', param || {})
  },
}
