import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { BoxFormParam, BoxPageParam, BoxQueryParam, BoxResult } from './model/box-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const boxApi = {

  // 分页查询纸箱箱形
  boxPage: (param: BoxPageParam): Promise<ResponseModel<PageResult<BoxResult>>> => {
    return postRequest<ResponseModel<PageResult<BoxResult>>, BoxQueryParam>('/box/boxPage', param)
  },
  // 列表查询纸箱箱形
  boxList: (param: BoxQueryParam): Promise<ResponseModel<BoxResult[]>> => {
    return postRequest<ResponseModel<BoxResult[]>, BoxQueryParam>('/box/boxList', param)
  },
  // 获取纸箱箱形详情
  boxDetail: (boxId: number | string): Promise<ResponseModel<BoxResult>> => {
    return getRequest<ResponseModel<BoxResult>>(`/box/boxDetail/${boxId}`, {})
  },
  // 添加纸箱箱形
  addBox: (param: BoxFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BoxFormParam>('/box/addBox', param)
  },
  // 更新纸箱箱形
  updateBox: (param: BoxFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BoxFormParam>('/box/updateBox', param)
  },
  // 删除纸箱箱形
  deleteBox: (boxId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/box/deleteBox/${boxId}`, {})
  },
  // 批量删除纸箱箱形
  batchDeleteBox: (boxIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/box/batchDeleteBox', boxIdList)
  },

  // 导入纸箱箱形
  importBox: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/box/importBoxExcel', file)
  },
  // 导出纸箱箱形
  exportBox: (param?: BoxQueryParam): void => {
    return getDownload('/box/exportBoxExcel', param || {})
  },
}
