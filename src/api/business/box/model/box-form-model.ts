import type { PageParam } from '@/api/base-model/page-model'
import type { BoxModel } from '@/api/business/box/model/box-model'

/**
 * 纸箱箱形查询参数
 */
export interface BoxQueryParam extends BoxModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 纸箱箱形分页查询参数
 */
export interface BoxPageParam extends BoxQueryParam {
  pageParam: PageParam
}

/**
 * 纸箱箱形表单参数类型
 */
export interface BoxFormParam extends BoxModel {
  boxConfig: BoxConfig
}

/**
 * 纸箱箱形查询结果类型
 */
export interface BoxResult extends BoxModel {
  boxConfig?: BoxConfig
}

export interface BoxConfig {
  needDieCutting?: boolean // 是否需要裁切
  needPrintingPlate?: boolean // 是否需要打印板
  doorWidthFormula?: string // 门宽公式
  totalLengthFormula?: string // 总长度公式
  doorWidthFormulaDesc?: string // 门宽公式描述
  totalLengthFormulaDesc?: string // 总长度公式描述
}
