import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 纸箱箱形类型定义
 */
export interface BoxModel extends BaseModel {
  /**
   * 纸箱箱形ID
   */
  id?: number

  /**
   * 箱型编码
   */
  boxTypeCode?: string

  /**
   * 箱型名称
   */
  boxTypeName?: string

  /**
   * 箱型描述
   */
  boxTypeDesc?: string

  /**
   * 箱型图片URL
   */
  imageUrl?: string

  /**
   * 是否启用(1启用/0禁用)
   */
  isActive?: boolean

  /**
   * 参数JSON
   */
  paramsJson?: string

  /**
   * 排序
   */
  sort?: number

}
