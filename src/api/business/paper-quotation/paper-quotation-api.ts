/*
 * @Description: 纸张报价管理API
 */
import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { LatestPaperQuotationQueryParam, PaperQuotationFormParam,PaperQuotationPageParam, PaperQuotationQueryParam, PaperQuotationResult } from './model/paper-quotation-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const paperQuotationApi = {
  // 添加纸张报价
  addPaperQuotation: (param: PaperQuotationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperQuotationFormParam>('/paperQuotation/addPaperQuotation', param)
  },
  // 删除纸张报价
  deletePaperQuotation: (paperQuotationId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/paperQuotation/deletePaperQuotation/${paperQuotationId}`, {})
  },
  // 批量删除纸张报价
  batchDeletePaperQuotation: (paperQuotationIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/paperQuotation/batchDeletePaperQuotation', paperQuotationIdList)
  },
  // 分页查询纸张报价
  paperQuotationPage: (param: PaperQuotationPageParam): Promise<ResponseModel<PageResult<PaperQuotationResult>>> => {
    return postRequest<ResponseModel<PageResult<PaperQuotationResult>>, PaperQuotationQueryParam>('/paperQuotation/paperQuotationPage', param)
  },

  // 查询供应商最新报价
  getLatestPaperQuotation: (param: LatestPaperQuotationQueryParam): Promise<ResponseModel<PaperQuotationResult>> => {
    return postRequest<ResponseModel<PaperQuotationResult>, LatestPaperQuotationQueryParam>('/paperQuotation/getLatestPaperQuotation', param)
  },

  // 列表查询纸张报价
  paperQuotationList: (param: PaperQuotationQueryParam): Promise<ResponseModel<PaperQuotationResult[]>> => {
    return postRequest<ResponseModel<PaperQuotationResult[]>, PaperQuotationQueryParam>('/paperQuotation/paperQuotationList', param)
  },

  // 更新纸张报价
  updatePaperQuotation: (param: PaperQuotationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperQuotationFormParam>('/paperQuotation/updatePaperQuotation', param)
  },
  // 获取纸张报价详情
  getPaperQuotationDetail: (paperQuotationId: number): Promise<ResponseModel<PaperQuotationResult>> => {
    return getRequest<ResponseModel<PaperQuotationResult>>(`/paperQuotation/paperQuotationDetail/${paperQuotationId}`, {})
  },
  // 导入纸张报价
  importPaperQuotation: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/paperQuotation/importPaperQuotationExcel', file)
  },
  // 导出纸张报价
  exportPaperQuotation: (param?: PaperQuotationQueryParam): void => {
    return getDownload('/paperQuotation/exportPaperQuotationExcel', param || {})
  },
}
