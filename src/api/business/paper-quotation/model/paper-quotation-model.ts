import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 纸张报价类型定义
 */
export interface PaperQuotationModel extends BaseModel {
  /**
   * 纸张报价ID
   */
  id?: number

  /**
   * 报价单号
   */
  quotationNo?: string

  /**
   * 供应商ID
   */
  supplierId?: number

  /**
   * 供应商名称
   */
  supplierName?: string

  /**
   * 报价日期
   */
  quotationDate?: string

  /**
   * 字典 | quotation_type 报价类型：CORRUGATED-瓦纸报价，RAW-原纸报价
   */
  quotationType?: string

  /**
   * 供应商折扣率
   */
  supplierDiscount?: number

  /**
   * 税率
   */
  taxRate?: number

  /**
   * 每平米2层加工费
   */
  processingFeeTwoLayer?: number

  /**
   * 每平米3层加工费
   */
  processingFeeThreeLayer?: number

  /**
   * 每平米4层加工费
   */
  processingFeeFourLayer?: number

  /**
   * 每平米5层加工费
   */
  processingFeeFiveLayer?: number

  /**
   * 每平米6层加工费
   */
  processingFeeSixLayer?: number

  /**
   * 每平米7层加工费
   */
  processingFeeSevenLayer?: number

  /**
   * 字典|paper_category_corrugated_shape 瓦纸类别楞型
   */
  paperCategoryCorrugatedShape?: string

  /**
   * 备注
   */
  remark?: string

  /**
   * 字典 | quotation_status 状态：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，REJECTED-已拒绝，EXPIRED-已过期
   */
  status?: string

  /**
   * 审批人ID
   */
  approvalUserId?: number

  /**
   * 审批时间
   */
  approvalTime?: string

  /**
   * 审批备注
   */
  approvalRemark?: string

}
