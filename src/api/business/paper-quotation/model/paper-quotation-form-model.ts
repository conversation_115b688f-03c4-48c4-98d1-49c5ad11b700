import type { PageParam } from '@/api/base-model/page-model'
import type { PaperQuotationItemModel } from '../../paper-quotation-item/model/paper-quotation-item-model'
import type { PaperQuotationModel } from './paper-quotation-model'

/**
 * 纸张报价查询参数
 */
export interface PaperQuotationQueryParam extends PaperQuotationModel {
  /**
   * 报价日期始
   */
  quotationDateFrom?: string

  /**
   * 报价日期至
   */
  quotationDateTo?: string
  /**
   * 审批时间始
   */
  approvalTimeFrom?: string

  /**
   * 审批时间至
   */
  approvalTimeTo?: string
  /**
   * 创建时间始
   */
  createTimeFrom?: string

  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string

  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 查询供应商最新报价参数
 */
export interface LatestPaperQuotationQueryParam {
  /**
   * 供应商ID
   */
  supplierId: number
  /**
   * 纸张类别
   */
  paperCategory?: string
  /**
   * 纸张种类
   */
  paperTypeList?: string[]

  /**
   * 纸张结构层
   */
  paperStructureLayer?: string
  /**
   * 纸张楞型
   */
  corrugatedShape?: string
}

/**
 * 纸张报价分页查询参数
 */
export interface PaperQuotationPageParam extends PaperQuotationQueryParam {
  pageParam: PageParam
}

/**
 * 纸张报价表单参数类型
 */
export interface PaperQuotationFormParam extends PaperQuotationModel {
  paperQuotationItemList: PaperQuotationItemModel[]
}

/**
 * 纸张报价查询结果类型
 */
export interface PaperQuotationResult extends PaperQuotationModel {
  paperQuotationItemList: PaperQuotationItemModel[]
}
