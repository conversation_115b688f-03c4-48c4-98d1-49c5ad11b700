import type { PageParam } from '@/api/base-model/page-model'
import type { WarehouseModel } from '@/api/business/warehouse/model/warehouse-model'

/**
 * 仓库信息查询参数
 */
export interface WarehouseQueryParam extends WarehouseModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 仓库信息分页查询参数
 */
export interface WarehousePageParam extends WarehouseQueryParam {
  pageParam: PageParam
}

/**
 * 仓库信息表单参数类型
 */
export interface WarehouseFormParam extends WarehouseModel {

}

/**
 * 仓库信息查询结果类型
 */
export interface WarehouseResult extends WarehouseModel {

}
