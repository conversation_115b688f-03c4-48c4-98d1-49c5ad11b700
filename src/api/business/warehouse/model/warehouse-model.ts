import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 仓库信息类型定义
 */
export interface WarehouseModel extends BaseModel {
  id?: number // 仓库信息ID

  warehouseCode?: string // 仓库编码

  warehouseName?: string // 仓库名称

  warehouseType?: string // 字典 | 仓库类型 warehouse_type：RAW_MATERIAL-原材料仓库，FINISHED_GOODS-成品仓库，SEMI_FINISHED-半成品仓库，SPARE_PARTS-备件仓库，RETURN_GOODS-退货仓库，TRANSIT-中转仓库

  province?: string // 省份

  city?: string // 城市

  district?: string // 区县

  address?: string // 详细地址

  postalCode?: string // 邮政编码

  contactPerson?: string // 联系人

  contactPhone?: string // 联系电话

  contactMobile?: string // 联系手机

  contactEmail?: string // 联系邮箱

  area?: number // 仓库面积(平方米)

  capacity?: number // 仓库容量

  temperatureControlled?: boolean // 是否温控：FALSE-否，TRUE-是

  minTemperature?: number // 最低温度

  maxTemperature?: number // 最高温度

  humidityControlled?: boolean // 是否湿度控制：FALSE-否，TRUE-是

  minHumidity?: number // 最低湿度

  maxHumidity?: number // 最高湿度

  managerId?: number // 仓库管理员ID | t_employee表的employee_id

  managerName?: string // 仓库管理员姓名

  parentWarehouseId?: number // 上级仓库ID | erp_warehouse表的id

  warehouseLevel?: number // 仓库层级

  status?: string // 字典 | 仓库状态 warehouse_status：ACTIVE-活跃，INACTIVE-非活跃，MAINTENANCE-维护中，CLOSED-已关闭

  isDefault?: boolean // 是否默认仓库：FALSE-否，TRUE-是

  sortOrder?: number // 排序

  remark?: string // 备注

}
