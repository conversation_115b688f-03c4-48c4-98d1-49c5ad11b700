import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  WarehouseFormParam,
  WarehousePageParam,
  WarehouseQueryParam,
  WarehouseResult,
} from './model/warehouse-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const warehouseApi = {

  // 分页查询仓库信息
  warehousePage: (param: WarehousePageParam): Promise<ResponseModel<PageResult<WarehouseResult>>> => {
    return postRequest<ResponseModel<PageResult<WarehouseResult>>, WarehouseQueryParam>('/warehouse/warehousePage', param)
  },
  // 列表查询仓库信息
  warehouseList: (): Promise<ResponseModel<WarehouseResult[]>> => {
    return getRequest<ResponseModel<WarehouseResult[]>>('/warehouse/warehouseList', {})
  },
  // 获取仓库信息详情
  warehouseDetail: (warehouseId: number): Promise<ResponseModel<WarehouseResult>> => {
    return getRequest<ResponseModel<WarehouseResult>>(`/warehouse/warehouseDetail/${warehouseId}`, {})
  },
  // 添加仓库信息
  addWarehouse: (param: WarehouseFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, WarehouseFormParam>('/warehouse/addWarehouse', param)
  },
  // 更新仓库信息
  updateWarehouse: (param: WarehouseFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, WarehouseFormParam>('/warehouse/updateWarehouse', param)
  },
  // 删除仓库信息
  deleteWarehouse: (warehouseId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/warehouse/deleteWarehouse/${warehouseId}`, {})
  },
  // 批量删除仓库信息
  batchDeleteWarehouse: (warehouseIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/warehouse/batchDeleteWarehouse', warehouseIdList)
  },

  // 导入仓库信息
  importWarehouse: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/warehouse/importWarehouseExcel', file)
  },
  // 导出仓库信息
  exportWarehouse: (param?: WarehouseQueryParam): void => {
    return getDownload('/warehouse/exportWarehouseExcel', param || {})
  },

  // 查询活跃仓库列表
  activeWarehouseList: (): Promise<ResponseModel<WarehouseResult[]>> => {
    return getRequest<ResponseModel<WarehouseResult[]>>('/warehouse/activeWarehouseList', {})
  },
}
