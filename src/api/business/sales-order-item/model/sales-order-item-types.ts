import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 订单明细类型定义
 */
export interface SalesOrderItem extends BaseModel {
  id?: number // 订单明细ID

  orderId?: number // 订单ID | erp_sales_order表的id

  orderNo?: string // 订单编号

  lineNo?: number // 行号

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  quantity?: number // 数量

  unitPrice?: number // 单价

  discountRate?: number // 折扣率

  discountAmount?: number // 折扣金额

  lineAmount?: number // 行金额 (数量×单价-折扣金额)

  deliveryDate?: string // 交货日期

  deliveryQuantity?: number // 已交货数量

  remainingQuantity?: number // 剩余数量

  itemStatus?: string // 字典 | 明细状态 item_status：PENDING-待处理，CONFIRMED-已确认，PRODUCTION-生产中，SHIPPED-已发货，DELIVERED-已交付，COMPLETED-已完成，CANCELLED-已取消

  remark?: string // 备注

}
/**
 * 订单明细查询参数
 */
export interface SalesOrderItemQueryParam extends SalesOrderItem {

  deliveryDateFrom?: string // 交货日期始

  deliveryDateTo?: string // 交货日期至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 订单明细分页查询参数
 */
export interface SalesOrderItemPageParam extends SalesOrderItemQueryParam {
  pageParam: PageParam
}

/**
 * 订单明细表单类型
 */
export interface SalesOrderItemForm extends SalesOrderItem {

}

/**
 * 订单明细查询结果类型
 */
export interface SalesOrderItemResult extends SalesOrderItem {

}
