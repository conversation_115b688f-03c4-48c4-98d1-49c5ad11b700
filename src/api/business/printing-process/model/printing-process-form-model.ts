import type { PageParamModel } from '@/api/base-model/page-param-model'
import type { PrintingProcessModel } from './printing-process-model'

/**
 * 印刷工艺表查询参数
 */
export interface PrintingProcessQueryParam extends PrintingProcessModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 印刷工艺表分页查询参数
 */
export interface PrintingProcessPageParam extends PrintingProcessQueryParam {
  pageParam: PageParamModel
}

/**
 * 印刷工艺表添加参数类型
 */
export interface PrintingProcessFormParam extends PrintingProcessModel {

}

/**
 * 印刷工艺表查询结果类型
 */
export interface PrintingProcessResult extends PrintingProcessModel {

}
