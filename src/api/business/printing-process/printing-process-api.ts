import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PrintingProcessFormParam,
  PrintingProcessPageParam,
  PrintingProcessQueryParam,
  PrintingProcessResult,
} from './model/printing-process-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const printingProcessApi = {

  // 分页查询印刷工艺表
  printingProcessPage: (param: PrintingProcessPageParam): Promise<ResponseModel<PageResult<PrintingProcessResult>>> => {
    return postRequest<ResponseModel<PageResult<PrintingProcessResult>>, PrintingProcessQueryParam>('/printingProcess/printingProcessPage', param)
  },
  // 列表查询印刷工艺表
  printingProcessList: (param: PrintingProcessQueryParam): Promise<ResponseModel<PrintingProcessResult[]>> => {
    return postRequest<ResponseModel<PrintingProcessResult[]>, PrintingProcessQueryParam>('/printingProcess/printingProcessList', param)
  },
  // 获取印刷工艺表详情
  printingProcessDetail: (printingProcessId: number): Promise<ResponseModel<PrintingProcessResult>> => {
    return getRequest<ResponseModel<PrintingProcessResult>>(`/printingProcess/printingProcessDetail/${printingProcessId}`, {})
  },
  // 添加印刷工艺表
  addPrintingProcess: (param: PrintingProcessFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PrintingProcessFormParam>('/printingProcess/addPrintingProcess', param)
  },
  // 更新印刷工艺表
  updatePrintingProcess: (param: PrintingProcessFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PrintingProcessFormParam>('/printingProcess/updatePrintingProcess', param)
  },
  // 删除印刷工艺表
  deletePrintingProcess: (printingProcessId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/printingProcess/deletePrintingProcess/${printingProcessId}`, {})
  },
  // 批量删除印刷工艺表
  batchDeletePrintingProcess: (printingProcessIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/printingProcess/batchDeletePrintingProcess', printingProcessIdList)
  },

  // 导入印刷工艺表
  importPrintingProcess: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/printingProcess/importPrintingProcessExcel', file)
  },
  // 导出印刷工艺表
  exportPrintingProcess: (param?: PrintingProcessQueryParam): void => {
    return getDownload('/printingProcess/exportPrintingProcessExcel', param || {})
  },
}
