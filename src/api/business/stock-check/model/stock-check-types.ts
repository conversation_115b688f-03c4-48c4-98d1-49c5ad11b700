import type {PageParam} from '@/api/base-model/page-model'
import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存盘点类型定义
 */
export interface StockCheckModel extends BaseModel {
  id?: number // 库存盘点ID

  checkNo?: string // 盘点单号

  checkName?: string // 盘点名称

  warehouseId?: number // 仓库ID | erp_warehouse表的id

  warehouseCode?: string // 仓库编码

  warehouseName?: string // 仓库名称

  checkType?: string // 字典 | 盘点类型 check_type：FULL-全盘，PARTIAL-部分盘点，CYCLE-循环盘点，SPOT-抽盘

  checkDate?: string // 盘点日期

  checkStatus?: string // 字典 | 盘点状态 check_status：DRAFT-草稿，IN_PROGRESS-盘点中，COMPLETED-已完成，APPROVED-已审批，CANCELLED-已取消

  checkScope?: string // 字典 | 盘点范围 check_scope：ALL-全部商品，CATEGORY-按分类，PRODUCT-指定商品

  productCategoryIds?: string // 商品分类ID列表 (JSON格式)

  productIds?: string // 商品ID列表 (JSON格式)

  totalItems?: number // 盘点商品总数

  checkedItems?: number // 已盘点商品数

  profitItems?: number // 盘盈商品数

  lossItems?: number // 盘亏商品数

  totalProfitAmount?: number // 盘盈总金额

  totalLossAmount?: number // 盘亏总金额

  checkerId?: number // 盘点人ID | t_employee表的employee_id

  checkerName?: string // 盘点人姓名

  checkStartTime?: string // 盘点开始时间

  checkEndTime?: string // 盘点结束时间

  approvalUserId?: number // 审批人ID | t_employee表的employee_id

  approvalTime?: string // 审批时间

  approvalRemark?: string // 审批备注

  remark?: string // 备注

}

/**
 * 库存盘点明细项
 */
export interface StockCheckItemParam {
  id?: number // 明细ID
  warehouseId?: number // 仓库ID
  productId?: number // 商品ID
  productCode?: string // 商品编码
  productName?: string // 商品名称
  skuId?: number // SKU ID
  skuCode?: string // SKU编码
  specSummary?: string // 规格摘要
  unit?: string // 单位
  bookStock?: number // 账面库存
  actualStock?: number // 实盘库存
  differenceStock?: number // 差异数量
  differenceType?: string // 差异类型
  unitCost?: number // 单位成本
  bookAmount?: number // 账面金额
  actualAmount?: number // 实盘金额
  differenceAmount?: number // 差异金额
  checkStatus?: string // 盘点状态
  isChecked?: boolean // 是否已盘点
  checkTime?: string // 盘点时间
  checkerId?: number // 盘点人ID
  checkerName?: string // 盘点人姓名
  differenceReason?: string // 差异原因
  remark?: string // 备注
}

/**
 * 库存盘点保存参数（包含明细）
 */
export interface StockCheckSaveParam extends StockCheckModel {
  items: StockCheckItemParam[] // 盘点明细项列表
}

/**
 * 库存盘点查询参数
 */
export interface StockCheckQueryParam extends StockCheckModel {

                checkDateFrom?: string // 盘点日期始

                checkDateTo?: string // 盘点日期至

                checkStartTimeFrom?: string // 盘点开始时间始

                checkStartTimeTo?: string // 盘点开始时间至

                checkEndTimeFrom?: string // 盘点结束时间始

                checkEndTimeTo?: string // 盘点结束时间至

                approvalTimeFrom?: string // 审批时间始

                approvalTimeTo?: string // 审批时间至

                createTimeFrom?: string // 创建时间始

                createTimeTo?: string // 创建时间至

                updateTimeFrom?: string // 更新时间始

                updateTimeTo?: string // 更新时间至
}

/**
 * 库存盘点分页查询参数
 */
export interface StockCheckPageParam extends StockCheckQueryParam {
    pageParam: PageParam
}

/**
 * 库存盘点表单类型
 */
export interface StockCheckForm extends StockCheckModel {

}

/**
 * 库存盘点查询结果类型
 */
export interface StockCheckResult extends StockCheckModel {

}
