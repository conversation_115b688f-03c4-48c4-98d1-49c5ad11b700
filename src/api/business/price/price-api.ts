import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PriceFormParam,
  PricePageParam,
  PriceQueryParam,
  PriceResult,
} from './model/price-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const priceApi = {

  // 分页查询商品价格
  pricePage: (param: PricePageParam): Promise<ResponseModel<PageResult<PriceResult>>> => {
    return postRequest<ResponseModel<PageResult<PriceResult>>, PriceQueryParam>('/price/pricePage', param)
  },
  // 列表查询商品价格
  priceList: (param: PriceQueryParam): Promise<ResponseModel<PriceResult[]>> => {
    return postRequest<ResponseModel<PriceResult[]>, PriceQueryParam>('/price/priceList', param)
  },
  // 获取商品价格详情
  priceDetail: (priceId: number): Promise<ResponseModel<PriceResult>> => {
    return getRequest<ResponseModel<PriceResult>>(`/price/priceDetail/${priceId}`, {})
  },
  // 添加商品价格
  addPrice: (param: PriceFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PriceFormParam>('/price/addPrice', param)
  },
  // 更新商品价格
  updatePrice: (param: PriceFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PriceFormParam>('/price/updatePrice', param)
  },
  // 删除商品价格
  deletePrice: (priceId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/price/deletePrice/${priceId}`, {})
  },
  // 批量删除商品价格
  batchDeletePrice: (priceIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/price/batchDeletePrice', priceIdList)
  },

  // 导入商品价格
  importPrice: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/price/importPriceExcel', file)
  },
  // 导出商品价格
  exportPrice: (param?: PriceQueryParam): void => {
    return getDownload('/price/exportPriceExcel', param || {})
  },
}

