import type { PageParam } from '@/api/base-model/page-model'
import type { PriceModel } from '@/api/business/price/model/price-model'

/**
 * 商品价格查询参数
 */
export interface PriceQueryParam extends PriceModel {
  /**
   * 价格生效日期始
   */
  effectiveDateFrom?: string
  /**
   * 价格生效日期至
   */
  effectiveDateTo?: string
  /**
   * 价格失效日期始
   */
  expiredDateFrom?: string
  /**
   * 价格失效日期至
   */
  expiredDateTo?: string
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品价格分页查询参数
 */
export interface PricePageParam extends PriceQueryParam {
  pageParam: PageParam
}

/**
 * 商品价格表单参数类型
 */
export interface PriceFormParam extends PriceModel {

}

/**
 * 商品价格查询结果类型
 */
export interface PriceResult extends PriceModel {

}

