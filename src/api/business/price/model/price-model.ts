import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品价格类型定义
 */
export interface PriceModel extends BaseModel {
  /**
   * 商品价格ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId?: number

  /**
   * 商品名称
   */
  productName?: string

  /**
   * SKU ID | erp_sku的id
   */
  skuId?: number

  /**
   * SKU编码
   */
  skuCode?: string

  /**
   * 规格摘要
   */
  specSummary?: string

  /**
   * 供应商ID | erp_supplier的id
   */
  supplierId?: number

  /**
   * 供应商名称
   */
  supplierName?: string

  /**
   * 客户ID | erp_customer的id
   */
  customerId?: number

  /**
   * 客户名称
   */
  customerName?: string

  /**
   * 供应商商品编码
   */
  supplierItemCode?: string

  /**
   * 是否首选供应商：0-否，1-是
   */
  isPreferredSupplier?: boolean

  /**
   * 价格
   */
  price?: number

  /**
   * 字典 | 价格类型 price_type：1-标准价，2-促销价，3-VIP价，4-成本价
   */
  priceType?: string

  /**
   * 最小采购量
   */
  minPurchaseQty?: number

  /**
   * 采购提前期(天)
   */
  leadTime?: number

  /**
   * 价格生效日期
   */
  effectiveDate?: string

  /**
   * 价格失效日期
   */
  expiredDate?: string

  /**
   * 价格状态：0-失效，1-生效
   */
  status?: boolean

}
