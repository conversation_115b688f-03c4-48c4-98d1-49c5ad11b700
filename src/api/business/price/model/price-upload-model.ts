/**
 * 商品价格上传参数模型
 */
export interface PriceUploadParam {
  /**
   * 商品价格ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId: number
  /**
   * 商品名称(冗余)
   */
  productName?: string
  /**
   * SKU ID | erp_sku的id
   */
  skuId: number
  /**
   * SKU编码(冗余)
   */
  skuCode?: string
  /**
   * 规格摘要(冗余)
   */
  specSummary?: string
  /**
   * 供应商ID
   */
  supplierId?: number
  /**
   * 供应商名称(冗余)
   */
  supplierName?: string
  /**
   * 客户ID
   */
  customerId?: number
  /**
   * 客户名称(冗余)
   */
  customerName?: string
  /**
   * 供应商商品编码
   */
  supplierItemCode?: string
  /**
   * 字典 | 是否首选供应商：0-否，1-是
   */
  isPreferredSupplier?: boolean
  /**
   * 价格
   */
  price: string
  /**
   * 字典 | 价格类型：1-标准价，2-促销价，3-VIP价，4-成本价
   */
  priceType: boolean
  /**
   * 最小采购量
   */
  minPurchaseQty?: number
  /**
   * 采购提前期(天)
   */
  leadTime?: number
  /**
   * 价格生效日期
   */
  effectiveDate: string
  /**
   * 价格失效日期
   */
  expiredDate?: string
  /**
   * 字典 | 价格状态：0-失效，1-生效
   */
  status: boolean
}
