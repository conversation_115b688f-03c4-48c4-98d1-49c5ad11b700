import type { BaseModel } from '@/api/base-model/base-model'

/**
 * SKU规格值关联类型定义
 */
export interface SkuSpecRelationModel extends BaseModel {
  /**
   * SKU规格值关联ID
   */
  id?: number

  /**
   * SKU ID | erp_sku的id
   */
  skuId?: number

  /**
   * SKU编码
   */
  skuCode?: string

  /**
   * 规格ID | erp_spec的id
   */
  specId?: number

  /**
   * 规格名称
   */
  specName?: string

  /**
   * 规格值ID | erp_spec_value的id
   */
  specValueId?: number

  /**
   * 规格值
   */
  specValueText?: string

  /**
   * 自定义规格值
   */
  customValue?: string

}
