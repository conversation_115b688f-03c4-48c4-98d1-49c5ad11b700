/**
 * SKU规格值关联上传参数模型
 */
export interface SkuSpecRelationUploadParam {
  /**
   * SKU规格值关联ID
   */
  id?: number

  /**
   * SKU ID | erp_sku的id
   */
  skuId: number
  /**
   * SKU编码(冗余)
   */
  skuCode?: string
  /**
   * 规格ID | erp_spec的id
   */
  specId: number
  /**
   * 规格名称(冗余)
   */
  specName?: string
  /**
   * 规格值ID | erp_spec_value的id
   */
  specValueId?: number
  /**
   * 规格值(冗余)
   */
  specValueText?: string
  /**
   * 自定义规格值
   */
  customValue?: string
}
