import type { PageParam } from '@/api/base-model/page-model'
import type { SkuSpecRelationModel } from '@/api/business/sku-spec-relation/model/sku-spec-relation-model'

/**
 * SKU规格值关联查询参数
 */
export interface SkuSpecRelationQueryParam extends SkuSpecRelationModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * SKU规格值关联分页查询参数
 */
export interface SkuSpecRelationPageParam extends SkuSpecRelationQueryParam {
  pageParam: PageParam
}

/**
 * SKU规格值关联表单参数类型
 */
export interface SkuSpecRelationFormParam extends SkuSpecRelationModel {

}

/**
 * SKU规格值关联查询结果类型
 */
export interface SkuSpecRelationResult extends SkuSpecRelationModel {

}

