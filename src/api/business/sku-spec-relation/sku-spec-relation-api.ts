import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  SkuSpecRelationFormParam,
  SkuSpecRelationPageParam,
  SkuSpecRelationQueryParam,
  SkuSpecRelationResult,
} from './model/sku-spec-relation-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const skuSpecRelationApi = {

  // 分页查询SKU规格值关联
  skuSpecRelationPage: (param: SkuSpecRelationPageParam): Promise<ResponseModel<PageResult<SkuSpecRelationResult>>> => {
    return postRequest<ResponseModel<PageResult<SkuSpecRelationResult>>, SkuSpecRelationQueryParam>('/skuSpecRelation/skuSpecRelationPage', param)
  },
  // 列表查询SKU规格值关联
  skuSpecRelationList: (param: SkuSpecRelationQueryParam): Promise<ResponseModel<SkuSpecRelationResult[]>> => {
    return postRequest<ResponseModel<SkuSpecRelationResult[]>, SkuSpecRelationQueryParam>('/skuSpecRelation/skuSpecRelationList', param)
  },
  // 获取SKU规格值关联详情
  skuSpecRelationDetail: (skuSpecRelationId: number): Promise<ResponseModel<SkuSpecRelationResult>> => {
    return getRequest<ResponseModel<SkuSpecRelationResult>>(`/skuSpecRelation/skuSpecRelationDetail/${skuSpecRelationId}`, {})
  },
  // 添加SKU规格值关联
  addSkuSpecRelation: (param: SkuSpecRelationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SkuSpecRelationFormParam>('/skuSpecRelation/addSkuSpecRelation', param)
  },
  // 更新SKU规格值关联
  updateSkuSpecRelation: (param: SkuSpecRelationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SkuSpecRelationFormParam>('/skuSpecRelation/updateSkuSpecRelation', param)
  },
  // 删除SKU规格值关联
  deleteSkuSpecRelation: (skuSpecRelationId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/skuSpecRelation/deleteSkuSpecRelation/${skuSpecRelationId}`, {})
  },
  // 批量删除SKU规格值关联
  batchDeleteSkuSpecRelation: (skuSpecRelationIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/skuSpecRelation/batchDeleteSkuSpecRelation', skuSpecRelationIdList)
  },

  // 导入SKU规格值关联
  importSkuSpecRelation: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/skuSpecRelation/importSkuSpecRelationExcel', file)
  },
  // 导出SKU规格值关联
  exportSkuSpecRelation: (param?: SkuSpecRelationQueryParam): void => {
    return getDownload('/skuSpecRelation/exportSkuSpecRelationExcel', param || {})
  },
}

