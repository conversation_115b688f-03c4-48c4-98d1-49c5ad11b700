import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 客户信息类型定义
 */
export interface Customer extends BaseModel {
  id?: number // 客户信息ID

  customerCode?: string // 客户编码

  customerName?: string // 客户名称

  customerType?: string // 字典 | 客户类型 customer_type：REGULAR-普通客户，VIP-VIP客户，STRATEGIC-战略客户，DISTRIBUTOR-经销商，AGENT-代理商

  creditLevel?: string // 字典 | 信用等级 credit_level：AAA-AAA级，AA-AA级，A-A级，BBB-BBB级，BB-BB级，B-B级，CCC-CCC级，CC-CC级，C-C级

  contactName?: string // 联系人姓名

  contactPosition?: string //

  contactDepartment?: string //

  contactPhone?: string // 联系人电话

  contactMobile?: string // 联系人手机

  contactEmail?: string // 联系人邮箱

  billingAddress?: string // 发票地址

  businessLicense?: string // 营业执照号

  taxId?: string // 税务登记号

  addressInfoList?: string // 地址信息列表(jsonb数组)

  paymentInfoList?: string // 支付账户信息列表(jsonb数组)

  status?: string // 字典 | 客户状态 customer_status：ACTIVE-活跃，INACTIVE-非活跃，BLACKLIST-黑名单，POTENTIAL-潜在客户

  remark?: string // 备注

}
/**
 * 客户信息查询参数
 */
export interface CustomerQueryParam extends Customer {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 客户信息分页查询参数
 */
export interface CustomerPageParam extends CustomerQueryParam {
  pageParam: PageParam
}

/**
 * 客户信息表单类型
 */
export interface CustomerForm extends Customer {

}

/**
 * 客户信息查询结果类型
 */
export interface CustomerResult extends Customer {

}
