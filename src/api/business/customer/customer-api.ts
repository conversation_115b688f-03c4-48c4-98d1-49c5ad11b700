import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  CustomerFormParam,
  CustomerPageParam,
  CustomerQueryParam,
  CustomerResult,
} from './model/customer-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const customerApi = {

  // 分页查询客户信息
  customerPage: (param: CustomerPageParam): Promise<ResponseModel<PageResult<CustomerResult>>> => {
    return postRequest<ResponseModel<PageResult<CustomerResult>>, CustomerQueryParam>('/customer/customerPage', param)
  },
  // 列表查询客户信息
  customerList: (param: CustomerQueryParam): Promise<ResponseModel<CustomerResult[]>> => {
    return postRequest<ResponseModel<CustomerResult[]>, CustomerQueryParam>('/customer/customerList', param)
  },
  // 获取客户信息详情
  customerDetail: (customerId: number): Promise<ResponseModel<CustomerResult>> => {
    return getRequest<ResponseModel<CustomerResult>>(`/customer/customerDetail/${customerId}`, {})
  },
  // 添加客户信息
  addCustomer: (param: CustomerFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, CustomerFormParam>('/customer/addCustomer', param)
  },
  // 更新客户信息
  updateCustomer: (param: CustomerFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, CustomerFormParam>('/customer/updateCustomer', param)
  },
  // 删除客户信息
  deleteCustomer: (customerId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/customer/deleteCustomer/${customerId}`, {})
  },
  // 批量删除客户信息
  batchDeleteCustomer: (customerIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/customer/batchDeleteCustomer', customerIdList)
  },

  // 导入客户信息
  importCustomer: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/customer/importCustomerExcel', file)
  },
  // 导出客户信息
  exportCustomer: (param?: CustomerQueryParam): void => {
    return getDownload('/customer/exportCustomerExcel', param || {})
  },
}

