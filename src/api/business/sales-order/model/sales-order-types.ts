import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'
import type { SalesOrderItem } from '../../sales-order-item/model/sales-order-item-types'

/**
 * 销售订单信息类型定义
 */
export interface SalesOrder extends BaseModel {
  id?: number // 销售订单信息ID

  orderNo?: string // 订单编号

  orderType?: string // 字典 | 开单方式 order_type：SALES-销售单，PURCHASE-订货单

  customerId?: number // 客户ID | erp_customer表的id

  customerName?: string // 客户名称

  customerCode?: string // 客户编码

  orderDate?: string // 订单日期

  deliveryDate?: string // 交货日期

  orderStatus?: string // 字典 | 订单状态 order_status：DRAFT-草稿，SUBMITTED-已提交，CONFIRMED-已确认，PRODUCTION-生产中，SHIPPED-已发货，DELIVERED-已交付，COMPLETED-已完成，CANCELLED-已取消

  paymentMethod?: string // 字典 | 付款方式 payment_method：CASH-现金，BANK_TRANSFER-银行转账，CREDIT_CARD-信用卡，CHECK-支票，ONLINE-在线支付

  paymentTerms?: string // 字典 | 付款条件 payment_terms：PREPAID-预付款，COD-货到付款，NET30-30天付款，NET60-60天付款，NET90-90天付款

  currency?: string // 币种

  exchangeRate?: number // 汇率

  subtotalAmount?: number // 商品小计 (所有明细行金额汇总)

  discountAmount?: number // 订单级折扣金额

  taxAmount?: number // 税额

  shippingFee?: number // 运费

  finalAmount?: number // 最终金额 (小计+税额+运费-折扣)

  shippingAddress?: string // 收货地址

  billingAddress?: string // 发票地址

  contactName?: string // 联系人

  contactPhone?: string // 联系电话

  contactMobile?: string // 联系手机

  salesPersonId?: number // 销售员ID | t_employee表的employee_id

  salesPersonName?: string // 销售员姓名

  orderSource?: string // 字典 | 订单来源 order_source：ONLINE-线上，OFFLINE-线下，PHONE-电话，EMAIL-邮件，WECHAT-微信

  priorityLevel?: string // 字典 | 优先级 priority_level：NORMAL-普通，URGENT-紧急，CRITICAL-特急

  estimatedCompletionDate?: string // 预计完成日期

  invoiceStatus?: string // 字典 | 发票状态 invoice_status：PENDING-待开票，INVOICED-已开票，RECEIVED-已收票

  paymentStatus?: string // 字典 | 付款状态 payment_status：UNPAID-未付款，PARTIAL-部分付款，PAID-已付款，OVERDUE-逾期

  paidAmount?: number // 已付金额

  approvalUserId?: number // 审批人ID | t_employee表的employee_id

  approvalTime?: string // 审批时间

  approvalRemark?: string // 审批备注

  remark?: string // 备注

  paymentList?: Record<string, undefined>[] // 付款列表 (JSON字符串)
}

/**
 * 销售订单信息查询参数
 */
export interface SalesOrderQueryParam extends SalesOrder {

  orderDateFrom?: string // 订单日期始

  orderDateTo?: string // 订单日期至

  deliveryDateFrom?: string // 交货日期始

  deliveryDateTo?: string // 交货日期至

  estimatedCompletionDateFrom?: string // 预计完成日期始

  estimatedCompletionDateTo?: string // 预计完成日期至

  approvalTimeFrom?: string // 审批时间始

  approvalTimeTo?: string // 审批时间至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 销售订单信息分页查询参数
 */
export interface SalesOrderPageParam extends SalesOrderQueryParam {
  pageParam: PageParam
}

/**
 * 销售订单信息表单类型
 */
export interface SalesOrderForm extends SalesOrder {
  orderItemList?: SalesOrderItem[] // 订单明细列表

  itemList?: SalesOrderItem[] // 订单明细列表
}

/**
 * 销售订单信息查询结果类型
 */
export interface SalesOrderResult extends SalesOrder {
  orderItemList?: SalesOrderItem[]
}
