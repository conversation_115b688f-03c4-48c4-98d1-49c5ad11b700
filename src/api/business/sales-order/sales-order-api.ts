import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  SalesOrderForm,
  SalesOrderPageParam,
  SalesOrderQueryParam,
  SalesOrderResult,
} from './model/sales-order-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const salesOrderApi = {

  // 分页查询订单信息
  salesOrderPage: (param: SalesOrderPageParam): Promise<ResponseModel<PageResult<SalesOrderResult>>> => {
    return postRequest<ResponseModel<PageResult<SalesOrderResult>>, SalesOrderQueryParam>('/salesOrder/salesOrderPage', param)
  },
  // 列表查询订单信息
  salesOrderList: (param: SalesOrderQueryParam): Promise<ResponseModel<SalesOrderForm[]>> => {
    return postRequest<ResponseModel<SalesOrderForm[]>, SalesOrderQueryParam>('/salesOrder/salesOrderList', param)
  },
  // 获取订单信息详情
  salesOrderDetail: (salesOrderId: number): Promise<ResponseModel<SalesOrderResult>> => {
    return getRequest<ResponseModel<SalesOrderResult>>(`/salesOrder/salesOrderDetail/${salesOrderId}`, {})
  },
  // 添加订单信息
  addSalesOrder: (param: SalesOrderForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SalesOrderForm>('/salesOrder/addSalesOrder', param)
  },
  // 更新订单信息
  updateSalesOrder: (param: SalesOrderForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SalesOrderForm>('/salesOrder/updateSalesOrder', param)
  },
  // 删除订单信息
  deleteSalesOrder: (salesOrderId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/salesOrder/deleteSalesOrder/${salesOrderId}`, {})
  },
  // 批量删除订单信息
  batchDeleteSalesOrder: (salesOrderIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/salesOrder/batchDeleteSalesOrder', salesOrderIdList)
  },

  // 导入订单信息
  importSalesOrder: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/salesOrder/importSalesOrderExcel', file)
  },
  // 导出订单信息
  exportSalesOrder: (param?: SalesOrderQueryParam): void => {
    return getDownload('/salesOrder/exportSalesOrderExcel', param || {})
  },
}
