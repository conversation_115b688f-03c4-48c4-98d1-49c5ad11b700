import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  StockCheckItemForm,
  StockCheckItemPageParam,
  StockCheckItemQueryParam,
  StockCheckItemResult,
} from './model/stock-check-item-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const stockCheckItemApi = {

  // 分页查询库存盘点明细
  stockCheckItemPage: (param: StockCheckItemPageParam): Promise<ResponseModel<PageResult<StockCheckItemResult>>> => {
    return postRequest<ResponseModel<PageResult<StockCheckItemResult>>, StockCheckItemQueryParam>('/stockCheckItem/stockCheckItemPage', param)
  },
  // 列表查询库存盘点明细
  stockCheckItemList: (param: StockCheckItemQueryParam): Promise<ResponseModel<StockCheckItemForm[]>> => {
    return postRequest<ResponseModel<StockCheckItemForm[]>, StockCheckItemQueryParam>('/stockCheckItem/stockCheckItemList', param)
  },
  // 获取库存盘点明细详情
  stockCheckItemDetail: (stockCheckItemId: number): Promise<ResponseModel<StockCheckItemResult>> => {
    return getRequest<ResponseModel<StockCheckItemResult>>(`/stockCheckItem/stockCheckItemDetail/${stockCheckItemId}`, {})
  },
  // 添加库存盘点明细
  addStockCheckItem: (param: StockCheckItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockCheckItemForm>('/stockCheckItem/addStockCheckItem', param)
  },
  // 更新库存盘点明细
  updateStockCheckItem: (param: StockCheckItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockCheckItemForm>('/stockCheckItem/updateStockCheckItem', param)
  },
  // 删除库存盘点明细
  deleteStockCheckItem: (stockCheckItemId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/stockCheckItem/deleteStockCheckItem/${stockCheckItemId}`, {})
  },
  // 批量删除库存盘点明细
  batchDeleteStockCheckItem: (stockCheckItemIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/stockCheckItem/batchDeleteStockCheckItem', stockCheckItemIdList)
  },

  // 导入库存盘点明细
  importStockCheckItem: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/stockCheckItem/importStockCheckItemExcel', file)
  },
  // 导出库存盘点明细
  exportStockCheckItem: (param?: StockCheckItemQueryParam): void => {
    return getDownload('/stockCheckItem/exportStockCheckItemExcel', param || {})
  },
}
