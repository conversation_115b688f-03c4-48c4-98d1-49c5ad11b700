import type {PageParam} from '@/api/base-model/page-model'
import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存盘点明细类型定义
 */
export interface StockCheckItemModel extends BaseModel {
  id?: number // 库存盘点明细ID

  checkId?: number // 盘点ID | erp_stock_check表的id

  checkNo?: string // 盘点单号

  lineNo?: number // 行号

  warehouseId?: number // 仓库ID | erp_warehouse表的id

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  bookStock?: number // 账面库存

  actualStock?: number // 实盘库存

  differenceStock?: number // 差异数量 (实盘-账面)

  differenceType?: string // 字典 | 差异类型 difference_type：NORMAL-正常，PROFIT-盘盈，LOSS-盘亏

  unitCost?: number // 单位成本

  bookAmount?: number // 账面金额

  actualAmount?: number // 实盘金额

  differenceAmount?: number // 差异金额

  checkStatus?: string // 字典 | 盘点状态 item_check_status：PENDING-待盘点，CHECKED-已盘点，CONFIRMED-已确认

  isChecked?: boolean // 是否已盘点：FALSE-否，TRUE-是

  checkTime?: string // 盘点时间

  checkerId?: number // 盘点人ID | t_employee表的employee_id

  checkerName?: string // 盘点人姓名

  differenceReason?: string // 字典 | 差异原因 difference_reason：DAMAGE-损坏，THEFT-盗失，EXPIRED-过期，MISCOUNT-计数错误，SYSTEM_ERROR-系统错误，OTHER-其他

  remark?: string // 备注

}
/**
 * 库存盘点明细查询参数
 */
export interface StockCheckItemQueryParam extends StockCheckItemModel {

                checkTimeFrom?: string // 盘点时间始

                checkTimeTo?: string // 盘点时间至

                createTimeFrom?: string // 创建时间始

                createTimeTo?: string // 创建时间至

                updateTimeFrom?: string // 更新时间始

                updateTimeTo?: string // 更新时间至
}

/**
 * 库存盘点明细分页查询参数
 */
export interface StockCheckItemPageParam extends StockCheckItemQueryParam {
    pageParam: PageParam
}

/**
 * 库存盘点明细表单类型
 */
export interface StockCheckItemForm extends StockCheckItemModel {

}

/**
 * 库存盘点明细查询结果类型
 */
export interface StockCheckItemResult extends StockCheckItemModel {

}
