import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  EntityParamFormParam,
  EntityParamPageParam,
  EntityParamQueryParam,
  EntityParamResult,
} from './model/entity-param-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const entityParamApi = {

  // 分页查询实体参数
  entityParamPage: (param: EntityParamPageParam): Promise<ResponseModel<PageResult<EntityParamResult>>> => {
    return postRequest<ResponseModel<PageResult<EntityParamResult>>, EntityParamQueryParam>('/entityParam/entityParamPage', param)
  },
  // 列表查询实体参数
  entityParamList: (param: EntityParamQueryParam): Promise<ResponseModel<EntityParamResult[]>> => {
    return postRequest<ResponseModel<EntityParamResult[]>, EntityParamQueryParam>('/entityParam/entityParamList', param)
  },
  // 获取实体参数详情
  entityParamDetail: (entityParamId: number): Promise<ResponseModel<EntityParamResult>> => {
    return getRequest<ResponseModel<EntityParamResult>>(`/entityParam/entityParamDetail/${entityParamId}`, {})
  },
  // 添加实体参数
  addEntityParam: (param: EntityParamFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityParamFormParam>('/entityParam/addEntityParam', param)
  },
  // 更新实体参数
  updateEntityParam: (param: EntityParamFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityParamFormParam>('/entityParam/updateEntityParam', param)
  },
  // 删除实体参数
  deleteEntityParam: (entityParamId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/entityParam/deleteEntityParam/${entityParamId}`, {})
  },
  // 批量删除实体参数
  batchDeleteEntityParam: (entityParamIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/entityParam/batchDeleteEntityParam', entityParamIdList)
  },

  // 导入实体参数
  importEntityParam: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/entityParam/importEntityParamExcel', file)
  },
  // 导出实体参数
  exportEntityParam: (param?: EntityParamQueryParam): void => {
    return getDownload('/entityParam/exportEntityParamExcel', param || {})
  },
}
