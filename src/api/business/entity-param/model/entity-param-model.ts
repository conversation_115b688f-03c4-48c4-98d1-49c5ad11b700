import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 实体参数类型定义
 */
export interface EntityParamModel extends BaseModel {
  /**
   * 实体参数ID
   */
  id?: number

  /**
   * 实体类型ID | erp_entity_type的id
   */
  entityTypeId?: number

  /**
   * 实体ID | 关联具体实体表的id
   */
  entityId?: number

  /**
   * 参数类型ID | erp_param_type的id
   */
  paramTypeId?: number

  /**
   * 参数值
   */
  paramValue?: string

  /**
   * 备注
   */
  remark?: string

}
