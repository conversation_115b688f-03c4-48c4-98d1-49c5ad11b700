import type { PageParam } from '@/api/base-model/page-model'
import type { EntityParamModel } from '@/api/business/entity-param/model/entity-param-model'

/**
 * 实体参数查询参数
 */
export interface EntityParamQueryParam extends EntityParamModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string

  /**
   * 实体类型编码
   */
  entityTypeCode?: string

  /**
   * 实体类型名称
   */
  entityTypeName?: string

  /**
   * 参数类型编码
   */
  paramTypeCode?: string

  /**
   * 参数类型名称
   */
  paramTypeName?: string

  /**
   * 数据类型
   */
  dataType?: string

  /**
   * 是否必填
   */
  isRequired?: boolean
}

/**
 * 实体参数分页查询参数
 */
export interface EntityParamPageParam extends EntityParamQueryParam {
  pageParam: PageParam
}

/**
 * 实体参数表单参数类型
 */
export interface EntityParamFormParam extends EntityParamModel {
  /**
   * 实体类型编码
   */
  entityTypeCode?: string

  /**
   * 实体类型名称
   */
  entityTypeName?: string

  /**
   * 参数类型编码
   */
  paramTypeCode?: string

  /**
   * 参数类型名称
   */
  paramTypeName?: string

  /**
   * 数据类型
   */
  dataType?: string

  /**
   * 是否必填
   */
  isRequired?: boolean
}

/**
 * 实体参数查询结果类型
 */
export interface EntityParamResult extends EntityParamModel {
  /**
   * 实体类型编码
   */
  entityTypeCode?: string

  /**
   * 实体类型名称
   */
  entityTypeName?: string

  /**
   * 参数类型编码
   */
  paramTypeCode?: string

  /**
   * 参数类型名称
   */
  paramTypeName?: string

  /**
   * 数据类型
   */
  dataType?: string

  /**
   * 是否必填
   */
  isRequired?: boolean
}
