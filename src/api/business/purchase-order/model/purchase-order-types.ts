import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'
import type { PurchaseOrderItem } from '../../purchase-order-item/model/purchase-order-item-types'

/**
 * 采购订单信息类型定义
 */
export interface PurchaseOrderModel extends BaseModel {
  id?: number // 采购订单信息ID

  orderNo?: string // 采购订单编号

  orderType?: string // 字典 | 采购类型 purchase_order_type：STANDARD-标准采购，URGENT-紧急采购，BLANKET-框架采购，CONSIGNMENT-寄售采购

  supplierId?: number // 供应商ID | erp_supplier表的id

  supplierName?: string // 供应商名称

  supplierCode?: string // 供应商编码

  orderDate?: string // 采购日期

  expectedDeliveryDate?: string // 预期交货日期

  orderStatus?: string // 字典 | 采购订单状态 purchase_order_status：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，CONFIRMED-已确认，PARTIAL_RECEIVED-部分收货，RECEIVED-已收货，COMPLETED-已完成，CANCELLED-已取消

  paymentMethod?: string // 字典 | 付款方式 payment_method：CASH-现金，BANK_TRANSFER-银行转账，CREDIT_CARD-信用卡，CHECK-支票，ONLINE-在线支付

  paymentTerms?: string // 字典 | 付款条件 payment_terms：PREPAID-预付款，COD-货到付款，NET30-30天付款，NET60-60天付款，NET90-90天付款

  currency?: string // 币种

  exchangeRate?: number // 汇率

  subtotalAmount?: number // 商品小计 (所有明细行金额汇总)

  discountAmount?: number // 订单级折扣金额

  taxAmount?: number // 税额

  shippingFee?: number // 运费

  finalAmount?: number // 最终金额 (小计+税额+运费-折扣)

  deliveryAddress?: string // 收货地址

  billingAddress?: string // 发票地址

  contactName?: string // 联系人

  contactPhone?: string // 联系电话

  contactMobile?: string // 联系手机

  purchaserId?: number // 采购员ID | t_employee表的employee_id

  purchaserName?: string // 采购员姓名

  orderSource?: string // 字典 | 采购来源 purchase_order_source：MANUAL-手工录入，MRP-MRP生成，REORDER-自动补货，REQUISITION-请购单转换

  priorityLevel?: string // 字典 | 优先级 priority_level：NORMAL-普通，URGENT-紧急，CRITICAL-特急

  estimatedCompletionDate?: string // 预计完成日期

  invoiceStatus?: string // 字典 | 发票状态 invoice_status：PENDING-待开票，INVOICED-已开票，RECEIVED-已收票

  paymentStatus?: string // 字典 | 付款状态 payment_status：UNPAID-未付款，PARTIAL-部分付款，PAID-已付款，OVERDUE-逾期

  paidAmount?: number // 已付金额

  approvalUserId?: number // 审批人ID | t_employee表的employee_id

  approvalTime?: string // 审批时间

  approvalRemark?: string // 审批备注

  remark?: string // 备注

}

/**
 * 采购订单信息查询参数
 */
export interface PurchaseOrderQueryParam extends PurchaseOrderModel {

  orderDateFrom?: string // 采购日期始

  orderDateTo?: string // 采购日期至

  expectedDeliveryDateFrom?: string // 预期交货日期始

  expectedDeliveryDateTo?: string // 预期交货日期至

  estimatedCompletionDateFrom?: string // 预计完成日期始

  estimatedCompletionDateTo?: string // 预计完成日期至

  approvalTimeFrom?: string // 审批时间始

  approvalTimeTo?: string // 审批时间至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至

  paymentList?: Record<string, undefined>[] // 付款列表 (JSON数组)

}

/**
 * 采购订单信息分页查询参数
 */
export interface PurchaseOrderPageParam extends PurchaseOrderQueryParam {
  pageParam: PageParam
}

/**
 * 采购订单信息表单类型
 */
export interface PurchaseOrderForm extends PurchaseOrderModel {
  itemList?: PurchaseOrderItem[] // 采购订单明细

}

/**
 * 采购订单信息查询结果类型
 */
export interface PurchaseOrderResult extends PurchaseOrderModel {
  itemList?: PurchaseOrderItem[] // 采购订单明细
}
