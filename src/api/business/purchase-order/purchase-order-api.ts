import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PurchaseOrderForm,
  PurchaseOrderPageParam,
  PurchaseOrderQueryParam,
  PurchaseOrderResult,
} from './model/purchase-order-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const purchaseOrderApi = {

  // 分页查询采购订单信息
  purchaseOrderPage: (param: PurchaseOrderPageParam): Promise<ResponseModel<PageResult<PurchaseOrderResult>>> => {
    return postRequest<ResponseModel<PageResult<PurchaseOrderResult>>, PurchaseOrderQueryParam>('/purchaseOrder/purchaseOrderPage', param)
  },
  // 列表查询采购订单信息
  purchaseOrderList: (param: PurchaseOrderQueryParam): Promise<ResponseModel<PurchaseOrderForm[]>> => {
    return postRequest<ResponseModel<PurchaseOrderForm[]>, PurchaseOrderQueryParam>('/purchaseOrder/purchaseOrderList', param)
  },
  // 获取采购订单信息详情
  purchaseOrderDetail: (purchaseOrderId: number): Promise<ResponseModel<PurchaseOrderResult>> => {
    return getRequest<ResponseModel<PurchaseOrderResult>>(`/purchaseOrder/purchaseOrderDetail/${purchaseOrderId}`, {})
  },
  // 添加采购订单信息
  addPurchaseOrder: (param: PurchaseOrderForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchaseOrderForm>('/purchaseOrder/addPurchaseOrder', param)
  },
  // 更新采购订单信息
  updatePurchaseOrder: (param: PurchaseOrderForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchaseOrderForm>('/purchaseOrder/updatePurchaseOrder', param)
  },
  // 删除采购订单信息
  deletePurchaseOrder: (purchaseOrderId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/purchaseOrder/deletePurchaseOrder/${purchaseOrderId}`, {})
  },
  // 批量删除采购订单信息
  batchDeletePurchaseOrder: (purchaseOrderIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/purchaseOrder/batchDeletePurchaseOrder', purchaseOrderIdList)
  },

  // 导入采购订单信息
  importPurchaseOrder: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/purchaseOrder/importPurchaseOrderExcel', file)
  },
  // 导出采购订单信息
  exportPurchaseOrder: (param?: PurchaseOrderQueryParam): void => {
    return getDownload('/purchaseOrder/exportPurchaseOrderExcel', param || {})
  },
}
