import type { PageParam } from '@/api/base-model/page-model'
import type { EntityParamTypeModel } from '@/api/business/entity-param-type/model/entity-param-type-model'

/**
 * 参数类型查询参数
 */
export interface EntityParamTypeQueryParam extends EntityParamTypeModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 参数类型分页查询参数
 */
export interface EntityParamTypePageParam extends EntityParamTypeQueryParam {
  pageParam: PageParam
}

/**
 * 参数类型表单参数类型
 */
export interface EntityParamTypeFormParam extends EntityParamTypeModel {

}

/**
 * 参数类型查询结果类型
 */
export interface EntityParamTypeResult extends EntityParamTypeModel {
  /**
   * 实体类型名称
   */
  entityTypeName?: string
  /**
   * 实体类型ID
   */
  entityTypeId?: string
}
