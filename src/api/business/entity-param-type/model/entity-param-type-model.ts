import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 参数类型类型定义
 */
export interface EntityParamTypeModel extends BaseModel {
  /**
   * 参数类型ID
   */
  id?: number

  /**
   * 参数类型编码
   */
  typeCode?: string

  /**
   * 参数类型名称
   */
  typeName?: string

  /**
   * 数据类型：STRING-字符串，INTEGER-整数，DECIMAL-小数，BOOLEAN-布尔值，DATE-日期，DATETIME-日期时间，JSON-JSON数据
   */
  dataType?: string

  /**
   * 默认值
   */
  defaultValue?: string

  /**
   * 验证规则(JSON格式，包含正则表达式、最小/最大值等验证信息)
   */
  validationRule?: string

  /**
   * 是否必填：0-非必填，1-必填
   */
  isRequired?: boolean

  /**
   * 排序
   */
  sort?: number

  /**
   * 是否可搜索：0-否，1-是
   */
  searchable?: boolean

  /**
   * 选项值列表，适用于下拉选择类型
   */
  options?: string

  /**
   * 备注
   */
  remark?: string

}
