import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  EntityParamTypeFormParam,
  EntityParamTypePageParam,
  EntityParamTypeQueryParam,
  EntityParamTypeResult,
} from './model/entity-param-type-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const entityParamTypeApi = {

  // 分页查询参数类型
  entityParamTypePage: (param: EntityParamTypePageParam): Promise<ResponseModel<PageResult<EntityParamTypeResult>>> => {
    return postRequest<ResponseModel<PageResult<EntityParamTypeResult>>, EntityParamTypeQueryParam>('/entityParamType/entityParamTypePage', param)
  },
  // 列表查询参数类型
  entityParamTypeList: (param: EntityParamTypeQueryParam): Promise<ResponseModel<EntityParamTypeResult[]>> => {
    return postRequest<ResponseModel<EntityParamTypeResult[]>, EntityParamTypeQueryParam>('/entityParamType/entityParamTypeList', param)
  },
  // 获取参数类型详情
  entityParamTypeDetail: (entityParamTypeId: number): Promise<ResponseModel<EntityParamTypeResult>> => {
    return getRequest<ResponseModel<EntityParamTypeResult>>(`/entityParamType/entityParamTypeDetail/${entityParamTypeId}`, {})
  },
  // 添加参数类型
  addEntityParamType: (param: EntityParamTypeFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityParamTypeFormParam>('/entityParamType/addEntityParamType', param)
  },
  // 更新参数类型
  updateEntityParamType: (param: EntityParamTypeFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityParamTypeFormParam>('/entityParamType/updateEntityParamType', param)
  },
  // 删除参数类型
  deleteEntityParamType: (entityParamTypeId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/entityParamType/deleteEntityParamType/${entityParamTypeId}`, {})
  },
  // 批量删除参数类型
  batchDeleteEntityParamType: (entityParamTypeIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/entityParamType/batchDeleteEntityParamType', entityParamTypeIdList)
  },

  // 导入参数类型
  importEntityParamType: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/entityParamType/importEntityParamTypeExcel', file)
  },
  // 导出参数类型
  exportEntityParamType: (param?: EntityParamTypeQueryParam): void => {
    return getDownload('/entityParamType/exportEntityParamTypeExcel', param || {})
  },
}
