import type { PageParam } from '@/api/base-model/page-model'
import type { EntityTypeModel } from '@/api/business/entity-type/model/entity-type-model'

/**
 * 实体类型查询参数
 */
export interface EntityTypeQueryParam extends EntityTypeModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 实体类型分页查询参数
 */
export interface EntityTypePageParam extends EntityTypeQueryParam {
  pageParam: PageParam
}

/**
 * 实体类型表单参数类型
 */
export interface EntityTypeFormParam extends EntityTypeModel {

}

/**
 * 实体类型查询结果类型
 */
export interface EntityTypeResult extends EntityTypeModel {

}

