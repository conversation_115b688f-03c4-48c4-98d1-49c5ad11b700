import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 实体类型类型定义
 */
export interface EntityTypeModel extends BaseModel {
  /**
   * 实体类型ID
   */
  id?: number

  /**
   * 实体类型编码
   */
  typeCode?: string

  /**
   * 实体类型名称
   */
  typeName?: string

  /**
   * 所属模块
   */
  module?: string

  /**
   * 是否系统内置：0-否，1-是
   */
  isSystem?: boolean

  /**
   * 是否允许自定义参数：0-否，1-是
   */
  allowCustomParam?: boolean

  /**
   * 备注
   */
  remark?: string

}
