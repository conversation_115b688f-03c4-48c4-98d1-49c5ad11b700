import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  EntityTypeFormParam,
  EntityTypePageParam,
  EntityTypeQueryParam,
  EntityTypeResult,
} from './model/entity-type-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const entityTypeApi = {

  // 分页查询实体类型
  entityTypePage: (param: EntityTypePageParam): Promise<ResponseModel<PageResult<EntityTypeResult>>> => {
    return postRequest<ResponseModel<PageResult<EntityTypeResult>>, EntityTypeQueryParam>('/entityType/entityTypePage', param)
  },
  // 列表查询实体类型
  entityTypeList: (param: EntityTypeQueryParam): Promise<ResponseModel<EntityTypeResult[]>> => {
    return postRequest<ResponseModel<EntityTypeResult[]>, EntityTypeQueryParam>('/entityType/entityTypeList', param)
  },
  // 获取实体类型详情
  entityTypeDetail: (entityTypeId: number): Promise<ResponseModel<EntityTypeResult>> => {
    return getRequest<ResponseModel<EntityTypeResult>>(`/entityType/entityTypeDetail/${entityTypeId}`, {})
  },
  // 添加实体类型
  addEntityType: (param: EntityTypeFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityTypeFormParam>('/entityType/addEntityType', param)
  },
  // 更新实体类型
  updateEntityType: (param: EntityTypeFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, EntityTypeFormParam>('/entityType/updateEntityType', param)
  },
  // 删除实体类型
  deleteEntityType: (entityTypeId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/entityType/deleteEntityType/${entityTypeId}`, {})
  },
  // 批量删除实体类型
  batchDeleteEntityType: (entityTypeIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/entityType/batchDeleteEntityType', entityTypeIdList)
  },

  // 导入实体类型
  importEntityType: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/entityType/importEntityTypeExcel', file)
  },
  // 导出实体类型
  exportEntityType: (param?: EntityTypeQueryParam): void => {
    return getDownload('/entityType/exportEntityTypeExcel', param || {})
  },
}

