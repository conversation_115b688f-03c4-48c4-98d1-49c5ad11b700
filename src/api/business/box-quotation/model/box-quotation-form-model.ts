import type { PageParam } from '@/api/base-model/page-model'
import type { PaperBoard, RawPaper } from '@/views/business-old/box-quotation-calculator/utils/types'
import type { PrintingProcessModel } from '../../printing-process/model/printing-process-model'
import type { BoxQuotationModel } from './box-quotation-model'

/**
 * 纸箱报价查询参数
 */
export interface BoxQuotationQueryParam extends BoxQuotationModel {
  /**
   * 报价日期始
   */
  quotationDateFrom?: string
  /**
   * 报价日期至
   */
  quotationDateTo?: string
  /**
   * 审批时间始
   */
  approvalTimeFrom?: string
  /**
   * 审批时间至
   */
  approvalTimeTo?: string
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 纸箱报价分页查询参数
 */
export interface BoxQuotationPageParam extends BoxQuotationQueryParam {
  pageParam: PageParam
}

/**
 * 纸箱报价添加参数类型
 */
export interface BoxQuotationFormParam extends BoxQuotationModel {
  paperboardList?: PaperBoard[] // 纸板配置列表
  rawPaper?: RawPaper // 印刷原纸
  printingProcessList?: PrintingProcessModel[] // 印刷工艺列表
}

/**
 * 纸箱报价查询结果类型
 */
export interface BoxQuotationResult extends BoxQuotationModel {
  paperboardList?: PaperBoard[] // 纸板配置列表
  rawPaper?: RawPaper // 印刷原纸
  printingProcessList?: PrintingProcessModel[] // 印刷工艺列表
}

/**
 * 纸箱报价列表结果类型
 */
export interface BoxQuotationLiteResult {
  id?: number // 主键id
  quotationNo?: string // 报价单号
  customerId?: number // 客户ID | erp_customer表的id
  customerName?: string // 客户名称
  quotationDate?: string // 报价日期
  printingMethod?: string // 印刷方式
  boxType?: string // 箱形
}
