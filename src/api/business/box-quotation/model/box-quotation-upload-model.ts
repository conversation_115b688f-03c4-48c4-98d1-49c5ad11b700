/**
 * 纸箱报价上传参数模型
 */
export interface BoxQuotationUploadParam {
  /**
   * 纸箱报价ID
   */
  id?: number

  /**
   * 报价单号
   */
  quotationNo: string
  /**
   * 客户ID | erp_customer表的id
   */
  customerId: number
  /**
   * 客户名称
   */
  customerName: string
  /**
   * 报价日期
   */
  quotationDate: string
  /**
   * 纸箱长度(mm)
   */
  boxLength?: number
  /**
   * 纸箱宽度(mm)
   */
  boxWidth?: number
  /**
   * 纸箱高度(mm)
   */
  boxHeight?: number
  /**
   * 纸板层数
   */
  paperboardLayerCount?: number
  /**
   * 纸箱数量
   */
  boxQuantity?: number
  /**
   * 楞型组合
   */
  corrugatedShapeGroup?: string
  /**
   * 字典 | 印刷方式：watermark-水印，colorPrint-彩印
   */
  printingMethod?: string
  /**
   * 字典 | 箱形：standard-标准箱，tailored-定制箱，telescope-套盖箱，folding-折叠箱
   */
  boxType?: string
  /**
   * 纸板供应商ID
   */
  paperboardSupplierId?: number
  /**
   * 纸板供应商名称
   */
  paperboardSupplierName?: string
  /**
   * 原纸供应商ID
   */
  rawPaperSupplierId?: number
  /**
   * 原纸供应商名称
   */
  rawPaperSupplierName?: string
  /**
   * 纸版供应商折扣率
   */
  supplierDiscount?: string
  /**
   * 纸板供应商税率
   */
  taxRate?: string
  /**
   * 字典 | 是否含税：1-含税，0-不含税
   */
  includeTax?: number
  /**
   * 纸箱总长(mm)
   */
  totalLength?: number
  /**
   * 纸箱门宽(mm)
   */
  boxDoorWidth?: number
  /**
   * 模切门宽(mm)
   */
  dieCuttingDoorWidth?: number
  /**
   * 模切数
   */
  dieCuttingQuantity?: number
  /**
   * 瓦纸门宽(mm)
   */
  corrugatedDoorWidth?: number
  /**
   * 实际瓦纸门宽(mm)
   */
  actualCorrugatedDoorWidth?: number
  /**
   * 瓦纸开数
   */
  corrugatedCount?: number
  /**
   * 面纸门宽(mm)
   */
  facePaperDoorWidth?: number
  /**
   * 实际面纸门宽(mm)
   */
  actualFacePaperDoorWidth?: number
  /**
   * 面纸开数
   */
  facePaperCount?: number
  /**
   * 舌头宽度(mm)
   */
  tongueLength?: number
  /**
   * 摇盖加分(mm)
   */
  waveCapAddition?: number
  /**
   * 修边位(mm)
   */
  trimPosition?: number
  /**
   * 水印色数
   */
  watermarkColors?: number
  /**
   * 水印单价(元/m²)
   */
  watermarkPrice?: string
  /**
   * 模切单价(元/张)
   */
  dieCuttingPrice?: string
  /**
   * 字典 | 封箱方式：nailing-钉箱，gluing-粘箱
   */
  boxClosingType?: string
  /**
   * 纸板明细JSON数组
   */
  paperboardJson?: string
  /**
   * 原纸信息
   */
  rawPaperJson?: string
  /**
   * 印刷JSON数组
   */
  printingProcessJson?: string
  /**
   * 纸箱面积(m²)
   */
  boxArea?: string
  /**
   * 纸箱体积(m³)
   */
  boxVolume?: string
  /**
   * 瓦纸面积(m²)
   */
  corrugatedArea?: string
  /**
   * 纸板单价(元/个)
   */
  paperboardUnitPrice?: string
  /**
   * 纸板加工费单价(元/个)
   */
  paperboardProcessingUnitPrice?: string
  /**
   * 纸板总价(元)
   */
  paperboardTotalPrice?: string
  /**
   * 原纸单价(元/个)
   */
  rawPaperUnitPrice?: string
  /**
   * 原纸总价(元)
   */
  rawPaperTotalPrice?: string
  /**
   * 水印单价(元/个)
   */
  watermarkUnitPrice?: string
  /**
   * 水印总价(元)
   */
  watermarkTotalPrice?: string
  /**
   * 模切单价(元/个)
   */
  dieCuttingUnitPrice?: string
  /**
   * 模切总价(元)
   */
  dieCuttingTotalPrice?: string
  /**
   * 封箱单价(元/个)
   */
  boxClosingUnitPrice?: string
  /**
   * 封箱总价(元)
   */
  boxClosingTotalPrice?: string
  /**
   * 纸板加工费(元/m²)
   */
  printingProcessingPrice?: string
  /**
   * 印刷工艺单价(元/个)
   */
  printingProcessUnitPrice?: string
  /**
   * 印刷工艺总价(元)
   */
  printingProcessTotalPrice?: string
  /**
   * 纸箱单价(元/个)
   */
  boxUnitPrice?: string
  /**
   * 纸箱总价(元)
   */
  boxTotalPrice?: string
  /**
   * 总成本(元)
   */
  totalCost?: string
  /**
   * 利润率
   */
  profitMargin?: string
  /**
   * 利润金额(元)
   */
  profitAmount?: string
  /**
   * 字典 | 报价状态：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，REJECTED-已拒绝
   */
  status: string
  /**
   * 审批人
   */
  approvalUserId?: number
  /**
   * 审批时间
   */
  approvalTime?: string
  /**
   * 审批备注
   */
  approvalRemark?: string
  /**
   * 备注
   */
  remark?: string
}
