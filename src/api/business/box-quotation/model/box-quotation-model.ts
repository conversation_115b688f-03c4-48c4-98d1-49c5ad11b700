import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 纸箱报价类型定义
 */
export interface BoxQuotationModel extends BaseModel {
  id?: number // 纸箱报价ID

  quotationNo?: string // 报价单号

  customerId?: number // 客户ID | erp_customer表的id

  customerName?: string // 客户名称

  quotationDate?: string // 报价日期

  boxLength?: number // 纸箱长度(mm)

  boxWidth?: number // 纸箱宽度(mm)

  boxHeight?: number // 纸箱高度(mm)

  paperboardLayerCount?: number // 纸板层数

  boxQuantity?: number // 纸箱数量

  corrugatedShapeGroup?: string // 楞型组合

  printingMethod?: string // 字典 | 印刷方式 printing_method：watermark-水印，colorPrint-彩印

  boxId?: number // 纸箱ID | erp_box表的id

  boxName?: string // 纸箱名称

  paperboardSupplierId?: number // 纸板供应商ID

  paperboardSupplierName?: string // 纸板供应商名称

  rawPaperSupplierId?: number // 原纸供应商ID

  rawPaperSupplierName?: string // 原纸供应商名称

  supplierDiscount?: number // 纸版供应商折扣率

  taxRate?: number // 纸板供应商税率

  includeTax?: boolean // 字典 | 是否含税：1-含税，0-不含税

  boxArea?: number // 纸箱面积(m²)

  boxVolume?: number // 纸箱体积(m³)

  corrugatedArea?: number // 瓦纸面积(m²)

  totalLength?: number // 纸箱总长(mm)

  boxDoorWidth?: number // 纸箱门宽(mm)

  dieCuttingDoorWidth?: number // 模切门宽(mm)

  dieCuttingQuantity?: number // 模切数

  corrugatedDoorWidth?: number // 瓦纸门宽(mm)

  actualCorrugatedDoorWidth?: number // 实际瓦纸门宽(mm)

  corrugatedCount?: number // 瓦纸开数

  facePaperDoorWidth?: number // 面纸门宽(mm)

  actualFacePaperDoorWidth?: number // 实际面纸门宽(mm)

  facePaperCount?: number // 面纸开数

  tongueLength?: number // 舌头宽度(mm)

  waveCapAddition?: number // 摇盖加分(mm)

  trimPosition?: number // 修边位(mm)

  watermarkColors?: number // 水印色数

  printingPlatePrice?: number // 印版单价(元/m²)

  dieCuttingPlatePrice?: number // 刀版单价(元/m²)

  watermarkPrice?: number // 水印单价(元/m²)

  paperboardPrice?: number // 纸板单价(元/m²)

  paperboardProcessingPrice?: number // 纸板加工费(元/m²)

  dieCuttingPrice?: number // 模切单价(元/张)

  boxClosingType?: string // 字典 | 封箱方式：nailing-钉箱，gluing-粘箱

  paperboardJson?: string // 纸板明细JSON数组

  rawPaperJson?: string // 原纸信息JSON

  printingProcessJson?: string // 印刷JSON数组

  paperboardUnitPrice?: number // 纸板单价(元/个)

  paperboardProcessingUnitPrice?: number // 纸板加工费单价(元/个)

  printingPlateUnitPrice?: number // 印版单价(元/个)

  dieCuttingPlateUnitPrice?: number // 刀版单价(元/个)

  rawPaperUnitPrice?: number // 原纸单价(元/个)

  watermarkUnitPrice?: number // 水印单价(元/个)

  dieCuttingUnitPrice?: number // 模切单价(元/个)

  boxClosingUnitPrice?: number // 封箱单价(元/个)

  printingProcessUnitPrice?: number // 印刷工艺单价(元/个)

  boxUnitPrice?: number // 纸箱单价(元/个)

  paperboardTotalPrice?: number // 纸板总价(元)

  printingPlateTotalPrice?: number // 印版总价(元)

  dieCuttingPlateTotalPrice?: number // 刀版总价(元)

  rawPaperTotalPrice?: number // 原纸总价(元)

  watermarkTotalPrice?: number // 水印总价(元)

  dieCuttingTotalPrice?: number // 模切总价(元)

  boxClosingTotalPrice?: number // 封箱总价(元)

  printingProcessTotalPrice?: number // 印刷工艺总价(元)

  boxTotalPrice?: number // 纸箱总价(元)

  totalCost?: number // 总成本(元)

  profitMargin?: number // 利润率

  profitAmount?: number // 利润金额(元)

  status?: string // 字典 | 报价状态：DRAFT-草稿，SUBMITTED-已提交，APPROVED-已审批，REJECTED-已拒绝

  approvalUserId?: number // 审批人

  approvalTime?: string // 审批时间

  approvalRemark?: string // 审批备注

  remark?: string // 备注

}
