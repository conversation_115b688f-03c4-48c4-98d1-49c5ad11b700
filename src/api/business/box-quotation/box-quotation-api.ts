import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  BoxQuotationFormParam,
  BoxQuotationLiteResult,
  BoxQuotationPageParam,
  BoxQuotationQueryParam,
  BoxQuotationResult,
} from './model/box-quotation-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const boxQuotationApi = {

  // 分页查询纸箱报价
  boxQuotationPage: (param: BoxQuotationPageParam): Promise<ResponseModel<PageResult<BoxQuotationLiteResult>>> => {
    return postRequest<ResponseModel<PageResult<BoxQuotationLiteResult>>, BoxQuotationQueryParam>('/boxQuotation/boxQuotationPage', param)
  },
  // 列表查询纸箱报价
  boxQuotationList: (param: BoxQuotationQueryParam): Promise<ResponseModel<BoxQuotationResult[]>> => {
    return postRequest<ResponseModel<BoxQuotationResult[]>, BoxQuotationQueryParam>('/boxQuotation/boxQuotationList', param)
  },
  // 获取纸箱报价详情
  boxQuotationDetail: (boxQuotationId: number): Promise<ResponseModel<BoxQuotationResult>> => {
    return getRequest<ResponseModel<BoxQuotationResult>>(`/boxQuotation/boxQuotationDetail/${boxQuotationId}`, {})
  },
  // 添加纸箱报价
  addBoxQuotation: (param: BoxQuotationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BoxQuotationFormParam>('/boxQuotation/addBoxQuotation', param)
  },
  // 更新纸箱报价
  updateBoxQuotation: (param: BoxQuotationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BoxQuotationFormParam>('/boxQuotation/updateBoxQuotation', param)
  },
  // 删除纸箱报价
  deleteBoxQuotation: (boxQuotationId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/boxQuotation/deleteBoxQuotation/${boxQuotationId}`, {})
  },
  // 批量删除纸箱报价
  batchDeleteBoxQuotation: (boxQuotationIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/boxQuotation/batchDeleteBoxQuotation', boxQuotationIdList)
  },

  // 导入纸箱报价
  importBoxQuotation: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/boxQuotation/importBoxQuotationExcel', file)
  },
  // 导出纸箱报价
  exportBoxQuotation: (param?: BoxQuotationQueryParam): void => {
    return getDownload('/boxQuotation/exportBoxQuotationExcel', param || {})
  },
}
