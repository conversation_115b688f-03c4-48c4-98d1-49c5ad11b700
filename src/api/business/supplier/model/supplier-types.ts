import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 供应商类型定义
 */
export interface Supplier extends BaseModel {
  id?: number // 供应商ID

  supplierCode?: string // 供应商编码

  supplierName?: string // 供应商名称

  supplierType?: string // 供应商类型：material-原材料供应商，equipment-设备供应商，service-服务供应商

  businessLicense?: string // 营业执照号

  taxId?: string // 税务登记号

  contactPerson?: string // 主要联系人

  contactPhone?: string // 联系电话

  contactEmail?: string // 联系邮箱

  contactPosition?: string // 联系人职位

  alternativeContact?: string // 备用联系人

  alternativePhone?: string // 备用联系电话

  addressInfoList?: Record<string, unknown>[] //

  paymentInfoList?: Record<string, unknown>[] //

  creditLimit?: number // 信用额度

  creditPeriod?: number // 信用期(天)

  cooperationStartDate?: string // 合作开始日期

  contractExpiryDate?: string // 合同到期日期

  lastOrderDate?: string // 最近订单日期

  totalOrderAmount?: number // 累计订单金额

  approvalStatus?: string // 审批状态：pending-待审批，approved-已批准，rejected-已拒绝

  approvalUserId?: number // 审批人ID

  approvalDate?: string // 审批日期

  approvalRemark?: string //

  remark?: string // 备注

  attachmentPath?: string // 附件路径

}
/**
 * 供应商查询参数
 */
export interface SupplierQueryParam extends Supplier {

  cooperationStartDateFrom?: string // 合作开始日期始

  cooperationStartDateTo?: string // 合作开始日期至

  contractExpiryDateFrom?: string // 合同到期日期始

  contractExpiryDateTo?: string // 合同到期日期至

  lastOrderDateFrom?: string // 最近订单日期始

  lastOrderDateTo?: string // 最近订单日期至

  approvalDateFrom?: string // 审批日期始

  approvalDateTo?: string // 审批日期至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 供应商分页查询参数
 */
export interface SupplierPageParam extends SupplierQueryParam {
  pageParam: PageParam
}

/**
 * 供应商表单类型
 */
export interface SupplierForm extends Supplier {

}

/**
 * 供应商查询结果类型
 */
export interface SupplierResult extends Supplier {

}
