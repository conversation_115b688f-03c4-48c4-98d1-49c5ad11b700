/*
 * @Description: 供应商管理API
 */
import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type { SupplierForm, SupplierPageParam, SupplierQueryParam, SupplierResult } from './model/supplier-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const supplierApi = {
  // 添加供应商
  addSupplier: (param: SupplierForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SupplierForm>('/supplier/addSupplier', param)
  },
  // 删除供应商
  deleteSupplier: (supplierId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/supplier/deleteSupplier/${supplierId}`, {})
  },
  // 批量删除供应商
  batchDeleteSupplier: (supplierIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/supplier/batchDeleteSupplier', supplierIdList)
  },
  // 分页查询供应商
  supplierPage: (param: SupplierPageParam): Promise<ResponseModel<PageResult<SupplierResult>>> => {
    return postRequest<ResponseModel<PageResult<SupplierResult>>, SupplierQueryParam>('/supplier/supplierPage', param)
  },
  // 列表查询供应商
  supplierList: (param: SupplierQueryParam): Promise<ResponseModel<SupplierResult[]>> => {
    return postRequest<ResponseModel<SupplierResult[]>, SupplierQueryParam>('/supplier/supplierList', param)
  },
  // 更新供应商
  updateSupplier: (param: SupplierForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SupplierForm>('/supplier/updateSupplier', param)
  },
  // 获取供应商详情
  getSupplierDetail: (supplierId: number): Promise<ResponseModel<SupplierResult>> => {
    return getRequest<ResponseModel<SupplierResult>>(`/supplier/supplierDetail/${supplierId}`, {})
  },
  // 导入供应商
  importSupplier: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/supplier/importSupplierExcel', file)
  },
  // 导出供应商
  exportSupplier: (param?: SupplierQueryParam): void => {
    return getDownload('/supplier/exportSupplierExcel', param || {})
  },
}
