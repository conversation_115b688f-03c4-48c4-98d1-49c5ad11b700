import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  SpecValueFormParam,
  SpecValuePageParam,
  SpecValueQueryParam,
  SpecValueResult,
} from './model/spec-value-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const specValueApi = {

  // 分页查询商品规格值
  specValuePage: (param: SpecValuePageParam): Promise<ResponseModel<PageResult<SpecValueResult>>> => {
    return postRequest<ResponseModel<PageResult<SpecValueResult>>, SpecValueQueryParam>('/specValue/specValuePage', param)
  },
  // 列表查询商品规格值
  specValueList: (param: SpecValueQueryParam): Promise<ResponseModel<SpecValueResult[]>> => {
    return postRequest<ResponseModel<SpecValueResult[]>, SpecValueQueryParam>('/specValue/specValueList', param)
  },
  // 获取商品规格值详情
  specValueDetail: (specValueId: number): Promise<ResponseModel<SpecValueResult>> => {
    return getRequest<ResponseModel<SpecValueResult>>(`/specValue/specValueDetail/${specValueId}`, {})
  },
  // 添加商品规格值
  addSpecValue: (param: SpecValueFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SpecValueFormParam>('/specValue/addSpecValue', param)
  },
  // 更新商品规格值
  updateSpecValue: (param: SpecValueFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SpecValueFormParam>('/specValue/updateSpecValue', param)
  },
  // 删除商品规格值
  deleteSpecValue: (specValueId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/specValue/deleteSpecValue/${specValueId}`, {})
  },
  // 批量删除商品规格值
  batchDeleteSpecValue: (specValueIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/specValue/batchDeleteSpecValue', specValueIdList)
  },

  // 导入商品规格值
  importSpecValue: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/specValue/importSpecValueExcel', file)
  },
  // 导出商品规格值
  exportSpecValue: (param?: SpecValueQueryParam): void => {
    return getDownload('/specValue/exportSpecValueExcel', param || {})
  },
}

