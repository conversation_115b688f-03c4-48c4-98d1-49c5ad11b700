import type { PageParam } from '@/api/base-model/page-model'
import type { SpecValueModel } from '@/api/business/spec-value/model/spec-value-model'

/**
 * 商品规格值查询参数
 */
export interface SpecValueQueryParam extends SpecValueModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品规格值分页查询参数
 */
export interface SpecValuePageParam extends SpecValueQueryParam {
  pageParam: PageParam
}

/**
 * 商品规格值表单参数类型
 */
export interface SpecValueFormParam extends SpecValueModel {

}

/**
 * 商品规格值查询结果类型
 */
export interface SpecValueResult extends SpecValueModel {

}

