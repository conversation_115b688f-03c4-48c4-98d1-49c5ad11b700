import type { ResponseModel } from '@/api/base-model/response-model'
import { postRequest } from '@/lib/http-request'

// OCR识别结果接口
export interface OcrResult {
  success: boolean
  text: string
  fileName: string
  fileUrl?: string
}

// 批量OCR识别结果接口
export interface BatchOcrResult {
  results: OcrResult[]
  totalSuccess: number
  totalFailed: number
}

export const ocrTextApi = {
  /**
   * 上传图片并识别文字
   * @param file 图片文件
   * @returns 识别结果
   */
  uploadAndRecognize: (file: File): Promise<ResponseModel<OcrResult>> => {
    const formData = new FormData()
    formData.append('file', file)
    return postRequest<ResponseModel<OcrResult>, FormData>('/business/ocr/recognize', formData)
  },

  /**
   * 批量上传图片并识别文字
   * @param files 图片文件数组
   * @returns 批量识别结果
   */
  batchUploadAndRecognize: (files: File[]): Promise<ResponseModel<BatchOcrResult>> => {
    const formData = new FormData()
    files.forEach((file) => {
      formData.append('files', file)
    })
    return postRequest<ResponseModel<BatchOcrResult>, FormData>('/business/ocr/batchRecognize', formData)
  },
}
