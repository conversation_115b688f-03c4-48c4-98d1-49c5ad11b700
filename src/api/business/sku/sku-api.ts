import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  SkuForm,
  SkuPageParam,
  SkuQueryParam,
  SkuResult,
} from './model/sku-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const skuApi = {

  // 分页查询商品SKU
  skuPage: (param: SkuPageParam): Promise<ResponseModel<PageResult<SkuResult>>> => {
    return postRequest<ResponseModel<PageResult<SkuResult>>, SkuQueryParam>('/sku/skuPage', param)
  },
  // 列表查询商品SKU
  skuList: (param: SkuQueryParam): Promise<ResponseModel<SkuForm[]>> => {
    return postRequest<ResponseModel<SkuForm[]>, SkuQueryParam>('/sku/skuList', param)
  },
  // 获取商品SKU详情
  skuDetail: (skuId: number): Promise<ResponseModel<SkuResult>> => {
    return getRequest<ResponseModel<SkuResult>>(`/sku/skuDetail/${skuId}`, {})
  },
  // 添加商品SKU
  addSku: (param: SkuForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SkuForm>('/sku/addSku', param)
  },
  // 更新商品SKU
  updateSku: (param: SkuForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SkuForm>('/sku/updateSku', param)
  },
  // 删除商品SKU
  deleteSku: (skuId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/sku/deleteSku/${skuId}`, {})
  },
  // 批量删除商品SKU
  batchDeleteSku: (skuIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/sku/batchDeleteSku', skuIdList)
  },

  // 导入商品SKU
  importSku: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/sku/importSkuExcel', file)
  },
  // 导出商品SKU
  exportSku: (param?: SkuQueryParam): void => {
    return getDownload('/sku/exportSkuExcel', param || {})
  },
}
