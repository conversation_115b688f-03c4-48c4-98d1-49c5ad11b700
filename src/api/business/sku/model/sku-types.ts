import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 商品SKU类型定义
 */
export interface SkuModel extends BaseModel {
  id?: number // 商品SKUID

  productId?: number // 商品ID | erp_product的id

  productName?: string // 商品名称

  productCode?: string // 商品编码

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要（如 颜色:红色,尺寸:XL）

  stock?: number // 库存

  status?: string // 字典 | SKU状态 sku_status：delist-下架 list-上架，soldout-缺货

  weight?: number // 重量(kg)

  price?: number // 单价

  leadTime?: number // 交付周期(天)

}

/**
 * 商品SKU查询参数
 */
export interface SkuQueryParam extends SkuModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至

  skuNameExt?: string // 商品名称扩展模糊查询
}

/**
 * 商品SKU分页查询参数
 */
export interface SkuPageParam extends SkuQueryParam {
  pageParam: PageParam
}

/**
 * 商品SKU表单类型
 */
export interface SkuForm extends SkuModel {

}

/**
 * 商品SKU查询结果类型
 */
export interface SkuResult extends SkuModel {

}
