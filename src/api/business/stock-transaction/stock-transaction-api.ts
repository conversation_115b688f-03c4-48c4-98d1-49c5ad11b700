import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  StockTransactionForm,
  StockTransactionPageParam,
  StockTransactionQueryParam,
  StockTransactionResult,
  StockTransactionSaveParam,
  StockTransactionAuditParam,
} from './model/stock-transaction-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const stockTransactionApi = {

  // 分页查询库存流水主表
  stockTransactionPage: (param: StockTransactionPageParam): Promise<ResponseModel<PageResult<StockTransactionResult>>> => {
    return postRequest<ResponseModel<PageResult<StockTransactionResult>>, StockTransactionQueryParam>('/stockTransaction/stockTransactionPage', param)
  },
  // 列表查询库存流水主表
  stockTransactionList: (param: StockTransactionQueryParam): Promise<ResponseModel<StockTransactionForm[]>> => {
    return postRequest<ResponseModel<StockTransactionForm[]>, StockTransactionQueryParam>('/stockTransaction/stockTransactionList', param)
  },
  // 获取库存流水主表详情
  stockTransactionDetail: (stockTransactionId: number): Promise<ResponseModel<StockTransactionResult>> => {
    return getRequest<ResponseModel<StockTransactionResult>>(`/stockTransaction/stockTransactionDetail/${stockTransactionId}`, {})
  },
  // 添加库存流水主表
  addStockTransaction: (param: StockTransactionForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionForm>('/stockTransaction/addStockTransaction', param)
  },
  // 更新库存流水主表
  updateStockTransaction: (param: StockTransactionForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionForm>('/stockTransaction/updateStockTransaction', param)
  },
  // 删除库存流水主表
  deleteStockTransaction: (stockTransactionId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/stockTransaction/deleteStockTransaction/${stockTransactionId}`, {})
  },
  // 批量删除库存流水主表
  batchDeleteStockTransaction: (stockTransactionIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/stockTransaction/batchDeleteStockTransaction', stockTransactionIdList)
  },

  // 创建库存流水单据（主表+明细）
  createStockTransaction: (param: StockTransactionSaveParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionSaveParam>('/stockTransaction/createStockTransaction', param)
  },

  // 更新库存流水单据（主表+明细）
  updateStockTransactionDoc: (param: StockTransactionSaveParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionSaveParam>('/stockTransaction/updateStockTransactionDoc', param)
  },

  // 库存流水审核
  auditStockTransaction: (param: StockTransactionAuditParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionAuditParam>('/stockTransaction/auditStockTransaction', param)
  },

  // 导入库存流水主表
  importStockTransaction: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/stockTransaction/importStockTransactionExcel', file)
  },
  // 导出库存流水主表
  exportStockTransaction: (param?: StockTransactionQueryParam): void => {
    return getDownload('/stockTransaction/exportStockTransactionExcel', param || {})
  },
}
