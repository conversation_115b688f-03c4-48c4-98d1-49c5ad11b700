import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 库存流水主表类型定义
 */
export interface StockTransactionModel extends BaseModel {
  id?: number // 库存流水主表ID

  transactionNo?: string // 流水单号（唯一）

  transactionType?: string // 字典 | 流水类型：IN_PURCHASE-采购入库，IN_RETURN-退货入库，IN_TRANSFER-调拨入库，IN_ADJUST-调整入库，IN_PRODUCTION-生产入库，OUT_SALES-销售出库，OUT_RETURN-退货出库，OUT_TRANSFER-调拨出库，OUT_ADJUST-调整出库，OUT_PRODUCTION-生产出库

  transactionDate?: string // 流水日期

  relatedType?: string // 字典 | 关联单据类型：ORDER-订单，PURCHASE-采购单，TRANSFER-调拨单，ADJUST-调整单，CHECK-盘点单

  relatedId?: number // 关联单据ID

  relatedNo?: string // 关联单据号

  operatorId?: number // 操作人ID | t_employee表的employee_id

  operatorName?: string // 操作人姓名

  totalQuantity?: number // 总数量（所有明细汇总）

  totalAmount?: number // 总金额（所有明细汇总）

  itemCount?: number // 明细条数

  auditStatus?: string // 字典 | 审核状态：PENDING-待审核，APPROVED-已审核，REJECTED-已拒绝，CANCELLED-已作废

  plannedQuantity?: number // 计划处理数量

  processedQuantity?: number // 已处理数量

  remainingQuantity?: number // 剩余未处理数量

  processStatus?: string // 字典 | 处理状态：PENDING-待处理，IN_PROGRESS-处理中，COMPLETED-已完成，CANCELLED-已取消

  remark?: string // 流水备注

}

/**
 * 库存流水主表添加参数
 */
export interface StockTransactionAddParam {
  transactionNo?: string // 流水单号（唯一）

  transactionType: string // 字典 | 流水类型（必填）

  transactionDate: string // 流水日期（必填）

  relatedType?: string // 字典 | 关联单据类型

  relatedId?: number // 关联单据ID

  relatedNo?: string // 关联单据号

  operatorId?: number // 操作人ID

  operatorName?: string // 操作人姓名

  remark?: string // 流水备注
}

/**
 * 库存流水明细添加参数
 */
export interface StockTransactionItemAddParam {
  warehouseId: number // 仓库ID（必填）

  warehouseName: string // 仓库名称（必填）

  productId: number // 商品ID（必填）

  productName: string // 商品名称（必填）

  skuId: number // SKU ID（必填）

  quantity: number // 变动数量（必填）

  unit: string // 单位（必填）

  unitCost: number // 单位成本（必填）

  totalAmount: number // 总金额（必填）

  remark?: string // 明细备注
}

/**
 * 库存流水保存参数（主表+明细）
 */
export interface StockTransactionSaveParam {
  stockTransaction: StockTransactionAddParam // 库存流水主表参数（必填）

  items: StockTransactionItemAddParam[] // 库存流水明细列表（必填）
}

/**
 * 库存流水主表查询参数
 */
export interface StockTransactionQueryParam extends StockTransactionModel {

  transactionDateFrom?: string // 流水日期始

  transactionDateTo?: string // 流水日期至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至
}

/**
 * 库存流水主表分页查询参数
 */
export interface StockTransactionPageParam extends StockTransactionQueryParam {
  pageParam: PageParam
}

/**
 * 库存流水主表表单类型
 */
export interface StockTransactionForm extends StockTransactionModel {

}

/**
 * 库存流水主表查询结果类型
 */
export interface StockTransactionResult extends StockTransactionModel {

}

/**
 * 库存流水审核参数
 */
export interface StockTransactionAuditParam {
  id: number // 库存流水ID（必填）

  auditStatus: string // 审核状态：PENDING-待审核，APPROVED-已审核，REJECTED-已拒绝，CANCELLED-已作废（必填）

  auditRemark?: string // 审核备注（可选）
}

/**
 * 审核状态常量
 */
export const AUDIT_STATUS = {
  PENDING: 'PENDING',     // 待审核
  APPROVED: 'APPROVED',   // 已审核
  REJECTED: 'REJECTED',   // 已拒绝
  CANCELLED: 'CANCELLED', // 已作废
} as const

/**
 * 审核状态选项
 */
export const AUDIT_STATUS_OPTIONS = [
  { label: '待审核', value: AUDIT_STATUS.PENDING },
  { label: '已审核', value: AUDIT_STATUS.APPROVED },
  { label: '已拒绝', value: AUDIT_STATUS.REJECTED },
  { label: '已作废', value: AUDIT_STATUS.CANCELLED },
]
