import type {
  PaperForm,
  PaperPageParam,
  PaperQueryParam,
  PaperResult,
} from './model/paper-types'
import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const paperApi = {

  // 分页查询纸张基础信息表
  paperPage: (param: PaperPageParam): Promise<ResponseModel<PageResult<PaperResult>>> => {
    return postRequest<ResponseModel<PageResult<PaperResult>>, PaperQueryParam>('/paper/paperPage', param)
  },
  // 列表查询纸张基础信息表
  paperList: (param: PaperQueryParam): Promise<ResponseModel<PaperResult[]>> => {
    return postRequest<ResponseModel<PaperResult[]>, PaperQueryParam>('/paper/paperList', param)
  },
  // 获取纸张基础信息表详情
  paperDetail: (paperId: number): Promise<ResponseModel<PaperForm>> => {
    return getRequest<ResponseModel<PaperForm>>(`/paper/paperDetail/${paperId}`, {})
  },
  // 添加纸张基础信息表
  addPaper: (param: PaperForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperForm>('/paper/addPaper', param)
  },
  // 更新纸张基础信息表
  updatePaper: (param: PaperForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PaperForm>('/paper/updatePaper', param)
  },
  // 删除纸张基础信息表
  deletePaper: (paperId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/paper/deletePaper/${paperId}`, {})
  },
  // 批量删除纸张基础信息表
  batchDeletePaper: (paperIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/paper/batchDeletePaper', paperIdList)
  },

  // 导入纸张基础信息表
  importPaper: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/paper/importPaperExcel', file)
  },
  // 导出纸张基础信息表
  exportPaper: (param?: PaperQueryParam): void => {
    return getDownload('/paper/exportPaperExcel', param || {})
  },
}
