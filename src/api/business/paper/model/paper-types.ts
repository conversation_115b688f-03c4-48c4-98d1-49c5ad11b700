import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 纸张基础信息表类型定义
 */
export interface PaperModel extends BaseModel {
  id?: number // 纸张基础信息表ID

  description?: string // 描述

  code?: string // 纸张编码

  paperCategory?: string // 字典|paper_category 纸张类别

  corrugatedShape?: string // 字典|corrugated_shape 楞形

  color?: string // 颜色代码

  weight?: string // 字典|paper_weight 克重(g)

  status?: boolean // 字典|paper_status 状态 (true=1)

}
/**
 * 纸张基础信息表查询参数
 */
export interface PaperQueryParam extends PaperModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 纸张基础信息表分页查询参数
 */
export interface PaperPageParam extends PaperQueryParam {
  pageParam: PageParam
}

/**
 * 纸张基础信息表表单类型
 */
export interface PaperForm extends PaperModel {

}

/**
 * 纸张基础信息表查询结果类型
 */
export interface PaperResult extends PaperModel {

}
