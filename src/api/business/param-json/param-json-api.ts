import type { ResponseModel } from '@/api/base-model/response-model'
import type { NumberConfig, SupplierConfig } from './model/param-config'
import LocalStorageKeyConst from '@/constants/local-storage-key-const'
import { getRequest, postRequest } from '@/lib/http-request'
import { localRead } from '@/utils/local-util'

export const paramJsonApi = {

  // 获取报价参数
  getBoxQuotationParam: (): Promise<ResponseModel<NumberConfig[]>> => {
    return getRequest<ResponseModel<NumberConfig[]>>('/paramJson/getBoxQuotationParam', {})
  },

  // 同步获取报价参数
  getBoxQuotationParamSync: (): ResponseModel<NumberConfig[]> => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', `${import.meta.env.VITE_APP_API_URL}/paramJson/getBoxQuotationParam`, false)
    xhr.setRequestHeader('Content-Type', 'application/json')
    const token = localRead(LocalStorageKeyConst.USER_TOKEN)
    if (token) {
      xhr.setRequestHeader('x-access-token', token)
    }
    xhr.send()

    if (xhr.status === 200) {
      const res = JSON.parse(xhr.responseText)
      if (res.code && res.code !== 1) {
        throw new Error(res.msg || '获取配置失败')
      }
      return res
    }
    throw new Error(`获取配置失败: ${xhr.status}`)
  },

  // 更新报价参数
  updateBoxQuotationParam: (paramList: NumberConfig[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, NumberConfig[]>('/paramJson/updateBoxQuotationParam', paramList)
  },

  // 获取供应商配置
  getSupplierConfig: (supplierId: number): Promise<ResponseModel<SupplierConfig>> => {
    return getRequest<ResponseModel<SupplierConfig>>(`/paramJson/getSupplierConfig/${supplierId}`, {})
  },

  // 更新供应商配置
  updateSupplierConfig: (supplierConfig: SupplierConfig): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SupplierConfig>('/paramJson/updateSupplierConfig', supplierConfig)
  },
}
