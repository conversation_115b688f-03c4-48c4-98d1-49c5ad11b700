export interface BaseConfig {
  id?: number // 配置ID
  code?: string // 参数编码
  name?: string // 参数名称
  description?: string // 参数描述
  orderNum?: number // 排序号
  groupCode?: string // 分组编码
  relationType?: string // 关系类型
  relationId?: number // 关系ID
}

export interface NumberConfig extends BaseConfig {
  value: number // 参数值
  step: number // 步进值
  min: number // 最小值
  max: number // 最大值
  decimalPlaces: number // 小数位数
}

export interface SupplierConfig extends BaseConfig {
  discountRate?: number // 折扣率
  taxRate?: number // 税率
  processingFee?: SupplierProcessingFee[] // 加工费
}

export interface SupplierProcessingFee {
  layer: number // 层数
  value: number // 加工费
  unit: string // 单位

}
