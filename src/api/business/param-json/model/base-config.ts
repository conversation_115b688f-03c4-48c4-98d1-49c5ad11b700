export interface BaseConfig {
  /** 配置ID */
  id?: number

  /** 参数编码 */
  code: string

  /** 参数名称 */
  name: string

  /** 参数描述 */
  description: string

  /** 排序号 */
  orderNum: number

  /** 分组编码 */
  groupCode: string

  /** 关系类型 */
  relationType?: string

  /** 关系ID */
  relationId?: number
}

export interface NumberConfig extends BaseConfig {
  /** 参数值 */
  value: number

  /** 步进值 */
  step: number

  /** 最小值 */
  min: number

  /** 最大值 */
  max: number

  /** 小数位数 */
  decimalPlaces: number
}

export interface SupplierConfig extends BaseConfig {
  /** 折扣率 */
  discountRate: number
}
