import type { PageParam } from '@/api/base-model/page-model'
import type { ParamJsonModel } from '@/api/business/param-json/model/param-json-model'

/**
 * json参数查询参数
 */
export interface ParamJsonQueryParam extends ParamJsonModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * json参数分页查询参数
 */
export interface ParamJsonPageParam extends ParamJsonQueryParam {
  pageParam: PageParam
}

/**
 * json参数表单参数类型
 */
export interface ParamJsonFormParam extends ParamJsonModel {

}

/**
 * json参数查询结果类型
 */
export interface ParamJsonResult extends ParamJsonModel {

}
