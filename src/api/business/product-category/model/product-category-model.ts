import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品分类基础信息类型定义
 */
export interface ProductCategoryModel extends BaseModel {
  id?: number // 商品分类基础信息ID

  categoryName?: string // 分类名称

  categoryType?: string // 字典 | 分类类型 category_type：product-商品分类，service-服务分类，material-物料分类

  parentId?: number // 父级分类ID | 关联erp_product_category的id

  sort?: number // 排序

  disabledFlag?: number // 禁用标志：0-启用，1-禁用

  remark?: string // 备注

}
