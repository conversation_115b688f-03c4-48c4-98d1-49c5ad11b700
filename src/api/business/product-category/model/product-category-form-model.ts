import type { PageParam } from '@/api/base-model/page-model'
import type { ProductCategoryModel } from '@/api/business/product-category/model/product-category-model'

/**
 * 商品分类基础信息查询参数
 */
export interface ProductCategoryQueryParam extends ProductCategoryModel {

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 商品分类基础信息分页查询参数
 */
export interface ProductCategoryPageParam extends ProductCategoryQueryParam {
  pageParam: PageParam
}

/**
 * 商品分类基础信息表单参数类型
 */
export interface ProductCategoryFormParam extends ProductCategoryModel {

}

/**
 * 商品分类基础信息查询结果类型
 */
export interface ProductCategoryResult extends ProductCategoryModel {

}

/**
 * 商品分类树形结构类型
 */
export interface ProductCategoryTreeResult extends ProductCategoryModel {
  children?: ProductCategoryTreeResult[] // 子分类列表
}
