import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  ProductCategoryFormParam,
  ProductCategoryPageParam,
  ProductCategoryQueryParam,
  ProductCategoryResult,
  ProductCategoryTreeResult,
} from './model/product-category-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const productCategoryApi = {

  // 分页查询商品分类基础信息
  productCategoryPage: (param: ProductCategoryPageParam): Promise<ResponseModel<PageResult<ProductCategoryResult>>> => {
    return postRequest<ResponseModel<PageResult<ProductCategoryResult>>, ProductCategoryQueryParam>('/productCategory/productCategoryPage', param)
  },
  // 列表查询商品分类基础信息
  productCategoryList: (param: ProductCategoryQueryParam): Promise<ResponseModel<ProductCategoryResult[]>> => {
    return postRequest<ResponseModel<ProductCategoryResult[]>, ProductCategoryQueryParam>('/productCategory/productCategoryList', param)
  },
  // 商品分类层级树查询
  productCategoryTree: (param: ProductCategoryQueryParam): Promise<ResponseModel<ProductCategoryTreeResult[]>> => {
    return postRequest<ResponseModel<ProductCategoryTreeResult[]>, ProductCategoryQueryParam>('/productCategory/productCategoryTree', param)
  },
  // 获取商品分类基础信息详情
  productCategoryDetail: (productCategoryId: number): Promise<ResponseModel<ProductCategoryResult>> => {
    return getRequest<ResponseModel<ProductCategoryResult>>(`/productCategory/productCategoryDetail/${productCategoryId}`, {})
  },
  // 添加商品分类基础信息
  addProductCategory: (param: ProductCategoryFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductCategoryFormParam>('/productCategory/addProductCategory', param)
  },
  // 更新商品分类基础信息
  updateProductCategory: (param: ProductCategoryFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductCategoryFormParam>('/productCategory/updateProductCategory', param)
  },
  // 删除商品分类基础信息
  deleteProductCategory: (productCategoryId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/productCategory/deleteProductCategory/${productCategoryId}`, {})
  },
  // 批量删除商品分类基础信息
  batchDeleteProductCategory: (productCategoryIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/productCategory/batchDeleteProductCategory', productCategoryIdList)
  },

  // 导入商品分类基础信息
  importProductCategory: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/productCategory/importProductCategoryExcel', file)
  },
  // 导出商品分类基础信息
  exportProductCategory: (param?: ProductCategoryQueryParam): void => {
    return getDownload('/productCategory/exportProductCategoryExcel', param || {})
  },

}
