import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PriceHistoryFormParam,
  PriceHistoryPageParam,
  PriceHistoryQueryParam,
  PriceHistoryResult,
} from './model/price-history-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const priceHistoryApi = {

  // 分页查询价格历史
  priceHistoryPage: (param: PriceHistoryPageParam): Promise<ResponseModel<PageResult<PriceHistoryResult>>> => {
    return postRequest<ResponseModel<PageResult<PriceHistoryResult>>, PriceHistoryQueryParam>('/priceHistory/priceHistoryPage', param)
  },
  // 列表查询价格历史
  priceHistoryList: (param: PriceHistoryQueryParam): Promise<ResponseModel<PriceHistoryResult[]>> => {
    return postRequest<ResponseModel<PriceHistoryResult[]>, PriceHistoryQueryParam>('/priceHistory/priceHistoryList', param)
  },
  // 获取价格历史详情
  priceHistoryDetail: (priceHistoryId: number): Promise<ResponseModel<PriceHistoryResult>> => {
    return getRequest<ResponseModel<PriceHistoryResult>>(`/priceHistory/priceHistoryDetail/${priceHistoryId}`, {})
  },
  // 添加价格历史
  addPriceHistory: (param: PriceHistoryFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PriceHistoryFormParam>('/priceHistory/addPriceHistory', param)
  },
  // 更新价格历史
  updatePriceHistory: (param: PriceHistoryFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PriceHistoryFormParam>('/priceHistory/updatePriceHistory', param)
  },
  // 删除价格历史
  deletePriceHistory: (priceHistoryId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/priceHistory/deletePriceHistory/${priceHistoryId}`, {})
  },
  // 批量删除价格历史
  batchDeletePriceHistory: (priceHistoryIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/priceHistory/batchDeletePriceHistory', priceHistoryIdList)
  },

  // 导入价格历史
  importPriceHistory: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/priceHistory/importPriceHistoryExcel', file)
  },
  // 导出价格历史
  exportPriceHistory: (param?: PriceHistoryQueryParam): void => {
    return getDownload('/priceHistory/exportPriceHistoryExcel', param || {})
  },
}

