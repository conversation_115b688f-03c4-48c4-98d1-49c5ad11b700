import type { PageParam } from '@/api/base-model/page-model'
import type { PriceHistoryModel } from '@/api/business/price-history/model/price-history-model'

/**
 * 价格历史查询参数
 */
export interface PriceHistoryQueryParam extends PriceHistoryModel {
  /**
   * 变动日期始
   */
  changeDateFrom?: string
  /**
   * 变动日期至
   */
  changeDateTo?: string
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 价格历史分页查询参数
 */
export interface PriceHistoryPageParam extends PriceHistoryQueryParam {
  pageParam: PageParam
}

/**
 * 价格历史表单参数类型
 */
export interface PriceHistoryFormParam extends PriceHistoryModel {

}

/**
 * 价格历史查询结果类型
 */
export interface PriceHistoryResult extends PriceHistoryModel {

}

