import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 价格历史类型定义
 */
export interface PriceHistoryModel extends BaseModel {
  /**
   * 价格历史ID
   */
  id?: number

  /**
   * 价格ID | erp_price的id
   */
  priceId?: number

  /**
   * 旧价格
   */
  oldPrice?: number

  /**
   * 新价格
   */
  newPrice?: number

  /**
   * 变动日期
   */
  changeDate?: string

  /**
   * 变更原因
   */
  changeReason?: string

}
