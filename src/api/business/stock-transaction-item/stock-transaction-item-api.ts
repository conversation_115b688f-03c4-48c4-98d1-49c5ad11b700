import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  StockTransactionItemForm,
  StockTransactionItemPageParam,
  StockTransactionItemQueryParam,
  StockTransactionItemResult,
} from './model/stock-transaction-item-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const stockTransactionItemApi = {

  // 分页查询库存流水明细表
  stockTransactionItemPage: (param: StockTransactionItemPageParam): Promise<ResponseModel<PageResult<StockTransactionItemResult>>> => {
    return postRequest<ResponseModel<PageResult<StockTransactionItemResult>>, StockTransactionItemQueryParam>('/stockTransactionItem/stockTransactionItemPage', param)
  },
  // 列表查询库存流水明细表
  stockTransactionItemList: (param: StockTransactionItemQueryParam): Promise<ResponseModel<StockTransactionItemForm[]>> => {
    return postRequest<ResponseModel<StockTransactionItemForm[]>, StockTransactionItemQueryParam>('/stockTransactionItem/stockTransactionItemList', param)
  },
  // 获取库存流水明细表详情
  stockTransactionItemDetail: (stockTransactionItemId: number): Promise<ResponseModel<StockTransactionItemResult>> => {
    return getRequest<ResponseModel<StockTransactionItemResult>>(`/stockTransactionItem/stockTransactionItemDetail/${stockTransactionItemId}`, {})
  },
  // 添加库存流水明细表
  addStockTransactionItem: (param: StockTransactionItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionItemForm>('/stockTransactionItem/addStockTransactionItem', param)
  },
  // 更新库存流水明细表
  updateStockTransactionItem: (param: StockTransactionItemForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, StockTransactionItemForm>('/stockTransactionItem/updateStockTransactionItem', param)
  },
  // 删除库存流水明细表
  deleteStockTransactionItem: (stockTransactionItemId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/stockTransactionItem/deleteStockTransactionItem/${stockTransactionItemId}`, {})
  },
  // 批量删除库存流水明细表
  batchDeleteStockTransactionItem: (stockTransactionItemIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/stockTransactionItem/batchDeleteStockTransactionItem', stockTransactionItemIdList)
  },

  // 导入库存流水明细表
  importStockTransactionItem: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/stockTransactionItem/importStockTransactionItemExcel', file)
  },
  // 导出库存流水明细表
  exportStockTransactionItem: (param?: StockTransactionItemQueryParam): void => {
    return getDownload('/stockTransactionItem/exportStockTransactionItemExcel', param || {})
  },
}
