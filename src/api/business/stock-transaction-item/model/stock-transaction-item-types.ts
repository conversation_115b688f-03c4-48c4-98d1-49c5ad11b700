import type {PageParam} from '@/api/base-model/page-model'
import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 库存流水明细表类型定义
 */
export interface StockTransactionItemModel extends BaseModel {
  id?: number // 库存流水明细表ID

  transactionId?: number // 流水主表ID（外键）

  transactionNo?: string // 流水单号（冗余字段）

  warehouseId?: number // 仓库ID | erp_warehouse表的id

  warehouseCode?: string // 仓库编码

  warehouseName?: string // 仓库名称

  productId?: number // 商品ID | erp_product表的id

  productCode?: string // 商品编码

  productName?: string // 商品名称

  skuId?: number // SKU ID | erp_sku表的id

  skuCode?: string // SKU编码

  specSummary?: string // 规格摘要

  unit?: string // 单位

  quantity?: number // 变动数量 (正数入库，负数出库)

  unitCost?: number // 单位成本

  totalAmount?: number // 总金额

  beforeStock?: number // 变动前库存

  afterStock?: number // 变动后库存

  remark?: string // 明细备注

}
/**
 * 库存流水明细表查询参数
 */
export interface StockTransactionItemQueryParam extends StockTransactionItemModel {

                createTimeFrom?: string // 创建时间始

                createTimeTo?: string // 创建时间至
}

/**
 * 库存流水明细表分页查询参数
 */
export interface StockTransactionItemPageParam extends StockTransactionItemQueryParam {
    pageParam: PageParam
}

/**
 * 库存流水明细表表单类型
 */
export interface StockTransactionItemForm extends StockTransactionItemModel {

}

/**
 * 库存流水明细表查询结果类型
 */
export interface StockTransactionItemResult extends StockTransactionItemModel {

}
