import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  ProductAttrFormParam,
  ProductAttrPageParam,
  ProductAttrQueryParam,
  ProductAttrResult,
} from './model/product-attr-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const productAttrApi = {

  // 分页查询商品属性
  productAttrPage: (param: ProductAttrPageParam): Promise<ResponseModel<PageResult<ProductAttrResult>>> => {
    return postRequest<ResponseModel<PageResult<ProductAttrResult>>, ProductAttrQueryParam>('/productAttr/productAttrPage', param)
  },
  // 列表查询商品属性
  productAttrList: (param: ProductAttrQueryParam): Promise<ResponseModel<ProductAttrResult[]>> => {
    return postRequest<ResponseModel<ProductAttrResult[]>, ProductAttrQueryParam>('/productAttr/productAttrList', param)
  },
  // 获取商品属性详情
  productAttrDetail: (productAttrId: number): Promise<ResponseModel<ProductAttrResult>> => {
    return getRequest<ResponseModel<ProductAttrResult>>(`/productAttr/productAttrDetail/${productAttrId}`, {})
  },
  // 添加商品属性
  addProductAttr: (param: ProductAttrFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductAttrFormParam>('/productAttr/addProductAttr', param)
  },
  // 更新商品属性
  updateProductAttr: (param: ProductAttrFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductAttrFormParam>('/productAttr/updateProductAttr', param)
  },
  // 删除商品属性
  deleteProductAttr: (productAttrId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/productAttr/deleteProductAttr/${productAttrId}`, {})
  },
  // 批量删除商品属性
  batchDeleteProductAttr: (productAttrIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/productAttr/batchDeleteProductAttr', productAttrIdList)
  },

  // 导入商品属性
  importProductAttr: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/productAttr/importProductAttrExcel', file)
  },
  // 导出商品属性
  exportProductAttr: (param?: ProductAttrQueryParam): void => {
    return getDownload('/productAttr/exportProductAttrExcel', param || {})
  },
}

