import type { PageParam } from '@/api/base-model/page-model'
import type { ProductAttrModel } from '@/api/business/product-attr/model/product-attr-model'

/**
 * 商品属性查询参数
 */
export interface ProductAttrQueryParam extends ProductAttrModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品属性分页查询参数
 */
export interface ProductAttrPageParam extends ProductAttrQueryParam {
  pageParam: PageParam
}

/**
 * 商品属性表单参数类型
 */
export interface ProductAttrFormParam extends ProductAttrModel {

}

/**
 * 商品属性查询结果类型
 */
export interface ProductAttrResult extends ProductAttrModel {

}

