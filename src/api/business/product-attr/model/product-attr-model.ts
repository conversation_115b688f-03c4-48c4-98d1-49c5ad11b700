import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品属性类型定义
 */
export interface ProductAttrModel extends BaseModel {
  /**
   * 商品属性ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId?: number

  /**
   * 属性名称
   */
  attrName?: string

  /**
   * 属性值
   */
  attrValue?: string

  /**
   * 是否参与搜索：0-否，1-是
   */
  isSearch?: boolean

  /**
   * 是否可见：0-否，1-是
   */
  isVisible?: boolean

  /**
   * 排序
   */
  sort?: number

}
