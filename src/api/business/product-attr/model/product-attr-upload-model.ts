/**
 * 商品属性上传参数模型
 */
export interface ProductAttrUploadParam {
  /**
   * 商品属性ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId: number
  /**
   * 属性名称
   */
  attrName: string
  /**
   * 属性值
   */
  attrValue: string
  /**
   * 字典 | 属性值类型：1-自由文本，2-枚举值
   */
  valueType: boolean
  /**
   * 字典 | 是否参与搜索：0-否，1-是
   */
  isSearch: boolean
  /**
   * 字典 | 是否可见：0-否，1-是
   */
  isVisible: boolean
  /**
   * 排序
   */
  sort: number
}
