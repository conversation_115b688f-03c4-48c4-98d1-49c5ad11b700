import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 采购付款记录类型定义
 */
export interface PurchasePaymentModel extends BaseModel {
  id?: number // 采购付款记录ID

  paymentNo?: string // 付款单号

  purchaseOrderId?: number // 采购订单ID | erp_purchase_order表的id

  purchaseOrderNo?: string // 采购订单编号

  paymentType?: string // 字典 | 付款类型 payment_type：ADVANCE-预付款，PROGRESS-进度款，FINAL-尾款，DEPOSIT-定金，BALANCE-余款，QUALITY_RETENTION-质保金

  paymentMethod?: string // 字典 | 付款方式 payment_method：CASH-现金，BANK_TRANSFER-银行转账，CREDIT_CARD-信用卡，CHECK-支票，ONLINE-在线支付

  paymentAmount?: number // 付款金额

  currency?: string // 币种

  exchangeRate?: number // 汇率

  plannedPaymentDate?: string // 计划付款日期

  actualPaymentDate?: string // 实际付款日期

  paymentDeadline?: string // 付款截止日期

  paymentStatus?: string // 字典 | 付款状态 payment_status：PENDING-待付款，PROCESSING-付款中，PAID-已付款，FAILED-付款失败，CANCELLED-已取消，REFUNDED-已退款

  isOverdue?: boolean // 是否逾期：FALSE-未逾期，TRUE-已逾期

  overdueDays?: number // 逾期天数

  supplierBankAccount?: string // 供应商银行账户

  supplierBankName?: string // 供应商银行名称

  transactionNo?: string // 银行流水号

  invoiceNo?: string // 发票号码

  invoiceAmount?: number // 发票金额

  invoiceStatus?: string // 字典 | 发票状态 invoice_status：PENDING-待开票，INVOICED-已开票，RECEIVED-已收票

  receiptNo?: string // 收据号码

  purchaserId?: number // 采购员ID | t_employee表的employee_id

  purchaserName?: string // 采购员姓名

  qualityRetentionRate?: number // 质保金比例

  qualityRetentionAmount?: number // 质保金金额

  taxDeductionAmount?: number // 税款扣除金额

  approvalStatus?: string // 字典 | 审批状态 approval_status：PENDING-待审批，APPROVED-已审批，REJECTED-已拒绝

  approvalUserId?: number // 审批人ID | t_employee表的employee_id

  approvalTime?: string // 审批时间

  approvalRemark?: string // 审批备注

  financeConfirmUserId?: number // 财务确认人ID | t_employee表的employee_id

  financeConfirmTime?: string // 财务确认时间

  financeRemark?: string // 财务备注

  paymentPurpose?: string // 付款用途

  paymentDescription?: string // 付款说明

  attachmentUrls?: string // 附件URL列表 (JSON格式)

  remark?: string // 备注

}
/**
 * 采购付款记录查询参数
 */
export interface PurchasePaymentQueryParam extends PurchasePaymentModel {

  plannedPaymentDateFrom?: string // 计划付款日期始

  plannedPaymentDateTo?: string // 计划付款日期至

  actualPaymentDateFrom?: string // 实际付款日期始

  actualPaymentDateTo?: string // 实际付款日期至

  paymentDeadlineFrom?: string // 付款截止日期始

  paymentDeadlineTo?: string // 付款截止日期至

  approvalTimeFrom?: string // 审批时间始

  approvalTimeTo?: string // 审批时间至

  financeConfirmTimeFrom?: string // 财务确认时间始

  financeConfirmTimeTo?: string // 财务确认时间至

  createTimeFrom?: string // 创建时间始

  createTimeTo?: string // 创建时间至

  updateTimeFrom?: string // 更新时间始

  updateTimeTo?: string // 更新时间至
}

/**
 * 采购付款记录分页查询参数
 */
export interface PurchasePaymentPageParam extends PurchasePaymentQueryParam {
  pageParam: PageParam
}

/**
 * 采购付款记录表单类型
 */
export interface PurchasePaymentForm extends PurchasePaymentModel {

}

/**
 * 采购付款记录查询结果类型
 */
export interface PurchasePaymentResult extends PurchasePaymentModel {

}
