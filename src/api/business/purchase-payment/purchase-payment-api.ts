import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  PurchasePaymentForm,
  PurchasePaymentPageParam,
  PurchasePaymentQueryParam,
  PurchasePaymentResult,
} from './model/purchase-payment-types'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const purchasePaymentApi = {

  // 分页查询采购付款记录
  purchasePaymentPage: (param: PurchasePaymentPageParam): Promise<ResponseModel<PageResult<PurchasePaymentResult>>> => {
    return postRequest<ResponseModel<PageResult<PurchasePaymentResult>>, PurchasePaymentQueryParam>('/purchasePayment/purchasePaymentPage', param)
  },
  // 列表查询采购付款记录
  purchasePaymentList: (param: PurchasePaymentQueryParam): Promise<ResponseModel<PurchasePaymentForm[]>> => {
    return postRequest<ResponseModel<PurchasePaymentForm[]>, PurchasePaymentQueryParam>('/purchasePayment/purchasePaymentList', param)
  },
  // 获取采购付款记录详情
  purchasePaymentDetail: (purchasePaymentId: number): Promise<ResponseModel<PurchasePaymentResult>> => {
    return getRequest<ResponseModel<PurchasePaymentResult>>(`/purchasePayment/purchasePaymentDetail/${purchasePaymentId}`, {})
  },
  // 添加采购付款记录
  addPurchasePayment: (param: PurchasePaymentForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchasePaymentForm>('/purchasePayment/addPurchasePayment', param)
  },
  // 更新采购付款记录
  updatePurchasePayment: (param: PurchasePaymentForm): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, PurchasePaymentForm>('/purchasePayment/updatePurchasePayment', param)
  },
  // 删除采购付款记录
  deletePurchasePayment: (purchasePaymentId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/purchasePayment/deletePurchasePayment/${purchasePaymentId}`, {})
  },
  // 批量删除采购付款记录
  batchDeletePurchasePayment: (purchasePaymentIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/purchasePayment/batchDeletePurchasePayment', purchasePaymentIdList)
  },

  // 导入采购付款记录
  importPurchasePayment: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/purchasePayment/importPurchasePaymentExcel', file)
  },
  // 导出采购付款记录
  exportPurchasePayment: (param?: PurchasePaymentQueryParam): void => {
    return getDownload('/purchasePayment/exportPurchasePaymentExcel', param || {})
  },
}
