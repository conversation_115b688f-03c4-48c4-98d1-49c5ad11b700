import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  SpecFormParam,
  SpecPageParam,
  SpecQueryParam,
  SpecResult,
} from './model/spec-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const specApi = {

  // 分页查询商品规格
  specPage: (param: SpecPageParam): Promise<ResponseModel<PageResult<SpecResult>>> => {
    return postRequest<ResponseModel<PageResult<SpecResult>>, SpecQueryParam>('/spec/specPage', param)
  },
  // 列表查询商品规格
  specList: (param: SpecQueryParam): Promise<ResponseModel<SpecResult[]>> => {
    return postRequest<ResponseModel<SpecResult[]>, SpecQueryParam>('/spec/specList', param)
  },
  // 获取商品规格详情
  specDetail: (specId: number): Promise<ResponseModel<SpecResult>> => {
    return getRequest<ResponseModel<SpecResult>>(`/spec/specDetail/${specId}`, {})
  },
  // 添加商品规格
  addSpec: (param: SpecFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SpecFormParam>('/spec/addSpec', param)
  },
  // 更新商品规格
  updateSpec: (param: SpecFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, SpecFormParam>('/spec/updateSpec', param)
  },
  // 删除商品规格
  deleteSpec: (specId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/spec/deleteSpec/${specId}`, {})
  },
  // 批量删除商品规格
  batchDeleteSpec: (specIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/spec/batchDeleteSpec', specIdList)
  },

  // 导入商品规格
  importSpec: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/spec/importSpecExcel', file)
  },
  // 导出商品规格
  exportSpec: (param?: SpecQueryParam): void => {
    return getDownload('/spec/exportSpecExcel', param || {})
  },
}

