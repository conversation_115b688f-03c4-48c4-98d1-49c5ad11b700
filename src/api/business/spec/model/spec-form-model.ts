import type { PageParam } from '@/api/base-model/page-model'
import type { SpecModel } from '@/api/business/spec/model/spec-model'

/**
 * 商品规格查询参数
 */
export interface SpecQueryParam extends SpecModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品规格分页查询参数
 */
export interface SpecPageParam extends SpecQueryParam {
  pageParam: PageParam
}

/**
 * 商品规格表单参数类型
 */
export interface SpecFormParam extends SpecModel {

}

/**
 * 商品规格查询结果类型
 */
export interface SpecResult extends SpecModel {

}

