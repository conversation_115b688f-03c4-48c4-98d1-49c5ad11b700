import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品规格类型定义
 */
export interface SpecModel extends BaseModel {
  /**
   * 商品规格ID
   */
  id?: number

  /**
   * 规格名称
   */
  name?: string

  /**
   * 字典 | 规格值来源 spec_value_source：1-枚举，2-用户输入
   */
  valueSource?: string

  /**
   * 字典 | 规格值类型 spec_value_type：1-文本，2-数字，3-日期
   */
  valueType?: string

  /**
   * 最小值（数字类型时有效）
   */
  minValue?: number

  /**
   * 最大值（数字类型时有效）
   */
  maxValue?: number

  /**
   * 步长（数字类型时有效）
   */
  stepSize?: number

  /**
   * 单位（数字类型时有效）
   */
  unit?: string

  /**
   * 排序
   */
  sort?: number

  /**
   * 规格状态：0-禁用，1-启用
   */
  status?: boolean

}
