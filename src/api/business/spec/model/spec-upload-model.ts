/**
 * 商品规格上传参数模型
 */
export interface SpecUploadParam {
  /**
   * 商品规格ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId: number
  /**
   * 规格名称
   */
  name: string
  /**
   * 字典 | 规格值来源：1-枚举，2-用户输入
   */
  valueSource: boolean
  /**
   * 字典 | 规格值类型：1-文本，2-数字，3-日期
   */
  valueType: boolean
  /**
   * 最小值（数字类型时有效）
   */
  minValue?: string
  /**
   * 最大值（数字类型时有效）
   */
  maxValue?: string
  /**
   * 步长（数字类型时有效）
   */
  stepSize?: string
  /**
   * 单位（数字类型时有效）
   */
  unit?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 字典 | 规格状态：0-禁用，1-启用
   */
  status: boolean
}
