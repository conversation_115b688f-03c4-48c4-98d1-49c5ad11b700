import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  BrandFormParam,
  BrandPageParam,
  BrandQueryParam,
  BrandResult,
} from './model/brand-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const brandApi = {

  // 分页查询商品品牌
  brandPage: (param: BrandPageParam): Promise<ResponseModel<PageResult<BrandResult>>> => {
    return postRequest<ResponseModel<PageResult<BrandResult>>, BrandQueryParam>('/brand/brandPage', param)
  },
  // 列表查询商品品牌
  brandList: (param: BrandQueryParam): Promise<ResponseModel<BrandResult[]>> => {
    return postRequest<ResponseModel<BrandResult[]>, BrandQueryParam>('/brand/brandList', param)
  },
  // 获取商品品牌详情
  brandDetail: (brandId: number): Promise<ResponseModel<BrandResult>> => {
    return getRequest<ResponseModel<BrandResult>>(`/brand/brandDetail/${brandId}`, {})
  },
  // 添加商品品牌
  addBrand: (param: BrandFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BrandFormParam>('/brand/addBrand', param)
  },
  // 更新商品品牌
  updateBrand: (param: BrandFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, BrandFormParam>('/brand/updateBrand', param)
  },
  // 删除商品品牌
  deleteBrand: (brandId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/brand/deleteBrand/${brandId}`, {})
  },
  // 批量删除商品品牌
  batchDeleteBrand: (brandIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/brand/batchDeleteBrand', brandIdList)
  },

  // 导入商品品牌
  importBrand: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/brand/importBrandExcel', file)
  },
  // 导出商品品牌
  exportBrand: (param?: BrandQueryParam): void => {
    return getDownload('/brand/exportBrandExcel', param || {})
  },
}
