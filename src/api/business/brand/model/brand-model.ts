import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品品牌类型定义
 */
export interface BrandModel extends BaseModel {
  /**
   * 商品品牌ID
   */
  id?: number

  /**
   * 品牌编码
   */
  brandCode?: string

  /**
   * 品牌名称
   */
  brandName?: string

  /**
   * 品牌Logo
   */
  logoUrl?: string

  /**
   * 品牌描述
   */
  description?: string

  /**
   * 品牌网站
   */
  website?: string

  /**
   * 排序
   */
  sort?: number

  /**
   * 品牌状态：0-禁用，1-启用
   */
  status?: boolean

}
