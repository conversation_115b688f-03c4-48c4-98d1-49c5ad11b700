import type { PageParam } from '@/api/base-model/page-model'
import type { BrandModel } from '@/api/business/brand/model/brand-model'

/**
 * 商品品牌查询参数
 */
export interface BrandQueryParam extends BrandModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品品牌分页查询参数
 */
export interface BrandPageParam extends BrandQueryParam {
  pageParam: PageParam
}

/**
 * 商品品牌表单参数类型
 */
export interface BrandFormParam extends BrandModel {

}

/**
 * 商品品牌查询结果类型
 */
export interface BrandResult extends BrandModel {

}
