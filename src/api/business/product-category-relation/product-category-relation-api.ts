import type { PageResult } from '@/api/base-model/page-model'
import type { ResponseModel } from '@/api/base-model/response-model'
import type {
  ProductCategoryRelationFormParam,
  ProductCategoryRelationPageParam,
  ProductCategoryRelationQueryParam,
  ProductCategoryRelationResult,
} from './model/product-category-relation-form-model'
import { getDownload, getRequest, postRequest } from '@/lib/http-request'

export const productCategoryRelationApi = {

  // 分页查询商品分类关联
  productCategoryRelationPage: (param: ProductCategoryRelationPageParam): Promise<ResponseModel<PageResult<ProductCategoryRelationResult>>> => {
    return postRequest<ResponseModel<PageResult<ProductCategoryRelationResult>>, ProductCategoryRelationQueryParam>('/productCategoryRelation/productCategoryRelationPage', param)
  },
  // 列表查询商品分类关联
  productCategoryRelationList: (param: ProductCategoryRelationQueryParam): Promise<ResponseModel<ProductCategoryRelationResult[]>> => {
    return postRequest<ResponseModel<ProductCategoryRelationResult[]>, ProductCategoryRelationQueryParam>('/productCategoryRelation/productCategoryRelationList', param)
  },
  // 获取商品分类关联详情
  productCategoryRelationDetail: (productCategoryRelationId: number): Promise<ResponseModel<ProductCategoryRelationResult>> => {
    return getRequest<ResponseModel<ProductCategoryRelationResult>>(`/productCategoryRelation/productCategoryRelationDetail/${productCategoryRelationId}`, {})
  },
  // 添加商品分类关联
  addProductCategoryRelation: (param: ProductCategoryRelationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductCategoryRelationFormParam>('/productCategoryRelation/addProductCategoryRelation', param)
  },
  // 更新商品分类关联
  updateProductCategoryRelation: (param: ProductCategoryRelationFormParam): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, ProductCategoryRelationFormParam>('/productCategoryRelation/updateProductCategoryRelation', param)
  },
  // 删除商品分类关联
  deleteProductCategoryRelation: (productCategoryRelationId: number): Promise<ResponseModel<string>> => {
    return getRequest<ResponseModel<string>>(`/productCategoryRelation/deleteProductCategoryRelation/${productCategoryRelationId}`, {})
  },
  // 批量删除商品分类关联
  batchDeleteProductCategoryRelation: (productCategoryRelationIdList: number[]): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, number[]>('/productCategoryRelation/batchDeleteProductCategoryRelation', productCategoryRelationIdList)
  },

  // 导入商品分类关联
  importProductCategoryRelation: (file: FormData): Promise<ResponseModel<string>> => {
    return postRequest<ResponseModel<string>, FormData>('/productCategoryRelation/importProductCategoryRelationExcel', file)
  },
  // 导出商品分类关联
  exportProductCategoryRelation: (param?: ProductCategoryRelationQueryParam): void => {
    return getDownload('/productCategoryRelation/exportProductCategoryRelationExcel', param || {})
  },
}

