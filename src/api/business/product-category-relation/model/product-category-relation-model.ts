import type { BaseModel } from '@/api/base-model/base-model'

/**
 * 商品分类关联类型定义
 */
export interface ProductCategoryRelationModel extends BaseModel {
  /**
   * 商品分类关联ID
   */
  id?: number

  /**
   * 商品ID | erp_product的id
   */
  productId?: number

  /**
   * 商品名称
   */
  productName?: string

  /**
   * 分类ID | t_category的id
   */
  categoryId?: number

  /**
   * 分类名称
   */
  categoryName?: string

}
