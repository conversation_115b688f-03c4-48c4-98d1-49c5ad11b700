import type { PageParam } from '@/api/base-model/page-model'
import type { ProductCategoryRelationModel } from '@/api/business/product-category-relation/model/product-category-relation-model'

/**
 * 商品分类关联查询参数
 */
export interface ProductCategoryRelationQueryParam extends ProductCategoryRelationModel {
  /**
   * 创建时间始
   */
  createTimeFrom?: string
  /**
   * 创建时间至
   */
  createTimeTo?: string
  /**
   * 更新时间始
   */
  updateTimeFrom?: string
  /**
   * 更新时间至
   */
  updateTimeTo?: string
}

/**
 * 商品分类关联分页查询参数
 */
export interface ProductCategoryRelationPageParam extends ProductCategoryRelationQueryParam {
  pageParam: PageParam
}

/**
 * 商品分类关联表单参数类型
 */
export interface ProductCategoryRelationFormParam extends ProductCategoryRelationModel {

}

/**
 * 商品分类关联查询结果类型
 */
export interface ProductCategoryRelationResult extends ProductCategoryRelationModel {

}
