import type { BaseModel } from '@/api/base-model/base-model'
import type { PageParam } from '@/api/base-model/page-model'

/**
 * 分类数据模型
 */

// 分类基础信息
export interface CategoryResult extends BaseModel {
  id?: number
  categoryName?: string
  categoryCode?: string
  parentId?: number
  level?: number
  sort?: number
  status?: string
  remark?: string
  children?: CategoryResult[]
}

// 分类表单参数
export interface CategoryFormParam extends BaseModel {
  id?: number
  categoryName?: string
  categoryCode?: string
  parentId?: number
  level?: number
  sort?: number
  status?: string
  remark?: string
}

// 分类分页查询参数
export interface CategoryPageParam {
  pageParam: PageParam
  categoryName?: string
  categoryCode?: string
  status?: string
}

// 分类树查询参数
export interface CategoryTreeParam {
  categoryName?: string
  status?: string
  parentId?: number
}
