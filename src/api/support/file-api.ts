/*
 * 文件上传
 */
import { getDownload, getRequest, postRequest } from '@/lib/axios'

// 文件夹类型
export enum FileFolder {
  DEFAULT = 0, // 默认
  COMMON = 1, // 通用
  NOTICE = 2, // 公告
  HELP_DOC = 3, // 帮助中心
  FEEDBACK = 4, // 意见反馈
  PRODUCT = 5, // 商品图片
}

// 接口参数和返回值类型定义
export interface FileUploadVO {
  fileKey: string
  fileUrl: string
  fileName: string
}

export interface FileQueryParam {
  pageNum: number
  pageSize: number
  searchWord?: string
  folder?: FileFolder
}

export const fileApi = {
  // 文件上传
  uploadUrl: '/support/file/upload',
  uploadFile: (param: FormData, folder: FileFolder) => {
    return postRequest(`/support/file/upload?folder=${folder}`, param)
  },

  /**
    分页查询
   */
  queryPage: (param: FileQueryParam) => {
    return postRequest('/support/file/queryPage', param)
  },
  /**
    获取文件URL：根据fileKey
   */
  getUrl: (fileKey: string) => {
    return getRequest(`/support/file/getFileUrl?fileKey=${fileKey}`)
  },

  /**
    下载文件流（根据fileKey）
   */
  downLoadFile: (fileKey: string) => {
    return getDownload('/support/file/downLoad', { fileKey })
  },
}
