import { postRequest } from '@/lib/axios'
import { message } from 'ant-design-vue'

// 文件夹枚举
export enum OssFolder {
  DEFAULT = 'default',
  PRODUCT = 'product',
  TEST_PRODUCT = 'test-product',
}

// 响应数据接口
export interface OssResponseData {
  fileName: string
  fileUrl: string
  fileSize: number
  fileType: string
  objectName: string
}

// 业务响应接口
export interface OssResponse {
  code: number
  success: boolean
  msg?: string
  data?: OssResponseData
}

export const ossApi = {
  /**
   * 通过后端服务上传文件到OSS
   * @param file 文件对象
   * @param folder 文件夹
   * @returns Promise<{fileUrl: string, fileName: string}>
   */
  async uploadFile(file: File, folder: OssFolder = OssFolder.DEFAULT): Promise<{ fileUrl: string, fileName: string }> {
    try {
      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', folder)

      // 调用后端接口上传文件
      const response = await postRequest('/support/file/uploadToOss', formData) as unknown as OssResponse

      if (response && response.success && response.data) {
        // 返回文件信息
        return {
          fileUrl: response.data.fileUrl,
          fileName: file.name,
        }
      }

      throw new Error(response.msg || '上传文件失败')
    }
    catch (error) {
      console.error('OSS上传错误:', error)
      message.error('文件上传到OSS失败')
      throw error
    }
  },
}
