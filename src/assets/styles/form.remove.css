/* @deprecated 需要删除的样式 */

/* 表单组件通用样式 */

/* 组件容器样式 */
.form-container {
    margin-bottom: 10px;
    padding: 0;
    background-color: #fff;
}

/* 表单行样式 */
.form-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

/* 表单行最后一个元素不需要底部间距 */
.form-row:last-child {
    margin-bottom: 0;
}

/* 表单标签样式 */
.form-label {
    width: 95px;
    min-width: 80px;
    font-weight: 500;
    color: #606266;
    flex-shrink: 0;
    white-space: nowrap;
    text-align: right;
    padding-right: 12px;
    box-sizing: border-box;
}

/* 表单内容容器样式 */
.form-content {
    flex: 1;
    min-width: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 表单内容列样式 */
.form-column {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

/* 输入框行样式 */
.input-row {
    display: flex;
    flex-direction: row;
    width: 100%;
    gap: 10px;
    margin-bottom: 0;
}

/* 当一行有两个元素时的样式 */
.form-row-half {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
    gap: 10px;
}

.form-row-half > * {
    flex: 1 1 calc(50% - 5px);
    min-width: 0;
}

/* 输入框样式 */
.form-input {
    flex: 1;
}

/* 自定义尺寸行样式 */
.custom-row {
    justify-content: space-between;
    gap: 10px;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    margin-left: 3px;
    white-space: nowrap;
    gap: 4px;
}

/* Element Plus 组件深度选择器样式 */
.form-container :deep(.el-input-number .el-input__inner),
.form-container :deep(.el-input .el-input__inner),
.form-container :deep(.el-select .el-input__inner),
.form-container :deep(.el-cascader .el-input__inner) {
    font-size: 13px;
    font-weight: 500;
}

/* 确保选择器宽度 */
.form-container :deep(.el-select),
.form-container :deep(.el-cascader) {
    width: 100%;
}

/* 级联选择器面板样式 */
.form-container :deep(.el-cascader-panel) {
    max-height: 300px;
}

.form-container :deep(.el-cascader-menu) {
    min-width: 200px;
}

/* 可点击元素样式 */
.clickable {
    cursor: pointer;
    color: #409EFF;
    text-decoration: underline;
}

/* 悬停信息样式 */
.hover-info {
    cursor: help;
    border-bottom: 1px dashed #909399;
}

h1 {
    text-align: center;
    padding: 10px;
}
