import type { App } from 'vue'
/*
 *  权限插件
 */
import { useUserStore } from '@/store/modules/system/user'

function privilege(value: string) {
  // 超级管理员
  if (useUserStore().administratorFlag) {
    return true
  }
  // 获取功能点权限
  const userPointsList = useUserStore().getPointList
  if (!userPointsList) {
    return false
  }
  return userPointsList && userPointsList.includes(value)
}

export default {
  install: (app: App): void => {
    app.config.globalProperties.$privilege = privilege
  },
}
