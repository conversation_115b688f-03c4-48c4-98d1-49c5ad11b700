<!--
  * 主应用页面
-->

<script setup lang="ts">
import { messages } from '@/i18n'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useSpinStore } from '@/store/modules/system/spin'
import { themeColors } from '@/theme/color.js'
import { theme } from 'ant-design-vue'

import dayjs from 'dayjs'

const antdLocale = computed(() => messages[useAppConfigStore().language].antdLocale)
const dayjsLocale = computed(() => messages[useAppConfigStore().language].dayjsLocale)
watch(dayjsLocale, () => {
  dayjs.locale(dayjsLocale.value)
})

// 全局loading
const spinStore = useSpinStore()
const spinning = computed(() => spinStore.loading)
// 是否紧凑
const compactFlag = computed(() => useAppConfigStore().compactFlag)
// 主题颜色
const colorIndex = computed(() => {
  return useAppConfigStore().colorIndex
})
// 圆角
const borderRadius = computed(() => {
  return useAppConfigStore().borderRadius
})
</script>

<template>
  <a-config-provider
    :locale="antdLocale"
    :theme="{
      algorithm: compactFlag ? theme.compactAlgorithm : theme.defaultAlgorithm,
      token: {
        colorPrimary: themeColors[colorIndex].primaryColor,
        colorLink: themeColors[colorIndex].primaryColor,
        colorLinkActive: themeColors[colorIndex].activeColor,
        colorLinkHover: themeColors[colorIndex].hoverColor,
        colorIcon: themeColors[colorIndex].primaryColor,
        borderRadius,
        fontSize: 14,
      },
      components: {
        Button: {
          colorLink: themeColors[colorIndex].primaryColor,
          colorLinkActive: themeColors[colorIndex].activeColor,
          colorLinkHover: themeColors[colorIndex].hoverColor,
        },
        // @ts-expect-error 不知道之前的作者为什么这么写，保留代码，忽略错误
        Icon: {
          colorIcon: themeColors[colorIndex].primaryColor,
        },
      },
    }"
  >
    <!-- 全局loading -->
    <a-spin :spinning="spinning" tip="稍等片刻，我在拼命加载中..." size="large">
      <!--- 路由 -->
      <RouterView />
    </a-spin>
  </a-config-provider>
</template>

<style lang="less">
@import '@/assets/styles/index.css';
</style>
