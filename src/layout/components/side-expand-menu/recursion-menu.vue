<!--
  * 递归菜单
-->
<script setup lang="ts">
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { router } from '@/router'
import { useUserStore } from '@/store/modules/system/user'
import { ref } from 'vue'
import menuEmitter from './side-expand-menu-mitt'
import SubMenu from './sub-menu.vue'

// 选中的顶级菜单
const topMenu = ref({})
menuEmitter.on('selectTopMenu', onSelectTopMenu)

// 监听选中顶级菜单事件
function onSelectTopMenu(selectedTopMenu) {
  topMenu.value = selectedTopMenu
  if (selectedTopMenu.children && selectedTopMenu.children.length > 0) {
    openKeys.value = _map(selectedTopMenu.children, 'menuId').map(e => e.toString())
  }
  else {
    openKeys.value = []
  }
  selectedKeys.value = []
}

// 展开的菜单
const selectedKeys = ref([])
const openKeys = ref([])

function updateSelectKeyAndOpenKey(parentList, currentSelectKey) {
  if (!parentList) {
    return
  }
  // 获取需要展开的menu key集合
  openKeys.value = _map(parentList, 'name')
  selectedKeys.value = [currentSelectKey]
}

// 页面跳转
function turnToPage(route) {
  useUserStore().deleteKeepAliveIncludes(route.menuId.toString())
  router.push({ name: route.menuId.toString() })
}

function goHome() {
  router.push({ name: HOME_PAGE_NAME })
}

defineExpose({ updateSelectKeyAndOpenKey })
</script>

<template>
  <div v-show="topMenu.children && topMenu.children.length > 0" class="recursion-container">
    <!-- 顶部顶级菜单名称 -->
    <div class="top-menu">
      <span class="ant-menu">{{ topMenu.menuName }}</span>
    </div>
    <!-- 次级菜单展示 -->
    <div class="bottom-menu">
      <a-menu :selected-keys="selectedKeys" :open-keys="openKeys" mode="inline">
        <template v-for="item in topMenu.children" :key="item.menuId">
          <template v-if="item.visibleFlag">
            <template v-if="$lodash.isEmpty(item.children)">
              <a-menu-item :key="item.menuId.toString()" @click="turnToPage(item)">
                <template v-if="item.icon" #icon>
                  <component :is="$antIcons[item.icon]" />
                </template>
                {{ item.menuName }}
              </a-menu-item>
            </template>
            <template v-else>
              <SubMenu :key="item.menuId" :menu-info="item" @turn-to-page="turnToPage" />
            </template>
          </template>
        </template>
      </a-menu>
    </div>
  </div>
</template>

<style scoped lang="less">
  .recursion-container {
    height: 100vh;
    background: #ffffff;
  }
  .bottom-menu{
    overflow: auto;
    display: flex;
    height: 90%;
    color: #515a6e;
  }
  .top-menu {
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    height: @header-user-height;
    font-size: 16px;
    color: #515a6e;
    border-bottom: 1px solid #f3f3f3;
    border-right: 1px solid #f3f3f3;
  }
</style>
