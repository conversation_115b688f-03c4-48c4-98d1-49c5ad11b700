<!--
  *  意见反馈提交弹窗
-->
<script setup lang="ts">
import { feedbackApi } from '@/api/support/feedback-api'
import { SmartLoading } from '@/components/base-old/smart-loading'
import Upload from '@/components/utils-old/file-upload/index.vue'
import { FILE_FOLDER_TYPE_ENUM } from '@/constants/support/file-const'
import { smartSentry } from '@/lib/smart-sentry'
import { message } from 'ant-design-vue'
import { reactive, ref } from 'vue'

defineExpose({
  show,
})

const visible = ref(false)

function show() {
  Object.assign(form, formDefault)
  console.log(form)
  visible.value = true
}

function hide() {
  visible.value = false
}

const formDefault = {
  feedbackContent: '',
  feedbackAttachment: '',
}
const form = reactive({ ...formDefault })

async function submit() {
  try {
    SmartLoading.show()
    if (!form.feedbackContent) {
      message.warn('请填写具体内容')
      return
    }
    await feedbackApi.addFeedback(form)
    message.success('提交成功')
    hide()
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  finally {
    SmartLoading.hide()
  }
}

function changeAttachment(fileList) {
  form.feedbackAttachment = fileList
}
</script>

<template>
  <a-modal :open="visible" title="意见反馈" :closable="false" :mask-closable="true">
    <a-form :label-col="{ span: 6 }">
      <a-form-item label="我要吐槽/建议：">
        <a-textarea v-model:value="form.feedbackContent" placeholder="请输入让您不满意的点，我们争取做到更好～" :rows="3" />
      </a-form-item>
      <a-form-item label="反馈图片：">
        <Upload
          accept=".jpg,.jpeg,.png,.gif"
          :max-upload-size="3"
          button-text="点击上传反馈图片"
          :default-file-list="form.feedbackAttachment || []"
          list-type="picture-card"
          :folder="FILE_FOLDER_TYPE_ENUM.FEEDBACK.value"
          @change="changeAttachment"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="hide">
        取消
      </a-button>
      <a-button type="primary" @click="submit">
        提交
      </a-button>
    </template>
  </a-modal>
</template>
