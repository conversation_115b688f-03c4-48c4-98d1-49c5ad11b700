<!--
  * 头部一整行
-->
<script setup lang="ts">
import { theme } from 'ant-design-vue'
import HeaderAvatar from './header-avatar.vue'
import HeaderMessage from './header-message.vue'
import HeaderSettingDrawer from './header-setting.vue'

// 设置
const headerSetting = ref()
function showSetting() {
  headerSetting.value.show()
}

// 帮助文档
// function showHelpDoc() {
//   useAppConfigStore().showHelpDoc()
// }

// const showHelpDocFlag = computed(() => {
//   return useAppConfigStore().helpDocFlag
// })

// const helpDocExpandFlag = computed(() => {
//   return useAppConfigStore().helpDocExpandFlag
// })

const { useToken } = theme
const { token } = useToken()
</script>

<template>
  <div class="inline-flex items-center gap-8px pr-12px">
    <div class="setting">
      <HeaderMessage />
      <!-- <a-button type="text" @click="showSetting" class="operate-icon">
        <template #icon><switcher-outlined /></template>
        i18n
      </a-button> -->
      <a-button type="text" class="operate-icon" @click="showSetting">
        <template #icon>
          <div>
            <SettingOutlined />
          </div>
        </template>
      </a-button>
    </div>
    <div class="user-space-item">
      <HeaderAvatar />
    </div>
    <HeaderSettingDrawer ref="headerSetting" />
  </div>
</template>

<style lang="less" scoped>
  .user-space-item {
    height: 100%;
    color: inherit;
    cursor: pointer;
    align-self: center;

    a {
      color: inherit;

      i {
        font-size: 16px;
      }
    }
  }

  .user-space-item:hover {
    color: v-bind('token.colorPrimary');
    background-color: @hover-bg-color !important;
  }

  .setting {
    height: @header-user-height;
    line-height: @header-user-height;
    vertical-align: middle;
    display: flex;
    align-items: center;
  }
</style>
