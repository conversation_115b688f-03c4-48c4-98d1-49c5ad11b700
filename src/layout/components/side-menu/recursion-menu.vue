<!--
  * 传统菜单-递归菜单
-->
<script setup lang="ts">
import { router } from '@/router/index'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useUserStore } from '@/store/modules/system/user'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import SubMenu from './sub-menu.vue'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
})

const theme = computed(() => useAppConfigStore().$state.sideMenuTheme)

const menuTree = computed(() => useUserStore().getMenuTree || [])

// 展开的菜单
const currentRoute = useRoute()
const selectedKeys = ref([])
const openKeys = ref([])

// 页面跳转
function turnToPage(menu) {
  useUserStore().deleteKeepAliveIncludes(menu.menuId.toString())
  router.push({ path: menu.path })
}

/**
 * SmartAdmin中 router的name 就是 后端存储menu的id
 * 所以此处可以直接监听路由，根据路由更新菜单的选中和展开
 */
function updateOpenKeysAndSelectKeys() {
  // 更新选中
  selectedKeys.value = [_toNumber(currentRoute.name)]

  /**
   * 更新展开（1、获取新展开的menu key集合；2、保留原有的openkeys，然后把新展开的与之合并）
   */
  // 获取需要展开的menu key集合
  const menuParentIdListMap = useUserStore().getMenuParentIdListMap
  const parentList = menuParentIdListMap.get(currentRoute.name) || []

  // 如果是折叠菜单的话，则不需要设置openkey
  if (!props.collapsed) {
    // 使用lodash的union函数，进行 去重合并两个数组
    const needOpenKeys = _map(parentList, 'name').map(Number)
    openKeys.value = _union(openKeys.value, needOpenKeys)
  }
}

watch(
  currentRoute,
  () => {
    updateOpenKeysAndSelectKeys()
  },
  {
    immediate: true,
  },
)

defineExpose({
  updateOpenKeysAndSelectKeys,
})
</script>

<template>
  <a-menu
    v-model:open-keys="openKeys"
    v-model:selected-keys="selectedKeys"
    class="smart-menu"
    mode="inline"
    :theme="theme"
    :inline-collapsed="collapsed"
  >
    <template v-for="item in menuTree" :key="item.menuId">
      <template v-if="item.visibleFlag && !item.disabledFlag">
        <template v-if="$lodash.isEmpty(item.children)">
          <a-menu-item :key="item.menuId" @click="turnToPage(item)">
            <template #icon>
              <component :is="$antIcons[item.icon]" />
            </template>
            {{ item.menuName }}
          </a-menu-item>
        </template>
        <template v-else>
          <SubMenu :key="item.menuId" :menu-info="item" @turn-to-page="turnToPage" />
        </template>
      </template>
    </template>
  </a-menu>
</template>

<style lang="less" scoped>
  .smart-menu {
    position: relative;
  }
</style>
