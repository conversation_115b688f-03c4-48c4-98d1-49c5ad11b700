<!--
  * 传统菜单-递归菜单
-->
<script setup lang="ts">
const props = defineProps({
  menuInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['turnToPage'])
function turnToPage(menu) {
  emits('turnToPage', menu)
}
</script>

<template>
  <a-sub-menu :key="menuInfo.menuId">
    <template #icon>
      <component :is="$antIcons[menuInfo.icon]" />
    </template>
    <template #title>
      {{ menuInfo.menuName }}
    </template>
    <template v-for="item in menuInfo.children" :key="item.menuId">
      <template v-if="item.visibleFlag && !item.disabledFlag">
        <template v-if="$lodash.isEmpty(item.children)">
          <a-menu-item :key="item.menuId" @click="turnToPage(item)">
            <template #icon>
              <component :is="$antIcons[item.icon]" />
            </template>
            {{ item.menuName }}
          </a-menu-item>
        </template>
        <template v-else>
          <SubMenu :key="item.menuId" :menu-info="item" @turn-to-page="turnToPage" />
        </template>
      </template>
    </template>
  </a-sub-menu>
</template>
