<!--
  * 标签页 入口，支持三种模式：默认、a-tabs, chrome-tabs
-->
<script setup lang="ts">
import { PAGE_TAG_ENUM } from '@/constants/layout-const.js'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { computed } from 'vue'
import AntdTab from './components/antd-tab.vue'
import ChromeTab from './components/chrome-tab.vue'
import DefaultTab from './components/default-tab.vue'

const pageTagStyle = computed(() => useAppConfigStore().$state.pageTagStyle)
</script>

<template>
  <div id="smartAdminPageTag">
    <DefaultTab v-if="pageTagStyle === PAGE_TAG_ENUM.DEFAULT.value" />
    <AntdTab v-if="pageTagStyle === PAGE_TAG_ENUM.ANTD.value" />
    <ChromeTab v-if="pageTagStyle === PAGE_TAG_ENUM.CHROME.value" />
  </div>
</template>
