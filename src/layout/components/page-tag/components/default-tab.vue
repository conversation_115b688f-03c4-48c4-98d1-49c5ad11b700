<!--
  * 标签页
-->
<script setup lang="ts">
import type { RouteRecordName } from 'vue-router'
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useUserStore } from '@/store/modules/system/user'
import { AppstoreOutlined, CloseOutlined, HomeOutlined } from '@ant-design/icons-vue'
import { theme } from 'ant-design-vue'
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 标签页 是否显示
const pageTagFlag = computed(() => useAppConfigStore().$state.pageTagFlag)

const router = useRouter()
const route = useRoute()
const tagNav = computed(() => _map(useUserStore().getTagNav || [], (tab: Recordable) => ({
  ...tab,
  label: tab.menuTitle,
  key: tab.menuName,
  closable: tab.menuName !== HOME_PAGE_NAME,
  menuQuery: tab.menuQuery,
  fromMenuName: tab.fromMenuName,
  fromMenuQuery: tab.fromMenuQuery,
})))
const selectedKey = ref<RouteRecordName | undefined>(route.name)

watch(
  () => route.name,
  (newValue) => {
    selectedKey.value = newValue
  },
  { immediate: true },
)

// 选择某个标签页
function selectTab(name: string) {
  if (selectedKey.value === name) {
    return
  }
  // 寻找tag
  const tag = tagNav.value.find(e => e.key === name)
  if (!tag) {
    router.push({ name: HOME_PAGE_NAME })
    return
  }
  router.push({ name, query: tag.menuQuery })
}

// 通过菜单关闭
function closeByMenu(closeAll: boolean) {
  const find = tagNav.value.find(e => e.key === selectedKey.value)
  if (!find || closeAll) {
    closeTag(null, true)
  }
  else {
    closeTag(find, true)
  }
}

// 直接关闭
interface TabItem {
  key: string
  menuQuery?: Record<string, unknown>
  fromMenuName?: string
  fromMenuQuery?: Record<string, unknown>
}

function closeTag(item: TabItem | null, closeAll: boolean) {
  // 关闭单个tag
  if (item && !closeAll) {
    let goName = HOME_PAGE_NAME
    let goQuery
    if (item.fromMenuName && item.fromMenuName !== item.key && tagNav.value.some(e => e.key === item.fromMenuName)) {
      goName = item.fromMenuName
      goQuery = item.fromMenuQuery
    }
    else {
      // 查询左侧tag
      const index = tagNav.value.findIndex(e => e.key === item.key)
      if (index > 0) {
        // 查询左侧tag
        const leftTagNav = tagNav.value[index - 1]
        goName = leftTagNav.key as string
        goQuery = leftTagNav.menuQuery
      }
    }
    router.push({ name: goName, query: goQuery })
  }
  else if (!item && closeAll) {
    // 关闭所有tag
    router.push({ name: HOME_PAGE_NAME })
  }
  // 关闭其他tag不做处理 直接调用closeTagNav
  useUserStore().closeTagNav(item ? item.key : null, closeAll)
}

const { useToken } = theme
const { token } = useToken()
</script>

<template>
  <!-- 标签页，共两部分：1、标签 ；2、标签操作区 -->
  <a-row v-show="pageTagFlag" class="smart-page-tag-container">
    <a-dropdown :trigger="['contextmenu']">
      <div class="smart-page-tag">
        <div class="custom-tabs">
          <div class="tabs">
            <div
              v-for="item in tagNav"
              :key="item.key"
              class="tabs-item" :class="[{ 'is-active': selectedKey === item.key }]"
              @click="selectTab(item.key as string)"
            >
              <span v-if="item.key === HOME_PAGE_NAME" class="tab-icon">
                <HomeOutlined />
              </span>
              <span class="tab-title">{{ item.label }}</span>
              <span
                v-if="item.key !== HOME_PAGE_NAME"
                class="tab-close"
                @click.stop="closeTag(item, false)"
              >
                <CloseOutlined />
              </span>
            </div>
          </div>
        </div>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="closeByMenu(false)">
            关闭其他
          </a-menu-item>
          <a-menu-item @click="closeByMenu(true)">
            关闭所有
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>

    <a-dropdown>
      <!-- 标签页操作区 -->
      <div class="smart-page-tag-operate">
        <div class="smart-page-tag-operate-icon">
          <AppstoreOutlined />
        </div>
      </div>
      <template #overlay>
        <a-menu>
          <a-menu-item @click="closeByMenu(false)">
            <div class="whitespace-nowrap">
              关闭其他
            </div>
          </a-menu-item>
          <a-menu-item @click="closeByMenu(true)">
            <div class="whitespace-nowrap">
              关闭所有
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </a-row>
</template>

<style scoped lang="less">
  @smart-page-tag-operate-width: 40px;
  @color-primary: v-bind('token.colorPrimary');
  @tab-background: #f5f7fa;
  @tab-active-background: v-bind('token.colorPrimary');
  @tab-hover-background: #e6f7ff;
  @tab-height: 32px;
  @page-tag-height: @tab-height;
  @tab-border-radius: v-bind('token.borderRadius');

  .smart-page-tag-container {
    border-bottom: 1px solid #eeeeee;
    position: relative;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }

  .smart-page-tag-operate {
    width: @smart-page-tag-operate-width;
    height: @tab-height;
    background-color: #ffffff;
    font-size: 16px;
    text-align: center;
    vertical-align: middle;
    line-height: @tab-height;
    padding-right: 10px;
    cursor: pointer;
    color: #606266;
    transition: color 0.2s;
    border-left: 1px solid #f0f0f0;

    &:hover {
      .smart-page-tag-operate-icon {
        transform: rotate(90deg);
        color: @color-primary;
      }
    }

    .smart-page-tag-operate-icon {
      width: 20px;
      height: 20px;
      transition: all 0.3s;
      transform-origin: 10px 16px;
    }
  }

  .smart-page-tag-operate:hover {
    color: @color-primary;
  }

  .smart-page-tag {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    min-height: @tab-height;
    padding-right: 10px;
    padding-left: 10px;
    user-select: none;
    background: #fff;
    width: calc(100% - @smart-page-tag-operate-width);
  }

  .custom-tabs {
    width: 100%;
    height: @tab-height;
    background-color: #fff;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: thin;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }

    .tabs {
      height: @tab-height;
      padding: 0 4px;
      background-color: #fff;
      display: flex;
      flex-wrap: nowrap;

      &-item {
        padding: 0 12px;
        height: @tab-height;
        line-height: @tab-height;
        border-radius: 8px 8px 0 0;
        background-color: @tab-background;
        margin: 0 2px;
        // max-width: 160px;
        // min-width: 80px;
        transition: all 0.25s;
        font-size: 13px;
        font-weight: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        position: relative;
        white-space: nowrap;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        // flex-shrink: 0;

        &.is-active {
          background-color: @tab-active-background;
          color: #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: @tab-active-background;
          }

          .tab-close {
            color: rgba(255, 255, 255, 0.85);

            &:hover {
              background-color: rgba(255, 255, 255, 0.3);
              color: #ffffff;
            }
          }
        }

        &:not(.is-active):hover {
          background-color: @tab-hover-background;
        }

        .tab-icon {
          margin-right: 4px;
          font-size: 12px;
          flex-shrink: 0;
        }

        .tab-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-weight: normal;
          padding: 0 4px;
        }

        .tab-close {
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          border-radius: 50%;
          transition: all 0.2s;
          margin-left: 4px;
          color: #666;
          background: transparent;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: @color-primary;
          }
        }
      }
    }
  }
</style>
