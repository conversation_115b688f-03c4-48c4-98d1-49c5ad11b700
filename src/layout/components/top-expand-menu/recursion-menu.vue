<!--
  * 递归菜单
-->
<script setup lang="ts">
import logoImg from '@/assets/images/logo/system-logo.png'
import { HOME_PAGE_NAME } from '@/constants/system/home-const'
import { router } from '@/router'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useUserStore } from '@/store/modules/system/user'
import { computed, ref } from 'vue'
import SubMenu from './sub-menu.vue'
import menuEmitter from './top-expand-menu-mitt'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false,
  },
})
const websiteName = computed(() => useAppConfigStore().websiteName)
const theme = computed(() => useAppConfigStore().$state.sideMenuTheme)

// 选中的顶级菜单
const topMenu = ref({})
menuEmitter.on('selectTopMenu', onSelectTopMenu)

// 监听选中顶级菜单事件
function onSelectTopMenu(selectedTopMenu) {
  topMenu.value = selectedTopMenu
  if (selectedTopMenu.children && selectedTopMenu.children.length > 0) {
    openKeys.value = _map(selectedTopMenu.children, 'menuId').map(e => e.toString())
  }
  else {
    openKeys.value = []
  }
  selectedKeys.value = []
}

// 展开的菜单
const selectedKeys = ref([])
const openKeys = ref([])

function updateSelectKeyAndOpenKey(parentList, currentSelectKey) {
  if (!parentList) {
    return
  }
  // 获取需要展开的menu key集合
  openKeys.value = _map(parentList, 'name')
  selectedKeys.value = [currentSelectKey]
}

// 页面跳转
function turnToPage(route) {
  useUserStore().deleteKeepAliveIncludes(route.menuId.toString())
  router.push({ name: route.menuId.toString() })
}

function onGoHome() {
  router.push({ name: HOME_PAGE_NAME })
}

defineExpose({ updateSelectKeyAndOpenKey })

const isLight = computed(() => useAppConfigStore().$state.sideMenuTheme === 'light')
const color = computed(() => {
  const isLight = useAppConfigStore().$state.sideMenuTheme === 'light'
  return {
    background: isLight ? '#FFFFFF' : '#001529',
  }
})
</script>

<template>
  <div v-show="topMenu.children && topMenu.children.length > 0" class="recursion-container">
    <!-- 顶部logo区域 -->
    <div v-if="!collapsed" class="logo" :style="sideMenuWidth" @click="onGoHome">
      <img class="logo-img" :src="logoImg">
      <div v-if="isLight" class="title smart-logo title-light">
        {{ websiteName }}
      </div>
      <div v-if="!isLight" class="title smart-logo title-dark">
        {{ websiteName }}
      </div>
    </div>
    <div v-if="collapsed" class="min-logo" @click="onGoHome">
      <img class="logo-img" :src="logoImg">
    </div>
    <!-- 次级菜单展示 -->
    <a-menu :selected-keys="selectedKeys" :theme="theme" :open-keys="openKeys" mode="inline">
      <template v-for="item in topMenu.children" :key="item.menuId">
        <template v-if="item.visibleFlag">
          <template v-if="$lodash.isEmpty(item.children)">
            <a-menu-item :key="item.menuId.toString()" @click="turnToPage(item)">
              <template v-if="item.icon" #icon>
                <component :is="$antIcons[item.icon]" />
              </template>
              {{ item.menuName }}
            </a-menu-item>
          </template>
          <template v-else>
            <SubMenu :key="item.menuId" :menu-info="item" @turn-to-page="turnToPage" />
          </template>
        </template>
      </template>
    </a-menu>
  </div>
</template>

<style scoped lang="less">
.recursion-container {
  height: 100%;
  background-color: v-bind('color.background');
}

  .min-logo {
    height: @header-user-height;
    line-height: @header-user-height;
    padding: 0px 15px 0px 15px;
    // background-color: v-bind('color.background');

    width: 80px;
    z-index: 21;
    display: flex;
    justify-content: center;
    align-items: center;
    .logo-img {
      width: 30px;
      height: 30px;
    }
  }
.top-menu {
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  height: @header-user-height;
  font-size: 16px;
  color: #515a6e;
  border-bottom: 1px solid #f3f3f3;
  border-right: 1px solid #f3f3f3;
}
.logo {
  height: @header-user-height;
  line-height: @header-user-height;
  padding: 0px 15px 0px 15px;
  width: 100%;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  .logo-img {
    width: 30px;
    height: 30px;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    overflow: hidden;
    word-wrap: break-word;
    white-space: nowrap;
    color: v-bind('theme === "light" ? "#001529" : "#ffffff"');
  }
}
</style>
