<!--
  * 顶部菜单-递归菜单
-->
<script setup lang="ts">
import { router } from '@/router/index'
import { useAppConfigStore } from '@/store/modules/system/app-config'
import { useUserStore } from '@/store/modules/system/user'
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import SubMenu from './sub-menu.vue'

const theme = computed(() => useAppConfigStore().$state.sideMenuTheme)

const menuTree = computed(() => useUserStore().getMenuTree || [])

// 展开的菜单
const currentRoute = useRoute()
const selectedKeys = ref([])
const openKeys = ref([])

// 页面跳转
function turnToPage(menu) {
  useUserStore().deleteKeepAliveIncludes(menu.menuId.toString())
  router.push({ path: menu.path })
}

/**
 * SmartAdmin中 router的name 就是 后端存储menu的id
 * 所以此处可以直接监听路由，根据路由更新菜单的选中和展开
 */
function updateSelectKeys() {
  // 更新选中
  selectedKeys.value = [_toNumber(currentRoute.name)]
}

watch(
  currentRoute,
  () => {
    updateSelectKeys()
  },
  {
    immediate: true,
  },
)

defineExpose({
  updateSelectKeys,
})
</script>

<template>
  <a-menu
    v-model:open-keys="openKeys"
    v-model:selected-keys="selectedKeys"
    class="smart-menu"
    mode="horizontal"
    :theme="theme"
  >
    <template v-for="item in menuTree" :key="item.menuId">
      <template v-if="item.visibleFlag && !item.disabledFlag">
        <template v-if="$lodash.isEmpty(item.children)">
          <a-menu-item :key="item.menuId" @click="turnToPage(item)">
            <template #icon>
              <component :is="$antIcons[item.icon]" />
            </template>
            {{ item.menuName }}
          </a-menu-item>
        </template>
        <template v-else>
          <SubMenu :key="item.menuId" :menu-info="item" @turn-to-page="turnToPage" />
        </template>
      </template>
    </template>
  </a-menu>
</template>

<style lang="less" scoped>
  .smart-menu {
    position: relative;
  }
</style>
