<script setup lang="ts">
import type { DictValueProps } from './type'
import { cacheGetDictValueList } from '@/utils/dict-util'

const props = withDefaults(defineProps<DictValueProps>(), {
  valueCode: '',
})

const dictName = ref('')

onMounted(async () => {
  if (!props.valueCode) {
    return
  }

  try {
    const result = await cacheGetDictValueList(props.keyCode)
    const item = _find(result, { valueCode: props.valueCode })
    if (item?.valueName) {
      dictName.value = item.valueName
    }
  }
  catch (error) {
    console.error('获取字典数据失败', error)
  }
})
</script>

<template>
  <span v-if="valueCode">{{ dictName || valueCode }}</span>
  <span v-else>-</span>
</template>
