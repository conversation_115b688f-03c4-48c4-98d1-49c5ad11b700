<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue'
import type { DictRadioProps } from './type'
import { dictApi } from '@/api/support/dict-api'

const {
  keyCode = undefined,
  modelValue = undefined,
  mode = 'default',
  disabled = false,
  loading = false,
  change,
} = defineProps<DictRadioProps>()

const emit = defineEmits(['update:modelValue'])

const dictValueList = ref<DictItem[]>([])

// 使用计算属性处理双向绑定
const currentValue = computed({
  get: () => modelValue,
  set: value => emit('update:modelValue', value),
})

// 处理变化事件
async function handleChange(e: RadioChangeEvent) {
  const value = e.target.value
  currentValue.value = value
  if (change) {
    await change(value)
  }
}

onMounted(async () => {
  if (!keyCode)
    return

  try {
    const res = await dictApi.valueList(keyCode as string)
    dictValueList.value = res.data as unknown as DictItem[]
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
})
</script>

<template>
  <div class="w-auto">
    <a-radio-group
      v-model:value="currentValue"
      :mode="mode"
      size="default"
      :disabled="disabled || loading"
      button-style="solid"
      @change="handleChange"
    >
      <!-- 隐藏的取消选中按钮 -->
      <a-radio-button
        :key="undefined"
        :value="undefined"
      >
        全部
      </a-radio-button>

      <a-radio-button
        v-for="item in dictValueList"
        :key="item.valueCode"
        :value="item.valueCode"
      >
        {{ item.valueName }}
      </a-radio-button>
    </a-radio-group>
  </div>
</template>
