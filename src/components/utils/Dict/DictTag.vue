<script setup lang="ts">
import type { DictTagProps } from './type'
import { cacheGetDictValueList } from '@/utils/dict-util'

const {
  keyCode = undefined,
  valueCode = undefined,
  color = 'default',
} = defineProps<DictTagProps>()

const dictName = ref('')

onMounted(async () => {
  try {
    const result = await cacheGetDictValueList(keyCode as string)
    const item = _find(result, { valueCode })

    if (item?.valueName) {
      dictName.value = item.valueName
    }
  }
  catch (error) {
    console.error('获取字典数据失败', error)
  }
})
</script>

<template>
  <a-tag v-if="dictName || valueCode" :color="color">
    {{ dictName || valueCode }}
  </a-tag>
  <span v-else>--</span>
</template>
