<!--
  *  表格列设置
-->

<script setup lang="ts">
import { tableColumnApi } from '@/api/support/table-column-api'
import { smartSentry } from '@/lib/smart-sentry'
import { useAppConfigStore } from '@/store/modules/system/app-config.js'
import { message } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import { mergeColumn } from './smart-table-column-merge'
import SmartTableColumnModal from './smart-table-column-modal.vue'

const props = defineProps({
  // 表格列数组
  modelValue: {
    type: Array,
    default: () => [],
  },
  // 刷新表格函数
  refresh: {
    type: Function,
    required: true,
  },
  // 表格id
  tableId: {
    type: Number,
    require: true,
  },
})

const emit = defineEmits(['update:modelValue'])

// 原始表格列数据（复制一份最原始的columns集合，以供后续各个地方使用）
let originalColumn = _cloneDeep(props.modelValue)

onMounted(buildUserTableColumns)

// 构建用户的数据列
async function buildUserTableColumns() {
  if (!props.tableId) {
    return
  }

  let userTableColumnArray = []
  try {
    const res = await tableColumnApi.getColumns(props.tableId)
    if (res.data) {
      try {
        userTableColumnArray = JSON.parse(res.data)
      }
      catch (e1) {
        smartSentry.captureError(e1)
      }
    }
  }
  catch (e) {
    smartSentry.captureError(e)
  }
  updateColumn(userTableColumnArray)
}

// ----------------- 全屏 -------------------
const fullScreenFlag = ref(false)

function onFullScreen() {
  if (fullScreenFlag.value) {
    // 退出全屏
    handleExitFullScreen()
    exitElementFullscreen(document.body)
  }
  else {
    fullScreenFlag.value = true
    useAppConfigStore().startFullScreen()
    launchElementFullScreen(document.body)
  }
}

// 处理退出全屏
function handleExitFullScreen() {
  fullScreenFlag.value = false
  useAppConfigStore().exitFullScreen()
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange) // Firefox
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange) // Chrome, Safari and Opera
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange) // Internet Explorer and Edge
}

// 判断各种浏览器 -全屏
function launchElementFullScreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen()
  }
  else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen()
  }
  else if (element.webkitRequestFullScreen) {
    element.webkitRequestFullScreen()
  }
  else if (element.msRequestFullscreen) {
    element.msRequestFullscreen()
  }
  else {
    message.error('当前浏览器不支持部分全屏！')
  }
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('mozfullscreenchange', handleFullscreenChange) // Firefox
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange) // Chrome, Safari and Opera
  document.addEventListener('MSFullscreenChange', handleFullscreenChange) // Internet Explorer and Edge
}

function handleFullscreenChange() {
  if (document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
    console.log('进入全屏模式')
  }
  else {
    console.log('退出全屏模式')
    handleExitFullScreen()
  }
}

// 判断各种浏览器 -退出全屏
function exitElementFullscreen(element) {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  }
  else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen()
  }
  else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen()
  }
  else if (document.msExitFullscreen) {
    document.msExitFullscreen()
  }
}

// ----------------- 弹窗 修改表格列 -------------------

const smartTableColumnModal = ref()
function showModal() {
  smartTableColumnModal.value.show(originalColumn, props.tableId)
}

// 将弹窗修改的列数据，赋值给原表格 列数组
function updateColumn(changeColumnArray) {
  // 合并列
  const newColumns = mergeColumn(_cloneDeep(originalColumn), changeColumnArray)
  emit(
    'update:modelValue',
    newColumns.filter(e => e.showFlag),
  )
}

// ========= 定义 watch 监听 ===============
watch(
  () => props.tableId,
  (e) => {
    if (e) {
      originalColumn = _cloneDeep(props.modelValue)
      buildUserTableColumns()
    }
  },
  { immediate: true },
)
</script>

<template>
  <span>
    <a-tooltip v-if="!fullScreenFlag" title="全屏">
      <a-button type="text" size="small" @click="onFullScreen">
        <template #icon><fullscreen-outlined /></template>
      </a-button>
    </a-tooltip>
    <a-tooltip v-if="fullScreenFlag" title="取消全屏">
      <a-button type="text" size="small" @click="onFullScreen">
        <template #icon><fullscreen-exit-outlined /></template>
      </a-button>
    </a-tooltip>
    <a-tooltip title="刷新">
      <a-button type="text" size="small" @click="props.refresh">
        <template #icon><redo-outlined /></template>
      </a-button>
    </a-tooltip>
    <a-tooltip title="列设置">
      <a-button type="text" size="small" @click="showModal">
        <template #icon><setting-outlined /></template>
      </a-button>
    </a-tooltip>
    <SmartTableColumnModal ref="smartTableColumnModal" @change="updateColumn" />
  </span>
</template>
