<!--
  * 字段 单选按钮组
-->
<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue'
import { getDictOptions } from '@/utils/dict-util'
import { computed, onMounted, ref, watch } from 'vue'

type OptionType = 'default' | 'button'
type ButtonStyle = 'outline' | 'solid'
type SizeType = 'large' | 'default' | 'small'

const props = withDefaults(defineProps<{
  value?: string | number
  dictCode: string
  optionType?: OptionType
  buttonStyle?: ButtonStyle
  disabled?: boolean
  size?: SizeType
  defaultFirst?: boolean
  padding?: number
}>(), {
  optionType: 'button',
  buttonStyle: 'solid',
  disabled: false,
  size: 'default',
  defaultFirst: false,
})

const emit = defineEmits(['update:value', 'change'])

const dictOptions = ref<{ label: string, value: string | number }[]>([])
const selectedValue = ref(props.value)

const radioStyle = computed(() => ({
  'padding-inline': `${props.padding}px`,
}))

// -------------------------- 初始化字典数据 --------------------------
const loading = ref(false)

// 获取字典数据
async function loadDictData() {
  if (!props.dictCode)
    return Promise.resolve()

  loading.value = true
  try {
    dictOptions.value = await getDictOptions(props.dictCode)

    if (props.defaultFirst && (!selectedValue.value || selectedValue.value === '') && dictOptions.value.length > 0) {
      const firstValue = dictOptions.value[0].value
      selectedValue.value = firstValue
      emit('update:value', firstValue)
      emit('change', firstValue)
    }
    return Promise.resolve(dictOptions.value)
  }
  catch (error) {
    console.error(`加载字典数据失败: ${props.dictCode}`, error)
    return Promise.reject(error)
  }
  finally {
    loading.value = false
  }
}

watch(() => props.dictCode, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    // 字典编码变化时，重新加载字典数据
    // 不清空当前选中值，而是在加载完成后处理
    // 这样可以避免选择一的闪烁
    const oldValue = selectedValue.value
    loadDictData().then(() => {
      // 如果字典选项中不包含当前值，则使用默认值
      if (oldValue && dictOptions.value.findIndex(opt => String(opt.value) === String(oldValue)) === -1) {
        if (props.defaultFirst && dictOptions.value.length > 0) {
          const firstValue = dictOptions.value[0].value
          selectedValue.value = firstValue
          emit('update:value', firstValue)
          emit('change', firstValue)
        }
        else {
          selectedValue.value = ''
          emit('update:value', '')
          emit('change', '')
        }
      }
    })
  }
})

onMounted(loadDictData)

watch(() => props.value, (newVal) => {
  selectedValue.value = newVal
})

function handleChange(e: RadioChangeEvent) {
  const value = e.target.value
  emit('update:value', value)
  emit('change', value)
}
</script>

<template>
  <div class="dict-radio">
    <a-spin :spinning="loading">
      <a-radio-group
        v-model:value="selectedValue"
        :button-style="buttonStyle"
        :disabled="disabled"
        :size="size"
        @change="handleChange"
      >
        <template v-if="dictOptions && dictOptions.length > 0">
          <component
            :is="optionType === 'button' ? 'a-radio-button' : 'a-radio'"
            v-for="item in dictOptions"
            :key="item.value"
            :value="item.value"
            :style="optionType === 'button' ? radioStyle : undefined"
            size="default"
          >
            {{ item.label }}
          </component>
        </template>
        <template v-else>
          <a-empty image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg" :image-style="{ height: '32px' }" description="无字典数据" />
        </template>
      </a-radio-group>
    </a-spin>
  </div>
</template>
