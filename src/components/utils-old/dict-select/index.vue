<script setup lang="ts">
import type { DictItem } from '@/utils/dict-util'
import type { SelectProps } from 'ant-design-vue'
import type { SizeType } from 'ant-design-vue/es/config-provider'
import { dictApi } from '@/api/support/dict-api'
import { onMounted, ref, watch } from 'vue'

const props = defineProps({
  keyCode: String,
  value: [String, Number],
  mode: {
    type: String as () => SelectProps['mode'],
    default: undefined,
  },
  width: {
    type: String,
    default: '200px',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  size: {
    type: String as () => SizeType,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value', 'change'])
// -------------------------- 查询 字典数据 --------------------------

const dictValueList = ref<DictItem[]>([])
async function queryDict() {
  if (!props.keyCode)
    return

  try {
    const res = await dictApi.valueList(props.keyCode)
    if (res?.data) {
      dictValueList.value = res.data as unknown as DictItem[]
    }
  }
  catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

onMounted(queryDict)

// -------------------------- 选中 相关、事件 --------------------------

const selectValue = ref(props.value)
watch(
  () => props.value,
  (value) => {
    selectValue.value = value
  },
)

function onChange(value: unknown) {
  if (!value) {
    // 当值为空时，根据不同场景返回不同的默认值
    // 在查询表单中通常需要返回undefined，在表单中通常需要返回空字符串或默认值
    const defaultEmptyValue = props.mode === 'multiple' || props.mode === 'tags' ? [] : undefined
    emit('update:value', defaultEmptyValue)
    emit('change', defaultEmptyValue)
    return
  }
  if (Array.isArray(value) && value.length === 0) {
    // 处理清空选择的情况，返回undefined而不是空数组
    const defaultEmptyValue = props.mode === 'multiple' || props.mode === 'tags' ? [] : undefined
    emit('update:value', defaultEmptyValue)
    emit('change', defaultEmptyValue)
    return
  }
  if (Array.isArray(value)) {
    emit('update:value', value)
    emit('change', value)
  }
  else {
    emit('update:value', value)
    emit('change', value)
  }
}
</script>

<template>
  <div class="w-full">
    <a-select
      v-model:value="selectValue"
      :placeholder="props.placeholder"
      :allow-clear="true"
      :size="size"
      :mode="mode"
      :disabled="disabled"
      @change="onChange"
    >
      <a-select-option v-for="item in dictValueList" :key="item.valueCode" :value="item.valueCode">
        {{ item.valueName }}
      </a-select-option>
    </a-select>
  </div>
</template>
