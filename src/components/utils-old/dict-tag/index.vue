<script setup lang="ts">
import { cacheGetDictValueList } from '@/utils/dict-util'

const props = defineProps<{
  keyCode: string
  valueCode: string
  color?: string
}>()

const dictName = ref('')
const tagColor = ref(props.color)

onMounted(async () => {
  try {
    const result = await cacheGetDictValueList(props.keyCode)
    const item = _find(result, { valueCode: props.valueCode })

    if (item?.valueName) {
      dictName.value = item.valueName
    }
  }
  catch (error) {
    console.error('获取字典数据失败', error)
  }
})
</script>

<template>
  <a-tag :color="tagColor">
    {{ dictName || valueCode }}
  </a-tag>
</template>

<style scoped lang="less"></style>
