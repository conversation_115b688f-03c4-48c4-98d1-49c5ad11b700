<!--
  * 表单分组标题组件
-->
<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    required: true,
    default: '表单标题',
  }
})
</script>

<template>
  <div class="title-form-container">
    <div class="title-form-header">
      <div class="title-text">{{ title }}</div>
    </div>
    <div class="title-form-content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.title-form-container {
  margin-bottom: 20px;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
}

.title-form-header {
  height: 40px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 16px;
  display: flex;
  align-items: center;
}

.title-text {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  font-size: 14px;
}

.title-form-content {
  padding: 16px;
}
</style> 