<script lang="ts" setup>
import type { Key } from 'ant-design-vue/es/table/interface'
import type { AxTableService } from '../ax-table/types'
import { inject } from 'vue'
import { CRUD_KEY } from '@/service/BaseCrudService'

interface AxTableExpandService extends AxTableService {
  // 内联表特有
  expandable?: object
  expandedRowKeys?: Key[]
  handleExpandedRow?: (expanded: boolean, record: Record<string, unknown>) => void
}

const service = inject<AxTableExpandService>(CRUD_KEY)!
if (!service) {
  throw new Error('AxTableExpandService is not injected')
}
</script>

<template>
  <a-table
    size="small"
    :data-source="service.getRawTableData()"
    :columns="service.columns"
    :row-key="service.rowKey"
    bordered
    :pagination="false"
    :loading="service.tableLoading"
    :scroll="service.scroll"
    :row-selection="{
      selectedRowKeys: service.selectedRowKeyList,
      onChange: service.onSelectChange,
    }"
    :expandable="service.expandable"
    :expanded-row-keys="service.expandedRowKeys"
    @expand="service.handleExpandedRow"
    @change="service.onChange"
  >
    <template v-for="name in Object.keys($slots)" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
    <template #expandedRowRender="slotProps">
      <slot name="expandedRowRender" v-bind="slotProps" />
    </template>
  </a-table>
</template>
