<!--
  * 枚举 多选框
-->
<template>
  <a-checkbox-group :style="`width: ${width}`" v-model:value="selectValue" :options="optionList" @change="handleChange" />
</template>

<script setup lang="ts">
  import { ref, watch, getCurrentInstance, onMounted } from 'vue';
  import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface';

  const props = defineProps({
    enumName: String,
    value: Array<CheckboxValueType>,
    width: {
      type: String,
      default: '200px',
    },
  });

  // ------------ 枚举数据 加载和构建 ------------

  const optionList = ref<Array<{value: CheckboxValueType, label: string}>>([]);
  function buildOptionList() {
    const internalInstance = getCurrentInstance(); // 有效  全局
    if (!internalInstance) return;
    
    const smartEnumPlugin = internalInstance.appContext.config.globalProperties.$smartEnumPlugin;
    const valueList = smartEnumPlugin.getValueDescList(props.enumName);
    optionList.value = valueList.map((e: any) => Object.assign({}, { value: e.value, label: e.desc }));
  }

  onMounted(buildOptionList);

  // ------------ 数据选中 事件及其相关 ------------

  const selectValue = ref<CheckboxValueType[]>(props.value || []);

  watch(
    () => props.value,
    (newValue) => {
      selectValue.value = newValue || [];
    }
  );

  const emit = defineEmits(['update:value', 'change']);
  function handleChange(value: CheckboxValueType[]) {
    emit('update:value', value);
    emit('change', value);
  }
</script>
