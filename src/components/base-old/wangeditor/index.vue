<!--
  * 编辑器
-->
<script setup lang="ts">
import { fileApi } from '@/api/support/file-api'
import { FILE_FOLDER_TYPE_ENUM } from '@/constants/support/file-const'
import { smartSentry } from '@/lib/smart-sentry'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { onBeforeUnmount, ref, shallowRef, watch } from 'vue'
import '@wangeditor/editor/dist/css/style.css'

let props = defineProps({
  modelValue: String,
  height: {
    type: Number,
    default: 500,
  },
})

// 获取编辑器实例html
const emit = defineEmits(['update:modelValue'])

// 菜单
const editorConfig = { MENU_CONF: {} }

// 上传
let customUpload = {
  async customUpload(file, insertFn) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      let res = await fileApi.uploadFile(formData, FILE_FOLDER_TYPE_ENUM.COMMON.value)
      let data = res.data
      insertFn(data.fileUrl)
    }
    catch (error) {
      smartSentry.captureError(error)
    }
  },
}
editorConfig.MENU_CONF.uploadImage = customUpload
editorConfig.MENU_CONF.uploadVideo = customUpload

// ----------------------- 以下是公用变量 emits props ----------------
const editorHtml = ref()
watch(
  () => props.modelValue,
  (nVal) => {
    editorHtml.value = nVal
  },
  {
    immediate: true,
    deep: true,
  },
)

const editorRef = shallowRef()
function handleCreated(editor) {
  editorRef.value = editor
}
function handleChange(editor) {
  emit('update:modelValue', editorHtml.value)
}

function getHtml() {
  const htmlContent = editorRef.value.getHtml()
  return htmlContent === '<p><br></p>' ? '' : htmlContent
}
function getText() {
  return editorRef.value.getText()
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null)
    return
  editor.destroy()
})

defineExpose({
  editorRef,
  getHtml,
  getText,
})
</script>

<template>
  <div style="border: 1px solid #ccc">
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" />
    <Editor
      v-model="editorHtml"
      style="overflow-y: hidden"
      :style="{ height: `${height}px` }"
      :default-config="editorConfig"
      @on-created="handleCreated"
      @on-change="handleChange"
    />
  </div>
</template>

<style scoped>
  .w-e-full-screen-container {
    z-index: 9999 !important;
  }
</style>
