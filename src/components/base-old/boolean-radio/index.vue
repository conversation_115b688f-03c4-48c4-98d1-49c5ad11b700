<script setup lang="ts">
import type { RadioChangeEvent } from 'ant-design-vue'

interface Props {
  modelValue: boolean | undefined
  trueText?: string
  falseText?: string
  disabled?: boolean
  size?: 'small' | 'large' | 'default'
  padding?: string
}

const props = withDefaults(defineProps<Props>(), {
  trueText: '是',
  falseText: '否',
  disabled: false,
  size: 'default',
  padding: '15',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean | undefined]
}>()

const innerValue = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function handleChange(e: RadioChangeEvent) {
  emit('update:modelValue', e.target.value)
}
</script>

<template>
  <a-radio-group
    v-model:value="innerValue"
    button-style="solid"
    :disabled="disabled" :size="size"
    @change="handleChange"
  >
    <a-radio-button :style="{ paddingInline: `${padding}px` }" :value="true">
      {{ trueText }}
    </a-radio-button>
    <a-radio-button :style="{ paddingInline: `${padding}px` }" :value="false">
      {{ falseText }}
    </a-radio-button>
  </a-radio-group>
</template>

<style scoped>
.ant-radio-group {
  display: inline-flex;
}
</style>
