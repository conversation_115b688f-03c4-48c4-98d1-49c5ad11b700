<!--
  *  布尔 树形选择组件
-->
<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  value: Number,
  width: {
    type: Number,
    default: 100,
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  size: {
    type: String,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value', 'change'])

function convertBoolean2number(value) {
  let result = null
  if (_isNaN(value) || _isNull(value) || _isUndefined(value)) {
    result = null
  }
  else {
    result = value ? 1 : 0
  }
  return result
}
// 箭头value变化
const selectValue = ref(convertBoolean2number(props.value))
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = convertBoolean2number(newValue)
  },
)

function handleChange(value) {
  console.log('boolean enum select', value)
  let booleanResult = null
  if (!_isUndefined(value)) {
    booleanResult = value === 1
  }
  emit('update:value', booleanResult)
  emit('change', booleanResult)
}
</script>

<template>
  <a-select
    v-model:value="selectValue"
    :style="`width: ${width}px`"
    :placeholder="placeholder"
    :show-search="true"
    :allow-clear="true"
    :size="size"
    @change="handleChange"
  >
    <a-select-option v-for="item in $smartEnumPlugin.getValueDescList('FLAG_NUMBER_ENUM')" :key="item.value" :value="item.value">
      {{ item.desc }}
    </a-select-option>
  </a-select>
</template>
