<!--
  * 地区选择框
-->

<script setup lang="ts">
import type { CascaderProps, ShowSearchType } from 'ant-design-vue/es/cascader'
import type { SizeType } from 'ant-design-vue/es/config-provider'
import { ref, toRaw, watch } from 'vue'
import { PROVINCE_CITY } from './province-city'
import { PROVINCE_CITY_DISTRICT } from './province-city-district'

// 自定义接口，用于类型转换
interface AreaSelectedItem {
  value: string | number
  label: string
  [key: string]: unknown
}

const props = defineProps({
  type: {
    type: String,
    default: '',
  },
  value: {
    type: [Number, Array],
    default: () => [],
  },
  width: {
    type: String,
    default: '200px',
  },
  placeholder: {
    type: String,
    default: '请选择地区',
  },
  size: {
    type: String as () => SizeType,
    default: 'default',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:value', 'change'])

// ============ 组件属性 ============

const TYPE_PROVINCE_CITY_DISTRICT = 'province_city_district'

// ============ 组件业务 ============
const areaOptionData = props.type === TYPE_PROVINCE_CITY_DISTRICT ? PROVINCE_CITY_DISTRICT : PROVINCE_CITY

// 绑定地区数据
const areaValue = ref<(string | number)[]>([])
// 监听value变化
watch(
  () => props.value,
  (newValue) => {
    if (newValue && Array.isArray(newValue)) {
      const array: (string | number)[] = []
      for (let index = 0; index < 3; index++) {
        const item = newValue[index] as AreaSelectedItem | undefined
        if (item && 'value' in item) {
          array.push(item.value)
        }
      }
      areaValue.value = array
    }
    else {
      areaValue.value = []
    }
  },
)

// 处理选择变化，使用 CascaderProps 的 onChange 类型
const handleChange: CascaderProps['onChange'] = (value, selectedOptions) => {
  emit('update:value', toRaw(selectedOptions))
  emit('change', value, toRaw(selectedOptions))
}

// 过滤函数，满足 ShowSearchType 类型需求
const filterFunction: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option =>
    typeof option.label === 'string' && option.label.toLowerCase().includes(inputValue.toLowerCase()),
  )
}
</script>

<template>
  <a-cascader
    v-model:value="areaValue"
    :style="`width:${width}`"
    :show-search="{ filter: filterFunction }"
    :options="areaOptionData"
    :placeholder="placeholder"
    :size="size"
    @change="handleChange"
  />
</template>
