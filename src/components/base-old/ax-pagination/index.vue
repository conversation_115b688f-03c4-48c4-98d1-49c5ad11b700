<script lang="ts" setup>
import type { PageParam } from '@/api/base-model/page-model'
import { PAGE_SIZE_OPTIONS } from '@/constants/common-const'
import { CRUD_KEY } from '@/service/BaseCrudService'
/**
 * 分页组件所需的最小服务接口
 * 仅包含分页组件所需的属性和方法
 */
interface PaginationComponentService {
  // 分页参数
  pageParam: PageParam
  // 总条数
  total: number
  // 页码变化处理函数
  onPageChange: (page: number, pageSize?: number) => void
}
const service = inject<PaginationComponentService>(CRUD_KEY)!
if (!service) {
  throw new Error('PaginationComponentService is not injected')
}
</script>

<template>
  <div class="ax-pagination">
    <a-pagination
      :current="service.pageParam.pageNum"
      :page-size="service.pageParam.pageSize"
      :total="service.total"
      :page-size-options="PAGE_SIZE_OPTIONS"
      :show-quick-jumper="true"
      :show-size-changer="true"
      :show-less-items="true"
      :show-total="(total) => `共${total}条`"
      @change="service.onPageChange"
    />
  </div>
</template>

<style scoped>
.ax-pagination {
  padding: 16px 0;
  text-align: right;
}
</style>
