<!--
  * 详情描述项组件
-->
<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  label: {
    type: String,
    required: true,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  bordered: {
    type: Boolean,
    default: false,
  },
  span: {
    type: Number,
    default: 1,
  },
})

// 计算要显示的内容
const displayContent = computed(() => {
  if (props.content === null || props.content === undefined || props.content === '') {
    return '--'
  }
  return props.content
})
</script>

<template>
  <div class="ax-description-item" :class="{ bordered }">
    <div class="label">
      {{ label }}
    </div>
    <div class="content">
      <slot>{{ displayContent }}</slot>
    </div>
  </div>
</template>

<style scoped>
.ax-description-item {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.ax-description-item.bordered {
  border-bottom: 1px solid #f0f0f0;
}

.label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  line-height: 1.5;
  padding-bottom: 4px;
}

.content {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  line-height: 1.5;
  word-break: break-word;
}
</style>
