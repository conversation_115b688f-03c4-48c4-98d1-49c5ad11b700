<script lang="ts" setup>
import type { Excel } from '@/service/interface/CrudInterface'
import { CRUD_KEY } from '@/service/BaseCrudService'
import {
  CheckCircleOutlined,
  DownOutlined,
  ImportOutlined,
  UploadOutlined,
} from '@ant-design/icons-vue'
import { inject } from 'vue'

// 获取服务实例
const service = inject<Excel>(CRUD_KEY)!
if (!service) {
  throw new Error('ImportExcelService is not injected')
}

// 计算是否有文件上传
</script>

<template>
  <a-modal
    :open="service.importModalOpen"
    width="300px"
    title="导入数据"
    :confirm-loading="service.importConfirmLoading"
    :get-container="false"
    @ok="service.onImport"
    @cancel="service.setImportModalOpen(false)"
  >
    <div style="text-align: center; width: 250px; margin: 0 auto">
      <div style="margin-bottom: 16px;">
        <a-button type="primary" ghost @click="service.downloadTemplate">
          <template #icon>
            <DownOutlined />
          </template>
          第一步：下载模板
        </a-button>
      </div>
      <a-upload
        name="file"
        :multiple="false"
        :show-upload-list="false"
        action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        accept=".xls,.xlsx"
        :before-upload="service.beforeUpload"
        @change="service.handleUploadChange"
      >
        <a-button :type="service.hasUploadFile ? 'default' : 'primary'">
          <template #icon>
            <UploadOutlined />
          </template>
          第二步：选择文件
        </a-button>
      </a-upload>

      <div v-if="service.hasUploadFile" style="margin: 8px 0; color: #1890ff;">
        <CheckCircleOutlined /> {{ service.uploadFile?.name }}
      </div>

      <div style="margin-top: 16px;">
        <a-button
          type="primary"
          :disabled="!service.hasUploadFile"
          @click="service.onImport"
        >
          <template #icon>
            <ImportOutlined />
          </template>
          第三步：开始导入
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<style scoped>
.a-upload {
  margin-bottom: 16px;
  display: inline-block;
}
</style>
