<script setup lang="ts">
import type { BoxResult } from '@/api/business/box/model/box-form-model'
import { boxApi } from '@/api/business/box/box-api'
import { injectBoxSelectService } from '@/components/business-old/ax-box-select/service'
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { onMounted, ref } from 'vue'
import BoxFormModal from './box-form-modal.vue'
import { BoxFormService } from './box-form-service'

// 创建服务实例
const boxService = injectBoxSelectService()
// 使用静态方法创建并提供 BoxFormService 实例
const boxFormService = BoxFormService.createAndProvide()

// 内部配置 - 不再通过props暴露
const config = {
  boxTypeCode: undefined,
  isActive: true, // 默认只显示活跃状态的纸箱
  disabled: false,
  placeholder: '请选择纸箱规格',
  allowClear: false,
  showSearch: true,
  defaultFirst: true, // 默认选择第一个
  showEditButton: true, // 默认显示编辑按钮
  showAddButton: true, // 默认显示新增按钮
  showDeleteButton: true, // 默认显示删除按钮
}

// 内部状态
const loading = ref(false)
const isAddCustom = ref(false)
const boxOptions = ref<BoxResult[]>([])

// 获取纸箱列表
async function fetchBoxList() {
  try {
    loading.value = true
    const param = {
      boxTypeCode: config.boxTypeCode,
      isActive: config.isActive,
    }

    const res = await boxApi.boxList(param)
    if (res.success && res.data) {
      boxOptions.value = res.data

      // 如果启用默认选择第一个选项，且当前未选择任何值
      if (config.defaultFirst && !boxService.selectedBox && boxOptions.value.length > 0) {
        const firstBox = boxOptions.value[0]

        // --- 修改点：直接使用列表中的对象，不再获取详情 ---
        boxService.selectedBox = firstBox
        // --- 移除获取详情的逻辑 ---
      }
    }
  }
  catch (error) {
    message.error('获取纸箱列表失败')
    console.error('获取纸箱列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 选项显示文本
function getOptionLabel(item: BoxResult) {
  return `${item.boxTypeName || '未命名'} (${item.boxTypeCode || '无编码'})`
}

// 处理选择变化的函数 (当绑定 ID 时) - 简化版 (假设 ID 必为 number)
function handleSelectChange(value: unknown) {
  let numericId: number | undefined

  if (value === undefined || value === null) {
    numericId = undefined // 清空
  }
  else if (typeof value === 'number') {
    numericId = value // 直接是数字
  }
  else {
    // 其他所有未预期类型，都视为无效
    console.error('Unexpected value type from @update:value (expected number, null or undefined):', value)
    numericId = undefined // 无效类型，视为清空
  }

  // 根据解析出的 numericId 更新状态
  if (numericId === undefined) {
    boxService.selectedBox = undefined
  }
  else {
    const selectedItem = boxOptions.value.find(item => item.id === numericId)
    boxService.selectedBox = selectedItem ? { ...selectedItem } : undefined
  }

  // 触发 onChange 钩子
  boxService.onChange()
}

// 处理新增按钮点击 - 调用服务方法
function handleAdd() {
  isAddCustom.value = true
  boxFormService.openAddForm()
}

// 处理删除按钮点击
function handleDelete(e: Event, boxId: number) {
  e.stopPropagation() // 阻止事件冒泡
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除该纸箱规格吗？删除后不可恢复。',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await boxApi.deleteBox(boxId)
        if (res.success) {
          message.success('删除成功')
          // 如果删除的是当前选中的纸箱，清空选择
          if (boxService.selectedBox?.id === boxId) {
            boxService.selectedBox = undefined
          }
          // 重新加载列表
          fetchBoxList()
        }
        else {
          message.error(res.msg || '删除失败')
        }
      }
      catch (error) {
        console.error('删除纸箱失败', error)
        message.error('删除失败')
      }
    },
  })
}

// 在组件挂载后初始化数据
onMounted(() => {
  fetchBoxList()
})
</script>

<template>
  <div class="box-select-wrapper" style="position: relative; display: inline-block; width: 100%">
    <a-select
      :value="isAddCustom ? 'empty-option' : boxService.selectedBox?.id"
      :loading="loading"
      :disabled="config.disabled"
      :placeholder="config.placeholder"
      :allow-clear="config.allowClear"
      :show-search="config.showSearch"
      :filter-option="true"
      style="width: 100%"
      :dropdown-style="{ minWidth: '200px' }"
      @change="handleSelectChange"
    >
      <!-- 自定义新增选项 -->
      <a-select-option v-if="config.showAddButton" key="add-option" class="custom-add-option">
        <div class="add-option-content" @click="handleAdd">
          <PlusOutlined /> 自定义 (custom)
        </div>
      </a-select-option>

      <a-select-option v-if="isAddCustom" key="empty-option" class="custom-empty-option">
        <span>--</span>
      </a-select-option>

      <!-- 动态选项: value 绑定 item.id -->
      <a-select-option v-for="item in boxOptions" :key="item.id" :value="item.id">
        <div class="option-with-actions">
          <span>{{ getOptionLabel(item) }}</span>
          <div class="option-buttons">
            <a-button
              v-if="config.showEditButton"
              type="link"
              class="edit-button"
              @click.stop="boxFormService.openEditForm(item)"
            >
              <EditOutlined />
            </a-button>
            <a-button
              v-if="config.showDeleteButton"
              type="link"
              class="delete-button"
              @click.stop="handleDelete($event, item.id as number)"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </div>
      </a-select-option>

      <template #notFoundContent>
        <span v-if="loading">加载中...</span>
        <span v-else>未找到符合条件的纸箱规格</span>
      </template>
    </a-select>

    <!-- 编辑/新增弹窗: 使用重构后的 Modal，传递刷新回调 -->
    <BoxFormModal
      :refresh-callback="fetchBoxList"
      @update:add="isAddCustom = $event"
    />
  </div>
</template>

<style scoped>
.box-select-wrapper {
  position: relative;
}

.add-button,
.edit-button,
.delete-button {
  line-height: 1;
  padding: 0 4px;
  font-size: 14px;
}

.add-option-content {
  cursor: pointer;
  padding: 4px 0;
  color: #666;
}

.add-option-content:hover {
  color: #1890ff;
}

.option-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.option-buttons {
  display: flex;
  gap: 4px;
}

/* 修复问题：确保选择器中不显示按钮 */
:deep(.ant-select-selection-item .option-buttons) {
  display: none;
}

.delete-button {
  color: #ff4d4f;
}

.delete-button:hover {
  color: #ff7875;
}

:deep(.custom-add-option) {
  border-bottom: 1px solid #f0f0f0;
}
</style>
