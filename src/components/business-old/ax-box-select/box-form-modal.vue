<script setup lang="ts">
import { ref } from 'vue' // 导入必要的 Vue 函数
import { injectBoxFormService } from './box-form-service' // 导入服务及注入辅助函数

// 定义 props
const props = defineProps<{
  refreshCallback?: () => void | Promise<void>
  isAddCustom?: boolean
}>()

const emit = defineEmits<{
  'update:add': [value: boolean]
}>()

// 使用辅助函数注入表单服务
const boxFormService = injectBoxFormService()

// 表单实例引用
const formRef = ref()

// 处理 Modal 确认事件
async function handleOk() {
  if (!boxFormService)
    return // 为 TypeScript 添加守卫
  try {
    await formRef.value?.validate()
    await boxFormService.submitForm(props.refreshCallback)
  }
  catch (validationError) {
    console.error('表单验证失败或提交错误:', validationError)
  }
}

function handleCancel() {
  emit('update:add', false)
  boxFormService.closeForm()
}
</script>

<template>
  <a-modal
    v-if="boxFormService"
    :title="boxFormService.formTitle"
    :open="boxFormService.formOpen"
    :confirm-loading="boxFormService.submitLoading"
    :mask-closable="false"
    width="800px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-spin :spinning="boxFormService.formLoading">
      <a-form
        ref="formRef"
        :model="boxFormService.formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        style="padding-top: 20px"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              label="箱型编码"
              name="boxTypeCode"
              :rules="[{ required: true, message: '请输入箱型编码' }]"
            >
              <a-input v-model:value="boxFormService.formData.boxTypeCode" placeholder="请输入箱型编码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="箱型名称"
              name="boxTypeName"
              :rules="[{ required: true, message: '请输入箱型名称' }]"
            >
              <a-input v-model:value="boxFormService.formData.boxTypeName" placeholder="请输入箱型名称" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="排序" name="sort">
              <a-input-number v-model:value="boxFormService.formData.sort" placeholder="请输入排序" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态" name="isActive">
              <a-switch v-model:checked="boxFormService.formData.isActive" checked-children="启用" un-checked-children="禁用" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="描述" name="boxTypeDesc" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-textarea v-model:value="boxFormService.formData.boxTypeDesc" placeholder="请输入描述" :rows="2" />
        </a-form-item>

        <a-divider>计算配置</a-divider>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="需要模切" name="needDieCutting">
              <a-switch v-model:checked="boxFormService.formData.boxConfig.needDieCutting" checked-children="是" un-checked-children="否" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="需要印刷版" name="needPrintingPlate">
              <a-switch v-model:checked="boxFormService.formData.boxConfig.needPrintingPlate" checked-children="是" un-checked-children="否" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="门幅公式" name="doorWidthFormula" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-input v-model:value="boxFormService.formData.boxConfig.doorWidthFormula" placeholder="请输入门幅计算公式，例如 (长+高)*2+接口" />
        </a-form-item>
        <a-form-item label="总长公式" name="totalLengthFormula" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-input v-model:value="boxFormService.formData.boxConfig.totalLengthFormula" placeholder="请输入总长计算公式，例如 (宽+高)*2+舌头长度" />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.form-section {
  margin-bottom: 12px;
}

.section-title {
  font-size: 14px;
  color: #555;
  font-weight: 500;
  margin-bottom: 12px;
  padding-left: 8px;
  line-height: 16px;
}

.form-row {
  display: flex;
}

.form-item {
  flex: 1;
  margin: 0 5px;
}

:deep(.ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-form-item-control-input) {
  min-height: unset;
}

.switch-item {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.switch-item .item-label {
  margin-right: 8px;
  margin-bottom: 0;
}

.item-label {
  font-size: 14px;
  margin-bottom: 4px;
  color: rgba(0, 0, 0, 0.85);
  display: block;
}

.required::before {
  content: "*";
  color: #ff4d4f;
  margin-right: 4px;
}

:deep(.ant-switch) {
  min-width: 40px;
}
</style>
