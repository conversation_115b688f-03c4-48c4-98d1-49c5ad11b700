<script lang="ts" setup>
import type { EntityParamTypeResult } from '@/api/business/entity-param-type/model/entity-param-type-form-model'
import type { FormInstance } from 'ant-design-vue/es/form'
import type { SelectValue } from 'ant-design-vue/es/select'
import { entityParamTypeApi } from '@/api/business/entity-param-type/entity-param-type-api'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { inject, onMounted, ref } from 'vue'

/**
 * 参数类型选择器服务接口
 */
interface EntityParamTypeSelectService {
  // 选中的值，可直接修改
  formData: { paramTypeId?: number | undefined }
}

// 从父组件注入服务对象
const service = inject<EntityParamTypeSelectService>('crudService')
if (!service) {
  throw new Error('EntityParamTypeSelectService is not injected')
}

// 参数类型列表
const paramTypeList = ref<EntityParamTypeResult[]>([])
// 是否正在加载
const loading = ref(false)

// 参数类型快速创建表单
const showParamTypeModal = ref(false)
const paramTypeForm = ref({
  typeCode: '',
  typeName: '',
  dataType: 'STRING',
  isRequired: false,
  sort: 0,
  searchable: false,
})
const paramTypeFormRef = ref<FormInstance>()
const paramTypeFormLoading = ref(false)

// 参数类型表单验证规则
const paramTypeRules = {
  typeCode: [{ required: true, message: '请输入参数类型编码' }],
  typeName: [{ required: true, message: '请输入参数类型名称' }],
  dataType: [{ required: true, message: '请选择数据类型' }],
}

// 加载参数类型
async function loadParamTypes() {
  try {
    loading.value = true
    const res = await entityParamTypeApi.entityParamTypeList({})
    if (res.data) {
      paramTypeList.value = res.data
    }
  }
  catch (error) {
    console.error('加载参数类型失败', error)
  }
  finally {
    loading.value = false
  }
}

// 快速创建参数类型
async function handleAddParamType() {
  try {
    if (paramTypeFormRef.value) {
      await paramTypeFormRef.value.validate()
      paramTypeFormLoading.value = true

      // 调用创建参数类型API
      const res = await entityParamTypeApi.addEntityParamType({
        typeCode: paramTypeForm.value.typeCode,
        typeName: paramTypeForm.value.typeName,
        dataType: paramTypeForm.value.dataType,
        isRequired: paramTypeForm.value.isRequired,
        sort: paramTypeForm.value.sort,
        searchable: paramTypeForm.value.searchable,
      })

      if (res.success) {
        message.success('创建参数类型成功')
        // 重新加载参数类型列表
        await loadParamTypes()
        // 关闭对话框
        showParamTypeModal.value = false
        // 重置表单
        paramTypeForm.value = {
          typeCode: '',
          typeName: '',
          dataType: 'STRING',
          isRequired: false,
          sort: 0,
          searchable: false,
        }

        // 如果创建成功，自动选中新创建的参数类型
        if (res.data && service) {
          // 直接修改注入的响应式对象
          service.formData.paramTypeId = Number(res.data)
        }
      }
    }
  }
  catch (error) {
    console.error('创建参数类型失败', error)
  }
  finally {
    paramTypeFormLoading.value = false
  }
}

// 选中值变化事件，直接修改注入的响应式对象
function handleChange(value: SelectValue) {
  if (service) {
    service.formData.paramTypeId = value as number | undefined
  }
}

// 处理删除参数类型
async function handleDeleteParamType(item: EntityParamTypeResult, e: Event) {
  // 阻止事件冒泡，避免触发选择操作
  e.stopPropagation()

  // 确认删除
  Modal.confirm({
    title: '确定删除',
    content: `确定要删除参数类型"${item.typeName}"吗？`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        if (item.id) {
          const res = await entityParamTypeApi.deleteEntityParamType(item.id)
          if (res.success) {
            message.success('删除成功')
            // 重新加载列表
            await loadParamTypes()
            // 如果当前选中项被删除，则清空选择
            if (service?.formData.paramTypeId === item.id) {
              service.formData.paramTypeId = undefined
            }
          }
        }
      }
      catch (error) {
        console.error('删除参数类型失败', error)
        message.error('删除参数类型失败')
      }
    },
  })
}

// 初始加载
onMounted(() => {
  loadParamTypes()
})
</script>

<template>
  <div class="ax-param-type-select">
    <div class="select-container">
      <a-select
        :value="service?.formData.paramTypeId"
        placeholder="请选择参数类型"
        :style="{ width: '100%' }"
        :loading="loading"
        :allow-clear="true"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        @change="handleChange"
      >
        <a-select-option v-for="item in paramTypeList" :key="item.id" :value="item.id">
          <div class="param-type-option">
            <span class="option-text">{{ item.typeName }} ({{ item.typeCode }})</span>
            <a-button
              class="delete-button"
              type="link"
              size="small"
              danger
              @click="(e) => handleDeleteParamType(item, e)"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </a-select-option>
      </a-select>
      <a-button class="add-button" type="primary" shape="circle" size="small" @click="showParamTypeModal = true">
        <template #icon>
          <PlusOutlined />
        </template>
      </a-button>
    </div>

    <!-- 快速创建参数类型弹窗 -->
    <a-modal
      v-model:open="showParamTypeModal"
      title="快速创建参数类型"
      :confirm-loading="paramTypeFormLoading"
      :mask-closable="true"
      :closable="true"
      :keyboard="true"
      @ok="handleAddParamType"
      @cancel="() => { showParamTypeModal = false }"
    >
      <a-form ref="paramTypeFormRef" :model="paramTypeForm" :rules="paramTypeRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="类型编码" name="typeCode">
          <a-input v-model:value="paramTypeForm.typeCode" placeholder="请输入类型编码" />
        </a-form-item>
        <a-form-item label="类型名称" name="typeName">
          <a-input v-model:value="paramTypeForm.typeName" placeholder="请输入类型名称" />
        </a-form-item>
        <a-form-item label="数据类型" name="dataType">
          <a-select v-model:value="paramTypeForm.dataType" placeholder="请选择数据类型">
            <a-select-option value="STRING">
              字符串
            </a-select-option>
            <a-select-option value="INTEGER">
              整数
            </a-select-option>
            <a-select-option value="DECIMAL">
              小数
            </a-select-option>
            <a-select-option value="BOOLEAN">
              布尔值
            </a-select-option>
            <a-select-option value="DATE">
              日期
            </a-select-option>
            <a-select-option value="DATETIME">
              日期时间
            </a-select-option>
            <a-select-option value="JSON">
              JSON数据
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否必填" name="isRequired">
          <a-switch v-model:checked="paramTypeForm.isRequired" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="paramTypeForm.sort" :min="0" :max="9999" style="width: 100%" />
        </a-form-item>
        <a-form-item label="是否可搜索" name="searchable">
          <a-switch v-model:checked="paramTypeForm.searchable" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.ax-param-type-select {
  display: inline-block;
  width: 100%;
}

.select-container {
  display: flex;
  align-items: center;
  position: relative;
}

.add-button {
  position: absolute;
  right: -32px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.param-type-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-button {
  margin-left: 8px;
  padding: 0 4px;
}

.option-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
