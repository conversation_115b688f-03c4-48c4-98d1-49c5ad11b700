<script setup lang="ts">
import type { CustomerQueryParam, CustomerResult } from '@/api/business/customer/model/customer-form-model'
import type { SelectProps } from 'ant-design-vue'
import { customerApi } from '@/api/business/customer/customer-api'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'

// 组件属性接口定义
interface CustomerSelectProps {
  modelValue?: number | string
  customerType?: string // 客户类型
  status?: string // 状态
  placeholder?: string
  width?: string
  disabled?: boolean
  showSearch?: boolean
  allowClear?: boolean
}

// 定义组件属性
const props = withDefaults(defineProps<CustomerSelectProps>(), {
  modelValue: undefined,
  customerType: undefined,
  status: 'ACTIVE', // 默认只显示活跃状态的客户
  placeholder: '请选择客户',
  width: '100%',
  disabled: false,
  showSearch: true,
  allowClear: true,
})

// 定义组件事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: number | string | undefined): void
  (e: 'change', value: SelectProps['value'], option: any): void
  (e: 'customerSelected', customerDetail: CustomerResult): void
}>()

// 客户列表
const customerList = ref<CustomerResult[]>([])
// 加载状态
const loading = ref(false)

// 当前选中的客户
const selectedValue = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val)
  },
})

// 监听参数变化，重新加载数据
watch([() => props.customerType, () => props.status], () => {
  fetchCustomerList()
}, { immediate: true })

// 获取客户列表
async function fetchCustomerList(keyword = '') {
  loading.value = true
  try {
    // 查询参数
    const param: CustomerQueryParam = {
      status: props.status,
      customerType: props.customerType,
    }

    // 添加关键字搜索
    if (keyword) {
      param.customerName = keyword
    }

    const res = await customerApi.customerList(param)
    if (res.success && res.data) {
      customerList.value = res.data
    }
  }
  catch (error) {
    message.error('获取客户列表失败')
    console.error('获取客户列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 获取单个客户详情
async function fetchCustomerDetail(id: number | string) {
  if (!id) {
    return
  }

  loading.value = true
  try {
    const res = await customerApi.customerDetail(Number(id))
    if (res.success && res.data) {
      // 确保客户列表中包含当前选中的客户
      const exists = customerList.value.some(item => item.id === Number(id))
      if (!exists) {
        customerList.value.push(res.data)
      }
    }
  }
  catch (error) {
    console.error('获取客户详情失败', error)
  }
  finally {
    loading.value = false
  }
}

// 选项显示文本
function getOptionLabel(item: CustomerResult) {
  return `${item.customerName || ''} (${item.customerCode || '无编码'})`
}

// 选择变更事件
function handleChange(value: SelectProps['value'], option: any) {
  // 将字符串类型转换为数字或undefined
  const typedValue = typeof value === 'number'
    ? value
    : typeof value === 'string' && value !== ''
      ? Number(value)
      : undefined

  emit('change', value, option)

  if (typedValue) {
    const selected = customerList.value.find(item => item.id === typedValue)
    if (selected) {
      emit('customerSelected', selected)
    }
  }
}

// 搜索过滤器
function filterOption(input: string, option: any) {
  const customer = option.item
  return (
    customer.customerName?.toLowerCase().indexOf(input.toLowerCase()) >= 0
    || customer.customerCode?.toLowerCase().indexOf(input.toLowerCase()) >= 0
    || customer.contactName?.toLowerCase().indexOf(input.toLowerCase()) >= 0
    || customer.contactMobile?.toLowerCase().indexOf(input.toLowerCase()) >= 0
  )
}

// 初始加载
onMounted(() => {
  fetchCustomerList()
  // 如果有初始值但没有数据，获取详情
  if (props.modelValue) {
    fetchCustomerDetail(props.modelValue)
  }
})

// 暴露方法
defineExpose({
  fetchCustomerList,
  customerList,
})
</script>

<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :style="{ width }"
    :disabled="disabled"
    :loading="loading"
    :show-search="showSearch"
    :allow-clear="allowClear"
    :filter-option="showSearch ? filterOption : undefined"
    :options="customerList.map(item => ({
      value: item.id,
      label: getOptionLabel(item),
      item,
    }))"
    @change="handleChange"
  >
    <template #notFoundContent>
      <span v-if="loading">加载中...</span>
      <span v-else>未找到符合条件的客户</span>
    </template>
  </a-select>
</template>
