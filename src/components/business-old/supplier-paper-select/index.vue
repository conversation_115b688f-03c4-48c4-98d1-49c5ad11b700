/**
 * 供应商纸张选择组件
 */
<script setup lang="ts">
import type { PaperQuotationItemModel } from '@/api/business/paper-quotation-item/model/paper-quotation-item-model'
import type {
  LatestPaperQuotationQueryParam,
  PaperQuotationResult,
} from '@/api/business/paper-quotation/model/paper-quotation-form-model'
import type { SelectProps } from 'ant-design-vue'
import { paperQuotationApi } from '@/api/business/paper-quotation/paper-quotation-api'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

// 组件属性定义
interface SupplierPaperSelectProps extends LatestPaperQuotationQueryParam {
  disabled?: boolean
  placeholder?: string
  allowClear?: boolean
  value?: number | undefined
  modelValue?: number | undefined
}

// 组件属性默认值
const props = withDefaults(defineProps<SupplierPaperSelectProps>(), {
  supplierId: undefined,
  paperCategory: undefined,
  corrugatedShape: undefined,
  paperStructureLayer: undefined,
  paperTypeList: undefined,
  disabled: false,
  placeholder: '请选择纸张',
  allowClear: true,
  value: undefined,
  modelValue: undefined,
})

// 组件事件
const emit = defineEmits<{
  // 纸张选择事件，提供完整的纸张对象
  (e: 'select', paper: PaperQuotationItemModel): void
  // 纸张清除事件
  (e: 'clear'): void
  // v-model 支持
  (e: 'update:modelValue', value: number | undefined): void
  // 纸张列表加载完成事件
  (e: 'loaded', papers: PaperQuotationItemModel[]): void
}>()

// 组件状态
const loading = ref(false)
const papers = ref<PaperQuotationItemModel[]>([])
const quotation = ref<PaperQuotationResult | null>(null)

// 监听查询条件变化，自动加载数据
watch(

  [() => props.supplierId, () => props.paperStructureLayer, () => props.corrugatedShape],
  async () => {
    if (props.supplierId) {
      await loadPaperList()
    }
    else {
      papers.value = []
      quotation.value = null
    }
  },
  { immediate: true },
)

/**
 * 加载纸张列表
 */
async function loadPaperList(): Promise<void> {
  if (!props.supplierId) {
    return
  }

  try {
    loading.value = true

    const queryParams: LatestPaperQuotationQueryParam = {
      supplierId: props.supplierId,
      paperCategory: props.paperStructureLayer || '',
      corrugatedShape: props.corrugatedShape || '',
      paperTypeList: props.paperTypeList || [],
      paperStructureLayer: props.paperStructureLayer || '',
    }

    const response = await paperQuotationApi.getLatestPaperQuotation(queryParams)

    if (response.success && response.data) {
      quotation.value = response.data
      papers.value = response.data.paperQuotationItemList || []
    }
  }
  catch (error) {
    message.error('获取纸张报价失败')
    console.error('获取纸张报价失败', error)
  }
  finally {
    loading.value = false
    // 加载完成后触发loaded事件，传递纸张列表
    emit('loaded', papers.value)
  }
}

/**
 * 格式化纸张选项文本
 */
function formatPaperOption(paper: PaperQuotationItemModel): string {
  return `${paper.paperDescription || ''} (${paper.unitPrice}/${paper.unit})`
}

/**
 * 处理纸张选择变更
 */
function handleSelectionChange(value: SelectProps['value']): void {
  // 将值转换为数字或undefined
  const paperId = value === null || value === undefined || value === ''
    ? undefined
    : Number(value)

  // 更新v-model值
  emit('update:modelValue', paperId)

  // 如果选择了纸张，触发select事件
  if (paperId !== undefined) {
    const selectedPaper = papers.value.find(item => item.id === paperId)
    if (selectedPaper) {
      emit('select', selectedPaper)
    }
  }
  // 如果清除了选择，触发clear事件
  else {
    emit('clear')
  }
}

// 公开组件方法和属性
defineExpose({
  quotation,
  papers,
  loadPaperList,
})
</script>

<template>
  <a-select
    :loading="loading"
    :disabled="disabled"
    :placeholder="placeholder"
    :allow-clear="allowClear"
    style="width: 100%"
    :value="value !== undefined ? value : modelValue"
    :options="papers.map(paper => ({
      value: paper.id,
      label: formatPaperOption(paper),
      paper,
    }))"
    :open="loading ? false : undefined"
    @change="handleSelectionChange"
  >
    <template #notFoundContent>
      <span v-if="loading">加载中...</span>
      <span v-else>未找到相关纸张报价</span>
    </template>
  </a-select>
</template>
