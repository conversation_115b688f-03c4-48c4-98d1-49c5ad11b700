import type { SupplierConfig } from '@/api/business/param-json/model/param-config'
import type { SupplierQueryParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { InjectionKey } from 'vue'

// SupplierSelect服务接口 - 只包含业务相关的属性和方法
export interface SupplierSelectService {
  // 对外暴露的数据
  selectedSupplier: SupplierResult | undefined
  readonly supplierConfig: SupplierConfig
  readonly queryParam: SupplierQueryParam

  // 内部使用的方法，允许组件设置配置
  setSupplierConfig: (config: SupplierConfig) => void

  // 设置查询参数
  setQueryParam: (param: Partial<SupplierQueryParam>) => void
}

// 注入键
export const SUPPLIER_SELECT_KEY: InjectionKey<SupplierSelectService> = Symbol('supplier-select-service')
