<script setup lang="ts">
import type { SupplierConfig } from '@/api/business/param-json/model/param-config'
import { paramJsonApi } from '@/api/business/param-json/param-json-api'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { injectSupplierSelectService } from './service'

interface Props {
  visible: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

// 获取服务实例
const supplierService = injectSupplierSelectService()

// 内部状态
const loading = ref(false)
const saving = ref(false)
const activeTabKey = ref('1')
const supplierConfig = computed(() => supplierService.supplierConfig)

const modalVisible = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val),
})

const supplierName = computed(() => {
  return supplierService.selectedSupplier?.supplierName || ''
})

// 确保存在2-6层的加工费数据
function ensureProcessingFeeLayers() {
  // 克隆当前配置，避免直接修改
  const currentConfig = JSON.parse(JSON.stringify(supplierConfig.value)) as SupplierConfig

  // 确保processingFee存在
  if (!currentConfig.processingFee) {
    currentConfig.processingFee = []
  }

  const layers = [2, 3, 4, 5, 7]
  const existingLayers = currentConfig.processingFee.map(item => item.layer) || []

  // 添加缺少的层数
  const missingLayers = layers.filter(layer => !existingLayers.includes(layer))

  missingLayers.forEach((layer) => {
    if (currentConfig.processingFee) { // 添加空检查
      currentConfig.processingFee.push({
        layer,
        value: 0,
        unit: '元/平方米',
      })
    }
  })

  // 过滤掉2-6之外的层数，确保processingFee存在
  if (currentConfig.processingFee) {
    currentConfig.processingFee = currentConfig.processingFee
      .filter(item => layers.includes(item.layer))
      .sort((a, b) => a.layer - b.layer)
  }

  // 更新配置到服务
  supplierService.setSupplierConfig(currentConfig)
}

// 保存供应商配置
async function saveConfig() {
  if (!supplierService.selectedSupplier?.id)
    return

  try {
    saving.value = true

    // 创建配置对象的副本
    const configToSave = JSON.parse(JSON.stringify(supplierConfig.value)) as SupplierConfig
    // 确保supplierId已设置
    configToSave.relationId = supplierService.selectedSupplier.id

    // 直接调用API更新
    const res = await paramJsonApi.updateSupplierConfig(configToSave)
    if (res.success) {
      // 更新成功后，更新服务中的配置
      supplierService.setSupplierConfig(configToSave)
      message.success('保存成功')
      modalVisible.value = false
    }
    else {
      message.error(res.msg || '保存失败')
    }
  }
  catch (error) {
    console.error('保存供应商配置失败', error)
    message.error('保存失败')
  }
  finally {
    saving.value = false
  }
}

// 当弹窗打开时确保层数数据正确
onMounted(() => {
  if (props.visible) {
    ensureProcessingFeeLayers()
  }
})

// 监听visible变化，打开时确保层数数据正确
watch(() => props.visible, (newVal) => {
  if (newVal) {
    ensureProcessingFeeLayers()
  }
})
</script>

<template>
  <a-modal
    :open="modalVisible"
    :title="`${supplierName} - 供应商参数设置`"
    :footer="null"
    :mask-closable="false"
    width="650px"
    @cancel="modalVisible = false"
  >
    <a-spin :spinning="loading">
      <a-tabs v-model:active-key="activeTabKey">
        <a-tab-pane key="1" tab="基本参数">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="折扣率 (%)">
                  <a-input-number
                    v-model:value="supplierConfig.discountRate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    style="width: 100%"
                    placeholder="请输入折扣率"
                  />
                  <div class="form-item-help">
                    <small>折扣率用于计算供应商折扣价格，例如：90表示9折</small>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="税率 (%)">
                  <a-input-number
                    v-model:value="supplierConfig.taxRate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    style="width: 100%"
                    placeholder="请输入税率"
                  />
                  <div class="form-item-help">
                    <small>税率用于计算供应商含税价格</small>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="2" tab="加工费设置">
          <a-form layout="vertical">
            <div class="processing-fee-container">
              <p class="fee-description">
                设置不同层数的加工费用
              </p>

              <div v-for="(item, index) in supplierConfig.processingFee" :key="index" class="processing-fee-item">
                <a-row :gutter="16">
                  <a-col :span="6">
                    <span class="layer-label">{{ item.layer }}层</span>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item :style="{ marginBottom: '8px' }">
                      <a-input-number
                        v-model:value="item.value"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        placeholder="请输入加工费"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-select v-model:value="item.unit" style="width: 100%">
                      <a-select-option value="元/平方米">
                        元/平方米
                      </a-select-option>
                      <a-select-option value="元/平方厘米">
                        元/平方厘米
                      </a-select-option>
                    </a-select>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form>
        </a-tab-pane>
      </a-tabs>

      <div class="footer-buttons">
        <a-button @click="modalVisible = false">
          取消
        </a-button>
        <a-button type="primary" :loading="saving" @click="saveConfig">
          保存
        </a-button>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.form-item-help {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.processing-fee-container {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
}

.fee-description {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.processing-fee-item {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #fff;
}

.processing-fee-item:last-child {
  margin-bottom: 0;
}

.layer-label {
  display: flex;
  align-items: center;
  height: 32px;
  font-weight: 500;
}
</style>
