<script setup lang="ts">
import type { SupplierQueryParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { SelectProps } from 'ant-design-vue'
import { paramJsonApi } from '@/api/business/param-json/param-json-api'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { EyeOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onMounted, ref, watch } from 'vue'
import { injectSupplierSelectService } from './service'

const supplierService = injectSupplierSelectService()

// 内部写死的配置
const config = {
  disabled: false,
  placeholder: '请选择供应商',
  allowClear: true,
  showSearch: true,
}

// 内部状态
const loading = ref(false)
const supplierOptions = ref<SupplierResult[]>([])
const selectedSupplierId = ref<number | undefined>(undefined)
const modalVisible = ref(false)

// 当selectedSupplierId变化时，更新supplierService
watch(selectedSupplierId, (newVal) => {
  if (newVal) {
    const selected = supplierOptions.value.find(item => item.id === newVal)
    if (selected) {
      supplierService.selectedSupplier = selected
    }
  }
  else {
    supplierService.selectedSupplier = undefined
  }
})

// 获取供应商列表
async function fetchSupplierList() {
  try {
    loading.value = true

    // 使用service中的查询参数
    const res = await supplierApi.supplierList(supplierService.queryParam)
    if (res.success && res.data) {
      supplierOptions.value = res.data
    }
  }
  catch (error) {
    message.error('获取供应商列表失败')
    console.error('获取供应商列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 选项显示文本
function getOptionLabel(item: SupplierResult) {
  return `${item.supplierName} (${item.supplierCode || '无编码'})`
}

// 处理选择变化
async function handleChange(value: SelectProps['value'], _option: unknown) {
  // 将字符串类型转换为数字或undefined
  const numericValue = typeof value === 'number'
    ? value
    : typeof value === 'string' && value !== ''
      ? Number(value)
      : undefined

  selectedSupplierId.value = numericValue

  if (numericValue) {
    const selected = supplierOptions.value.find(item => item.id === numericValue)
    if (selected) {
      supplierService.selectedSupplier = selected

      // 当选择新供应商时，加载其配置信息
      await loadSupplierConfig(numericValue)
    }
  }
  else {
    supplierService.selectedSupplier = undefined
  }
}

// 加载供应商配置
async function loadSupplierConfig(supplierId: number) {
  try {
    loading.value = true
    const res = await paramJsonApi.getSupplierConfig(supplierId)
    if (res.success && res.data) {
      supplierService.setSupplierConfig(res.data)
    }
    else {
      // 如果没有配置数据，初始化一个空的配置对象
      supplierService.setSupplierConfig({
        discountRate: 0,
        taxRate: 0,
        processingFee: [],
      })
    }
  }
  catch (error) {
    console.error('获取供应商配置失败', error)
    // 错误时设置默认值
    supplierService.setSupplierConfig({
      discountRate: 0,
      taxRate: 0,
      processingFee: [],
    })
  }
  finally {
    loading.value = false
  }
}

// 打开参数设置弹窗
function openParamModal(e: Event) {
  e.stopPropagation() // 阻止事件冒泡
  if (selectedSupplierId.value) {
    modalVisible.value = true
  }
  else {
    message.warning('请先选择供应商')
  }
}

// 设置查询参数
function setQueryParam(param: Partial<SupplierQueryParam>) {
  supplierService.setQueryParam(param)
  fetchSupplierList() // 参数变化时自动刷新列表
}

// 初始化
onMounted(() => {
  fetchSupplierList()
})

// 暴露方法和服务
defineExpose({
  fetchSupplierList,
  supplierOptions,
  supplierService,
  setQueryParam,
})
</script>

<template>
  <div class="supplier-select-wrapper">
    <a-select
      v-model:value="selectedSupplierId"
      :loading="loading"
      :disabled="config.disabled"
      :placeholder="config.placeholder"
      :allow-clear="config.allowClear"
      :show-search="config.showSearch"
      :filter-option="true"
      style="width: 100%"
      :options="supplierOptions.map(item => ({
        value: item.id,
        label: getOptionLabel(item),
        item,
      }))"
      @change="handleChange"
    >
      <template #notFoundContent>
        <span v-if="loading">加载中...</span>
        <span v-else>未找到符合条件的供应商</span>
      </template>
    </a-select>
    <a-button
      v-if="selectedSupplierId"
      type="link"
      size="small"
      class="eye-button"
      @click="openParamModal"
    >
      <template #icon>
        <EyeOutlined />
      </template>
    </a-button>
    <SupplierModal
      v-model:visible="modalVisible"
    />
  </div>
</template>

<style scoped>
.supplier-select-wrapper {
  position: relative;
  width: 100%;
}

.supplier-select-wrapper :deep(.ant-select) {
  width: 100%;
}

/* 隐藏下拉箭头图标 */
.supplier-select-wrapper :deep(.ant-select-arrow) {
  display: none;
  pointer-events: none;
}

/* 修复可访问性问题 - 处理Ant Design内部元素冲突 */
.supplier-select-wrapper :deep([aria-hidden="true"][tabindex="0"]) {
  display: none !important;
}

.eye-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

/* 调整清除按钮位置，避免与眼睛图标重叠 */
.supplier-select-wrapper :deep(.ant-select-clear) {
  right: 28px;
}
</style>
