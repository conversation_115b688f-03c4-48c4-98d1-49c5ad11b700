import type { SupplierConfig } from '@/api/business/param-json/model/param-config'
import type { SupplierQueryParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import { inject, provide, ref } from 'vue'
import { SUPPLIER_SELECT_KEY, type SupplierSelectService } from './types'

/**
 * 供应商选择器服务类
 * 可以直接实例化使用，也可以通过内部单例使用
 */
export class SupplierSelectServiceImpl implements SupplierSelectService {
  // 私有属性 - 内部使用的ref
  private _selectedSupplierRef = ref<SupplierResult | undefined>(undefined)
  private _supplierConfigRef = ref<SupplierConfig>({
    discountRate: 0,
    taxRate: 0,
    processingFee: [],
  })

  private _queryParamRef = ref<SupplierQueryParam>({
    supplierType: '',
    status: 'active', // 默认只显示活跃状态的供应商
    supplierName: '',
  })

  // 通过getter访问器暴露属性
  get selectedSupplier(): SupplierResult | undefined {
    return this._selectedSupplierRef.value
  }

  // 通过setter访问器设置属性
  set selectedSupplier(supplier: SupplierResult | undefined) {
    this._selectedSupplierRef.value = supplier

    // 如果清空供应商，也清空配置
    if (!supplier) {
      this._supplierConfigRef.value = {
        discountRate: 0,
        taxRate: 0,
        processingFee: [],
      }
    }
  }

  // 只读访问器，外部只能获取不能修改
  get supplierConfig(): SupplierConfig {
    return this._supplierConfigRef.value
  }

  // 查询参数访问器
  get queryParam(): SupplierQueryParam {
    return this._queryParamRef.value
  }

  // 内部提供一个直接设置配置的方法给组件使用
  setSupplierConfig(config: SupplierConfig): void {
    this._supplierConfigRef.value = config
  }

  // 设置查询参数
  setQueryParam(param: Partial<SupplierQueryParam>): void {
    this._queryParamRef.value = {
      ...this._queryParamRef.value,
      ...param,
    }
  }

  /**
   * 将当前服务实例提供给依赖注入系统
   * @param key 注入键，默认使用SUPPLIER_SELECT_KEY
   */
  provide(): void {
    provide(SUPPLIER_SELECT_KEY, this)
  }
}

/**
 * 注入供应商选择器服务
 * @param key 注入键，默认使用SUPPLIER_SELECT_KEY
 * @returns 注入的服务实例
 */
export function injectSupplierSelectService(key: typeof SUPPLIER_SELECT_KEY = SUPPLIER_SELECT_KEY): SupplierSelectService {
  const service = inject(key)
  if (!service) {
    throw new Error('SupplierSelectService is not provided. Make sure to call service.provide() in a parent component.')
  }

  return service
}
