<script setup lang="ts">
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select'
import type { WarehouseResult } from '@/api/business/warehouse/model/warehouse-form-model'
import { Select } from 'ant-design-vue'
import { computed, onMounted, ref } from 'vue'
import { warehouseApi } from '@/api/business/warehouse/warehouse-api'

// 定义props
interface Props {
  modelValue?: number | string
  placeholder?: string
  allowClear?: boolean
  disabled?: boolean
  onlyActive?: boolean // 是否只显示活跃仓库
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择仓库',
  allowClear: true,
  disabled: false,
  onlyActive: true,
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: number | string | undefined]
  'change': [value: number | string | undefined, option: DefaultOptionType | DefaultOptionType[]]
}>()

// 响应式数据
const loading = ref(false)
const warehouseList = ref<WarehouseResult[]>([])

// 计算属性：转换为Select组件需要的选项格式
const options = computed(() => {
  return warehouseList.value.map(warehouse => ({
    label: `${warehouse.warehouseName || '未知'} (${warehouse.warehouseCode || '无编码'})`,
    value: warehouse.id,
    warehouse,
  }))
})

// 加载仓库列表
async function loadWarehouseList() {
  try {
    loading.value = true
    const response = props.onlyActive
      ? await warehouseApi.activeWarehouseList()
      : await warehouseApi.warehouseList()

    if (response.code === 0) {
      warehouseList.value = response.data || []
    }
    else {
      warehouseList.value = []
    }
  }
  catch (error) {
    console.error('加载仓库列表失败:', error)
    warehouseList.value = []
  }
  finally {
    loading.value = false
  }
}

// 值变化处理
function handleChange(value: SelectValue, option: DefaultOptionType | DefaultOptionType[]) {
  const selectedValue = value as number | string | undefined
  emit('update:modelValue', selectedValue)
  emit('change', selectedValue, option)
}

// 组件挂载时加载数据
onMounted(() => {
  loadWarehouseList()
})
</script>

<template>
  <Select
    :value="props.modelValue"
    :placeholder="props.placeholder"
    :allow-clear="props.allowClear"
    :disabled="props.disabled"
    :loading="loading"
    :options="options"
    show-search
    option-filter-prop="label"
    @change="handleChange"
  />
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
