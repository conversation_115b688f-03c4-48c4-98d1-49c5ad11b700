<script setup lang="ts">
import type { SupplierQueryParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { SelectProps } from 'ant-design-vue'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { EyeOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { ref, watch } from 'vue'

interface SupplierSelectProps {
  value?: number // 当前选中的供应商ID
  supplierType?: string // 供应商类型 字典 | supplierType
  status?: string // 状态：active-活跃，inactive-非活跃，blacklisted-黑名单
  disabled?: boolean // 是否禁用
  placeholder?: string // 占位文本
  allowClear?: boolean // 是否允许清空
  showSearch?: boolean // 是否显示搜索框
}

// 组件属性
const props = withDefaults(defineProps<SupplierSelectProps>(), {
  value: undefined,
  supplierType: undefined,
  status: 'active', // 默认只显示活跃状态的供应商
  disabled: false,
  placeholder: '请选择供应商',
  allowClear: true,
  showSearch: true,
})

// 组件事件
const emit = defineEmits<{
  (e: 'update:value', value: number | undefined): void
  (e: 'change', value: number | undefined, option: unknown): void
  (e: 'supplierSelected', supplierDetail: SupplierResult): void
}>()

// 内部状态
const loading = ref(false)
const supplierOptions = ref<SupplierResult[]>([])
const selectedSupplierId = ref<number | undefined>(props.value)
const modalVisible = ref(false)
const currentSupplier = ref<SupplierResult | undefined>()

// 监听值变化
watch(() => props.value, (newVal) => {
  selectedSupplierId.value = newVal
})

// 监听供应商类型变化
watch([() => props.supplierType, () => props.status], () => {
  fetchSupplierList()
}, { immediate: true })

// 获取供应商列表
async function fetchSupplierList() {
  try {
    loading.value = true
    const param: SupplierQueryParam = {
      supplierType: props.supplierType || '',
      status: props.status || 'active',
      supplierName: '',
    }

    const res = await supplierApi.supplierList(param)
    if (res.success && res.data) {
      supplierOptions.value = res.data
    }
  }
  catch (error) {
    message.error('获取供应商列表失败')
    console.error('获取供应商列表失败', error)
  }
  finally {
    loading.value = false
  }
}

// 选项显示文本
function getOptionLabel(item: SupplierResult) {
  return `${item.supplierName} (${item.supplierCode || '无编码'})`
}

// 处理选择变化
function handleChange(value: SelectProps['value'], option: unknown) {
  // 将字符串类型转换为数字或undefined
  const numericValue = typeof value === 'number'
    ? value
    : typeof value === 'string' && value !== ''
      ? Number(value)
      : undefined

  selectedSupplierId.value = numericValue

  emit('update:value', numericValue)
  emit('change', numericValue, option)

  if (numericValue) {
    const selected = supplierOptions.value.find(item => item.id === numericValue)
    if (selected) {
      currentSupplier.value = selected
      emit('supplierSelected', selected)
    }
  }
  else {
    currentSupplier.value = undefined
  }
}

// 打开参数设置弹窗
function openParamModal(e: Event) {
  e.stopPropagation()
  if (selectedSupplierId.value) {
    modalVisible.value = true
  }
  else {
    message.warning('请先选择供应商')
  }
}

// 暴露方法
defineExpose({
  fetchSupplierList,
  supplierOptions,
})
</script>

<template>
  <div class="supplier-select-wrapper">
    <a-select
      v-model:value="selectedSupplierId"
      :loading="loading"
      :disabled="disabled"
      :placeholder="placeholder"
      :allow-clear="allowClear"
      :show-search="showSearch"
      :filter-option="true"
      style="width: 100%"
      :options="supplierOptions.map(item => ({
        value: item.id,
        label: getOptionLabel(item),
        item,
      }))"
      @change="handleChange"
    >
      <template #notFoundContent>
        <span v-if="loading">加载中...</span>
        <span v-else>未找到符合条件的供应商</span>
      </template>
    </a-select>
    <a-button
      v-if="selectedSupplierId"
      type="link"
      size="small"
      class="eye-button"
      @click="openParamModal"
    >
      <template #icon>
        <EyeOutlined />
      </template>
    </a-button>
    <SupplierModal
      v-if="currentSupplier"
      v-model:visible="modalVisible"
      :supplier="currentSupplier"
    />
  </div>
</template>

<style scoped>
.supplier-select-wrapper {
  position: relative;
  width: 100%;
}

.supplier-select-wrapper :deep(.ant-select) {
  width: 100%;
}

/* 隐藏下拉箭头图标 */
.supplier-select-wrapper :deep(.ant-select-arrow) {
  display: none;
  pointer-events: none;
}

/* 修复可访问性问题 - 处理Ant Design内部元素冲突 */
.supplier-select-wrapper :deep([aria-hidden="true"][tabindex="0"]) {
  display: none !important;
}

.eye-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

/* 调整清除按钮位置，避免与眼睛图标重叠 */
.supplier-select-wrapper :deep(.ant-select-clear) {
  right: 32px;
}
</style>
