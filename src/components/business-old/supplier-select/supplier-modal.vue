<script setup lang="ts">
import type { SupplierConfig } from '@/api/business/param-json/model/param-config'
import type { SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import { paramJsonApi } from '@/api/business/param-json/param-json-api'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'

interface Props {
  visible: boolean
  supplier: SupplierResult
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void
}>()

// 内部状态
const loading = ref(false)
const saving = ref(false)
const activeTabKey = ref('1')
const supplierConfig = ref<SupplierConfig>({
  id: undefined,
  discountRate: 0,
  taxRate: 0,
  processingFee: [],
})

const modalVisible = computed({
  get: () => props.visible,
  set: val => emit('update:visible', val),
})

const supplierName = computed(() => {
  return props.supplier?.supplierName || ''
})

// 获取供应商配置参数
async function fetchSupplierConfig() {
  if (!props.supplier?.id)
    return

  try {
    loading.value = true
    const res = await paramJsonApi.getSupplierConfig(props.supplier.id)
    if (res.success && res.data) {
      supplierConfig.value = res.data
      // 确保processingFee存在
      if (!supplierConfig.value.processingFee)
        supplierConfig.value.processingFee = []

      // 确保有2-6层的加工费数据
      ensureProcessingFeeLayers()
    }
    else {
      // 如果没有配置数据，初始化一个空的配置对象
      supplierConfig.value = {
        id: undefined,
        discountRate: 0,
        taxRate: 0,
        processingFee: [],
      }
      // 初始化2-6层加工费
      ensureProcessingFeeLayers()
    }
  }
  catch (error) {
    console.error('获取供应商配置失败', error)
  }
  finally {
    loading.value = false
  }
}

// 确保存在2-6层的加工费数据
function ensureProcessingFeeLayers() {
  const layers = [2, 3, 4, 5, 6, 7]
  const existingLayers = supplierConfig.value.processingFee?.map(item => item.layer) || []

  // 添加缺少的层数
  const missingLayers = layers.filter(layer => !existingLayers.includes(layer))
  missingLayers.forEach((layer) => {
    supplierConfig.value.processingFee?.push({
      layer,
      value: 0,
      unit: '元/平方米',
    })
  })

  // 过滤掉2-6之外的层数
  supplierConfig.value.processingFee = supplierConfig.value.processingFee
    ?.filter(item => layers.includes(item.layer))
    ?.sort((a, b) => a.layer - b.layer)
}

// 保存供应商配置
async function saveConfig() {
  if (!props.supplier?.id)
    return

  try {
    saving.value = true

    // 确保supplierId已设置
    supplierConfig.value.relationId = props.supplier.id
    const res = await paramJsonApi.updateSupplierConfig(supplierConfig.value)
    if (res.success) {
      message.success('保存成功')
      modalVisible.value = false
    }
    else {
      message.error(res.msg || '保存失败')
    }
  }
  catch (error) {
    console.error('保存供应商配置失败', error)
    message.error('保存失败')
  }
  finally {
    saving.value = false
    fetchSupplierConfig()
  }
}

// 当弹窗打开时获取数据
onMounted(() => {
  if (props.visible) {
    fetchSupplierConfig()
  }
})

// 监听visible变化，当打开时获取数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchSupplierConfig()
  }
})
</script>

<template>
  <a-modal
    :open="modalVisible"
    :title="`${supplierName} - 供应商参数设置`"
    :footer="null"
    :mask-closable="false"
    width="650px"
    @cancel="modalVisible = false"
  >
    <a-spin :spinning="loading">
      <a-tabs v-model:active-key="activeTabKey">
        <a-tab-pane key="1" tab="基本参数">
          <a-form layout="vertical">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="折扣率 (%)">
                  <a-input-number
                    v-model:value="supplierConfig.discountRate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    style="width: 100%"
                    placeholder="请输入折扣率"
                  />
                  <div class="form-item-help">
                    <small>折扣率用于计算供应商折扣价格，例如：90表示9折</small>
                  </div>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="税率 (%)">
                  <a-input-number
                    v-model:value="supplierConfig.taxRate"
                    :min="0"
                    :max="100"
                    :precision="2"
                    style="width: 100%"
                    placeholder="请输入税率"
                  />
                  <div class="form-item-help">
                    <small>税率用于计算供应商含税价格</small>
                  </div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-tab-pane>

        <a-tab-pane key="2" tab="加工费设置">
          <a-form layout="vertical">
            <div class="processing-fee-container">
              <p class="fee-description">
                设置不同层数的加工费用
              </p>

              <div v-for="(item, index) in supplierConfig.processingFee" :key="index" class="processing-fee-item">
                <a-row :gutter="16">
                  <a-col :span="6">
                    <span class="layer-label">{{ item.layer }}层</span>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item :style="{ marginBottom: '8px' }">
                      <a-input-number
                        v-model:value="item.value"
                        :min="0"
                        :precision="2"
                        style="width: 100%"
                        placeholder="请输入加工费"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="6">
                    <a-select v-model:value="item.unit" style="width: 100%">
                      <a-select-option value="元/平方米">
                        元/平方米
                      </a-select-option>
                      <a-select-option value="元/平方厘米">
                        元/平方厘米
                      </a-select-option>
                    </a-select>
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-form>
        </a-tab-pane>
      </a-tabs>

      <div class="footer-buttons">
        <a-button @click="modalVisible = false">
          取消
        </a-button>
        <a-button type="primary" :loading="saving" @click="saveConfig">
          保存
        </a-button>
      </div>
    </a-spin>
  </a-modal>
</template>

<style scoped>
.footer-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.form-item-help {
  color: rgba(0, 0, 0, 0.45);
  margin-top: 4px;
}

.processing-fee-container {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
}

.fee-description {
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.processing-fee-item {
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #fff;
}

.processing-fee-item:last-child {
  margin-bottom: 0;
}

.layer-label {
  display: flex;
  align-items: center;
  height: 32px;
  font-weight: 500;
}
</style>
