<script setup lang="ts">
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import WarehouseSelect from '@/components/business-old/warehouse-select/index.vue'

// 定义props
interface Props {
  modelValue?: number | string
  label?: string
  placeholder?: string
  onlyActive?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  label: '选择仓库',
  placeholder: '请选择仓库',
  onlyActive: true,
  required: false,
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: number | string | undefined]
  'change': [value: number | string | undefined, option: DefaultOptionType | DefaultOptionType[]]
}>()

// 值变化处理
function handleValueChange(value: number | string | undefined) {
  emit('update:modelValue', value)
}

function handleChange(value: number | string | undefined, option: DefaultOptionType | DefaultOptionType[]) {
  emit('change', value, option)
}
</script>

<template>
  <!-- 仓库选择 -->
  <div class="warehouse-selector">
    <label v-if="props.label" class="label" :class="{ required: props.required }">
      {{ props.label }}
    </label>
    <WarehouseSelect
      :model-value="props.modelValue"
      :placeholder="props.placeholder"
      :only-active="props.onlyActive"
      @update:model-value="handleValueChange"
      @change="handleChange"
    />
  </div>
</template>

<style scoped>
.warehouse-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.label.required::after {
  content: '*';
  color: #ff4d4f;
  margin-left: 4px;
}
</style>
