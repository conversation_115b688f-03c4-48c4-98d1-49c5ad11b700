<script setup lang="ts">
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import WarehouseSelect from '@/components/business-old/warehouse-select/index.vue'

// 定义props - 完全照抄BusinessTypeSelector的使用方式
interface Props {
  modelValue?: number | string
  placeholder?: string
  onlyActive?: boolean
  allowClear?: boolean
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择仓库',
  onlyActive: true,
  allowClear: true,
  disabled: false,
})

// 定义事件 - 照抄CustomerSelect的事件定义
const emit = defineEmits<{
  'update:modelValue': [value: number | string | undefined]
  'change': [value: number | string | undefined, option: DefaultOptionType | DefaultOptionType[]]
}>()

// 值变化处理 - 完全照抄BusinessTypeSelector中CustomerSelect的用法
function handleValueChange(value: number | string | undefined) {
  emit('update:modelValue', value)
}

function handleChange(value: number | string | undefined, option: DefaultOptionType | DefaultOptionType[]) {
  emit('change', value, option)
}
</script>

<template>
  <!-- 仓库选择器 - 照抄CustomerSelect的使用方式 -->
  <WarehouseSelect
    :model-value="props.modelValue"
    :placeholder="props.placeholder"
    :only-active="props.onlyActive"
    :allow-clear="props.allowClear"
    :disabled="props.disabled"
    @update:model-value="handleValueChange"
    @change="handleChange"
  />
</template>
