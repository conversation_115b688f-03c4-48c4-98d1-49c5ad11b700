<script lang="ts" setup>
import type { EntityTypeResult } from '@/api/business/entity-type/model/entity-type-form-model'
import type { FormInstance } from 'ant-design-vue/es/form'
import type { SelectValue } from 'ant-design-vue/es/select'
import { entityTypeApi } from '@/api/business/entity-type/entity-type-api'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { inject, onMounted, ref } from 'vue'

/**
 * 实体类型选择器服务接口
 */
interface EntityTypeSelectService {
  // 选中的值，可直接修改
  formData: { entityTypeId?: number | undefined }
}

// 从父组件注入服务对象
const service = inject<EntityTypeSelectService>('crudService')
if (!service) {
  throw new Error('EntityTypeSelectService is not injected')
}

// 实体类型列表
const entityTypeList = ref<EntityTypeResult[]>([])
// 是否正在加载
const loading = ref(false)

// 实体类型快速创建表单
const showEntityTypeModal = ref(false)
const entityTypeForm = ref({
  typeCode: '',
  typeName: '',
  module: 'ERP',
})
const entityTypeFormRef = ref<FormInstance>()
const entityTypeFormLoading = ref(false)

// 实体类型表单验证规则
const entityTypeRules = {
  typeCode: [{ required: true, message: '请输入实体类型编码' }],
  typeName: [{ required: true, message: '请输入实体类型名称' }],
}

// 加载实体类型
async function loadEntityTypes() {
  try {
    loading.value = true
    const res = await entityTypeApi.entityTypeList({})
    if (res.data) {
      entityTypeList.value = res.data
    }
  }
  catch (error) {
    console.error('加载实体类型失败', error)
  }
  finally {
    loading.value = false
  }
}

// 快速创建实体类型
async function handleAddEntityType() {
  try {
    if (entityTypeFormRef.value) {
      await entityTypeFormRef.value.validate()
      entityTypeFormLoading.value = true

      // 调用创建实体类型API
      const res = await entityTypeApi.addEntityType({
        typeCode: entityTypeForm.value.typeCode,
        typeName: entityTypeForm.value.typeName,
        module: entityTypeForm.value.module,
        // 下面是默认值
        isSystem: false,
        allowCustomParam: true,
      })

      if (res.success) {
        message.success('创建实体类型成功')
        // 重新加载实体类型列表
        await loadEntityTypes()
        // 关闭对话框
        showEntityTypeModal.value = false
        // 重置表单
        entityTypeForm.value = {
          typeCode: '',
          typeName: '',
          module: 'ERP',
        }

        // 如果创建成功，自动选中新创建的实体类型
        if (res.data && service) {
          // 直接修改注入的响应式对象
          service.formData.entityTypeId = Number(res.data)
        }
      }
    }
  }
  catch (error) {
    console.error('创建实体类型失败', error)
  }
  finally {
    entityTypeFormLoading.value = false
  }
}

// 选中值变化事件，直接修改注入的响应式对象
function handleChange(value: SelectValue) {
  if (service) {
    service.formData.entityTypeId = value as number | undefined
  }
}

// 处理删除实体类型
async function handleDeleteEntityType(item: EntityTypeResult, e: Event) {
  // 阻止事件冒泡，避免触发选择操作
  e.stopPropagation()

  // 确认删除
  Modal.confirm({
    title: '确定删除',
    content: `确定要删除实体类型"${item.typeName}"吗？`,
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        if (item.id) {
          const res = await entityTypeApi.deleteEntityType(item.id)
          if (res.success) {
            message.success('删除成功')
            // 重新加载列表
            await loadEntityTypes()
            // 如果当前选中项被删除，则清空选择
            if (service?.formData.entityTypeId === item.id) {
              service.formData.entityTypeId = undefined
            }
          }
        }
      }
      catch (error) {
        console.error('删除实体类型失败', error)
        message.error('删除实体类型失败')
      }
    },
  })
}

// 初始加载
onMounted(() => {
  loadEntityTypes()
})
</script>

<template>
  <div class="ax-entity-type-select">
    <div class="select-container">
      <a-select
        :value="service?.formData.entityTypeId"
        placeholder="请选择实体类型"
        :style="{ width: '100%' }"
        :loading="loading"
        :allow-clear="true"
        :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
        @change="handleChange"
      >
        <a-select-option v-for="item in entityTypeList" :key="item.id" :value="item.id">
          <div class="entity-type-option">
            <span class="option-text">{{ item.typeName }} ({{ item.typeCode }})</span>
            <a-button
              class="delete-button"
              type="link"
              size="small"
              danger
              @click="(e) => handleDeleteEntityType(item, e)"
            >
              <DeleteOutlined />
            </a-button>
          </div>
        </a-select-option>
      </a-select>
      <a-button class="add-button" type="primary" shape="circle" size="small" @click="showEntityTypeModal = true">
        <template #icon>
          <PlusOutlined />
        </template>
      </a-button>
    </div>

    <!-- 快速创建实体类型弹窗 -->
    <a-modal
      v-model:open="showEntityTypeModal"
      title="快速创建实体类型"
      :confirm-loading="entityTypeFormLoading"
      :mask-closable="true"
      :closable="true"
      :keyboard="true"
      @ok="handleAddEntityType"
      @cancel="() => { showEntityTypeModal = false }"
    >
      <a-form ref="entityTypeFormRef" :model="entityTypeForm" :rules="entityTypeRules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="类型编码" name="typeCode">
          <a-input v-model:value="entityTypeForm.typeCode" placeholder="请输入类型编码" />
        </a-form-item>
        <a-form-item label="类型名称" name="typeName">
          <a-input v-model:value="entityTypeForm.typeName" placeholder="请输入类型名称" />
        </a-form-item>
        <a-form-item label="所属模块" name="module">
          <a-input v-model:value="entityTypeForm.module" placeholder="请输入所属模块" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<style scoped>
.ax-entity-type-select {
  display: inline-block;
  width: 100%;
}

.select-container {
  display: flex;
  align-items: center;
  position: relative;
}

.add-button {
  position: absolute;
  right: -32px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.entity-type-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-button {
  margin-left: 8px;
  padding: 0 4px;
}

.option-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
