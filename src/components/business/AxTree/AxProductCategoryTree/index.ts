import AxProductCategoryTree from './AxProductCategoryTree.vue'
import { AX_PRODUCT_CATEGORY_TREE_SERVICE, AxProductCategoryTreeService } from './AxProductCategoryTreeService'

export interface AxProductCategoryTreeParentService {
  queryParam: Record<string, unknown>
  formParam: Record<string, unknown>
  formOpen: boolean
}

export { AX_PRODUCT_CATEGORY_TREE_SERVICE, AxProductCategoryTree, AxProductCategoryTreeService }
