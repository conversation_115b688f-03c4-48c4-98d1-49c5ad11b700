<script setup lang="ts">
import type { DataNode } from '@/components/base/CustomTree'
import type { AxProductCategoryTreeService } from './AxProductCategoryTreeService'

import { CustomTree as Tree } from '@/components/base/CustomTree'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { AX_PRODUCT_CATEGORY_TREE_SERVICE } from './AxProductCategoryTreeService'

// 获取服务实例
const service = inject(AX_PRODUCT_CATEGORY_TREE_SERVICE) as AxProductCategoryTreeService
service.parentService = inject(CRUD_KEY)

function handleChange(value: string | number | undefined) {
  service.onProductCategoryTreeChange(value)
}
</script>

<template>
  <div class="w-full">
    <Tree
      :tree-data="service.rawPageResult as unknown as DataNode[]"
      label-key="categoryName"
      value-key="id"
      children-key="children"
      @change="handleChange"
    />
  </div>
</template>
