import type { ProductCategoryPageParam, ProductCategoryTreeResult } from '@/api/business/product-category/model/product-category-form-model'
import type { AxProductCategoryTreeParentService } from './index'
import { productCategoryApi } from '@/api/business/product-category/product-category-api'
import { ListProcessor } from '@/service/internal/ListProcessor'

export const AX_PRODUCT_CATEGORY_TREE_SERVICE = Symbol('AX_PRODUCT_CATEGORY_TREE_SERVICE')

export class AxProductCategoryTreeService extends ListProcessor<ProductCategoryTreeResult, ProductCategoryPageParam> {
  parentService: AxProductCategoryTreeParentService | undefined = undefined

  constructor() {
    super('商品分类', {
      queryPage: productCategoryApi.productCategoryTree as unknown as (param: ProductCategoryPageParam) => Promise<{ data?: PageResult<ProductCategoryTreeResult> | undefined }>,
    })
  }

  provide = (): void => {
    provide(AX_PRODUCT_CATEGORY_TREE_SERVICE, this)
  }

  // ------------------------------ 混合业务逻辑 ------------------------------
  onProductCategoryTreeChange = (value: string | number | undefined): void => {
    console.log('onWarehouseChange', value)
  }
}
