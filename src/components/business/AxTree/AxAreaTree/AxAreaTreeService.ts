import type { AxAreaTreeParentService } from './index'
import { areaApi } from '@/api/system/area-api'
import { ListProcessor } from '@/service/internal/ListProcessor'
import { provide } from 'vue'

export const AX_AREA_TREE_SERVICE = Symbol('AX_AREA_TREE_SERVICE')

// 定义数据节点接口
interface AreaNode {
  id: number
  name: string
  children?: AreaNode[]
}

// 查询参数接口
interface AreaTreeQueryParam {
  pageParam: {
    pageNum: number
    pageSize: number
  }
  // 可添加其他查询条件
}

export class AxAreaTreeService extends ListProcessor<AreaNode, AreaTreeQueryParam> {
  parentService: AxAreaTreeParentService | undefined = undefined

  constructor() {
    super('地区', {
      queryPage: async (param: AreaTreeQueryParam) => {
        const response = await areaApi.areaTree()
        return {
          data: response.data || [],
        }
      },
    })
  }

  provide = (): void => {
    provide(AX_AREA_TREE_SERVICE, this)
  }

  // ------------------------------ 混合业务逻辑 ------------------------------
  onAreaTreeChange = (value: string | number | undefined): void => {
    console.log('onAreaTreeChange', value)

    // 业务逻辑处理
    if (this.parentService) {
      // 更新父服务的查询参数
      this.parentService.queryParam.areaId = value
      // 可触发父组件数据刷新
      // this.parentService.queryPage?.()
    }
  }
}
