import { AxAreaTreeService } from './AxAreaTreeService'

export class AxAreaTreeServiceEx extends AxAreaTreeService {
  constructor() {
    super()
  }

  // ------------------------------ 混合业务逻辑 ------------------------------
  onAreaTreeChange = (value: string | number | undefined): void => {
    if (this.parentService?.queryParam) {
      Object.assign(this.parentService.queryParam, { areaId: value as string | number | undefined })
    }
  }
}
