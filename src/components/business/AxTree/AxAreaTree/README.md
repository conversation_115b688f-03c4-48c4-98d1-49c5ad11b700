# AxAreaTree 地区树组件

## 🎯 概述

`AxAreaTree` 是基于 `CustomTree` 组件实现的地区树形选择组件，支持地区的层级展示和选择功能。

## 📋 组件特性

- **树形结构展示**：支持多层级地区数据的树形展示
- **异步数据加载**：基于 `ListProcessor` 的异步数据处理能力
- **业务逻辑集成**：与父组件的数据联动机制
- **可扩展性**：支持通过继承扩展业务逻辑

## 🚀 基础使用

### 1. 在页面中引入组件

```vue
<script setup lang="ts">
import { AxAreaTree, AxAreaTreeService } from '@/components/business/AxTree/AxAreaTree'

// 创建并提供服务实例
const areaTreeService = new AxAreaTreeService()
areaTreeService.provide()
</script>

<template>
  <div class="filter-section">
    <FormItem label="地区" class="smart-query-form-item">
      <AxAreaTree />
    </FormItem>
  </div>
</template>
```

### 2. 扩展业务逻辑

如果需要自定义业务逻辑，可以继承 `AxAreaTreeService`：

```typescript
// service/AreaTreeServiceEx.ts
import { AxAreaTreeService } from '@/components/business/AxTree/AxAreaTree'

export class AreaTreeServiceEx extends AxAreaTreeService {
  constructor() {
    super()
  }

  onAreaTreeChange = (value: string | number | undefined): void => {
    if (this.parentService?.queryParam) {
      // 自定义业务逻辑：更新父组件查询参数
      Object.assign(this.parentService.queryParam, { areaId: value })
      // 可选：触发父组件数据刷新
      // this.parentService.queryPage?.()
    }
  }
}
```

然后在页面中使用扩展的服务：

```vue
<script setup lang="ts">
import { AxAreaTree } from '@/components/business/AxTree/AxAreaTree'
import { AreaTreeServiceEx } from './service/AreaTreeServiceEx'

const areaTreeServiceEx = new AreaTreeServiceEx()
areaTreeServiceEx.provide()
</script>
```

## 🎨 组件配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `label-key` | `string` | `name` | 显示标签字段名 |
| `value-key` | `string` | `id` | 值字段名 |
| `children-key` | `string` | `children` | 子节点字段名 |
| `placeholder` | `string` | `请选择地区` | 占位符文本 |
| `树组件宽度` | `string` | `w-64` | 组件宽度类名 |

## 🔧 API 接口

组件依赖的 API 接口：

```typescript
// /system/area/tree
interface AreaNode {
  id: number
  name: string
  children?: AreaNode[]
}

areaApi.areaTree(): Promise<ResponseModel<AreaNode[]>>
```

## 📊 数据结构示例

```json
[
  {
    "id": 1,
    "name": "北京市",
    "children": [
      {
        "id": 11,
        "name": "东城区",
        "children": []
      },
      {
        "id": 12,
        "name": "西城区",
        "children": []
      }
    ]
  },
  {
    "id": 2,
    "name": "上海市",
    "children": [
      {
        "id": 21,
        "name": "黄浦区",
        "children": []
      }
    ]
  }
]
```

## 🌟 最佳实践

### 1. 性能优化
- 大数据量时考虑虚拟滚动或分页加载
- 合理设置树的默认展开层级

### 2. 用户体验
- 提供清晰的加载状态提示
- 支持搜索过滤功能（通过 `CustomTree` 的 `filterProp` 属性）

### 3. 错误处理
- 网络错误时的友好提示
- 数据为空时的占位状态

## 🔍 故障排除

### 常见问题

1. **组件不显示数据**
   - 检查 API 接口是否正常返回数据
   - 确认服务实例是否正确提供

2. **选择事件不触发**
   - 检查是否正确注入了父服务
   - 确认 `onAreaTreeChange` 方法是否正确实现

3. **TypeScript 类型错误**
   - 确认导入的类型是否正确
   - 检查数据结构是否与接口定义匹配 