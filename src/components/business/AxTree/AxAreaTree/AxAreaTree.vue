<script setup lang="ts">
import type { DataNode } from '@/components/base/CustomTree'
import type { AxAreaTreeService } from './AxAreaTreeService'

import { CustomTree as Tree } from '@/components/base/CustomTree'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { AX_AREA_TREE_SERVICE } from './AxAreaTreeService'

// 获取服务实例
const service = inject(AX_AREA_TREE_SERVICE) as AxAreaTreeService
service.parentService = inject(CRUD_KEY)

function handleChange(value: string | number | undefined) {
  service.onAreaTreeChange(value)
}
</script>

<template>
  <div class="w-full">
    <Tree
      :tree-data="service.rawPageResult as unknown as DataNode[]"
      label-key="name"
      value-key="id"
      children-key="children"
      placeholder="请选择地区"
      :tree-default-expand-all="false"
      @change="handleChange"
    />
  </div>
</template>
