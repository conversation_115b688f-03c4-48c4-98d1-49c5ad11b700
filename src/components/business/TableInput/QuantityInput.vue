<script lang="ts" setup>
import type { QuantityInputEmits, QuantityInputProps } from './index'

const { value, min = 1, max, precision = 0, size = 'middle', readonly = false, placeholder = '请输入数量' } = defineProps<QuantityInputProps>()

const emit = defineEmits<QuantityInputEmits>()

function handleChange(event: Event) {
  const target = event.target as HTMLInputElement
  const value = target.valueAsNumber

  // 如果输入无效，返回null
  if (Number.isNaN(value)) {
    emit('change', null)
    return
  }

  emit('change', value)
}

function handleBlur(event: Event) {
  const target = event.target as HTMLInputElement
  const value = target.valueAsNumber

  // 失焦时确保值在有效范围内
  if (!Number.isNaN(value)) {
    if (min !== undefined && value < min) {
      target.value = String(min)
      emit('change', min)
    }
    else if (max !== undefined && value > max) {
      target.value = String(max)
      emit('change', max)
    }
  }
}
</script>

<template>
  <div class="w-full">
    <!-- 只读状态 -->
    <span
      v-if="readonly"
      class="text-gray-900 text-sm font-medium"
    >
      {{ value }}
    </span>

    <!-- 编辑状态 -->
    <input
      v-else
      type="number"
      :value="value"
      :min="min"
      :max="max"
      :step="precision === 0 ? 1 : Math.pow(10, -precision)"
      :placeholder="placeholder"
      class="w-full border border-gray-300 rounded transition-colors duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none hover:border-gray-400"
      :class="{
        'px-2 py-1 text-xs h-6': size === 'small',
        'px-3 py-2 text-sm h-8': size === 'middle',
        'px-3 py-2 text-base h-10': size === 'large',
      }"
      @input="handleChange"
      @blur="handleBlur"
    >
  </div>
</template>
