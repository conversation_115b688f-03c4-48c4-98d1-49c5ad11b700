export { default as QuantityInput } from './QuantityInput.vue'

// 导出类型定义
export interface QuantityInputProps {
  /** 当前值 */
  value: number
  /** 最小值 */
  min?: number
  /** 最大值 */
  max?: number
  /** 是否只读 */
  readonly?: boolean
  /** 尺寸 */
  size?: 'small' | 'middle' | 'large' | 'default'
  /** 精度 */
  precision?: number
  /** 占位符 */
  placeholder?: string
}

export interface QuantityInputEmits {
  (e: 'change', value: number | null): void
}
