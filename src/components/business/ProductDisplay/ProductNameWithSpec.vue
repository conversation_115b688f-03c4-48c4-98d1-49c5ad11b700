<script lang="ts" setup>
import type { ProductDisplayProps } from './index'

const { productName = '', specSummary = '', nameClass = '', specClass = '', showFullSpec = true, vertical = true } = defineProps<ProductDisplayProps>()
</script>

<template>
  <div class="w-full" :class="[vertical ? 'flex flex-col' : 'flex items-center gap-2']">
    <!-- 商品名称 -->
    <span :class="nameClass">
      {{ productName }}
    </span>

    <!-- 规格摘要 -->
    <span
      v-if="specSummary"
      class="truncate text-sm text-gray-500" :class="[specClass]"
      :title="showFullSpec ? specSummary : undefined"
    >
      {{ specSummary }}
    </span>
  </div>
</template>
