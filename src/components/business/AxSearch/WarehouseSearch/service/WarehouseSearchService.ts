import type { WarehouseFormParam, WarehousePageParam, WarehouseResult } from '@/api/business/warehouse/model/warehouse-form-model'
import type { AxSearchConfig, AxSearchParentService } from '@/components/business/AxSearch/type'
import { warehouseApi } from '@/api/business/warehouse/warehouse-api'
import { BaseCrudService } from '@/service/BaseCrudService'

export const WAREHOUSE_SEARCH_KEY = Symbol('WAREHOUSE_SEARCH_KEY')

/**
 * 仓库服务
 * 提供仓库相关的业务逻辑和数据管理
 */
export class WarehouseSearchService extends BaseCrudService<WarehouseResult, WarehouseFormParam, WarehousePageParam> {
  config: AxSearchConfig = {
    // 默认配置
    allowAdd: true,
    allowEdit: true,
    allowDelete: true,
  }

  parentService: AxSearchParentService | undefined = undefined

  constructor(config?: AxSearchConfig) {
    // 初始化服务
    super(
      '仓库信息', // 业务名称
      [],
      {
        // 使用已有的API
        add: warehouseApi.addWarehouse,
        queryPage: warehouseApi.warehousePage,
        getDetail: warehouseApi.warehouseDetail,
        update: warehouseApi.updateWarehouse,
        import: warehouseApi.importWarehouse,
        export: warehouseApi.exportWarehouse,
        delete: warehouseApi.deleteWarehouse,
        batchDelete: warehouseApi.batchDeleteWarehouse,
        downloadTemplate: warehouseApi.exportWarehouse,
      },
    )

    if (config) {
      this.config = {
        ...this.config,
        ...config,
      }
    }
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide(WAREHOUSE_SEARCH_KEY, this)
  }
  // ---------------------------- 自身业务逻辑 ----------------------------

  getOptionValue = (item: WarehouseResult): string | number => {
    return item.warehouseName || ''
  }

  getOptionKey = (item: WarehouseResult): number | undefined => {
    return item.id || undefined
  }

  // ---------------------------- 混合业务逻辑 ----------------------------
  onWarehouseChange = (value: unknown, option?: unknown | unknown[]): void => {
    console.log('onWarehouseChange', value, option)
  }
}
