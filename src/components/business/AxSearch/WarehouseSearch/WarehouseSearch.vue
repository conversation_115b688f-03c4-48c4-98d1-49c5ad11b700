<script setup lang="ts">
import type { AxSearchParentService } from '@/components/business/AxSearch/type'
// ant-design-vue
import SearchSelect from '@/components/base/SearchSelect/SearchSelect.vue'
import { CRUD_KEY } from '@/service/BaseCrudService'
import WarehouseSearchModal from './component/WarehouseSearchModal.vue'

import { WAREHOUSE_SEARCH_KEY, type WarehouseSearchService } from './index'

// 仓库服务
const service = inject<WarehouseSearchService>(WAREHOUSE_SEARCH_KEY)!
// 父级服务
service.parentService = inject<AxSearchParentService>(CRUD_KEY)
</script>

<template>
  <SearchSelect
    :model-value="service?.selectedKey"
    :placeholder="service?.listPlaceholder"
    :allow-add="service?.config.allowAdd"
    :allow-edit="service?.config.allowEdit"
    :allow-delete="service?.config.allowDelete"
    :loading="service?.listLoading"
    :list-data="service?.listData"
    :open-add-form="() => service?.openAddForm?.()"
    :open-edit-form="(value) => service?.openEditForm?.(value.id!)"
    :on-delete="(value) => service?.deleteEntity?.(value.id!)"
    :on-list-select-change="(value) => service?.onWarehouseChange?.(value)"
    :get-option-value="(value) => service?.getOptionValue?.(value)"
    :get-option-key="(value) => service?.getOptionKey?.(value)"
  />
  <WarehouseSearchModal />
</template>
