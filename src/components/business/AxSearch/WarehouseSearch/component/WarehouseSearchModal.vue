<script setup lang="ts">
// ant-design-vue
import type { FormInstance } from 'ant-design-vue/es/form'
// 业务组件
import type { WarehouseSearchService } from '../service/WarehouseSearchService'
import { CustomDivider } from '@/components/base/CustomDivider'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { inject, ref } from 'vue'
import { WAREHOUSE_SEARCH_KEY } from '../service/WarehouseSearchService'
// 表单验证规则
import { rules } from './rule'

// 使用辅助函数注入表单服务
const warehouseFormService = inject(WAREHOUSE_SEARCH_KEY) as WarehouseSearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Modal 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await warehouseFormService.submitFormAndRefresh()
  warehouseFormService.resetListQuery()
}
</script>

<template>
  <Modal
    :title="warehouseFormService.formTitle"
    :open="warehouseFormService.formOpen"
    :confirm-loading="warehouseFormService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1000px"
    @ok="handleSubmit"
    @cancel="warehouseFormService.closeForm()"
  >
    <Spin :spinning="warehouseFormService.formLoading">
      <Form
        ref="formRef"
        :model="warehouseFormService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem
              label="仓库编码"
              name="warehouseCode"
            >
              <InputText v-model:value="warehouseFormService.formData.warehouseCode" placeholder="请输入仓库编码" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem
              label="仓库名称"
              name="warehouseName"
            >
              <InputText v-model:value="warehouseFormService.formData.warehouseName" placeholder="请输入仓库名称" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库类型" name="warehouseType">
              <DictSelect v-model:value="warehouseFormService.formData.warehouseType" key-code="warehouse_type" placeholder="请选择仓库类型" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库状态" name="status">
              <DictSelect v-model:value="warehouseFormService.formData.status" key-code="warehouse_status" placeholder="请选择仓库状态" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <CustomDivider label="地址信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="省份" name="province">
              <InputText v-model:value="warehouseFormService.formData.province" placeholder="请输入省份" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="城市" name="city">
              <InputText v-model:value="warehouseFormService.formData.city" placeholder="请输入城市" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="区县" name="district">
              <InputText v-model:value="warehouseFormService.formData.district" placeholder="请输入区县" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="warehouseFormService.formData.address" placeholder="请输入详细地址" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="邮政编码" name="postalCode">
              <InputText v-model:value="warehouseFormService.formData.postalCode" placeholder="请输入邮政编码" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <CustomDivider label="联系信息" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人" name="contactPerson">
              <InputText v-model:value="warehouseFormService.formData.contactPerson" placeholder="请输入联系人" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="warehouseFormService.formData.contactPhone" placeholder="请输入联系电话" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model:value="warehouseFormService.formData.contactMobile" placeholder="请输入联系手机" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="warehouseFormService.formData.contactEmail" placeholder="请输入联系邮箱" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 仓库属性 -->
        <CustomDivider label="仓库属性" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="仓库面积(㎡)" name="area">
              <InputNumber v-model:value="warehouseFormService.formData.area" placeholder="请输入仓库面积" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库容量" name="capacity">
              <InputNumber v-model:value="warehouseFormService.formData.capacity" placeholder="请输入仓库容量" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 温湿度控制 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="温度控制" name="temperatureControlled">
              <a-switch v-model:checked="warehouseFormService.formData.temperatureControlled" checked-children="是" un-checked-children="否" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="湿度控制" name="humidityControlled">
              <a-switch v-model:checked="warehouseFormService.formData.humidityControlled" checked-children="是" un-checked-children="否" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow v-if="warehouseFormService.formData.temperatureControlled" :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最低温度(℃)" name="minTemperature">
              <InputNumber v-model:value="warehouseFormService.formData.minTemperature" placeholder="请输入最低温度" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="最高温度(℃)" name="maxTemperature">
              <InputNumber v-model:value="warehouseFormService.formData.maxTemperature" placeholder="请输入最高温度" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow v-if="warehouseFormService.formData.humidityControlled" :gutter="16">
          <FormColumn :span="12">
            <FormItem label="最低湿度(%)" name="minHumidity">
              <InputNumber v-model:value="warehouseFormService.formData.minHumidity" placeholder="请输入最低湿度" :min="0" :max="100" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="最高湿度(%)" name="maxHumidity">
              <InputNumber v-model:value="warehouseFormService.formData.maxHumidity" placeholder="请输入最高湿度" :min="0" :max="100" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 管理信息 -->
        <a-divider>管理信息</a-divider>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="管理员ID" name="managerId">
              <InputNumber v-model:value="warehouseFormService.formData.managerId" placeholder="请输入管理员ID" :min="1" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="管理员姓名" name="managerName">
              <InputText v-model:value="warehouseFormService.formData.managerName" placeholder="请输入管理员姓名" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="上级仓库ID" name="parentWarehouseId">
              <InputNumber v-model:value="warehouseFormService.formData.parentWarehouseId" placeholder="请输入上级仓库ID" :min="1" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="仓库层级" name="warehouseLevel">
              <InputNumber v-model:value="warehouseFormService.formData.warehouseLevel" placeholder="请输入仓库层级" :min="1" :max="10" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="默认仓库" name="isDefault">
              <a-switch v-model:checked="warehouseFormService.formData.isDefault" checked-children="是" un-checked-children="否" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="排序" name="sortOrder">
              <InputNumber v-model:value="warehouseFormService.formData.sortOrder" placeholder="请输入排序" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormItem label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-textarea v-model:value="warehouseFormService.formData.remark" placeholder="请输入备注" :rows="3" />
        </FormItem>
      </Form>
    </Spin>
  </Modal>
</template>
