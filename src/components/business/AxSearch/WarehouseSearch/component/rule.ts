export const rules = {
  warehouseCode: [
    { required: true, message: '请输入仓库编码' },
    { max: 50, message: '仓库编码最多50个字符' },
  ],
  warehouseName: [
    { required: true, message: '请输入仓库名称' },
    { max: 100, message: '仓库名称最多100个字符' },
  ],
  warehouseType: [
    { required: true, message: '请选择仓库类型' },
  ],
  province: [
    { max: 50, message: '省份最多50个字符' },
  ],
  city: [
    { max: 50, message: '城市最多50个字符' },
  ],
  district: [
    { max: 50, message: '区县最多50个字符' },
  ],
  address: [
    { max: 200, message: '详细地址最多200个字符' },
  ],
  postalCode: [
    { max: 20, message: '邮政编码最多20个字符' },
    { pattern: /^\d{6}$/, message: '邮政编码格式不正确' },
  ],
  contactPerson: [
    { max: 50, message: '联系人最多50个字符' },
  ],
  contactPhone: [
    { max: 20, message: '联系电话最多20个字符' },
    { pattern: /^[0-9-()（）\s]*$/, message: '联系电话格式不正确' },
  ],
  contactMobile: [
    { max: 20, message: '联系手机最多20个字符' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
  ],
  contactEmail: [
    { max: 100, message: '联系邮箱最多100个字符' },
    { type: 'email', message: '邮箱格式不正确' },
  ],
  area: [
    { type: 'number', min: 0, message: '仓库面积不能小于0' },
  ],
  capacity: [
    { type: 'number', min: 0, message: '仓库容量不能小于0' },
  ],
  minTemperature: [
    { type: 'number', message: '最低温度必须为数字' },
  ],
  maxTemperature: [
    { type: 'number', message: '最高温度必须为数字' },
  ],
  minHumidity: [
    { type: 'number', min: 0, max: 100, message: '最低湿度范围为0-100' },
  ],
  maxHumidity: [
    { type: 'number', min: 0, max: 100, message: '最高湿度范围为0-100' },
  ],
  warehouseLevel: [
    { type: 'number', min: 1, max: 10, message: '仓库层级范围为1-10' },
  ],
  status: [
    { required: true, message: '请选择仓库状态' },
  ],
  sortOrder: [
    { type: 'number', min: 0, message: '排序不能小于0' },
  ],
  remark: [
    { max: 500, message: '备注最多500个字符' },
  ],
}
