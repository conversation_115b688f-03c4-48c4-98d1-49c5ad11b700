import type { SupplierFormParam, SupplierPageParam, SupplierResult } from '@/api/business/supplier/model/supplier-form-model'
import type { AxSearchConfig, AxSearchParentService } from '@/components/business/AxSearch/type'
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import { supplierApi } from '@/api/business/supplier/supplier-api'
import { BaseCrudService } from '@/service/BaseCrudService'

export const SUPPLIER_SEARCH_KEY = Symbol('SUPPLIER_SEARCH_KEY')

/**
 * 供应商服务
 * 提供供应商相关的业务逻辑和数据管理
 */
export class SupplierSearchService extends BaseCrudService<SupplierResult, SupplierFormParam, SupplierPageParam> {
  config: AxSearchConfig = {
    // 默认配置
    allowAdd: true,
    allowEdit: true,
    allowDelete: true,
  }

  parentService: AxSearchParentService | undefined = undefined

  constructor(config?: AxSearchConfig) {
    // 初始化服务
    super(
      '供应商', // 业务名称
      [],
      {
        // 使用已有的API
        add: supplierApi.addSupplier,
        queryPage: supplierApi.supplierPage,
        getDetail: supplierApi.getSupplierDetail,
        update: supplierApi.updateSupplier,
        import: supplierApi.importSupplier,
        export: supplierApi.exportSupplier,
        delete: supplierApi.deleteSupplier,
        batchDelete: supplierApi.batchDeleteSupplier,
        downloadTemplate: supplierApi.exportSupplier,
      },
    )

    if (config) {
      this.config = {
        ...this.config,
        ...config,
      }
    }
  }

  // ---- 提供服务 ----
  provide() {
    provide(SUPPLIER_SEARCH_KEY, this)
  }

  // ---- 自身业务逻辑 ----

  getOptionValue = (item: SupplierResult): string | number => {
    return item.supplierName || ''
  }

  getOptionKey = (item: SupplierResult): number | undefined => {
    return item.id || undefined
  }

  // ---- 混合业务逻辑 ----
  onSupplierChange = (value: unknown, option?: DefaultOptionType): void => {
    console.log('onSupplierChange', value, option)
  }
}
