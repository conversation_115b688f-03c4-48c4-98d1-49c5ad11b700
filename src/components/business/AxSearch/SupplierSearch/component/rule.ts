export const rules = {
  // 基本字段验证
  supplierCode: [
    { required: true, message: '请输入供应商编码' },
    { max: 50, message: '供应商编码最多50个字符' },
  ],
  supplierName: [
    { required: true, message: '请输入供应商名称' },
    { max: 100, message: '供应商名称最多100个字符' },
  ],
  supplierType: [
    { required: true, message: '请选择供应商类型' },
  ],
  
  // 证件信息验证
  businessLicense: [
    { max: 50, message: '营业执照号最多50个字符' },
  ],
  taxId: [
    { max: 50, message: '税务登记号最多50个字符' },
  ],
  
  // 联系信息验证
  contactPerson: [
    { required: true, message: '请输入主要联系人' },
    { max: 50, message: '主要联系人最多50个字符' },
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话' },
    { max: 20, message: '联系电话最多20个字符' },
    { pattern: /^[0-9-()（）\s]*$/, message: '联系电话格式不正确' },
  ],
  contactEmail: [
    { max: 100, message: '联系邮箱最多100个字符' },
    { type: 'email', message: '邮箱格式不正确' },
  ],
  contactPosition: [
    { max: 50, message: '联系人职位最多50个字符' },
  ],
  alternativeContact: [
    { max: 50, message: '备用联系人最多50个字符' },
  ],
  alternativePhone: [
    { max: 20, message: '备用联系电话最多20个字符' },
    { pattern: /^[0-9-()（）\s]*$/, message: '备用联系电话格式不正确' },
  ],
  
  // 地址信息验证
  province: [
    { max: 50, message: '省份最多50个字符' },
  ],
  city: [
    { max: 50, message: '城市最多50个字符' },
  ],
  district: [
    { max: 50, message: '区县最多50个字符' },
  ],
  address: [
    { max: 200, message: '详细地址最多200个字符' },
  ],
  postalCode: [
    { max: 20, message: '邮政编码最多20个字符' },
    { pattern: /^\d{6}$/, message: '邮政编码格式不正确' },
  ],
  
  // 银行信息验证
  bankName: [
    { max: 100, message: '开户银行最多100个字符' },
  ],
  bankAccount: [
    { max: 50, message: '银行账号最多50个字符' },
    { pattern: /^[0-9-]*$/, message: '银行账号格式不正确' },
  ],
  bankAccountName: [
    { max: 100, message: '开户名称最多100个字符' },
  ],
  
  // 商务信息验证
  paymentTerms: [
    { max: 200, message: '付款条件最多200个字符' },
  ],
  creditLimit: [
    { type: 'number', min: 0, message: '信用额度不能小于0' },
  ],
  creditPeriod: [
    { type: 'number', min: 0, message: '信用期不能小于0' },
  ],
  discountRate: [
    { type: 'number', min: 0, max: 100, message: '折扣率范围为0-100' },
  ],
  processingFeeSingle: [
    { type: 'number', min: 0, message: '单瓦加工费不能小于0' },
  ],
  processingFeeDouble: [
    { type: 'number', min: 0, message: '双瓦加工费不能小于0' },
  ],
  taxRate: [
    { type: 'number', min: 0, max: 100, message: '税率范围为0-100' },
  ],
  minOrderQuantity: [
    { type: 'number', min: 0, message: '最小订单量不能小于0' },
  ],
  leadTime: [
    { type: 'number', min: 0, message: '交货提前期不能小于0' },
  ],
  
  // 状态验证
  status: [
    { required: true, message: '请选择状态' },
  ],
  approvalStatus: [
    { required: true, message: '请选择审批状态' },
  ],
  
  // 备注验证
  remark: [
    { max: 500, message: '备注最多500个字符' },
  ],
  approvalNotes: [
    { max: 500, message: '审批备注最多500个字符' },
  ],
} 