<script setup lang="ts">
import type { FormInstance } from 'ant-design-vue/es/form'
import type { SupplierSearchService } from '../service/SupplierSearchService'
import { CustomDivider } from '@/components/base/CustomDivider'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { inject, ref } from 'vue'
import { SUPPLIER_SEARCH_KEY } from '../service/SupplierSearchService'
import { rules } from './rule'

// 使用辅助函数注入表单服务
const supplierFormService = inject(SUPPLIER_SEARCH_KEY) as SupplierSearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Modal 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await supplierFormService.submitFormAndRefresh()
  supplierFormService.resetListQuery()
}
</script>

<template>
  <Modal
    :title="supplierFormService.formTitle"
    :open="supplierFormService.formOpen"
    :confirm-loading="supplierFormService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
    @ok="handleSubmit"
    @cancel="supplierFormService.closeForm()"
  >
    <Spin :spinning="supplierFormService.formLoading">
      <Form
        ref="formRef"
        :model="supplierFormService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="供应商编码" name="supplierCode">
              <InputText v-model:value="supplierFormService.formData.supplierCode" placeholder="请输入供应商编码" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="供应商名称" name="supplierName">
              <InputText v-model:value="supplierFormService.formData.supplierName" placeholder="请输入供应商名称" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="供应商类型" name="supplierType">
              <DictSelect v-model:value="supplierFormService.formData.supplierType" key-code="supplier_type" placeholder="请选择供应商类型" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="营业执照号" name="businessLicense">
              <InputText v-model:value="supplierFormService.formData.businessLicense" placeholder="请输入营业执照号" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="税务登记号" name="taxId">
              <InputText v-model:value="supplierFormService.formData.taxId" placeholder="请输入税务登记号" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="状态" name="status">
              <DictSelect v-model:value="supplierFormService.formData.status" key-code="supplier_status" placeholder="请选择状态" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <CustomDivider label="联系信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="主要联系人" name="contactPerson">
              <InputText v-model:value="supplierFormService.formData.contactPerson" placeholder="请输入主要联系人" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="supplierFormService.formData.contactPhone" placeholder="请输入联系电话" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="supplierFormService.formData.contactEmail" placeholder="请输入联系邮箱" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="联系人职位" name="contactPosition">
              <InputText v-model:value="supplierFormService.formData.contactPosition" placeholder="请输入联系人职位" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="备用联系人" name="alternativeContact">
              <InputText v-model:value="supplierFormService.formData.alternativeContact" placeholder="请输入备用联系人" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="备用联系电话" name="alternativePhone">
              <InputText v-model:value="supplierFormService.formData.alternativePhone" placeholder="请输入备用联系电话" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <CustomDivider label="地址信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="省份" name="province">
              <InputText v-model:value="supplierFormService.formData.province" placeholder="请输入省份" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="城市" name="city">
              <InputText v-model:value="supplierFormService.formData.city" placeholder="请输入城市" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="区县" name="district">
              <InputText v-model:value="supplierFormService.formData.district" placeholder="请输入区县" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="16">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="supplierFormService.formData.address" placeholder="请输入详细地址" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="邮政编码" name="postalCode">
              <InputText v-model:value="supplierFormService.formData.postalCode" placeholder="请输入邮政编码" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 银行信息 -->
        <CustomDivider label="银行信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="开户银行" name="bankName">
              <InputText v-model:value="supplierFormService.formData.bankName" placeholder="请输入开户银行" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="银行账号" name="bankAccount">
              <InputText v-model:value="supplierFormService.formData.bankAccount" placeholder="请输入银行账号" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="开户名称" name="bankAccountName">
              <InputText v-model:value="supplierFormService.formData.bankAccountName" placeholder="请输入开户名称" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 商务信息 -->
        <CustomDivider label="商务信息" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="付款条件" name="paymentTerms">
              <InputText v-model:value="supplierFormService.formData.paymentTerms" placeholder="请输入付款条件" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="信用额度" name="creditLimit">
              <InputNumber v-model:value="supplierFormService.formData.creditLimit" placeholder="请输入信用额度" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="信用期(天)" name="creditPeriod">
              <InputNumber v-model:value="supplierFormService.formData.creditPeriod" placeholder="请输入信用期" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="6">
            <FormItem label="折扣率(%)" name="discountRate">
              <InputNumber v-model:value="supplierFormService.formData.discountRate" placeholder="请输入折扣率" :min="0" :max="100" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="单瓦加工费" name="processingFeeSingle">
              <InputNumber v-model:value="supplierFormService.formData.processingFeeSingle" placeholder="元/m²" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="双瓦加工费" name="processingFeeDouble">
              <InputNumber v-model:value="supplierFormService.formData.processingFeeDouble" placeholder="元/m²" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="6">
            <FormItem label="税率(%)" name="taxRate">
              <InputNumber v-model:value="supplierFormService.formData.taxRate" placeholder="请输入税率" :min="0" :max="100" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="最小订单量" name="minOrderQuantity">
              <InputNumber v-model:value="supplierFormService.formData.minOrderQuantity" placeholder="请输入最小订单量" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="交货提前期(天)" name="leadTime">
              <InputNumber v-model:value="supplierFormService.formData.leadTime" placeholder="请输入交货提前期" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="战略供应商" name="isStrategic">
              <a-switch v-model:checked="supplierFormService.formData.isStrategic" checked-children="是" un-checked-children="否" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 合作信息 -->
        <CustomDivider label="合作信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="合作开始日期" name="cooperationStartDate">
              <DatePicker v-model:value="supplierFormService.formData.cooperationStartDate" style="width: 100%" placeholder="请选择合作开始日期" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="合同到期日期" name="contractExpiryDate">
              <DatePicker v-model:value="supplierFormService.formData.contractExpiryDate" style="width: 100%" placeholder="请选择合同到期日期" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="累计订单金额" name="totalOrderAmount">
              <InputNumber v-model:value="supplierFormService.formData.totalOrderAmount" :disabled="true" style="width: 100%" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 审批信息 -->
        <CustomDivider label="审批信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="审批状态" name="approvalStatus">
              <DictSelect v-model:value="supplierFormService.formData.approvalStatus" key-code="approval_status" placeholder="请选择审批状态" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="审批人ID" name="approvalUserId">
              <InputNumber v-model:value="supplierFormService.formData.approvalUserId" placeholder="请输入审批人ID" :min="1" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="审批日期" name="approvalDate">
              <DatePicker v-model:value="supplierFormService.formData.approvalDate" style="width: 100%" placeholder="请选择审批日期" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 备注信息 -->
        <FormItem label="审批备注" name="approvalNotes" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-textarea v-model:value="supplierFormService.formData.approvalNotes" placeholder="请输入审批备注" :rows="2" />
        </FormItem>

        <FormItem label="备注" name="remark" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
          <a-textarea v-model:value="supplierFormService.formData.remark" placeholder="请输入备注" :rows="3" />
        </FormItem>
      </Form>
    </Spin>
  </Modal>
</template>
