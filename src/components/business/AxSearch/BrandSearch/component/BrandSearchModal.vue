<script setup lang="ts">
// ant-design-vue
import type { FormInstance } from 'ant-design-vue/es/form'
// 业务组件
import type { BrandSearchService } from '../service/BrandSearchService'
import { CustomDivider } from '@/components/base/CustomDivider'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputNumber, InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { inject, ref } from 'vue'
import { BRAND_SEARCH_KEY } from '../service/BrandSearchService'
// 表单验证规则
import { rules } from './rule'

// 使用辅助函数注入表单服务
const brandFormService = inject(BRAND_SEARCH_KEY) as BrandSearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Modal 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await brandFormService.submitFormAndRefresh()
  brandFormService.resetListQuery()
}
</script>

<template>
  <Modal
    :title="brandFormService.formTitle"
    :open="brandFormService.formOpen"
    :confirm-loading="brandFormService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="800px"
    @ok="handleSubmit"
    @cancel="brandFormService.closeForm()"
  >
    <Spin :spinning="brandFormService.formLoading">
      <Form
        ref="formRef"
        :model="brandFormService.formData"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem
              label="品牌编码"
              name="brandCode"
            >
              <InputText v-model:value="brandFormService.formData.brandCode" placeholder="请输入品牌编码" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem
              label="品牌名称"
              name="brandName"
            >
              <InputText v-model:value="brandFormService.formData.brandName" placeholder="请输入品牌名称" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="品牌Logo" name="logoUrl">
              <InputText v-model:value="brandFormService.formData.logoUrl" placeholder="请输入品牌Logo地址" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="品牌网站" name="website">
              <InputText v-model:value="brandFormService.formData.website" placeholder="请输入品牌网站" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="排序" name="sort">
              <InputNumber v-model:value="brandFormService.formData.sort" placeholder="请输入排序" :min="0" style="width: 100%" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="品牌状态" name="status">
              <a-switch v-model:checked="brandFormService.formData.status" checked-children="启用" un-checked-children="禁用" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 品牌描述 -->
        <CustomDivider label="品牌描述" />

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="品牌描述" name="description">
              <a-textarea
                v-model:value="brandFormService.formData.description"
                placeholder="请输入品牌描述"
                :rows="4"
                :max-length="500"
                show-count
              />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
