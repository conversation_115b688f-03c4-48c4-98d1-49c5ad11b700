import type { BrandFormParam, BrandPageParam, BrandResult } from '@/api/business/brand/model/brand-form-model'
import type { AxSearchConfig, AxSearchParentService } from '@/components/business/AxSearch/type'
import { brandApi } from '@/api/business/brand/brand-api'
import { BaseCrudService } from '@/service/BaseCrudService'
import { provide } from 'vue'

export const BRAND_SEARCH_KEY = Symbol('BRAND_SEARCH_KEY')

/**
 * 品牌服务
 * 提供品牌相关的业务逻辑和数据管理
 */
export class BrandSearchService extends BaseCrudService<BrandResult, BrandFormParam, BrandPageParam> {
  config: AxSearchConfig = {
    allowAdd: true,
    allowEdit: true,
    allowDelete: true,
  }

  parentService: AxSearchParentService | undefined = undefined

  constructor() {
    // 初始化服务
    super(
      '品牌信息', // 业务名称
      [],
      {
        // 使用已有的API
        add: brandApi.addBrand,
        queryPage: brandApi.brandPage,
        getDetail: brandApi.brandDetail,
        update: brandApi.updateBrand,
        import: brandApi.importBrand,
        export: brandApi.exportBrand,
        delete: brandApi.deleteBrand,
        batchDelete: brandApi.batchDeleteBrand,
        downloadTemplate: brandApi.exportBrand,
      },
    )
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide(BRAND_SEARCH_KEY, this)
  }

  // ---------------------------- 自身业务逻辑 ----------------------------
  getOptionValue = (item: BrandResult): string | number => {
    return item.brandName || ''
  }

  getOptionKey = (item: BrandResult): string => {
    return item.id?.toString() || ''
  }

  // ---------------------------- 混合业务逻辑 ----------------------------
  onBrandChange = (value: unknown, option?: unknown | unknown[]): void => {
    console.log('onBrandChange', value, option)
  }
}
