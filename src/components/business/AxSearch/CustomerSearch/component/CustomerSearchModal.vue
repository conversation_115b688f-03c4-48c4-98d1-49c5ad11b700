<script setup lang="ts">
// ant-design-vue
import type { FormInstance } from 'ant-design-vue/es/form'
// 业务组件
import { CustomDivider } from '@/components/base/CustomDivider'
import { CustomTextArea as TextArea } from '@/components/base/CustomText'
import { Form, FormColumn, FormItem, FormRow } from '@/components/base/Form'
import { InputText } from '@/components/base/Input'
import { Modal } from '@/components/base/Modal'
import { Spin } from '@/components/base/Spin'
import { DictSelect } from '@/components/utils/Dict'
import { inject, ref } from 'vue'
import { CUSTOMER_SEARCH_KEY ,type CustomerSearchService} from '../service/CustomerSearchService'
// 表单验证规则
import { rules } from './rule'

// 使用辅助函数注入表单服务
const customerFormService = inject(CUSTOMER_SEARCH_KEY) as CustomerSearchService

// 表单引用
const formRef = ref<FormInstance>()

// 处理 Modal 确认事件
async function handleSubmit() {
  if (formRef.value)
    await formRef.value.validate()
  // 验证通过后提交（使用带自动刷新的提交方法）
  await customerFormService.submitFormAndRefresh()
  customerFormService.resetListQuery()
}
</script>

<template>
  <Modal
    :title="customerFormService.formTitle"
    :open="customerFormService.formOpen"
    :confirm-loading="customerFormService.submitLoading"
    :mask-closable="false"
    :destroy-on-close="true"
    width="1200px"
    @ok="handleSubmit"
    @cancel="customerFormService.closeForm()"
  >
    <Spin :spinning="customerFormService.formLoading">
      <Form
        ref="formRef"
        :model="customerFormService.formData"
        :rules="rules"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
      >
        <!-- 基本信息 -->
        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem
              label="客户编码"
              name="customerCode"
            >
              <InputText v-model:value="customerFormService.formData.customerCode" placeholder="请输入客户编码" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem
              label="客户名称"
              name="customerName"
            >
              <InputText v-model:value="customerFormService.formData.customerName" placeholder="请输入客户名称" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="客户类型" name="customerType">
              <DictSelect v-model:value="customerFormService.formData.customerType" key-code="customer_type" placeholder="请选择客户类型" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="信用等级" name="creditLevel">
              <DictSelect v-model:value="customerFormService.formData.creditLevel" key-code="credit_level" placeholder="请选择信用等级" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 联系信息 -->
        <CustomDivider label="联系信息" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人姓名" name="contactName">
              <InputText v-model:value="customerFormService.formData.contactName" placeholder="请输入联系人姓名" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系人职位" name="contactPosition">
              <InputText v-model:value="customerFormService.formData.contactPosition" placeholder="请输入联系人职位" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系人部门" name="contactDepartment">
              <InputText v-model:value="customerFormService.formData.contactDepartment" placeholder="请输入联系人部门" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系电话" name="contactPhone">
              <InputText v-model:value="customerFormService.formData.contactPhone" placeholder="请输入联系电话" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="联系手机" name="contactMobile">
              <InputText v-model:value="customerFormService.formData.contactMobile" placeholder="请输入联系手机" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="联系邮箱" name="contactEmail">
              <InputText v-model:value="customerFormService.formData.contactEmail" placeholder="请输入联系邮箱" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 地址信息 -->
        <CustomDivider label="地址信息" />

        <FormRow :gutter="16">
          <FormColumn :span="8">
            <FormItem label="省份" name="province">
              <InputText v-model:value="customerFormService.formData.province" placeholder="请输入省份" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="城市" name="city">
              <InputText v-model:value="customerFormService.formData.city" placeholder="请输入城市" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="8">
            <FormItem label="区县" name="district">
              <InputText v-model:value="customerFormService.formData.district" placeholder="请输入区县" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="详细地址" name="address">
              <InputText v-model:value="customerFormService.formData.address" placeholder="请输入详细地址" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="收货地址" name="shippingAddress">
              <InputText v-model:value="customerFormService.formData.shippingAddress" placeholder="请输入收货地址" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="发票地址" name="billingAddress">
              <InputText v-model:value="customerFormService.formData.billingAddress" placeholder="请输入发票地址" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 企业信息 -->
        <CustomDivider label="企业信息" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="营业执照号" name="businessLicense">
              <InputText v-model:value="customerFormService.formData.businessLicense" placeholder="请输入营业执照号" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="税务登记号" name="taxId">
              <InputText v-model:value="customerFormService.formData.taxId" placeholder="请输入税务登记号" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="开户银行" name="bankName">
              <InputText v-model:value="customerFormService.formData.bankName" placeholder="请输入开户银行" />
            </FormItem>
          </FormColumn>
          <FormColumn :span="12">
            <FormItem label="银行账号" name="bankAccount">
              <InputText v-model:value="customerFormService.formData.bankAccount" placeholder="请输入银行账号" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <!-- 其他信息 -->
        <CustomDivider label="其他信息" />

        <FormRow :gutter="16">
          <FormColumn :span="12">
            <FormItem label="客户状态" name="status">
              <DictSelect v-model:value="customerFormService.formData.status" key-code="customer_status" placeholder="请选择客户状态" />
            </FormItem>
          </FormColumn>
        </FormRow>

        <FormRow :gutter="16">
          <FormColumn :span="24">
            <FormItem label="备注" name="remark">
              <TextArea v-model:value="customerFormService.formData.remark" placeholder="请输入备注" :rows="3" />
            </FormItem>
          </FormColumn>
        </FormRow>
      </Form>
    </Spin>
  </Modal>
</template>
