import type { CustomerFormParam, CustomerPageParam, CustomerResult } from '@/api/business/customer/model/customer-form-model'
import type { AxSearchConfig, AxSearchParentService } from '@/components/business/AxSearch/type'
import type { DefaultOptionType } from 'ant-design-vue/es/select'
import { customerApi } from '@/api/business/customer/customer-api'
import { BaseCrudService } from '@/service/BaseCrudService'

export const CUSTOMER_SEARCH_KEY = Symbol('CUSTOMER_SEARCH_KEY')

/**
 * 客户服务
 * 提供客户相关的业务逻辑和数据管理
 */
export class CustomerSearchService extends BaseCrudService<CustomerResult, CustomerFormParam, CustomerPageParam> {
  config: AxSearchConfig = {
    // 默认配置
    allowAdd: true,
    allowEdit: true,
    allowDelete: true,
  }

  parentService: AxSearchParentService | undefined = undefined

  constructor(config?: AxSearchConfig) {
    // 初始化服务
    super(
      '客户信息', // 业务名称
      [],
      {
        // 使用已有的API
        add: customerApi.addCustomer,
        queryPage: customerApi.customerPage,
        getDetail: customerApi.customerDetail,
        update: customerApi.updateCustomer,
        import: customerApi.importCustomer,
        export: customerApi.exportCustomer,
        delete: customerApi.deleteCustomer,
        batchDelete: customerApi.batchDeleteCustomer,
        downloadTemplate: customerApi.exportCustomer,
      },
    )

    if (config) {
      this.config = {
        ...this.config,
        ...config,
      }
    }
  }

  // ---------------------------- 提供服务 ----------------------------
  provide() {
    provide(CUSTOMER_SEARCH_KEY, this)
  }
  // ---------------------------- 自身业务逻辑 ----------------------------

  getOptionValue = (item: CustomerResult): string | number => {
    return item.customerName || ''
  }

  getOptionKey = (item: CustomerResult): number | undefined => {
    return item.id || undefined
  }

  // ---------------------------- 混合业务逻辑 ----------------------------
  onCustomerChange = (value: unknown, option?: DefaultOptionType): void => {
    console.log('onCustomerChange', value, option)
  }
}
