import type { SkuPageParam, SkuResult } from '@/api/business/sku/model/sku-types'
import { skuApi } from '@/api/business/sku/sku-api'
import { TableProcessor } from '@/service/internal/TableProcessor'
import { ref } from 'vue'

export const AX_SKU_TABLE_SELECTOR_SERVICE = Symbol('AxSkuTableSelectorService')

export class AxSkuTableSelectorService extends TableProcessor<SkuResult, SkuPageParam> {
  // sku搜索参数
  private _skuSearchParam = reactive({
    skuNameExt: '', // 使用扩展搜索字段
    status: '1', // 只获取上架sku
    pageParam: {
      pageNum: 1,
      pageSize: 10,
    },
  })

  // sku列表数据
  private _skuList = ref<SkuResult[]>([])

  // sku列表总数
  private _skuTotal = ref(0)

  // sku列表加载状态
  private _skuLoading = ref(false)

  // 已选sku列表
  private _selectedSkus = ref<SkuResult[]>([])

  constructor() {
    super('AxSkuTableSelectorService', ref([]), {
      queryPage: skuApi.skuPage,
      delete: skuApi.deleteSku,
      batchDelete: skuApi.batchDeleteSku,
      import: skuApi.importSku,
      export: skuApi.exportSku,
      downloadTemplate: skuApi.exportSku,
    })
  }

  provide() {
    provide(AX_SKU_TABLE_SELECTOR_SERVICE, this)
  }

  get productList() {
    return this._skuList.value
  }

  get productSearchParam() {
    return this._skuSearchParam
  }

  get productTotal() {
    return this._skuTotal.value
  }

  get productLoading() {
    return this._skuLoading.value
  }

  // ==================== 防抖搜索方法 ====================

  /**
   * 执行实际的搜索操作
   */
  private _executeSearch = async () => {
    try {
      this._skuLoading.value = true
    }
    catch (error) {
      console.error('搜索SKU失败:', error)
    }
    finally {
      this._skuLoading.value = false
    }
  }

  // ==================== 搜索方法 ====================

  searchProducts = async () => {
    await this._executeSearch()
  }

  resetSearch = () => {
    this._skuSearchParam.skuNameExt = ''
    this._skuSearchParam.status = '1'
    this._skuSearchParam.pageParam.pageNum = 1
    this._skuSearchParam.pageParam.pageSize = 10
    this._executeSearch()
  }

  // ==================== 获取方法 ====================
  getSelectedSkus = (): SkuResult[] => {
    return this._selectedSkus.value
  }

  // ==================== 设置方法 ====================
}
