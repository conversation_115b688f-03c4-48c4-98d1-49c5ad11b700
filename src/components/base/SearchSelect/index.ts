import type { BaseModel } from '@/api/base-model/base-model'
import type { DefaultOptionType } from 'ant-design-vue/es/select'
// 组件
import SearchSelect from './SearchSelect.vue'

// 类型
interface SearchSelectProps {
  modelValue?: string | number
  placeholder?: string
  allowAdd?: boolean
  allowEdit?: boolean
  allowDelete?: boolean
  loading?: boolean
  listData?: BaseModel[]
  openAddForm?: () => void
  openEditForm?: (value: BaseModel) => void
  onDelete?: (value: BaseModel) => void
  onListSelectChange?: (value: string | number | undefined, option?: DefaultOptionType) => void
  getOptionValue?: (item: BaseModel) => string | number
  getOptionKey?: (item: BaseModel) => number | string | undefined
  onSearch?: () => void
  onFocus?: () => void
  onBlur?: () => void
}

export { SearchSelect, type SearchSelectProps }
