<script setup lang="ts">
import type { BaseModel } from '@/api/base-model/base-model'
// ant-design-vue
import type { DefaultOptionType, SelectValue } from 'ant-design-vue/es/select'
import type { SearchSelectProps } from './index'
import { CustomIconButton, CustomIconText } from '../CustomIcon'

import FlexRow from '../Flex/FlexRow.vue'

const {
  modelValue,
  placeholder = '请选择',
  allowAdd = true,
  allowEdit = true,
  allowDelete = true,
  loading = false,
  listData = [],
  openAddForm = () => { },
  openEditForm = () => { },
  onDelete = () => { },
  onListSelectChange = () => { },
  getOptionValue = () => '',
  getOptionKey = () => '',
} = defineProps<SearchSelectProps>()

function handleChange(value: SelectValue, option: DefaultOptionType) {
  if (value === '--') {
    openAddForm?.()
    onListSelectChange?.('')
  }
  else {
    onListSelectChange?.(value as string | number | undefined, option)
  }
}

function handleOpenAddForm() {
  openAddForm?.()
}

function handleOpenEditForm(value: BaseModel) {
  openEditForm?.(value)
}

function handleOnDelete(value: BaseModel) {
  onDelete?.(value)
}
</script>

<template>
  <div class="w-full">
    <a-select
      class="w-full"
      :value="modelValue"
      :loading="loading"
      :placeholder="placeholder"
      :allow-clear="true"
      :show-search="true"
      option-label-prop="label"
      @change="handleChange"
    >
      <!-- 自定义新增选项 -->
      <a-select-option
        v-if="allowAdd"
        key="--"
        v-privilege="'add'" value="--" class="border-b border-gray-200 border-solid cursor-pointer text-gray-500"
        @click="handleOpenAddForm"
      >
        <CustomIconText class="p-2" label="自定义 (custom)" icon-type="PlusOutlined" />
      </a-select-option>

      <!-- 列表选项 -->
      <a-select-option
        v-for="item in listData"
        :key="getOptionKey(item)"
        :value="getOptionValue(item)"
      >
        <FlexRow>
          <span class="flex-1 truncate">{{ getOptionValue(item) }}</span>
          <FlexRow v-if="allowEdit || allowDelete" gap="2px">
            <CustomIconButton v-if="allowEdit" icon-type="EditOutlined" type="link" @click="handleOpenEditForm(item)" />
            <CustomIconButton v-if="allowDelete" icon-type="DeleteOutlined" type="link" @click="handleOnDelete(item)" />
          </FlexRow>
        </FlexRow>
      </a-select-option>

      <!-- 自定义未找到内容 -->
      <template #notFoundContent>
        <slot name="notFoundContent">
          <span v-if="loading">加载中...</span>
          <span v-else>暂无数据</span>
        </slot>
      </template>
    </a-select>
  </div>
</template>
