<script setup lang="ts">
import { defineModel, defineProps } from 'vue'

const {
  allowClear = true,
  placeholder = '请输入',
} = defineProps<{
  placeholder?: string
  allowClear?: boolean
}>()

const modelValue = defineModel<string>({ default: '' })
</script>

<template>
  <a-input
    v-model:value="modelValue"
    v-bind="$attrs"
    class="w-full mx-auto"
    type="text"
    :allow-clear="allowClear"
    :placeholder="placeholder"
  />
</template>
