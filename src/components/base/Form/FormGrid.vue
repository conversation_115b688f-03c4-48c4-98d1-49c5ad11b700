<script setup lang="ts">
import type { FormGridProps } from './index'

const {
  columns = 5,
  gap = 4,
  responsive = true,
} = defineProps<FormGridProps>()

</script>

<template>
  <div
    class="w-full grid"
    :class="[
      `grid-cols-${columns}`,
      {
        'sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5': responsive,
        'gap-4': gap === 4,
        'gap-8': gap === 8,
        'gap-12': gap === 12,
        'gap-16': gap === 16,
        'gap-20': gap === 20,
        'gap-24': gap === 24,
        'gap-28': gap === 28,
      },
    ]"
  >
    <slot />
  </div>
</template>