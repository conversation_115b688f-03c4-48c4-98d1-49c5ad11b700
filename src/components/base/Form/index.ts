import Form from './Form.vue'
import FormColumn from './FormColumn.vue'
import FormGrid from './FormGrid.vue'
import FormItem from './FormItem.vue'
import FormRow from './FormRow.vue'

export { Form, FormColumn, FormGrid, FormItem, FormRow }

export interface FormGridProps {
  // 自定义列数，默认为5
  columns?: number
  // 列间距，默认为4 (1rem)
  gap?: number
  // 是否响应式布局
  responsive?: boolean
}

export interface FormItemProps {
  row?: number
  col?: number
}
