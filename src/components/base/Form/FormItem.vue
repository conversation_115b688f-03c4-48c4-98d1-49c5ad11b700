<script setup lang="ts">
import type { FormItemProps } from '.'
import { computed } from 'vue'

const { row = 1, col = 1 } = defineProps<FormItemProps>()

// 动态样式对象
const dynamicGridStyle = computed(() => {
  return {
    gridColumn: `span ${col}`,
    gridRow: `span ${row}`,
  }
})
</script>

<template>
  <div class="w-full mx-auto" :style="dynamicGridStyle">
    <a-form-item v-bind="$attrs">
      <slot />
    </a-form-item>
  </div>
</template>
