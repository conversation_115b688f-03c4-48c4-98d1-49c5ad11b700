<script setup lang="ts">
import type { CustomTextAreaProps } from './index'
import { defineModel, defineProps } from 'vue'

const {
  allowClear = true,
  placeholder = '请输入',
} = defineProps<CustomTextAreaProps>()

const modelValue = defineModel<string>({ default: '' })
</script>

<template>
  <a-textarea
    v-model:value="modelValue"
    v-bind="$attrs"
    :allow-clear="allowClear"
    :placeholder="placeholder"
  />
</template>
