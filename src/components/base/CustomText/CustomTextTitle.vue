<script setup lang="ts">
import { computed } from 'vue'
import { CustomTextTitleClassNameMap, CustomTextTitleLevelEnum, type CustomTextTitleProps, getTitleLevel } from './index'

const {
  level = CustomTextTitleLevelEnum.H1,
  value = '',
  replace = '--',
  className = '',
} = defineProps<CustomTextTitleProps>()

const classes = computed(() => {
  return [
    CustomTextTitleClassNameMap[level],
    className,
    'm-0',
  ].filter(Boolean).join(' ')
})

const htmlLevel = computed(() => getTitleLevel(level))
</script>

<template>
  <component :is="`h${htmlLevel}`" :class="classes" :style="{ fontWeight: 'bold' }">
    <slot>
      {{ `${value}` || replace }}
    </slot>
  </component>
</template>
