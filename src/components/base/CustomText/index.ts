import CustomColorText from './CustomColorText.vue'
import CustomText from './CustomText.vue'
import CustomTextArea from './CustomTextArea.vue'
import CustomTextTitle from './CustomTextTitle.vue'

export { CustomColorText, CustomText, CustomTextArea, CustomTextTitle }

export interface CustomTextBaseProps {
  value?: string | number | null
  replace?: string
  className?: string
}

export interface CustomTextProps extends CustomTextBaseProps {
  strong?: boolean
}

export interface CustomTextAreaProps extends CustomTextProps {
  placeholder?: string
  allowClear?: boolean
}

export interface CustomColorTextProps extends CustomTextProps {
  color?: string
}

export enum CustomTextTitleLevelEnum {
  H1 = 1,
  H2 = 2,
  H3 = 3,
  H4 = 4,
  H5 = 5,
}

// @unocss-include
export const CustomTextTitleClassNameMap: Record<CustomTextTitleLevelEnum, string> = {
  [CustomTextTitleLevelEnum.H1]: 'text-2xl',
  [CustomTextTitleLevelEnum.H2]: 'text-xl',
  [CustomTextTitleLevelEnum.H3]: 'text-lg',
  [CustomTextTitleLevelEnum.H4]: 'text-base',
  [CustomTextTitleLevelEnum.H5]: 'text-sm',
}

export function getTitleLevel(level: CustomTextTitleLevelEnum): number {
  const levelMap = {
    [CustomTextTitleLevelEnum.H1]: 1,
    [CustomTextTitleLevelEnum.H2]: 2,
    [CustomTextTitleLevelEnum.H3]: 3,
    [CustomTextTitleLevelEnum.H4]: 4,
    [CustomTextTitleLevelEnum.H5]: 5,
  }
  return levelMap[level]
}

export interface CustomTextTitleProps extends CustomTextBaseProps {
  level?: CustomTextTitleLevelEnum
}
