<script setup lang="ts">
import type { CustomTreeProps } from './index'

const {
  treeData = [],
  labelKey = 'label',
  valueKey = 'value',
  childrenKey = 'children',
  placeholder = '请选择',
  filterProp,
} = defineProps<CustomTreeProps>()

const emit = defineEmits(['change'])
const value = ref()
const internalTreeData = ref(treeData)

watch(
  () => treeData,
  (newData) => {
    internalTreeData.value = newData
  },
  { deep: true },
)

function onChange(e: unknown) {
  emit('change', e)
}
</script>

<template>
  <a-tree-select
    v-model:value="value"
    :tree-data="internalTreeData"
    :field-names="{ label: labelKey, value: valueKey, children: childrenKey }"
    :placeholder="placeholder"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-node-filter-prop="filterProp || labelKey"
    show-search
    allow-clear
    tree-default-expand-all
    class="w-full"
    @change="onChange"
  />
</template>
