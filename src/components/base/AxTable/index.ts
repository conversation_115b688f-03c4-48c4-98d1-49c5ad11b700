import AxTableOperateButtons from '../AxTableOperateButtons/AxTableOperateButtons.vue'
import AxTable from './AxTable.vue'

export { AxTable, AxTableOperateButtons }

type Key = string | number

export interface AxTableService {
  columns: Record<string, unknown>[]
  tableLoading: boolean
  rowKey: string
  scroll?: object
  selectedRowKeyList: Key[]
  expandable?: object
  expandedRowKeys?: Key[]
  handleExpandedRow?: (expanded: boolean, record: Record<string, unknown>) => void
  onChange?: (...args: unknown[]) => void
  getRawTableData: () => unknown[]
  onSelectChange: (selectedRowKeys: Key[], selectedRows: unknown[]) => void
}

interface RowSelection {
  selectedRowKeys: Key[]
  onChange: (selectedRowKeys: Key[], selectedRows: unknown[]) => void
}

type TableSize = 'small' | 'middle' | 'large'

export interface AxTableProps {
  needInjectService?: boolean
  loading?: boolean
  size?: TableSize
  showPagination?: boolean
  columns?: Record<string, unknown>[]
  dataSource?: unknown[]
  injectKey?: string | symbol
  rowKey?: string
  rowSelection?: RowSelection | Record<string, never>
}
