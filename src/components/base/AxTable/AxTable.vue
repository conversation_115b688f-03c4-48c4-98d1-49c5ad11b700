<script lang="ts" setup>
import type { AxTableProps, AxTableService } from './index'
import { CRUD_KEY } from '@/service/BaseCrudService'
import { inject } from 'vue'

const { needInjectService = true, size = 'small', loading = false, showPagination = true, dataSource = undefined, injectKey = CRUD_KEY, columns = undefined, rowKey = undefined, rowSelection = undefined } = defineProps<AxTableProps>()

const service = needInjectService ? inject<AxTableService>(injectKey)! : undefined
</script>

<template>
  <a-table
    v-if="needInjectService && service"
    :size="size"
    :data-source="service.getRawTableData()"
    :columns="service.columns"
    :row-key="service.rowKey"
    bordered
    :pagination="false"
    :loading="service.tableLoading"
    :scroll="service.scroll"
    :row-selection="{
      selectedRowKeys: service.selectedRowKeyList,
      onChange: service.onSelectChange,
    }"
    :expandable="service.expandable"
    :expanded-row-keys="service.expandedRowKeys"
    @expand="service?.handleExpandedRow"
    @change="service?.onChange"
  >
    <template v-for="name in Object.keys($slots)" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
  </a-table>
  <a-table
    v-else
    :size="size"
    :data-source="dataSource"
    :columns="columns"
    :row-key="rowKey"
    bordered
    :pagination="false"
    :loading="loading"
    :row-selection="rowSelection"
  >
    <template v-for="name in Object.keys($slots)" #[name]="slotProps">
      <slot :name="name" v-bind="slotProps" />
    </template>
  </a-table>
  <AxPagination v-if="showPagination" />
</template>
