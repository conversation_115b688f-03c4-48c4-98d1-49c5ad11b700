<script setup lang="ts">
import type { IconTextProps } from './type'
import CustomIcon from './CustomIcon.vue'

const {
  label = '',
  iconPosition = 'left',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
} = defineProps<IconTextProps>()
</script>

<template>
  <div v-bind="$attrs" class="flex items-center gap-1">
    <CustomIcon v-if="iconPosition === 'left'" :icon-type="iconType" :spin="spin" :rotate="rotate" />
    <span>{{ label }}</span>
    <CustomIcon v-if="iconPosition === 'right'" :icon-type="iconType" :spin="spin" :rotate="rotate" />
  </div>
</template>
