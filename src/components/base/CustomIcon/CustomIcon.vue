<script setup lang="ts">
import type { IconProps } from './type'
import * as Icons from '@ant-design/icons-vue'
import { computed } from 'vue'

const { iconType = 'UserOutlined', spin = false, rotate = 0 } = defineProps<IconProps>()

const iconComponent = computed(() => {
  // 首字母大写，Antd Icon 组件名如：UserOutlined
  const iconName = iconType.endsWith('Outlined') ? iconType : `${iconType}Outlined`
  return Icons[iconName as keyof typeof Icons]
})
</script>

<template>
  <template v-if="iconComponent">
    <component
      :is="iconComponent as unknown as Component"
      :spin="spin"
      :rotate="rotate"
    />
  </template>
  <slot v-else />
</template>
