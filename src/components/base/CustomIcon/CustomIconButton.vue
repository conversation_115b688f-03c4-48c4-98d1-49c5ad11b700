<script setup lang="ts">
import type { IconButtonProps } from './type'
import { Button } from '../Button'
import CustomIcon from './CustomIcon.vue'

const {
  label = '',
  iconType = 'UserOutlined',
  spin = false,
  rotate = 0,
} = defineProps<IconButtonProps>()
</script>

<template>
  <Button v-bind="$attrs">
    <template #icon>
      <CustomIcon :icon-type="iconType" :spin="spin" :rotate="rotate" />
    </template>
    {{ label }}
  </Button>
</template>
