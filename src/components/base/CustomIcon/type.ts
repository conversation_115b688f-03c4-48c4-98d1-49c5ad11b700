export interface IconProps {
  iconType?: string
  spin?: boolean
  rotate?: number
}

export interface IconAnchorProps extends IconProps {
  label?: string
  color?: string
  [key: string]: unknown
}

export interface IconButtonProps extends IconProps {
  label?: string
  [key: string]: unknown
}

export interface IconTextProps extends IconProps {
  label?: string
  iconPosition?: 'left' | 'right'
  [key: string]: unknown
}
