<script setup lang="ts">
// ant design vue 的日期选择器
// https://ant.design/components/date-picker-cn/#components-date-picker-demo-range
import type { RangePicker as AntRangePicker } from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import type { DateRangePickerProps } from './type'
import { computed, defineExpose, ref } from 'vue'

// 定义 props
const props = defineProps<DateRangePickerProps>()
// 定义 emits
const emit = defineEmits<{
  'update:from': [value: string | Dayjs | undefined]
  'update:to': [value: string | Dayjs | undefined]
}>()
// format 默认值
const format = 'YYYY-MM-DD'
const valueFormat = 'YYYY-MM-DD HH:mm:ss'

// 明确 ref 类型（假设 a-date-picker 是 Ant Design Vue 组件）
const datePickerRef = ref<InstanceType<typeof AntRangePicker>>()

// 计算属性：将from和to组合成range数组格式
const rangeValue = computed({
  get(): any {
    return props.from && props.to ? [props.from, props.to] : undefined
  },
  set(value: any) {
    if (value && value.length === 2) {
      emit('update:from', value[0])
      emit('update:to', value[1])
    }
    else {
      emit('update:from', undefined)
      emit('update:to', undefined)
    }
  },
})

// 处理日期范围变化
function handleRangeChange(dates: any) {
  if (dates && dates.length === 2) {
    emit('update:from', dates[0])
    emit('update:to', dates[1])
  }
  else {
    emit('update:from', undefined)
    emit('update:to', undefined)
  }
}

// 直接在顶层暴露
defineExpose({
  datePickerRef, // 暴露 ref，命名更清晰
})
</script>

<template>
  <div :class="props.class || 'w-full'">
    <!-- 透传 $attrs，方便外部自定义 -->
    <a-range-picker
      ref="datePickerRef"
      class="w-full"
      :placeholder="props.placeholder || ['起始时间', '结束时间']"
      :format="format"
      v-bind="$attrs"
      :value-format="valueFormat"
      :value="rangeValue"
      @change="handleRangeChange"
    />
  </div>
</template>
