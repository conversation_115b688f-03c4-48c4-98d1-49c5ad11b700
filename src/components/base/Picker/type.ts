import type { Dayjs } from "dayjs";

// 参考ant design 的日期范围选择器
// https://ant.design/components/date-picker-cn/#components-date-picker-demo-range
type Generic = string;
type GenericFn = (value: Dayjs) => string;
type FormatType = Generic | GenericFn | Array<Generic | GenericFn>;

type TimeType = string | Dayjs | undefined

export interface PickerProps {
  from: TimeType;
  to: TimeType;
  placeholder?: string[];
  [key: string]: unknown;
}

export interface DatePickerProps {
  from?: TimeType;
  placeholder?: string[];
  format?: FormatType;
  class?: string;
  [key: string]: unknown;
}

export interface TimePickerProps extends PickerProps {
  format?: FormatType;
  class?: string;
}

export interface DateRangePickerProps extends PickerProps {
  format?: FormatType;
  class?: string;
}
