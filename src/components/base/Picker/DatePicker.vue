<script setup lang="ts">
// ant design vue 的日期选择器
// https://ant.design/components/date-picker-cn/#components-date-picker-demo-basic
import type { DatePicker as AntDatePicker } from 'ant-design-vue'
import type { Dayjs } from 'dayjs'
import type { DatePickerProps } from './type'
import { computed, defineExpose, ref } from 'vue'

// 定义 props
const props = defineProps<DatePickerProps>()
// 定义 emits
const emit = defineEmits<{
  'update:from': [value: string | Dayjs | undefined]
}>()

// format 默认值
const format = 'YYYY-MM-DD'
const valueFormat = 'YYYY-MM-DD HH:mm:ss'

// 明确 ref 类型
const datePickerRef = ref<InstanceType<typeof AntDatePicker>>()

// 计算属性：处理单个日期值
const dateValue = computed({
  get(): string | Dayjs | undefined {
    return props.from
  },
  set(value: string | Dayjs | undefined) {
    emit('update:from', value)
  },
})

// 处理日期变化
function handleDateChange(date: string | Dayjs | undefined) {
  emit('update:from', date)
}

// 暴露必要的方法和属性
defineExpose({
  datePickerRef,
})
</script>

<template>
  <div :class="props.class || 'w-72'">
    <!-- 透传 $attrs，方便外部自定义 -->
    <a-date-picker
      ref="datePickerRef"
      :placeholder="props.placeholder?.[0] || '选择日期'"
      :format="format"
      v-bind="$attrs"
      :value-format="valueFormat"
      :value="dateValue"
      @change="handleDateChange"
    />
  </div>
</template>
