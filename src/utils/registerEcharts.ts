import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {
  DatasetComponent,
  DataZoomComponent,
  GeoComponent,
  GraphicComponent,
  GridComponent,
  LegendComponent,
  Mark<PERSON>ineComponent,
  MarkPointComponent,
  TitleComponent,
  TooltipComponent,
} from 'echarts/components'
import * as echart from 'echarts/core'
import { SVGRenderer } from 'echarts/renderers'

export function registerEcharts() {
  echart.use([<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line<PERSON>hart, PictorialBarChart])
  echart.use([DatasetComponent, DataZoomComponent, GeoComponent, GraphicComponent, GridComponent, LegendComponent, TitleComponent, Toolt<PERSON>Component, MarkPointComponent, MarkLineComponent])
  echart.use([SVGRenderer])
}
