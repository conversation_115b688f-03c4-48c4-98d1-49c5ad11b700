import { dictApi } from '@/api/support/dict-api'

/**
 * 字典项类型定义
 */
export interface DictItem {
  id: number
  valueCode: string
  valueName: string
  dictKeyId: number
  sort: number
  remark?: string
}

/**
 * 字典值列表类型定义
 */

const dictMapping: Record<string, Promise<DictItem[]> | DictItem[] | undefined> = {}

/**
 * 缓存获取字典值列表
 * @param dictKeyCode 字典key编码
 * @returns 返回字典值列表
 */
export function cacheGetDictValueList(dictKeyCode: string): Promise<DictItem[]> {
  const val = dictMapping[dictKeyCode]
  if (!val) {
    const promise = dictApi.valueList(dictKeyCode).then((resp: any) => {
      if (resp && resp.data) {
        dictMapping[dictKeyCode] = resp.data
        return resp.data
      }
      else {
        console.warn(`字典 ${dictKeyCode} 没有数据`)
        return [] as DictValueList
      }
    })
    dictMapping[dictKeyCode] = promise
    return promise
  }
  if (val instanceof Promise) {
    return val
  }
  return Promise.resolve(val)
}

/**
 * 根据字典编码和值获取对应的字典项
 * @param dictCode 字典编码
 * @param value 字典值
 * @returns 返回字典项
 */
export async function getDictItem(dictCode: string, value: string | number): Promise<DictItem | undefined> {
  try {
    const dictList = await cacheGetDictValueList(dictCode)
    return dictList.find(item => item.valueCode === value)
  }
  catch (error) {
    console.error(`获取字典项失败: ${dictCode}, ${value}`, error)
    return undefined
  }
}

/**
 * 根据字典编码和值获取对应的字典名称
 * @param dictCode 字典编码
 * @param value 字典值
 * @param defaultValue 默认值，当找不到对应的字典项时返回
 * @returns 返回字典名称或默认值
 */
export async function getDictLabel(dictCode: string, value: string | number, defaultValue: string = ''): Promise<string> {
  try {
    const item = await getDictItem(dictCode, value)
    return item ? item.valueName : defaultValue
  }
  catch (error) {
    console.error(`获取字典名称失败: ${dictCode}, ${value}`, error)
    return defaultValue
  }
}

/**
 * 获取字典选项列表，可用于下拉框等组件
 * @param dictCode 字典编码
 * @returns 返回选项列表 {label, value}
 */
export async function getDictOptions(dictCode: string): Promise<{ label: string, value: string | number }[]> {
  try {
    const dictList = await cacheGetDictValueList(dictCode)
    return dictList.map(item => ({
      label: item.valueName,
      value: item.valueCode,
    }))
  }
  catch (error) {
    console.error(`获取字典选项失败: ${dictCode}`, error)
    return []
  }
}

/**
 * 根据字典编码创建一个Fast-CRUD兼容的字典配置
 * @param dictCode 字典编码
 * @returns 返回一个Promise，解析为字典配置对象
 */
export async function createDictConfig(dictCode: string): Promise<any> {
  try {
    const options = await getDictOptions(dictCode)
    return {
      data: options,
    }
  }
  catch (error) {
    console.error(`创建字典配置失败: ${dictCode}`, error)
    return { data: [] }
  }
}
