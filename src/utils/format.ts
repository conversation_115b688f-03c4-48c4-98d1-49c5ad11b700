/*
 * 格式化工具函数
 */
import dayjs from 'dayjs'

/**
 * 格式化日期时间
 * @param time 日期时间
 * @param format 格式化字符串
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(time: string | null | undefined, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!time) {
    return '--'
  }
  return dayjs(time).format(format)
}

/**
 * 格式化日期
 * @param time 日期时间
 * @param format 格式化字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(time: string | null | undefined, format = 'YYYY-MM-DD'): string {
  if (!time) {
    return '--'
  }
  return dayjs(time).format(format)
}
interface FormatMoneyOptionsType extends Intl.NumberFormatOptions {
  locale: Intl.LocalesArgument
}
/**
 * 格式化金额
 * @param value 金额
 * @param options 选项
 * @returns 格式化后的金额
 */
export function formatMoney(value: number | undefined, options: FormatMoneyOptionsType = {
  locale: 'zh-CN',
  currency: 'CNY',
  minimumFractionDigits: 3,
}) {
  const { locale, currency, minimumFractionDigits } = options

  if (typeof value !== 'number' || Number.isNaN(value)) {
    return '--'
  }
  return value.toLocaleString(locale, { style: 'currency', currency, minimumFractionDigits, ...options })
}
