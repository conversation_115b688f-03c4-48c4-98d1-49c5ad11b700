/*
 * 权限
 */

import { useUserStore } from '@/store/modules/system/user'

export function privilegeDirective(el: { parentNode: { removeChild: (arg0: any) => void } }, binding: DirectiveBinding<any>) {
  // 超级管理员
  if (useUserStore().administratorFlag) {
    return true
  }
  // 获取功能点权限
  const userPointsList = useUserStore().getPointList
  if (!userPointsList) {
    return false
  }
  // 如果有权限，删除节点
  if (!_some(userPointsList, ['webPerms', binding.value])) {
    el.parentNode.removeChild(el)
  }
  return true
}
