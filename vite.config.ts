import * as path from 'node:path'
// 导入 process
import process from 'node:process'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { codeInspectorPlugin } from 'code-inspector-plugin'

import LodashImports from 'lodash-imports'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import AutoImportComponents from 'unplugin-vue-components/vite'
// import VueRouter from 'unplugin-vue-router/vite'
import { defineConfig, loadEnv } from 'vite'
import customVariables from './src/theme/custom-variables.ts'

const env = loadEnv('development', import.meta.dirname, 'VITE_')

export default defineConfig({
  base: process.env.NODE_ENV === 'production' ? '/' : '/',
  root: process.cwd(),
  resolve: {
    alias: {
      'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
      '@': path.resolve(import.meta.dirname, 'src'),
    },
  },
  // 服务端渲染
  server: {
    port: +env.VITE_PORT,
    host: true,
    proxy: {
      '/api': {
        // target: 'https://www.txy.info/api',
        target: 'http://127.0.0.1:10244',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, ''),
      },
    },
  },
  plugins: [
    // VueRouter({
    //   dts: './types/typed-router.d.ts',
    // }),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
    vue(),
    vueJsx(),
    UnoCSS(),
    AutoImport({
      dirs: [
        // './src/composables/**/*.ts',
        // './src/events/**/*.ts',
        // './src/enum/**/*.ts',
        './src/store/**/*.ts',
        './src/utils/**/*.ts',
      ],
      imports: [
        'vue',
        'vue-router',
        '@vueuse/core',
        LodashImports(),
        {
          from: 'vue-loading-overlay',
          imports: ['useLoading'],
        },
        {
          from: '@/plugins/dayjs',
          imports: ['dayjs'],
        },
        {
          from: '@faker-js/faker',
          imports: [['fakerZH_CN', 'faker']],
        },
      ],
      dts: './types/auto-imports.d.ts',
    }),
    AutoImportComponents({
      resolvers: [
        AntDesignVueResolver({ importStyle: false, resolveIcons: true }),
      ],
      dts: './types/components.d.ts',
    }),
  ],
  optimizeDeps: {
    include: [
      'lodash-es',
      '@vueuse/core',
      'echarts/core',
      '@iconify-json/ant-design',
      'ant-design-vue/es/locale/zh_CN',
      'dayjs/locale/zh-cn',
      'ant-design-vue/es/locale/en_US',

    ],
    exclude: ['vue-demi'],
  },
  esbuild: {
    // 清除console和debugger
    // pure: ['console.log', 'debugger'],
  },
  build: {
    rollupOptions: {
      output: {
        // 配置这个是让不同类型文件放在不同文件夹，不会显得太乱
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        manualChunks(id) {
          // 静态资源分拆打包
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString()
          }
        },
      },
    },
    target: 'modules',
    outDir: 'dist', // 指定输出路径
    assetsDir: 'assets', // 指定生成静态文件目录
    assetsInlineLimit: 4096, // 小于此阈值的导入或引用资源将内联为 base64 编码
    chunkSizeWarningLimit: 500, // chunk 大小警告的限制
    // minify: 'terser', // 混淆器，terser构建后文件体积更小
    emptyOutDir: true, // 打包前先清空原有打包文件
  },
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: customVariables,
        javascriptEnabled: true,
      },
    },
  },
  define: {
    __INTLIFY_PROD_DEVTOOLS__: false,
  },
})
